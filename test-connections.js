#!/usr/bin/env node

// 简单的数据库连接测试脚本
const { Client } = require('pg');
const Redis = require('ioredis');

// 从环境变量或使用默认值
const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://qyqm_user:qyqm_password@localhost:5432/qyqm_teachers';
const REDIS_URL = process.env.REDIS_URL || 'redis://:qyqm_redis_password@localhost:6379';

async function testPostgreSQL() {
  console.log('🔍 测试 PostgreSQL 连接...');
  
  try {
    const client = new Client({
      connectionString: DATABASE_URL,
    });
    
    await client.connect();
    console.log('✅ PostgreSQL 连接成功');
    
    // 测试查询
    const result = await client.query('SELECT version()');
    console.log('📊 PostgreSQL 版本:', result.rows[0].version.split(',')[0]);
    
    // 检查扩展
    const extensions = await client.query(`
      SELECT extname FROM pg_extension 
      WHERE extname IN ('uuid-ossp', 'pg_trgm', 'btree_gin')
    `);
    console.log('🔧 已安装的扩展:', extensions.rows.map(row => row.extname));
    
    await client.end();
  } catch (error) {
    console.error('❌ PostgreSQL 连接失败:', error.message);
    return false;
  }
  
  return true;
}

async function testRedis() {
  console.log('🔍 测试 Redis 连接...');
  
  try {
    const redis = new Redis(REDIS_URL);
    
    // 测试连接
    const pong = await redis.ping();
    console.log('✅ Redis 连接成功, PING 响应:', pong);
    
    // 测试基本操作
    await redis.set('test:connection', 'success', 'EX', 10);
    const value = await redis.get('test:connection');
    console.log('📊 Redis 测试值:', value);
    
    // 获取 Redis 信息
    const info = await redis.info('server');
    const version = info.match(/redis_version:([^\r\n]+)/)?.[1];
    console.log('📊 Redis 版本:', version);
    
    await redis.quit();
  } catch (error) {
    console.error('❌ Redis 连接失败:', error.message);
    return false;
  }
  
  return true;
}

async function main() {
  console.log('🚀 开始测试数据库连接...\n');
  
  const pgSuccess = await testPostgreSQL();
  console.log('');
  const redisSuccess = await testRedis();
  
  console.log('\n📋 测试结果:');
  console.log(`PostgreSQL: ${pgSuccess ? '✅ 正常' : '❌ 失败'}`);
  console.log(`Redis: ${redisSuccess ? '✅ 正常' : '❌ 失败'}`);
  
  if (pgSuccess && redisSuccess) {
    console.log('\n🎉 所有数据库连接测试通过！');
    console.log('\n📝 下一步:');
    console.log('1. 安装项目依赖: pnpm install');
    console.log('2. 运行 Prisma 迁移: cd packages/database && pnpm run db:push');
    console.log('3. 启动开发服务器: pnpm run dev');
  } else {
    console.log('\n⚠️  部分连接测试失败，请检查服务状态');
    process.exit(1);
  }
}

// 运行测试
main().catch(console.error);
