# Docker 环境设置指南

本项目使用 Docker 来运行 Redis 和 PostgreSQL 服务，配合 OrbStack 提供高性能的开发环境。

## 前置要求

- [OrbStack](https://orbstack.dev/) 已安装并运行
- Docker Compose 已安装（OrbStack 自带）

## 快速开始

### 1. 复制环境变量文件

```bash
cp .env.example .env
```

### 2. 启动服务

使用便捷脚本启动 Redis 和 PostgreSQL：

```bash
# 启动基础服务（Redis + PostgreSQL）
./scripts/docker.sh start

# 或启动所有服务（包括管理工具）
./scripts/docker.sh start-all
```

### 3. 验证服务

```bash
# 查看服务状态
./scripts/docker.sh status

# 查看日志
./scripts/docker.sh logs
```

## 服务信息

### PostgreSQL
- **端口**: 5432
- **数据库**: qyqm_teachers
- **用户名**: qyqm_user
- **密码**: qyqm_password
- **连接字符串**: `postgresql://qyqm_user:qyqm_password@localhost:5432/qyqm_teachers`

### Redis
- **端口**: 6379
- **密码**: qyqm_redis_password
- **连接字符串**: `redis://:qyqm_redis_password@localhost:6379`

### 管理工具（可选）

启动管理工具：`./scripts/docker.sh start-all`

- **pgAdmin**: http://localhost:8080
  - 邮箱: <EMAIL>
  - 密码: admin123

- **Redis Commander**: http://localhost:8081

## 常用命令

```bash
# 启动服务
./scripts/docker.sh start

# 停止服务
./scripts/docker.sh stop

# 重启服务
./scripts/docker.sh restart

# 查看服务状态
./scripts/docker.sh status

# 查看日志
./scripts/docker.sh logs

# 查看特定服务日志
./scripts/docker.sh logs postgres
./scripts/docker.sh logs redis

# 进入 PostgreSQL 命令行
./scripts/docker.sh psql

# 进入 Redis 命令行
./scripts/docker.sh redis-cli

# 备份数据库
./scripts/docker.sh backup

# 清理所有数据（危险操作）
./scripts/docker.sh clean
```

## 数据库迁移

启动 PostgreSQL 后，运行 Prisma 迁移：

```bash
# 进入数据库包目录
cd packages/database

# 推送数据库结构（开发环境）
npm run db:push

# 或运行迁移（生产环境）
npm run db:migrate

# 查看数据库
npm run studio
```

## 项目集成

### 环境变量配置

确保你的 `.env` 文件包含正确的数据库连接信息：

```env
DATABASE_URL="postgresql://qyqm_user:qyqm_password@localhost:5432/qyqm_teachers?schema=public"
REDIS_URL="redis://:qyqm_redis_password@localhost:6379"
```

### 应用连接

你的应用应该能够通过以下方式连接：

1. **Prisma** 会自动使用 `DATABASE_URL` 环境变量
2. **ioredis** 包会使用 `REDIS_URL` 或相关的 Redis 配置

## 故障排除

### 端口冲突

如果端口被占用，可以修改 `docker-compose.yml` 中的端口映射：

```yaml
ports:
  - "15432:5432"  # PostgreSQL
  - "16379:6379"  # Redis
```

### 权限问题

确保脚本有执行权限：

```bash
chmod +x scripts/docker.sh
```

### 数据持久化

数据存储在 Docker volumes 中：
- `postgres_data`: PostgreSQL 数据
- `redis_data`: Redis 数据
- `pgadmin_data`: pgAdmin 配置

### 清理和重置

如果需要完全重置环境：

```bash
# 停止服务并删除所有数据
./scripts/docker.sh clean

# 重新启动
./scripts/docker.sh start
```

## 生产环境注意事项

在生产环境中使用时，请：

1. 修改默认密码
2. 配置适当的资源限制
3. 设置备份策略
4. 配置监控和日志
5. 使用 Docker Secrets 管理敏感信息

## 性能优化

### PostgreSQL 优化

可以在 `docker-compose.yml` 中添加性能参数：

```yaml
command: >
  postgres
  -c shared_preload_libraries=pg_stat_statements
  -c max_connections=200
  -c shared_buffers=256MB
  -c effective_cache_size=1GB
```

### Redis 优化

Redis 配置文件位于 `docker/redis/redis.conf`，可以根据需要调整内存和持久化设置。
