# Monorepo 包管理指南

## 📦 包结构

```
packages/
├── ioredis/          # Redis客户端包
│   ├── index.ts      # 源码
│   ├── index.d.ts    # 类型定义
│   └── dist/         # 编译输出
└── database/         # Prisma数据库包
    ├── src/          # 源码
    ├── prisma/       # Prisma schema
    └── dist/         # 编译输出
```

## 🚀 快速开始

### 安装依赖

```bash
# 在根目录安装所有依赖
pnpm install
```

### 构建包

```bash
# 构建所有包
pnpm build

# 或者单独构建
pnpm --filter @workspace/ioredis build
pnpm --filter @workspace/database build
```

### 开发模式

```bash
# 启动所有项目的开发模式
pnpm dev

# 或者单独启动
pnpm --filter @workspace/ioredis dev
pnpm --filter @workspace/database dev
```

## 📋 包详情

### @workspace/ioredis

- **功能**: Redis客户端封装
- **导出**: redis实例、辅助函数、常量
- **依赖**: ioredis
- **构建**: TypeScript编译（CJS + ESM）

### @workspace/database

- **功能**: Prisma客户端和服务层
- **导出**: prisma实例、服务类、Zod schemas
- **依赖**: @prisma/client, @workspace/ioredis, jsonwebtoken, zod
- **构建**: Prisma生成 + TypeScript编译

## 🔧 使用方式

### 在其他包中使用

```json
{
  "dependencies": {
    "@workspace/database": "workspace:*",
    "@workspace/ioredis": "workspace:*"
  }
}
```

### 导入示例

```typescript
// 使用数据库
import { prisma, AdminAuthService } from "@workspace/database";

// 使用Redis
import { redis, redisHelpers, REDIS_KEYS } from "@workspace/ioredis";
```

## ⚡ 开发建议

1. **构建顺序**: ioredis → database → 其他应用
2. **热重载**: 修改包后会自动重新构建
3. **类型检查**: 使用 `pnpm check-types` 检查所有包
4. **数据库操作**: 使用 `pnpm --filter @workspace/database db:push` 等命令

## 🛠️ 故障排除

### 包未找到错误

```bash
# 重新安装依赖
pnpm install

# 重新构建包
pnpm build
```

### 类型错误

```bash
# 重新生成Prisma客户端
pnpm --filter @workspace/database build:prisma

# 重新构建TypeScript
pnpm --filter @workspace/database build:ts
```

### 开发模式问题

```bash
# 清理并重建
pnpm --filter @workspace/database prebuild
pnpm --filter @workspace/database build
```
