-- Create<PERSON><PERSON>
CREATE TYPE "RequirementCategory" AS ENUM ('LEARNING_ABILITY', 'PSY<PERSON><PERSON><PERSON><PERSON>CAL_SUPPORT', 'COLLEGE_APPLICATION', 'SPECIAL_ADMISSION', 'HON<PERSON><PERSON><PERSON>_MACAO', '<PERSON><PERSON><PERSON><PERSON><PERSON>_COUNTRIES', 'NON_ENGLISH_COUNTRIES', 'ACADEM<PERSON>_PLANNING', 'CAREER_PLANNING', 'STUDY_ABROAD', 'EXAM_PREPARATION', 'EXTRACURRICULAR');

-- AlterTable
ALTER TABLE "MatchRequest" ADD COLUMN     "category" "RequirementCategory";

-- CreateTable
CREATE TABLE "MatchAlgorithmConfig" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "category" "RequirementCategory",
    "gradeMatchWeight" DOUBLE PRECISION NOT NULL DEFAULT 0.2,
    "subjectMatchWeight" DOUBLE PRECISION NOT NULL DEFAULT 0.2,
    "locationMatchWeight" DOUBLE PRECISION NOT NULL DEFAULT 0.1,
    "ratingWeight" DOUBLE PRECISION NOT NULL DEFAULT 0.2,
    "successRateWeight" DOUBLE PRECISION NOT NULL DEFAULT 0.2,
    "capacityWeight" DOUBLE PRECISION NOT NULL DEFAULT 0.05,
    "responseTimeWeight" DOUBLE PRECISION NOT NULL DEFAULT 0.05,
    "priceMatchWeight" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "urgencyMultiplier" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "newPlannerBoost" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "repeatCustomerBoost" DOUBLE PRECISION NOT NULL DEFAULT 0.1,
    "minRating" DOUBLE PRECISION,
    "minSuccessRate" DOUBLE PRECISION,
    "maxResponseTime" INTEGER,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MatchAlgorithmConfig_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WeightAdjustmentHistory" (
    "id" TEXT NOT NULL,
    "configId" TEXT NOT NULL,
    "adjustedBy" TEXT NOT NULL,
    "adjustmentType" TEXT NOT NULL,
    "reason" TEXT,
    "oldWeights" JSONB NOT NULL,
    "newWeights" JSONB NOT NULL,
    "performanceData" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "WeightAdjustmentHistory_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "MatchAlgorithmConfig_name_key" ON "MatchAlgorithmConfig"("name");

-- CreateIndex
CREATE INDEX "MatchAlgorithmConfig_category_idx" ON "MatchAlgorithmConfig"("category");

-- CreateIndex
CREATE INDEX "MatchAlgorithmConfig_isActive_idx" ON "MatchAlgorithmConfig"("isActive");

-- CreateIndex
CREATE INDEX "MatchAlgorithmConfig_isDefault_idx" ON "MatchAlgorithmConfig"("isDefault");

-- CreateIndex
CREATE INDEX "WeightAdjustmentHistory_configId_idx" ON "WeightAdjustmentHistory"("configId");

-- CreateIndex
CREATE INDEX "WeightAdjustmentHistory_createdAt_idx" ON "WeightAdjustmentHistory"("createdAt");

-- AddForeignKey
ALTER TABLE "WeightAdjustmentHistory" ADD CONSTRAINT "WeightAdjustmentHistory_configId_fkey" FOREIGN KEY ("configId") REFERENCES "MatchAlgorithmConfig"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
