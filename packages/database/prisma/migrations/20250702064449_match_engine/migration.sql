-- Create<PERSON><PERSON>
CREATE TYPE "MatchRequestStatus" AS ENUM ('PENDING', 'MATCHING', 'MATCHED', 'CONFIRMED', 'CANCELLED', 'EXPIRED');

-- CreateEnum
CREATE TYPE "MatchRequestType" AS ENUM ('STUDENT_TO_PLANNER', 'PARENT_TO_PLANNER', 'PLANNER_TO_PLANNER', 'INSTITUTION_BATCH');

-- CreateEnum
CREATE TYPE "MatchMode" AS ENUM ('PUBLIC_POOL', 'SMART_MATCH', 'DIRECT_ASSIGN');

-- CreateTable
CREATE TABLE "MatchRequest" (
    "id" TEXT NOT NULL,
    "requesterId" TEXT NOT NULL,
    "requesterType" "MatchRequestType" NOT NULL,
    "tenantId" TEXT,
    "requirements" JSONB NOT NULL,
    "urgency" INTEGER NOT NULL DEFAULT 0,
    "mode" "MatchMode" NOT NULL DEFAULT 'SMART_MATCH',
    "status" "MatchRequestStatus" NOT NULL DEFAULT 'PENDING',
    "expiredAt" TIMESTAMP(3),
    "matchedPlannerId" TEXT,
    "matchedAt" TIMESTAMP(3),
    "confirmedAt" TIMESTAMP(3),
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MatchRequest_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MatchResponse" (
    "id" TEXT NOT NULL,
    "matchRequestId" TEXT NOT NULL,
    "plannerId" TEXT NOT NULL,
    "message" TEXT,
    "availability" JSONB,
    "quote" DECIMAL(65,30),
    "isAccepted" BOOLEAN NOT NULL DEFAULT false,
    "isRejected" BOOLEAN NOT NULL DEFAULT false,
    "respondedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "MatchResponse_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MatchEvaluation" (
    "id" TEXT NOT NULL,
    "matchRequestId" TEXT NOT NULL,
    "evaluatorId" TEXT NOT NULL,
    "evaluatorType" TEXT NOT NULL,
    "rating" INTEGER NOT NULL,
    "comment" TEXT,
    "tags" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "MatchEvaluation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PlannerMatchProfile" (
    "id" TEXT NOT NULL,
    "plannerId" TEXT NOT NULL,
    "isAcceptingMatch" BOOLEAN NOT NULL DEFAULT true,
    "maxConcurrent" INTEGER NOT NULL DEFAULT 10,
    "preferredGrades" TEXT[],
    "preferredSubjects" TEXT[],
    "preferredLocations" TEXT[],
    "expertise" JSONB,
    "availability" JSONB,
    "responseTime" INTEGER NOT NULL DEFAULT 24,
    "basePrice" DECIMAL(65,30),
    "priceRange" JSONB,
    "matchScore" DOUBLE PRECISION NOT NULL DEFAULT 0.5,
    "successRate" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PlannerMatchProfile_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MatchHistory" (
    "id" TEXT NOT NULL,
    "matchRequestId" TEXT NOT NULL,
    "plannerId" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "details" JSONB,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "MatchHistory_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "MatchRequest_requesterId_idx" ON "MatchRequest"("requesterId");

-- CreateIndex
CREATE INDEX "MatchRequest_tenantId_idx" ON "MatchRequest"("tenantId");

-- CreateIndex
CREATE INDEX "MatchRequest_status_idx" ON "MatchRequest"("status");

-- CreateIndex
CREATE INDEX "MatchRequest_createdAt_idx" ON "MatchRequest"("createdAt");

-- CreateIndex
CREATE INDEX "MatchResponse_plannerId_idx" ON "MatchResponse"("plannerId");

-- CreateIndex
CREATE INDEX "MatchResponse_respondedAt_idx" ON "MatchResponse"("respondedAt");

-- CreateIndex
CREATE UNIQUE INDEX "MatchResponse_matchRequestId_plannerId_key" ON "MatchResponse"("matchRequestId", "plannerId");

-- CreateIndex
CREATE INDEX "MatchEvaluation_matchRequestId_idx" ON "MatchEvaluation"("matchRequestId");

-- CreateIndex
CREATE INDEX "MatchEvaluation_evaluatorId_idx" ON "MatchEvaluation"("evaluatorId");

-- CreateIndex
CREATE UNIQUE INDEX "PlannerMatchProfile_plannerId_key" ON "PlannerMatchProfile"("plannerId");

-- CreateIndex
CREATE INDEX "PlannerMatchProfile_plannerId_idx" ON "PlannerMatchProfile"("plannerId");

-- CreateIndex
CREATE INDEX "MatchHistory_matchRequestId_idx" ON "MatchHistory"("matchRequestId");

-- CreateIndex
CREATE INDEX "MatchHistory_plannerId_idx" ON "MatchHistory"("plannerId");

-- CreateIndex
CREATE INDEX "MatchHistory_timestamp_idx" ON "MatchHistory"("timestamp");

-- AddForeignKey
ALTER TABLE "MatchRequest" ADD CONSTRAINT "MatchRequest_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MatchRequest" ADD CONSTRAINT "MatchRequest_matchedPlannerId_fkey" FOREIGN KEY ("matchedPlannerId") REFERENCES "Planner"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MatchResponse" ADD CONSTRAINT "MatchResponse_matchRequestId_fkey" FOREIGN KEY ("matchRequestId") REFERENCES "MatchRequest"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MatchResponse" ADD CONSTRAINT "MatchResponse_plannerId_fkey" FOREIGN KEY ("plannerId") REFERENCES "Planner"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MatchEvaluation" ADD CONSTRAINT "MatchEvaluation_matchRequestId_fkey" FOREIGN KEY ("matchRequestId") REFERENCES "MatchRequest"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PlannerMatchProfile" ADD CONSTRAINT "PlannerMatchProfile_plannerId_fkey" FOREIGN KEY ("plannerId") REFERENCES "Planner"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
