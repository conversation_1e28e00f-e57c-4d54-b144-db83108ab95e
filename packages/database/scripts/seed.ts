import { PrismaClient } from "../src/generated/prisma";
import { createHash } from "crypto";

const prisma = new PrismaClient();

// MD5加密函数
function hashPassword(password: string): string {
  return createHash("md5").update(password).digest("hex");
}

async function main() {
  console.log("开始种子数据...");

  // ==================== 管理后台用户 ====================

  // 创建测试管理员用户
  const adminUser = await prisma.adminUser.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      username: "admin",
      name: "超级管理员",
      password: hashPassword("123456"), // 密码: 123456
      role: "SUPER_ADMIN",
      status: "ACTIVE",
      permissions: ["*"], // 所有权限
      allowedModules: ["*"], // 所有模块
    },
  });

  console.log("创建管理员用户:", adminUser);

  // 创建测试普通管理员
  const normalAdmin = await prisma.adminUser.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      username: "manager",
      name: "系统管理员",
      password: hashPassword("123456"), // 密码: 123456
      role: "ADMIN",
      status: "ACTIVE",
      permissions: [
        "users:read",
        "users:write",
        "courses:read",
        "courses:write",
        "system:read",
      ],
      allowedModules: ["users", "courses", "system"],
    },
  });

  console.log("创建普通管理员:", normalAdmin);

  // ==================== 客户端体验用户 ====================

  // 创建体验用户
  const demoUser = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      username: "demo",
      name: "体验用户",
      password: hashPassword("123456"), // 密码: 123456
      isActive: true,
      emailVerified: true,
      bio: "这是一个演示账户，用于体验系统功能",
    },
  });

  console.log("创建体验用户:", demoUser);

  // 创建机构管理员用户
  const institutionAdmin = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      username: "institution_admin",
      name: "机构管理员",
      password: hashPassword("123456"), // 密码: 123456
      isActive: true,
      emailVerified: true,
      bio: "演示机构管理员账户",
    },
  });

  console.log("创建机构管理员:", institutionAdmin);

  // 创建规划师用户
  const plannerUser = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      username: "planner",
      name: "资深规划师",
      password: hashPassword("123456"), // 密码: 123456
      isActive: true,
      emailVerified: true,
      bio: "专业升学规划师，5年经验",
    },
  });

  console.log("创建规划师用户:", plannerUser);

  // ==================== 创建租户/工作区 ====================

  // 创建机构租户
  const institutionTenant = await prisma.tenant.upsert({
    where: {
      customDomain: "demo-institution",
    },
    update: {},
    create: {
      name: "演示教育机构",
      type: "INSTITUTION",
      status: "ACTIVE",
      description: "这是一个演示教育机构，用于展示系统功能",
      contactName: "机构负责人",
      contactPhone: "13800138000",
      contactEmail: "<EMAIL>",
      address: "北京市朝阳区示例街道123号",
      customDomain: "demo-institution",
      settings: {
        allowStudentSelfRegister: true,
        requireParentApproval: false,
        defaultPlanType: "升学规划",
      },
    },
  });

  console.log("创建机构租户:", institutionTenant);

  // 创建个人规划师租户
  const plannerTenant = await prisma.tenant.upsert({
    where: {
      customDomain: "planner-li",
    },
    update: {},
    create: {
      name: "李老师工作室",
      type: "INDIVIDUAL",
      status: "ACTIVE",
      description: "专业升学规划服务",
      contactName: "李老师",
      contactPhone: "13900139000",
      contactEmail: "<EMAIL>",
      address: "上海市浦东新区规划路456号",
      customDomain: "planner-li",
      settings: {
        maxStudents: 50,
        specialties: ["国际升学", "艺术留学"],
        workingHours: "周一至周五 9:00-18:00",
      },
    },
  });

  console.log("创建个人规划师租户:", plannerTenant);

  // ==================== 创建租户用户关系 ====================

  // 体验用户 -> 机构租户 (多个角色)
  const demoTenantUser = await prisma.tenantUser.upsert({
    where: {
      userId_tenantId: {
        userId: demoUser.id,
        tenantId: institutionTenant.id,
      },
    },
    update: {},
    create: {
      userId: demoUser.id,
      tenantId: institutionTenant.id,
      role: "TENANT_ADMIN",
      isActive: true,
    },
  });

  console.log("创建体验用户租户关系:", demoTenantUser);

  // 机构管理员 -> 机构租户
  const institutionTenantUser = await prisma.tenantUser.upsert({
    where: {
      userId_tenantId: {
        userId: institutionAdmin.id,
        tenantId: institutionTenant.id,
      },
    },
    update: {},
    create: {
      userId: institutionAdmin.id,
      tenantId: institutionTenant.id,
      role: "TENANT_ADMIN",
      isActive: true,
    },
  });

  console.log("创建机构管理员租户关系:", institutionTenantUser);

  // 规划师 -> 个人租户
  const plannerTenantUser = await prisma.tenantUser.upsert({
    where: {
      userId_tenantId: {
        userId: plannerUser.id,
        tenantId: plannerTenant.id,
      },
    },
    update: {},
    create: {
      userId: plannerUser.id,
      tenantId: plannerTenant.id,
      role: "TENANT_ADMIN",
      isActive: true,
    },
  });

  console.log("创建规划师租户关系:", plannerTenantUser);

  // 规划师也加入机构（展示多租户功能）
  const plannerInInstitution = await prisma.tenantUser.upsert({
    where: {
      userId_tenantId: {
        userId: plannerUser.id,
        tenantId: institutionTenant.id,
      },
    },
    update: {},
    create: {
      userId: plannerUser.id,
      tenantId: institutionTenant.id,
      role: "PLANNER",
      isActive: true,
    },
  });

  console.log("创建规划师在机构的关系:", plannerInInstitution);

  // ==================== 创建规划师档案 ====================

  const plannerProfile = await prisma.planner.upsert({
    where: { tenantUserId: plannerTenantUser.id },
    update: {},
    create: {
      tenantUserId: plannerTenantUser.id,
      tenantId: plannerTenant.id,
      title: "高级升学规划师",
      specialties: ["国际教育", "艺术留学", "理工科升学"],
      experience: 5,
      introduction:
        "拥有5年升学规划经验，成功帮助200+学生进入理想院校。擅长国际教育路径规划和艺术类专业申请指导。",
      totalStudents: 156,
      successRate: 0.92,
      rating: 4.8,
    },
  });

  console.log("创建规划师档案:", plannerProfile);

  // ==================== 创建规划师匹配配置 ====================
  // 注意：系统已实现延迟初始化策略
  // PlannerMatchProfile 会在规划师首次访问匹配功能时自动创建
  // 这里为演示目的创建一个完整配置的示例

  const plannerMatchProfile = await prisma.plannerMatchProfile.upsert({
    where: { plannerId: plannerProfile.id },
    update: {},
    create: {
      plannerId: plannerProfile.id,
      isAcceptingMatch: true,
      maxConcurrent: 15,
      preferredGrades: ["高一", "高二", "高三"],
      preferredSubjects: ["数学", "物理", "化学", "生物"],
      preferredLocations: ["北京", "上海", "广州", "深圳"],
      expertise: {
        specialties: ["理工科升学", "国际教育", "竞赛指导"],
        certifications: ["升学规划师认证", "心理咨询师"],
        languages: ["中文", "英文"]
      },
      availability: {
        weekdays: ["周一", "周二", "周三", "周四", "周五"],
        timeSlots: ["09:00-12:00", "14:00-17:00", "19:00-21:00"],
        timezone: "Asia/Shanghai"
      },
      responseTime: 2, // 2小时内响应
      basePrice: 500.00,
      priceRange: {
        consultationFee: { min: 300, max: 800 },
        planningFee: { min: 2000, max: 8000 },
        currency: "CNY"
      },
      matchScore: 4.8,
      successRate: 0.92
    },
  });

  console.log("创建规划师匹配配置（演示数据）:", plannerMatchProfile);

  // ==================== 创建匹配算法配置 ====================

  const defaultMatchConfig = await prisma.matchAlgorithmConfig.upsert({
    where: { name: "default" },
    update: {},
    create: {
      name: "default",
      description: "默认匹配算法配置",
      category: null, // 通用配置
      gradeMatchWeight: 0.2,
      subjectMatchWeight: 0.2,
      locationMatchWeight: 0.1,
      ratingWeight: 0.2,
      successRateWeight: 0.2,
      capacityWeight: 0.05,
      responseTimeWeight: 0.05,
      priceMatchWeight: 0.0,
      urgencyMultiplier: 1.0,
      newPlannerBoost: 0.1,
      repeatCustomerBoost: 0.1,
      minRating: 3.0,
      minSuccessRate: 0.6,
      maxResponseTime: 24,
      isActive: true,
      isDefault: true,
    },
  });

  // 紧急匹配配置
  const urgentMatchConfig = await prisma.matchAlgorithmConfig.upsert({
    where: { name: "urgent" },
    update: {},
    create: {
      name: "urgent",
      description: "紧急匹配算法配置",
      category: null,
      gradeMatchWeight: 0.15,
      subjectMatchWeight: 0.15,
      locationMatchWeight: 0.1,
      ratingWeight: 0.25,
      successRateWeight: 0.25,
      capacityWeight: 0.05,
      responseTimeWeight: 0.05,
      priceMatchWeight: 0.0,
      urgencyMultiplier: 1.5,
      newPlannerBoost: 0.0,
      repeatCustomerBoost: 0.2,
      minRating: 4.0,
      minSuccessRate: 0.8,
      maxResponseTime: 6,
      isActive: true,
      isDefault: false,
    },
  });

  console.log("创建匹配算法配置:", { defaultMatchConfig, urgentMatchConfig });

  // ==================== 创建演示学生 ====================

  const demoStudent = await prisma.student.create({
    data: {
      tenantId: institutionTenant.id,
      name: "张小明",
      gender: "男",
      birthday: new Date("2008-05-15"),
      phone: "13700137000",
      email: "<EMAIL>",
      school: "北京市第一中学",
      grade: "高二",
      gpa: 3.8,
      rank: 15,
      parentName: "张父",
      parentPhone: "13600136000",
      parentEmail: "<EMAIL>",
      familyBackground: "父母均为高校教师，注重教育，希望孩子能进入985高校",
      tags: ["理科强", "数学竞赛", "编程爱好"],
      notes: "学习成绩优秀，对计算机科学感兴趣，有参加数学竞赛的经历",
      status: "ACTIVE",
    },
  });

  console.log("创建演示学生:", demoStudent);

  // ==================== 创建更多学生用于匹配演示 ====================

  const demoStudent2 = await prisma.student.create({
    data: {
      tenantId: institutionTenant.id,
      name: "李小华",
      gender: "女",
      birthday: new Date("2007-08-20"),
      phone: "13700137001",
      email: "<EMAIL>",
      school: "北京市第二中学",
      grade: "高三",
      gpa: 3.9,
      rank: 8,
      parentName: "李母",
      parentPhone: "13600136001",
      parentEmail: "<EMAIL>",
      familyBackground: "父母从事金融行业，希望孩子能进入顶尖财经院校",
      tags: ["文科强", "英语优秀", "领导能力"],
      notes: "学习成绩优异，担任班长，对金融专业感兴趣",
      status: "ACTIVE",
    },
  });

  console.log("创建演示学生2:", demoStudent2);

  // ==================== 创建匹配请求演示数据 ====================

  // 创建待匹配的请求
  const matchRequest1 = await prisma.matchRequest.create({
    data: {
      requesterId: demoStudent.id,
      requesterType: "STUDENT_TO_PLANNER",
      tenantId: institutionTenant.id,
      requirements: {
        studentInfo: {
          name: "张小明",
          grade: "高二",
          school: "北京市第一中学",
          currentGPA: 3.8,
          targetMajor: "计算机科学",
          preferredUniversities: ["清华大学", "北京大学", "中科院大学"]
        },
        plannerPreferences: {
          preferredGrades: ["高二", "高三"],
          preferredSubjects: ["数学", "物理", "信息技术"],
          preferredLocations: ["北京", "上海"],
          maxBudget: 8000,
          urgencyLevel: "高",
          communicationMode: ["线上", "线下"],
          sessionFrequency: "每周2次"
        },
        specificRequirements: {
          hasCompetitionExperience: true,
          needsCompetitionGuidance: true,
          focusAreas: ["学业规划", "竞赛指导", "大学申请"],
          timeline: "2024年3月-2025年6月",
          additionalNotes: "希望规划师有理工科背景，了解计算机专业申请"
        }
      },
      category: "ACADEMIC_PLANNING",
      urgency: 8,
      mode: "SMART_MATCH",
      status: "PENDING",
      expiredAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后过期
      metadata: {
        source: "web_portal",
        deviceType: "desktop",
        submissionTime: new Date().toISOString()
      }
    },
  });

  // 创建紧急匹配请求
  const matchRequest2 = await prisma.matchRequest.create({
    data: {
      requesterId: demoStudent2.id,
      requesterType: "STUDENT_TO_PLANNER",
      tenantId: institutionTenant.id,
      requirements: {
        studentInfo: {
          name: "李小华",
          grade: "高三",
          school: "北京市第二中学", 
          currentGPA: 3.9,
          targetMajor: "金融学",
          preferredUniversities: ["北京大学", "清华大学", "复旦大学", "上海财经大学"]
        },
        plannerPreferences: {
          preferredGrades: ["高三"],
          preferredSubjects: ["数学", "英语", "政治"],
          preferredLocations: ["北京", "上海", "深圳"],
          maxBudget: 12000,
          urgencyLevel: "紧急",
          communicationMode: ["线上", "线下"],
          sessionFrequency: "每周3次"
        },
        specificRequirements: {
          hasCompetitionExperience: false,
          needsCompetitionGuidance: false,
          focusAreas: ["志愿填报", "面试指导", "大学申请"],
          timeline: "2024年1月-2024年6月",
          additionalNotes: "距离高考时间紧迫，需要经验丰富的规划师"
        }
      },
      category: "COLLEGE_APPLICATION",
      urgency: 10,
      mode: "SMART_MATCH",
      status: "PENDING",
      expiredAt: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3天后过期
      metadata: {
        source: "mobile_app",
        deviceType: "mobile",
        submissionTime: new Date().toISOString()
      }
    },
  });

  // 创建已匹配的请求
  const matchRequest3 = await prisma.matchRequest.create({
    data: {
      requesterId: demoStudent.id,
      requesterType: "STUDENT_TO_PLANNER",
      tenantId: institutionTenant.id,
      requirements: {
        studentInfo: {
          name: "张小明",
          grade: "高二",
          school: "北京市第一中学",
          currentGPA: 3.8,
          targetMajor: "物理学",
          preferredUniversities: ["清华大学", "北京大学", "中科院大学"]
        },
        plannerPreferences: {
          preferredGrades: ["高二"],
          preferredSubjects: ["物理", "数学"],
          preferredLocations: ["北京"],
          maxBudget: 6000,
          urgencyLevel: "中",
          communicationMode: ["线上"],
          sessionFrequency: "每周1次"
        },
        specificRequirements: {
          hasCompetitionExperience: true,
          needsCompetitionGuidance: true,
          focusAreas: ["物理竞赛", "学业规划"],
          timeline: "2024年2月-2024年12月",
          additionalNotes: "主要关注物理竞赛指导"
        }
      },
      category: "ACADEMIC_PLANNING",
      urgency: 5,
      mode: "SMART_MATCH",
      status: "MATCHED",
      matchedPlannerId: plannerProfile.id,
      matchedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2天前匹配
      expiredAt: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5天后过期
      metadata: {
        source: "web_portal",
        deviceType: "desktop",
        submissionTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
      }
    },
  });

  console.log("创建匹配请求:", { matchRequest1, matchRequest2, matchRequest3 });

  // ==================== 创建匹配响应演示数据 ====================

  // 规划师对第一个请求的响应
  const matchResponse1 = await prisma.matchResponse.create({
    data: {
      matchRequestId: matchRequest1.id,
      plannerId: plannerProfile.id,
      message: "我有5年理工科升学规划经验，特别擅长计算机专业申请指导。已成功帮助多位学生进入清华、北大计算机系。可以为您提供全面的学业规划和竞赛指导服务。",
      availability: {
        weekdays: ["周二", "周四", "周六"],
        timeSlots: ["14:00-16:00", "19:00-21:00"],
        startDate: "2024-01-15",
        preferredMode: "线上+线下结合"
      },
      quote: 6000.00,
      isAccepted: false,
      isRejected: false,
      respondedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1天前响应
    },
  });

  // 规划师对第二个紧急请求的响应
  const matchResponse2 = await prisma.matchResponse.create({
    data: {
      matchRequestId: matchRequest2.id,
      plannerId: plannerProfile.id,
      message: "理解您的紧急需求，我有丰富的高三志愿填报经验，成功率92%。可以立即开始服务，提供密集的指导方案。",
      availability: {
        weekdays: ["周一", "周二", "周三", "周四", "周五"],
        timeSlots: ["09:00-12:00", "14:00-17:00", "19:00-21:00"],
        startDate: "2024-01-10",
        preferredMode: "线上为主"
      },
      quote: 10000.00,
      isAccepted: false,
      isRejected: false,
      respondedAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2小时前响应
    },
  });

  console.log("创建匹配响应:", { matchResponse1, matchResponse2 });

  // ==================== 创建匹配历史记录 ====================

  const matchHistory1 = await prisma.matchHistory.create({
    data: {
      matchRequestId: matchRequest1.id,
      plannerId: plannerProfile.id,
      action: "VIEWED",
      details: {
        viewDuration: 120, // 秒
        deviceType: "desktop",
        source: "match_pool"
      },
      timestamp: new Date(Date.now() - 25 * 60 * 60 * 1000), // 25小时前
    },
  });

  const matchHistory2 = await prisma.matchHistory.create({
    data: {
      matchRequestId: matchRequest1.id,
      plannerId: plannerProfile.id,
      action: "RESPONDED",
      details: {
        responseTime: 1440, // 分钟
        messageLength: 150,
        includesQuote: true
      },
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 24小时前
    },
  });

  const matchHistory3 = await prisma.matchHistory.create({
    data: {
      matchRequestId: matchRequest3.id,
      plannerId: plannerProfile.id,
      action: "MATCHED",
      details: {
        matchAlgorithm: "smart_match_v2",
        matchScore: 0.85,
        matchReason: "高匹配度：专业对口、地区合适、价格匹配"
      },
      timestamp: new Date(Date.now() - 48 * 60 * 60 * 1000), // 48小时前
    },
  });

  console.log("创建匹配历史:", { matchHistory1, matchHistory2, matchHistory3 });

  // ==================== 创建订阅计划 ====================

  // 免费版
  const freePlan = await prisma.subscriptionPlan.upsert({
    where: { code: "free" },
    update: {},
    create: {
      name: "免费版",
      code: "free",
      type: "FREE",
      description: "适合个人用户体验基础功能",
      features: {
        studentCRM: true,
        basicReports: true,
        communityAccess: true,
      },
      limits: {
        maxUsers: 1,
        maxStudents: 5,
        maxStorage: 100,
      },
      maxUsers: 1,
      maxStudents: 5,
      maxPlanners: 1,
      maxStorage: 100,
      maxAIRequests: 10,
      hasStudentCRM: true,
      hasAITools: false,
      hasDataDashboard: false,
      hasScheduledTasks: false,
      hasLandingPage: false,
      hasCommunity: true,
      hasCustomBranding: false,
      hasAPIAccess: false,
      hasPrioritySupport: false,
      displayOrder: 1,
      isPopular: false,
      isActive: true,
      isVisible: true,
    },
  });

  // 专业版
  const proPlan = await prisma.subscriptionPlan.upsert({
    where: { code: "professional" },
    update: {},
    create: {
      name: "专业版",
      code: "professional",
      type: "PERSONAL",
      description: "适合专业规划师和小型机构",
      features: {
        studentCRM: true,
        aiTools: true,
        advancedReports: true,
        landingPage: true,
        scheduling: true,
      },
      limits: {
        maxUsers: 3,
        maxStudents: 50,
        maxStorage: 1000,
      },
      maxUsers: 3,
      maxStudents: 50,
      maxPlanners: 2,
      maxStorage: 1000,
      maxAIRequests: 500,
      hasStudentCRM: true,
      hasAITools: true,
      hasDataDashboard: true,
      hasScheduledTasks: true,
      hasLandingPage: true,
      hasCommunity: true,
      hasCustomBranding: false,
      hasAPIAccess: false,
      hasPrioritySupport: false,
      displayOrder: 2,
      isPopular: true,
      badge: "最受欢迎",
      isActive: true,
      isVisible: true,
    },
  });

  console.log("创建订阅计划:", { freePlan, proPlan });

  // ==================== 为租户创建订阅 ====================

  // 机构租户使用专业版
  const institutionSubscription = await prisma.subscription.upsert({
    where: { tenantId: institutionTenant.id },
    update: {},
    create: {
      tenantId: institutionTenant.id,
      planId: proPlan.id,
      status: "TRIAL",
      billingCycle: "MONTHLY",
      currentPrice: 299.0,
      trialEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天试用
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1年
    },
  });

  // 个人规划师使用免费版
  const plannerSubscription = await prisma.subscription.upsert({
    where: { tenantId: plannerTenant.id },
    update: {},
    create: {
      tenantId: plannerTenant.id,
      planId: freePlan.id,
      status: "ACTIVE",
      billingCycle: "MONTHLY",
      currentPrice: 0.0,
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1年
    },
  });

  console.log("创建订阅关系:", {
    institutionSubscription,
    plannerSubscription,
  });

  console.log("\n🎉 种子数据创建完成!");
  console.log("\n📋 测试账户信息:");
  console.log("==================== 管理后台 ====================");
  console.log("超级管理员: <EMAIL> / 123456");
  console.log("系统管理员: <EMAIL> / 123456");
  console.log("\n==================== 客户端体验账户 ====================");
  console.log("体验用户(多角色): <EMAIL> / 123456");
  console.log("机构管理员: <EMAIL> / 123456");
  console.log("规划师: <EMAIL> / 123456");
  console.log("\n🏢 租户信息:");
  console.log("- 演示教育机构 (机构类型)");
  console.log("- 李老师工作室 (个人规划师)");
  console.log("\n👥 学生信息:");
  console.log("- 张小明 (演示学生)");
}

main()
  .catch((e) => {
    console.error("种子数据失败:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
