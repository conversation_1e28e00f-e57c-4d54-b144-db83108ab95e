# 种子数据说明文档

## 🌱 概述

此种子数据为升学规划师多租户系统创建了完整的演示数据，包括管理后台用户、客户端用户、租户、学生、订阅计划等，帮助快速了解和体验系统的各项功能。

## 🚀 快速开始

```bash
cd packages/database
npm run seed
```

## 📋 账户信息总览

### 👨‍💼 管理后台账户

| 角色       | 邮箱                  | 密码     | 用途                       |
| ---------- | --------------------- | -------- | -------------------------- |
| 超级管理员 | `<EMAIL>`   | `123456` | 系统最高权限，管理所有功能 |
| 系统管理员 | `<EMAIL>` | `123456` | 部分管理权限，日常运营管理 |

### 👥 客户端体验账户

| 角色       | 邮箱                      | 密码     | 租户关系                                      | 用途                     |
| ---------- | ------------------------- | -------- | --------------------------------------------- | ------------------------ |
| 体验用户   | `<EMAIL>`        | `123456` | 演示教育机构(管理员)                          | 多功能体验，展示租户管理 |
| 机构管理员 | `<EMAIL>` | `123456` | 演示教育机构(管理员)                          | 机构类型租户管理         |
| 规划师     | `<EMAIL>`     | `123456` | 李老师工作室(管理员)<br/>演示教育机构(规划师) | 个人工作室 + 兼职规划师  |

## 🏢 租户(工作区)信息

### 1. 演示教育机构

- **类型**: 机构 (INSTITUTION)
- **域名**: `demo-institution.example.com`
- **订阅**: 专业版试用 (30天)
- **管理员**: <EMAIL>, <EMAIL>
- **规划师**: <EMAIL> (兼职)
- **学生**: 张小明

### 2. 李老师工作室

- **类型**: 个人规划师 (INDIVIDUAL)
- **域名**: `planner-li.example.com`
- **订阅**: 免费版
- **管理员**: <EMAIL>

## 👨‍🎓 学生信息

### 张小明

- **所属租户**: 演示教育机构
- **基本信息**: 男，2008年生，高二学生
- **学校**: 北京市第一中学
- **成绩**: GPA 3.8，年级排名15
- **特点**: 理科强，数学竞赛，编程爱好
- **家庭**: 父母均为高校教师

## 🎯 演示场景与登录指南

### 场景1: 体验多租户功能

**登录**: `<EMAIL>` / `123456`

**可以体验**:

- 租户选择界面（属于演示教育机构）
- 机构管理员功能
- 学生管理
- 规划师管理
- 专业版功能（试用期内）

**演示要点**:

- 展示用户可以属于多个租户
- 体验完整的机构管理功能
- 查看订阅状态和权限限制

### 场景2: 机构管理员日常工作

**登录**: `<EMAIL>` / `123456`

**可以体验**:

- 机构管理员视角
- 学生档案管理（张小明）
- 规划师团队管理
- 机构数据统计
- 订阅计划管理

**演示要点**:

- 专注于机构日常运营
- 学生CRM功能
- 团队协作功能

### 场景3: 个人规划师工作室

**登录**: `<EMAIL>` / `123456`

**租户选择**: 选择"李老师工作室"

**可以体验**:

- 个人工作室管理
- 免费版功能限制
- 个人品牌展示
- 学生管理（限制数量）

**演示要点**:

- 个人规划师独立运营
- 免费版与专业版功能对比
- 个人品牌建设

### 场景4: 规划师多租户工作

**登录**: `<EMAIL>` / `123456`

**租户选择**: 选择"演示教育机构"

**可以体验**:

- 同一用户在不同租户的不同角色
- 从个人工作室切换到机构兼职
- 不同租户的权限差异

**演示要点**:

- 展示灵活的多租户架构
- 用户可以在多个工作环境切换
- 角色权限的动态变化

### 场景5: 管理后台运营

**登录**: `<EMAIL>` / `123456`

**可以体验**:

- 系统全局管理
- 用户管理
- 租户管理
- 订阅计划管理
- 系统监控

**演示要点**:

- 平台运营商视角
- 全局数据统计
- 用户行为分析

## 🔗 用户关系图

```
管理后台
├── <EMAIL> (超级管理员)
└── <EMAIL> (系统管理员)

客户端多租户架构
├── 演示教育机构 (专业版试用)
│   ├── <EMAIL> (租户管理员)
│   ├── <EMAIL> (租户管理员)
│   ├── <EMAIL> (规划师)
│   └── 学生: 张小明
│
└── 李老师工作室 (免费版)
    └── <EMAIL> (租户管理员)
```

## 📦 订阅计划详情

### 免费版 (李老师工作室使用)

- **用户限制**: 1个
- **学生限制**: 5个
- **规划师限制**: 1个
- **存储限制**: 100MB
- **AI请求**: 10次/月
- **功能**: 基础学生CRM、社区访问

### 专业版 (演示教育机构试用)

- **用户限制**: 3个
- **学生限制**: 50个
- **规划师限制**: 2个
- **存储限制**: 1GB
- **AI请求**: 500次/月
- **功能**: 完整CRM、AI工具、数据看板、定时任务、落地页

## 🎪 推荐演示流程

### 1. 管理员视角 (5分钟)

1. 登录 `<EMAIL>`
2. 查看用户管理
3. 查看租户管理
4. 查看订阅统计

### 2. 机构管理员视角 (10分钟)

1. 登录 `<EMAIL>`
2. 选择"演示教育机构"
3. 查看学生档案(张小明)
4. 查看规划师团队
5. 体验专业版功能

### 3. 个人规划师视角 (10分钟)

1. 继续使用 `<EMAIL>`
2. 切换到"李老师工作室"
3. 对比免费版功能限制
4. 展示个人工作室管理

### 4. 多租户切换演示 (5分钟)

1. 展示同一用户在不同租户的角色变化
2. 演示租户切换功能
3. 对比不同租户的权限和功能

## 🛠️ 开发测试建议

### 功能测试

- 使用不同账户测试权限边界
- 验证多租户数据隔离
- 测试订阅限制功能

### 压力测试

- 可以基于这些种子数据创建更多测试数据
- 验证系统在多租户环境下的性能

### 新功能开发

- 在现有租户下添加新的测试数据
- 利用现有用户关系测试新功能

## 📞 支持

如果在使用种子数据时遇到问题，请检查：

1. **数据库连接**: 确保 DATABASE_URL 配置正确
2. **Redis连接**: 确保Redis服务正常运行
3. **权限问题**: 确保数据库用户有足够权限
4. **重复运行**: 种子脚本支持重复运行（upsert操作）

---

🎉 **祝您体验愉快！这套种子数据展示了一个完整的多租户SaaS系统的复杂性和灵活性。**
