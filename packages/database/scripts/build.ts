#!/usr/bin/env node

import { execSync } from "child_process";
import { existsSync, mkdirSync } from "fs";
import { join } from "path";

const DIST_DIR = join(__dirname, "../dist");

// 确保 dist 目录存在
if (!existsSync(DIST_DIR)) {
  mkdirSync(DIST_DIR, { recursive: true });
}

try {
  console.log("🔨 Building @workspace/database...");

  // 1. 生成 Prisma Client
  console.log("📦 Generating Prisma Client...");
  execSync("prisma generate", { stdio: "inherit" });

  // 2. 编译 TypeScript
  console.log("🔧 Compiling TypeScript...");
  execSync("tsc", { stdio: "inherit" });

  // 3. 处理路径别名
  console.log("🔗 Resolving path aliases...");
  execSync("tsc-alias", { stdio: "inherit" });

  console.log("✅ Build completed successfully!");
} catch (error) {
  console.error("❌ Build failed:", error);
  process.exit(1);
}
