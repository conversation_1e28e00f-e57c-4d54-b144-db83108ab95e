# 权限系统设计文档

## 目录

- [系统概述](#系统概述)
- [权限模型设计](#权限模型设计)
- [权限定义规范](#权限定义规范)
- [技术实现方案](#技术实现方案)
- [前端实现（Next.js）](#前端实现nextjs)
- [后端实现（Next.js API + Redis）](#后端实现nextjs-api--redis)
- [最佳实践](#最佳实践)
- [示例代码](#示例代码)

## 系统概述

### 架构设计

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   Next.js App   │────▶│  Next.js API    │────▶│     Redis       │
│  (前端路由守卫) │     │  (权限验证)     │     │  (会话缓存)     │
└─────────────────┘     └─────────────────┘     └─────────────────┘
         │                       │                         │
         │                       ▼                         │
         │              ┌─────────────────┐               │
         └─────────────▶│   PostgreSQL    │◀──────────────┘
                        │  (权限数据)     │
                        └─────────────────┘
```

### 权限验证策略

1. **前端控制**：路由守卫、UI 元素显隐（用户体验）
2. **API 层验证**：严格操作的权限验证（安全保障）
3. **缓存策略**：使用 Redis 缓存权限数据，减少数据库查询

## 权限模型设计

### 1. 权限命名规范

采用三级命名结构：`module:resource:action`

```typescript
// 权限类型定义
export interface Permission {
  code: string; // 权限代码：module:resource:action
  name: string; // 权限名称
  description: string; // 权限描述
  module: string; // 所属模块
  resource: string; // 资源类型
  action: string; // 操作类型
}
```

### 2. 完整权限定义

```typescript
// src/lib/permissions/definitions.ts
export const MODULES = {
  // 社区系统（公开）
  COMMUNITY: "community",
  COURSE: "course",
  PRODUCT: "product",

  // 工作区系统（租户隔离）
  WORKSPACE: "workspace",

  // 管理系统
  SYSTEM: "system",
  FINANCE: "finance",
  ADMIN: "admin",
} as const;

export const ACTIONS = {
  // 基础操作
  VIEW: "view",
  CREATE: "create",
  EDIT: "edit",
  DELETE: "delete",

  // 扩展操作
  PUBLISH: "publish",
  REVIEW: "review",
  APPROVE: "approve",
  EXPORT: "export",
  IMPORT: "import",

  // 特殊操作
  MANAGE: "manage", // 包含所有操作权限
  CONFIG: "config", // 配置权限
  ASSIGN: "assign", // 分配权限
} as const;

// 权限定义映射表
export const PERMISSIONS = {
  // ========== 社区模块权限 ==========
  COMMUNITY: {
    POST: {
      VIEW: "community:post:view",
      CREATE: "community:post:create",
      EDIT: "community:post:edit",
      DELETE: "community:post:delete",
      PUBLISH: "community:post:publish",
      REVIEW: "community:post:review",
    },
    COMMENT: {
      VIEW: "community:comment:view",
      CREATE: "community:comment:create",
      EDIT: "community:comment:edit",
      DELETE: "community:comment:delete",
    },
    RESOURCE: {
      VIEW: "community:resource:view",
      UPLOAD: "community:resource:create",
      EDIT: "community:resource:edit",
      DELETE: "community:resource:delete",
    },
  },

  // ========== 课程模块权限 ==========
  COURSE: {
    MANAGEMENT: {
      VIEW: "course:management:view",
      CREATE: "course:management:create",
      EDIT: "course:management:edit",
      DELETE: "course:management:delete",
      PUBLISH: "course:management:publish",
    },
    CHAPTER: {
      CREATE: "course:chapter:create",
      EDIT: "course:chapter:edit",
      DELETE: "course:chapter:delete",
    },
    ENROLLMENT: {
      VIEW: "course:enrollment:view",
      MANAGE: "course:enrollment:manage",
      EXPORT: "course:enrollment:export",
    },
  },

  // ========== 产品模块权限 ==========
  PRODUCT: {
    MANAGEMENT: {
      VIEW: "product:management:view",
      CREATE: "product:management:create",
      EDIT: "product:management:edit",
      DELETE: "product:management:delete",
      PUBLISH: "product:management:publish",
    },
    INQUIRY: {
      VIEW_ALL: "product:inquiry:view", // 查看所有询价
      VIEW_OWN: "product:inquiry:view_own", // 查看自己的询价
      CREATE: "product:inquiry:create", // 提交询价
      ASSIGN: "product:inquiry:assign", // 分配询价
      PROCESS: "product:inquiry:process", // 处理询价
      EXPORT: "product:inquiry:export", // 导出询价
    },
  },

  // ========== 工作区模块权限（租户内） ==========
  WORKSPACE: {
    DASHBOARD: {
      VIEW: "workspace:dashboard:view",
      CONFIG: "workspace:dashboard:config",
    },
    STUDENT: {
      VIEW: "workspace:student:view",
      CREATE: "workspace:student:create",
      EDIT: "workspace:student:edit",
      DELETE: "workspace:student:delete",
      EXPORT: "workspace:student:export",
      IMPORT: "workspace:student:import",
    },
    PLANNER: {
      VIEW: "workspace:planner:view",
      MANAGE: "workspace:planner:manage",
      ASSIGN: "workspace:planner:assign",
    },
    PLAN: {
      VIEW: "workspace:plan:view",
      CREATE: "workspace:plan:create",
      EDIT: "workspace:plan:edit",
      DELETE: "workspace:plan:delete",
      APPROVE: "workspace:plan:approve",
    },
    AI_TOOL: {
      USE: "workspace:ai_tool:use",
      CONFIG: "workspace:ai_tool:config",
      VIEW_USAGE: "workspace:ai_tool:view_usage",
    },
    APPOINTMENT: {
      VIEW: "workspace:appointment:view",
      CREATE: "workspace:appointment:create",
      EDIT: "workspace:appointment:edit",
      DELETE: "workspace:appointment:delete",
    },
    LANDING_PAGE: {
      VIEW: "workspace:landing_page:view",
      CREATE: "workspace:landing_page:create",
      EDIT: "workspace:landing_page:edit",
      DELETE: "workspace:landing_page:delete",
      PUBLISH: "workspace:landing_page:publish",
    },
    TASK: {
      VIEW: "workspace:task:view",
      CREATE: "workspace:task:create",
      EDIT: "workspace:task:edit",
      DELETE: "workspace:task:delete",
      EXECUTE: "workspace:task:execute",
    },
  },

  // ========== 系统管理权限 ==========
  SYSTEM: {
    USER: {
      VIEW: "system:user:view",
      CREATE: "system:user:create",
      EDIT: "system:user:edit",
      DELETE: "system:user:delete",
      BAN: "system:user:ban",
      RESET_PASSWORD: "system:user:reset_password",
    },
    TENANT: {
      VIEW: "system:tenant:view",
      CREATE: "system:tenant:create",
      EDIT: "system:tenant:edit",
      DELETE: "system:tenant:delete",
      SUSPEND: "system:tenant:suspend",
      CONFIG: "system:tenant:config",
    },
    PERMISSION: {
      VIEW: "system:permission:view",
      ASSIGN: "system:permission:assign",
    },
    CONFIG: {
      VIEW: "system:config:view",
      EDIT: "system:config:edit",
    },
    MONITOR: {
      VIEW: "system:monitor:view",
      EXPORT: "system:monitor:export",
    },
    LOG: {
      VIEW: "system:log:view",
      EXPORT: "system:log:export",
    },
  },

  // ========== 财务管理权限 ==========
  FINANCE: {
    SUBSCRIPTION: {
      VIEW: "finance:subscription:view",
      CREATE: "finance:subscription:create",
      EDIT: "finance:subscription:edit",
      CANCEL: "finance:subscription:cancel",
    },
    PAYMENT: {
      VIEW: "finance:payment:view",
      PROCESS: "finance:payment:process",
      REFUND: "finance:payment:refund",
      EXPORT: "finance:payment:export",
    },
    INVOICE: {
      VIEW: "finance:invoice:view",
      CREATE: "finance:invoice:create",
      VOID: "finance:invoice:void",
      DOWNLOAD: "finance:invoice:download",
    },
    COUPON: {
      VIEW: "finance:coupon:view",
      CREATE: "finance:coupon:create",
      EDIT: "finance:coupon:edit",
      DELETE: "finance:coupon:delete",
    },
  },

  // ========== 管理后台权限 ==========
  ADMIN: {
    DASHBOARD: {
      VIEW: "admin:dashboard:view",
    },
    ANNOUNCEMENT: {
      VIEW: "admin:announcement:view",
      CREATE: "admin:announcement:create",
      EDIT: "admin:announcement:edit",
      DELETE: "admin:announcement:delete",
    },
    ADMIN_USER: {
      VIEW: "admin:admin_user:view",
      CREATE: "admin:admin_user:create",
      EDIT: "admin:admin_user:edit",
      DELETE: "admin:admin_user:delete",
    },
  },
} as const;

// 角色默认权限配置
export const ROLE_PERMISSIONS = {
  // 超级管理员：所有权限
  SUPER_ADMIN: ["*"],

  // 平台管理员
  ADMIN: [
    "community:*",
    "course:*",
    "product:*",
    "system:user:*",
    "system:tenant:view",
    "system:tenant:edit",
    "system:monitor:view",
    "system:log:view",
    "finance:*",
    "admin:*",
  ],

  // 运营人员
  OPERATOR: [
    // 社区管理
    "community:post:view",
    "community:post:review",
    "community:comment:view",
    "community:comment:delete",

    // 课程管理
    "course:management:view",
    "course:management:edit",
    "course:enrollment:view",

    // 产品管理
    "product:*",

    // 基础查看权限
    "system:monitor:view",
    "admin:dashboard:view",
  ],

  // 租户管理员
  TENANT_ADMIN: [
    // 工作区所有权限
    "workspace:*",

    // 社区权限
    "community:post:*",
    "community:comment:*",
    "community:resource:*",

    // 产品询价
    "product:inquiry:view_own",
    "product:inquiry:create",

    // 课程学习
    "course:enrollment:view",
  ],

  // 规划师
  PLANNER: [
    // 工作区权限
    "workspace:dashboard:view",
    "workspace:student:*",
    "workspace:plan:*",
    "workspace:appointment:*",
    "workspace:ai_tool:use",
    "workspace:landing_page:*",
    "workspace:task:view",

    // 社区权限
    "community:post:view",
    "community:post:create",
    "community:post:edit",
    "community:comment:*",
    "community:resource:view",

    // 课程学习
    "course:enrollment:view",
  ],

  // 学生
  STUDENT: [
    // 查看自己的信息
    "workspace:dashboard:view",
    "workspace:plan:view",
    "workspace:appointment:view",

    // 社区基础权限
    "community:post:view",
    "community:comment:view",
    "community:comment:create",
    "community:resource:view",

    // 课程学习
    "course:enrollment:view",
  ],

  // 社区用户
  COMMUNITY_USER: [
    "community:post:view",
    "community:post:create",
    "community:comment:*",
    "community:resource:view",
    "course:enrollment:view",
    "product:inquiry:create",
  ],
} as const;

// 权限描述信息
export const PERMISSION_DESCRIPTIONS: Record<
  string,
  { name: string; description: string }
> = {
  "community:post:view": {
    name: "查看帖子",
    description: "查看社区中的帖子内容",
  },
  "community:post:create": {
    name: "发布帖子",
    description: "在社区中发布新帖子",
  },
  "community:post:edit": {
    name: "编辑帖子",
    description: "编辑自己发布的帖子",
  },
  "community:post:delete": {
    name: "删除帖子",
    description: "删除帖子",
  },
  "community:post:publish": {
    name: "发布帖子",
    description: "将草稿帖子发布到社区",
  },
  "community:post:review": {
    name: "审核帖子",
    description: "审核社区帖子内容",
  },
  // ... 更多权限描述
};
```

## 技术实现方案

### Redis 数据结构设计

```typescript
// Redis Key 设计
const REDIS_KEYS = {
  // 用户会话
  SESSION: (token: string) => `session:${token}`,

  // 用户权限缓存
  USER_PERMISSIONS: (userId: string) => `permissions:user:${userId}`,

  // 租户用户权限缓存
  TENANT_USER_PERMISSIONS: (tenantUserId: string) =>
    `permissions:tenant_user:${tenantUserId}`,

  // 角色权限缓存
  ROLE_PERMISSIONS: (role: string) => `permissions:role:${role}`,

  // 权限检查缓存（减少重复计算）
  PERMISSION_CHECK: (userId: string, permission: string) =>
    `perm_check:${userId}:${permission}`,
} as const;

// 缓存过期时间
const CACHE_TTL = {
  SESSION: 7 * 24 * 60 * 60, // 7天
  PERMISSIONS: 60 * 60, // 1小时
  PERMISSION_CHECK: 5 * 60, // 5分钟
} as const;
```

## 前端实现（Next.js）

### 1. 权限 Hook

```typescript
// src/hooks/usePermissions.ts
import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";

export interface UsePermissionsReturn {
  permissions: string[];
  isLoading: boolean;
  hasPermission: (permission: string | string[]) => boolean;
  hasAllPermissions: (...permissions: string[]) => boolean;
  hasAnyPermission: (...permissions: string[]) => boolean;
  hasModuleAccess: (module: string) => boolean;
}

export function usePermissions(): UsePermissionsReturn {
  const { data: session } = useSession();
  const [permissions, setPermissions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (session?.user) {
      // 从 session 或 API 获取权限
      fetchUserPermissions().then((perms) => {
        setPermissions(perms);
        setIsLoading(false);
      });
    } else {
      setPermissions([]);
      setIsLoading(false);
    }
  }, [session]);

  const hasPermission = (permission: string | string[]): boolean => {
    if (Array.isArray(permission)) {
      return hasAllPermissions(...permission);
    }

    // 超级管理员拥有所有权限
    if (permissions.includes("*")) return true;

    // 检查具体权限
    if (permissions.includes(permission)) return true;

    // 检查通配符权限
    const parts = permission.split(":");
    for (let i = parts.length - 1; i > 0; i--) {
      const wildcardPerm = parts.slice(0, i).join(":") + ":*";
      if (permissions.includes(wildcardPerm)) return true;
    }

    return false;
  };

  const hasAllPermissions = (...requiredPermissions: string[]): boolean => {
    return requiredPermissions.every((perm) => hasPermission(perm));
  };

  const hasAnyPermission = (...requiredPermissions: string[]): boolean => {
    return requiredPermissions.some((perm) => hasPermission(perm));
  };

  const hasModuleAccess = (module: string): boolean => {
    return permissions.some((perm) => perm.startsWith(`${module}:`));
  };

  return {
    permissions,
    isLoading,
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
    hasModuleAccess,
  };
}

// 获取用户权限
async function fetchUserPermissions(): Promise<string[]> {
  try {
    const response = await fetch("/api/auth/permissions");
    const data = await response.json();
    return data.permissions || [];
  } catch (error) {
    console.error("Failed to fetch permissions:", error);
    return [];
  }
}
```

### 2. 路由守卫

```typescript
// src/components/auth/RouteGuard.tsx
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { usePermissions } from '@/hooks/usePermissions';

interface RouteGuardProps {
  children: React.ReactNode;
  permissions?: string[];
  requireAuth?: boolean;
  fallbackUrl?: string;
}

export function RouteGuard({
  children,
  permissions = [],
  requireAuth = true,
  fallbackUrl = '/403',
}: RouteGuardProps) {
  const router = useRouter();
  const { status } = useSession();
  const { hasAllPermissions, isLoading } = usePermissions();

  useEffect(() => {
    if (status === 'loading' || isLoading) return;

    // 检查认证
    if (requireAuth && status === 'unauthenticated') {
      router.push('/login');
      return;
    }

    // 检查权限
    if (permissions.length > 0 && !hasAllPermissions(...permissions)) {
      router.push(fallbackUrl);
      return;
    }
  }, [status, isLoading, permissions, requireAuth, hasAllPermissions, router, fallbackUrl]);

  if (status === 'loading' || isLoading) {
    return <div>Loading...</div>;
  }

  if (requireAuth && status === 'unauthenticated') {
    return null;
  }

  if (permissions.length > 0 && !hasAllPermissions(...permissions)) {
    return null;
  }

  return <>{children}</>;
}
```

### 3. 权限组件

```typescript
// src/components/auth/PermissionGate.tsx
import { usePermissions } from '@/hooks/usePermissions';

interface PermissionGateProps {
  permissions: string | string[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requireAll?: boolean;
}

export function PermissionGate({
  permissions,
  children,
  fallback = null,
  requireAll = true,
}: PermissionGateProps) {
  const { hasPermission, hasAllPermissions, hasAnyPermission } = usePermissions();

  const hasAccess = Array.isArray(permissions)
    ? requireAll
      ? hasAllPermissions(...permissions)
      : hasAnyPermission(...permissions)
    : hasPermission(permissions);

  return hasAccess ? <>{children}</> : <>{fallback}</>;
}

// 使用示例
<PermissionGate permissions={PERMISSIONS.COURSE.MANAGEMENT.CREATE}>
  <Button onClick={handleCreateCourse}>创建课程</Button>
</PermissionGate>

// 多权限示例（需要所有权限）
<PermissionGate
  permissions={[
    PERMISSIONS.COURSE.MANAGEMENT.EDIT,
    PERMISSIONS.COURSE.MANAGEMENT.PUBLISH
  ]}
  requireAll={true}
>
  <Button onClick={handlePublish}>发布课程</Button>
</PermissionGate>

// 多权限示例（任一权限即可）
<PermissionGate
  permissions={[
    PERMISSIONS.SYSTEM.USER.EDIT,
    PERMISSIONS.SYSTEM.USER.DELETE
  ]}
  requireAll={false}
  fallback={<span>无权限操作</span>}
>
  <UserActions />
</PermissionGate>
```

### 4. 页面级权限控制

```typescript
// src/pages/admin/courses/index.tsx
import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/pages/api/auth/[...nextauth]';
import { checkPermissions } from '@/lib/auth/permissions';
import { PERMISSIONS } from '@/lib/permissions/definitions';

// 页面组件
export default function CoursesManagementPage() {
  return (
    <RouteGuard permissions={[PERMISSIONS.COURSE.MANAGEMENT.VIEW]}>
      <CourseManagement />
    </RouteGuard>
  );
}

// 服务端权限检查（可选）
export const getServerSideProps: GetServerSideProps = async (context) => {
  const session = await getServerSession(context.req, context.res, authOptions);

  if (!session) {
    return {
      redirect: {
        destination: '/login',
        permanent: false,
      },
    };
  }

  const hasPermission = await checkPermissions(
    session.user.id,
    PERMISSIONS.COURSE.MANAGEMENT.VIEW
  );

  if (!hasPermission) {
    return {
      redirect: {
        destination: '/403',
        permanent: false,
      },
    };
  }

  return {
    props: {},
  };
};
```

## 后端实现（Next.js API + Redis）

### 1. Redis 客户端配置

```typescript
// src/lib/redis/client.ts
import { Redis } from "ioredis";

const redis = new Redis({
  host: process.env.REDIS_HOST || "localhost",
  port: parseInt(process.env.REDIS_PORT || "6379"),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || "0"),
  maxRetriesPerRequest: 3,
  retryStrategy: (times) => {
    const delay = Math.min(times * 50, 2000);
    return delay;
  },
});

redis.on("error", (err) => {
  console.error("Redis Client Error:", err);
});

redis.on("connect", () => {
  console.log("Redis Client Connected");
});

export default redis;
```

### 2. 权限服务

```typescript
// src/lib/auth/permission.service.ts
import redis from "@/lib/redis/client";
import { prisma } from "@/lib/prisma";
import { REDIS_KEYS, CACHE_TTL } from "@/lib/redis/keys";

export class PermissionService {
  /**
   * 获取用户权限（带缓存）
   */
  async getUserPermissions(userId: string): Promise<string[]> {
    // 尝试从缓存获取
    const cacheKey = REDIS_KEYS.USER_PERMISSIONS(userId);
    const cached = await redis.get(cacheKey);

    if (cached) {
      return JSON.parse(cached);
    }

    // 从数据库获取
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        tenantUsers: {
          include: {
            tenant: true,
          },
        },
      },
    });

    if (!user) {
      return [];
    }

    // 获取用户的所有权限
    const permissions = new Set<string>();

    // 社区用户基础权限
    if (user.emailVerified) {
      const communityPerms = ROLE_PERMISSIONS.COMMUNITY_USER;
      communityPerms.forEach((perm) => permissions.add(perm));
    }

    // 租户用户权限
    for (const tenantUser of user.tenantUsers) {
      if (tenantUser.isActive && tenantUser.tenant.status === "ACTIVE") {
        const rolePerms = ROLE_PERMISSIONS[tenantUser.role] || [];
        rolePerms.forEach((perm) => permissions.add(perm));
      }
    }

    const permissionArray = Array.from(permissions);

    // 缓存权限
    await redis.setex(
      cacheKey,
      CACHE_TTL.PERMISSIONS,
      JSON.stringify(permissionArray),
    );

    return permissionArray;
  }

  /**
   * 获取租户用户权限（带缓存）
   */
  async getTenantUserPermissions(tenantUserId: string): Promise<string[]> {
    const cacheKey = REDIS_KEYS.TENANT_USER_PERMISSIONS(tenantUserId);
    const cached = await redis.get(cacheKey);

    if (cached) {
      return JSON.parse(cached);
    }

    const tenantUser = await prisma.tenantUser.findUnique({
      where: { id: tenantUserId },
      include: {
        tenant: true,
      },
    });

    if (
      !tenantUser ||
      !tenantUser.isActive ||
      tenantUser.tenant.status !== "ACTIVE"
    ) {
      return [];
    }

    const permissions = ROLE_PERMISSIONS[tenantUser.role] || [];

    // 缓存权限
    await redis.setex(
      cacheKey,
      CACHE_TTL.PERMISSIONS,
      JSON.stringify(permissions),
    );

    return permissions;
  }

  /**
   * 检查用户是否有特定权限（带缓存）
   */
  async checkPermission(userId: string, permission: string): Promise<boolean> {
    // 检查缓存
    const cacheKey = REDIS_KEYS.PERMISSION_CHECK(userId, permission);
    const cached = await redis.get(cacheKey);

    if (cached !== null) {
      return cached === "1";
    }

    // 获取用户权限
    const permissions = await this.getUserPermissions(userId);
    const hasPermission = this.hasPermission(permissions, permission);

    // 缓存结果
    await redis.setex(
      cacheKey,
      CACHE_TTL.PERMISSION_CHECK,
      hasPermission ? "1" : "0",
    );

    return hasPermission;
  }

  /**
   * 判断权限列表中是否包含指定权限
   */
  private hasPermission(permissions: string[], required: string): boolean {
    // 超级管理员
    if (permissions.includes("*")) return true;

    // 精确匹配
    if (permissions.includes(required)) return true;

    // 通配符匹配
    const parts = required.split(":");
    for (let i = parts.length - 1; i > 0; i--) {
      const wildcardPerm = parts.slice(0, i).join(":") + ":*";
      if (permissions.includes(wildcardPerm)) return true;
    }

    return false;
  }

  /**
   * 清除用户权限缓存
   */
  async clearUserPermissionCache(userId: string): Promise<void> {
    const keys = [
      REDIS_KEYS.USER_PERMISSIONS(userId),
      // 清除所有权限检查缓存
      `perm_check:${userId}:*`,
    ];

    // 使用 pipeline 批量删除
    const pipeline = redis.pipeline();

    for (const key of keys) {
      if (key.includes("*")) {
        // 处理通配符
        const matchedKeys = await redis.keys(key);
        matchedKeys.forEach((k) => pipeline.del(k));
      } else {
        pipeline.del(key);
      }
    }

    await pipeline.exec();
  }

  /**
   * 清除租户用户权限缓存
   */
  async clearTenantUserPermissionCache(tenantUserId: string): Promise<void> {
    await redis.del(REDIS_KEYS.TENANT_USER_PERMISSIONS(tenantUserId));
  }
}

export const permissionService = new PermissionService();
```

### 3. API 中间件

```typescript
// src/lib/auth/middleware.ts
import { NextApiRequest, NextApiResponse } from "next";
import { getServerSession } from "next-auth";
import { authOptions } from "@/pages/api/auth/[...nextauth]";
import { permissionService } from "./permission.service";

export interface AuthenticatedRequest extends NextApiRequest {
  user?: {
    id: string;
    email: string;
    permissions: string[];
  };
  tenantId?: string;
}

/**
 * 认证中间件
 */
export function withAuth(
  handler: (req: AuthenticatedRequest, res: NextApiResponse) => Promise<void>,
) {
  return async (req: AuthenticatedRequest, res: NextApiResponse) => {
    const session = await getServerSession(req, res, authOptions);

    if (!session?.user) {
      return res.status(401).json({ error: "Unauthorized" });
    }

    // 获取用户权限
    const permissions = await permissionService.getUserPermissions(
      session.user.id,
    );

    req.user = {
      id: session.user.id,
      email: session.user.email,
      permissions,
    };

    // 如果请求包含租户ID，验证用户是否属于该租户
    const tenantId = req.headers["x-tenant-id"] as string;
    if (tenantId) {
      const tenantUser = await prisma.tenantUser.findFirst({
        where: {
          userId: session.user.id,
          tenantId,
          isActive: true,
        },
      });

      if (!tenantUser) {
        return res.status(403).json({ error: "Access denied to this tenant" });
      }

      req.tenantId = tenantId;
    }

    return handler(req, res);
  };
}

/**
 * 权限检查中间件
 */
export function withPermissions(...requiredPermissions: string[]) {
  return (
    handler: (req: AuthenticatedRequest, res: NextApiResponse) => Promise<void>,
  ) => {
    return withAuth(async (req, res) => {
      const { permissions } = req.user!;

      // 检查权限
      const hasPermission = requiredPermissions.every((required) =>
        permissionService.hasPermission(permissions, required),
      );

      if (!hasPermission) {
        return res.status(403).json({
          error: "Insufficient permissions",
          required: requiredPermissions,
        });
      }

      return handler(req, res);
    });
  };
}

/**
 * 租户隔离中间件
 */
export function withTenant(
  handler: (req: AuthenticatedRequest, res: NextApiResponse) => Promise<void>,
) {
  return withAuth(async (req, res) => {
    if (!req.tenantId) {
      return res.status(400).json({ error: "Tenant ID is required" });
    }

    return handler(req, res);
  });
}
```

### 4. API 路由示例

```typescript
// src/pages/api/courses/index.ts
import type { NextApiRequest, NextApiResponse } from "next";
import { withPermissions } from "@/lib/auth/middleware";
import { PERMISSIONS } from "@/lib/permissions/definitions";

async function handler(req: AuthenticatedRequest, res: NextApiResponse) {
  switch (req.method) {
    case "GET":
      // 获取课程列表
      return handleGetCourses(req, res);

    case "POST":
      // 创建课程（需要额外权限）
      if (
        !req.user?.permissions.includes(PERMISSIONS.COURSE.MANAGEMENT.CREATE)
      ) {
        return res.status(403).json({ error: "Permission denied" });
      }
      return handleCreateCourse(req, res);

    default:
      return res.status(405).json({ error: "Method not allowed" });
  }
}

// 应用权限中间件
export default withPermissions(PERMISSIONS.COURSE.MANAGEMENT.VIEW)(handler);

// src/pages/api/workspace/students/[id].ts
import { withTenant, withPermissions } from "@/lib/auth/middleware";
import { PERMISSIONS } from "@/lib/permissions/definitions";

async function handler(req: AuthenticatedRequest, res: NextApiResponse) {
  const { id } = req.query;
  const { tenantId } = req;

  switch (req.method) {
    case "GET":
      // 获取学生详情
      const student = await prisma.student.findFirst({
        where: {
          id: id as string,
          tenantId: tenantId!,
        },
      });

      if (!student) {
        return res.status(404).json({ error: "Student not found" });
      }

      return res.json(student);

    case "PUT":
      // 更新学生信息（需要编辑权限）
      if (!req.user?.permissions.includes(PERMISSIONS.WORKSPACE.STUDENT.EDIT)) {
        return res.status(403).json({ error: "Permission denied" });
      }

      // 更新逻辑...
      break;

    default:
      return res.status(405).json({ error: "Method not allowed" });
  }
}

// 需要租户上下文和查看权限
export default withTenant(
  withPermissions(PERMISSIONS.WORKSPACE.STUDENT.VIEW)(handler),
);
```

### 5. 权限 API

```typescript
// src/pages/api/auth/permissions.ts
import type { NextApiRequest, NextApiResponse } from "next";
import { withAuth } from "@/lib/auth/middleware";

async function handler(req: AuthenticatedRequest, res: NextApiResponse) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  // 返回当前用户的权限
  return res.json({
    permissions: req.user!.permissions,
  });
}

export default withAuth(handler);
```

## 最佳实践

### 1. 性能优化

```typescript
// 批量权限检查
export async function checkMultiplePermissions(
  userId: string,
  permissions: string[],
): Promise<Record<string, boolean>> {
  const pipeline = redis.pipeline();
  const uncachedPerms: string[] = [];

  // 检查缓存
  for (const perm of permissions) {
    const cacheKey = REDIS_KEYS.PERMISSION_CHECK(userId, perm);
    pipeline.get(cacheKey);
  }

  const results = await pipeline.exec();
  const permissionResults: Record<string, boolean> = {};

  results?.forEach((result, index) => {
    const [err, value] = result;
    const perm = permissions[index];

    if (!err && value !== null) {
      permissionResults[perm] = value === "1";
    } else {
      uncachedPerms.push(perm);
    }
  });

  // 处理未缓存的权限
  if (uncachedPerms.length > 0) {
    const userPerms = await permissionService.getUserPermissions(userId);
    const cachePipeline = redis.pipeline();

    for (const perm of uncachedPerms) {
      const hasPermission = permissionService.hasPermission(userPerms, perm);
      permissionResults[perm] = hasPermission;

      // 缓存结果
      cachePipeline.setex(
        REDIS_KEYS.PERMISSION_CHECK(userId, perm),
        CACHE_TTL.PERMISSION_CHECK,
        hasPermission ? "1" : "0",
      );
    }

    await cachePipeline.exec();
  }

  return permissionResults;
}
```

### 2. 权限预加载

```typescript
// 在 Next.js 中预加载权限
// src/pages/_app.tsx
import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { usePermissions } from '@/hooks/usePermissions';

function MyApp({ Component, pageProps }: AppProps) {
  const { data: session } = useSession();
  const { permissions } = usePermissions();

  useEffect(() => {
    if (session?.user) {
      // 预加载常用权限到本地存储
      localStorage.setItem('user_permissions', JSON.stringify(permissions));
    }
  }, [session, permissions]);

  return <Component {...pageProps} />;
}
```

### 3. 权限变更处理

```typescript
// 权限变更时的处理
export async function handlePermissionChange(userId: string) {
  // 清除缓存
  await permissionService.clearUserPermissionCache(userId);

  // 通知前端刷新权限
  // 可以使用 WebSocket 或 Server-Sent Events
  await notifyClient(userId, "PERMISSION_CHANGED");
}

// 前端监听权限变更
useEffect(() => {
  const handlePermissionChange = () => {
    // 重新获取权限
    window.location.reload();
  };

  // 监听权限变更事件
  eventBus.on("PERMISSION_CHANGED", handlePermissionChange);

  return () => {
    eventBus.off("PERMISSION_CHANGED", handlePermissionChange);
  };
}, []);
```

### 4. 开发环境权限调试

```typescript
// src/lib/auth/debug.ts
export function debugPermissions(permissions: string[], required: string) {
  if (process.env.NODE_ENV === "development") {
    console.group("Permission Check Debug");
    console.log("Required:", required);
    console.log("User Permissions:", permissions);
    console.log(
      "Has Permission:",
      permissionService.hasPermission(permissions, required),
    );
    console.groupEnd();
  }
}
```

## 安全建议

1. **最小权限原则**：用户应该只拥有完成工作所需的最小权限集
2. **定期审计**：定期审查用户权限，移除不必要的权限
3. **权限继承**：避免过度使用通配符权限
4. **缓存安全**：敏感权限检查不应过度依赖缓存
5. **日志记录**：记录所有权限变更和敏感操作

## 总结

这个权限系统设计提供了：

1. **灵活的权限模型**：支持模块化权限定义和通配符
2. **高性能**：使用 Redis 缓存减少数据库查询
3. **前后端一致**：统一的权限定义和检查逻辑
4. **易于扩展**：新增权限只需更新定义文件
5. **开发友好**：提供完善的 TypeScript 类型支持

通过这个设计，您可以在 Next.js 应用中实现一个强大且高效的权限管理系统。
