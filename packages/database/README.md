# 学业规划 SaaS 平台完整数据库设计文档

## 目录

1. [系统概述](#系统概述)
2. [系统架构](#系统架构)
3. [核心模块说明](#核心模块说明)
4. [数据表详解](#数据表详解)
5. [业务流程](#业务流程)
6. [权限体系](#权限体系)
7. [开发指南](#开发指南)
8. [部署与运维](#部署与运维)

## 系统概述

### 项目背景

学业规划 SaaS 平台是一个面向教育规划机构和个人规划师的综合性解决方案，集成了社区交流、专业工具、AI 辅助、营销推广等功能。

### 核心价值

- **社区生态**：开放的知识分享和资源交流平台
- **专业工具**：租户隔离的学生管理和规划工具
- **AI 赋能**：深度集成的 AI 辅助功能
- **商业闭环**：从获客到服务的完整解决方案

### 技术栈

- **数据库**：PostgreSQL
- **ORM**：Prisma
- **主键策略**：CUID
- **更新时间**：2025-06-12

## 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                         统一用户认证系统                          │
│                    (User + TenantUser + AdminUser)               │
└────────────┬────────────────┬────────────────┬──────────────────┘
             │                │                │
    ┌────────▼────────┐ ┌────▼─────────┐ ┌───▼──────────┐
    │   社区系统      │ │  工作区系统   │ │ 管理后台系统  │
    │  (公开访问)     │ │ (租户隔离)    │ │ (平台管理)    │
    ├─────────────────┤ ├──────────────┤ ├──────────────┤
    │ • 文章分享      │ │ • 学生CRM     │ │ • 内容管理    │
    │ • 课程学习      │ │ • AI工具      │ │ • 用户管理    │
    │ • 资源下载      │ │ • 规划方案    │ │ • 租户管理    │
    │ • 产品展示      │ │ • 数据分析    │ │ • 系统监控    │
    │ • 用户互动      │ │ • 营销工具    │ │ • 数据统计    │
    └─────────────────┘ └──────────────┘ └──────────────┘
             │                │                │
    └────────┴────────────────┴────────────────┴──────────┐
                                                          │
                     ┌──────────────────────────┐         │
                     │      订阅与计费系统       │◄────────┘
                     │   (支付/发票/优惠券)      │
                     └──────────────────────────┘
```

### 数据隔离策略

1. **社区系统**：无租户概念，所有用户共享内容
2. **工作区系统**：严格的租户数据隔离
3. **管理后台**：独立的管理员体系

## 核心模块说明

### 1. 用户与认证模块

- **User**：社区用户基础信息
- **TenantUser**：用户在租户中的身份
- **AdminUser**：平台管理员账号
- **Session/AdminSession**：会话管理

### 2. 社区系统模块

#### 2.1 内容模块

- **Post**：文章、分享、问题等
- **Comment**：评论系统
- **Like/Favorite**：点赞收藏
- **Follow**：用户关注

#### 2.2 课程模块

- **Course**：课程信息
- **CourseChapter**：章节
- **CourseLesson**：课时
- **LessonTimestamp**：时间戳标记
- **CourseEnrollment**：报名记录
- **LessonProgress**：学习进度

#### 2.3 资源产品模块

- **Resource**：可下载资源
- **Product**：研学营、证书等产品
- **ProductInquiry**：产品询价单
- **Partner**：合作方

### 3. 工作区系统模块

#### 3.1 CRM模块

- **Student**：学生信息
- **Planner**：规划师信息
- **StudentPlanner**：学生规划师关系

#### 3.2 规划模块

- **Plan**：规划方案
- **Milestone**：里程碑
- **Appointment**：预约

#### 3.3 AI工具模块

- **AITool**：AI工具配置
- **AIUsageRecord**：使用记录

#### 3.4 文档模块

- **Document**：文档管理
- **Assessment**：评估测试

### 4. 营销系统模块

- **LandingPage**：落地页
- **Form**：表单
- **FormSubmission**：表单提交

### 5. 任务系统模块

- **Task**：定时任务
- **TaskExecution**：执行记录

### 6. 订阅计费模块

- **SubscriptionPlan**：订阅计划
- **Subscription**：订阅记录
- **Payment**：支付记录
- **Invoice**：发票
- **Coupon**：优惠券

### 7. 系统管理模块

- **Permission**：权限定义
- **Role**：角色
- **OperationLog**：操作日志
- **SystemAnnouncement**：系统公告
- **SystemMonitor**：系统监控

## 数据表详解

### 用户认证相关表

#### User（用户表）

所有用户的基础表，支持社区和工作区功能。

| 字段          | 类型      | 必填 | 说明           | 示例                  |
| ------------- | --------- | ---- | -------------- | --------------------- |
| id            | String    | ✓    | 主键           | "clh1234567890"       |
| email         | String    | ✓    | 邮箱（唯一）   | "<EMAIL>"    |
| username      | String    | ✓    | 用户名（唯一） | "johndoe"             |
| password      | String    | ✓    | 密码（加密）   | "$2b$10$..."          |
| name          | String    | ✓    | 姓名           | "张三"                |
| avatar        | String?   | -    | 头像URL        | "https://..."         |
| phone         | String?   | -    | 手机号         | "13800138000"         |
| bio           | String?   | -    | 个人简介       | "资深留学规划师..."   |
| isActive      | Boolean   | ✓    | 是否激活       | true                  |
| emailVerified | Boolean   | ✓    | 邮箱是否验证   | true                  |
| lastLoginAt   | DateTime? | -    | 最后登录时间   | "2025-06-12 10:00:00" |

#### TenantUser（租户用户关系表）

用户在特定租户下的身份和权限。

| 字段     | 类型     | 必填 | 说明             | 示例            |
| -------- | -------- | ---- | ---------------- | --------------- |
| id       | String   | ✓    | 主键             | "clh1234567890" |
| userId   | String   | ✓    | 用户ID           | "clh0987654321" |
| tenantId | String   | ✓    | 租户ID           | "clh1111111111" |
| role     | UserRole | ✓    | 在该租户下的角色 | PLANNER         |
| isActive | Boolean  | ✓    | 是否激活         | true            |
| joinedAt | DateTime | ✓    | 加入时间         | "2025-06-01"    |

#### AdminUser（管理员表）

平台管理后台的独立用户体系。

| 字段           | 类型        | 必填 | 说明           | 示例                            |
| -------------- | ----------- | ---- | -------------- | ------------------------------- |
| id             | String      | ✓    | 主键           | "clh1234567890"                 |
| email          | String      | ✓    | 邮箱（唯一）   | "<EMAIL>"            |
| username       | String      | ✓    | 用户名（唯一） | "admin"                         |
| password       | String      | ✓    | 密码（加密）   | "$2b$10$..."                    |
| name           | String      | ✓    | 姓名           | "管理员"                        |
| role           | UserRole    | ✓    | 角色           | SUPER_ADMIN                     |
| status         | AdminStatus | ✓    | 状态           | ACTIVE                          |
| permissions    | String[]    | ✓    | 权限列表       | ["user.manage", "content.edit"] |
| allowedModules | String[]    | ✓    | 允许的模块     | ["user", "content", "system"]   |

### 社区系统相关表

#### Post（帖子表）

社区内容的核心表。

| 字段          | 类型       | 必填 | 说明             | 示例               |
| ------------- | ---------- | ---- | ---------------- | ------------------ |
| id            | String     | ✓    | 主键             | "clh1234567890"    |
| title         | String     | ✓    | 标题             | "如何准备托福考试" |
| content       | String     | ✓    | 内容             | "详细内容..."      |
| type          | PostType   | ✓    | 类型             | ARTICLE            |
| status        | PostStatus | ✓    | 状态             | PUBLISHED          |
| viewCount     | Int        | ✓    | 浏览次数         | 1234               |
| isTop         | Boolean    | ✓    | 是否置顶         | false              |
| isRecommended | Boolean    | ✓    | 是否推荐         | true               |
| isOriginal    | Boolean    | ✓    | 是否原创         | true               |
| authorId      | String     | ✓    | 作者ID           | "clh0987654321"    |
| authorName    | String?    | -    | 作者名称（冗余） | "张老师"           |
| authorAvatar  | String?    | -    | 作者头像（冗余） | "https://..."      |

#### Course（课程表）

在线课程信息。

| 字段        | 类型         | 必填 | 说明         | 示例                 |
| ----------- | ------------ | ---- | ------------ | -------------------- |
| id          | String       | ✓    | 主键         | "clh1234567890"      |
| title       | String       | ✓    | 课程标题     | "托福听力技巧全攻略" |
| subtitle    | String?      | -    | 副标题       | "30天突破听力瓶颈"   |
| description | String       | ✓    | 详细介绍     | "本课程..."          |
| cover       | String       | ✓    | 封面图       | "https://..."        |
| categoryId  | String       | ✓    | 分类ID       | "clh2222222222"      |
| level       | CourseLevel  | ✓    | 难度级别     | INTERMEDIATE         |
| duration    | Int          | ✓    | 总时长(分钟) | 480                  |
| price       | Decimal      | ✓    | 价格         | 299.00               |
| isFree      | Boolean      | ✓    | 是否免费     | false                |
| status      | CourseStatus | ✓    | 状态         | PUBLISHED            |
| rating      | Float?       | -    | 评分         | 4.8                  |

#### Product（产品表）

研学营、证书等产品信息。

| 字段        | 类型          | 必填 | 说明             | 示例                 |
| ----------- | ------------- | ---- | ---------------- | -------------------- |
| id          | String        | ✓    | 主键             | "clh1234567890"      |
| name        | String        | ✓    | 产品名称         | "哈佛大学暑期研学营" |
| code        | String        | ✓    | 产品编码（唯一） | "HARVARD-2025-SUM"   |
| type        | ProductType   | ✓    | 产品类型         | STUDY_CAMP           |
| description | String        | ✓    | 详细介绍         | "为期两周..."        |
| price       | Decimal?      | -    | 参考价格         | 29800.00             |
| duration    | String?       | -    | 时长             | "14天13夜"           |
| location    | String?       | -    | 地点             | "美国波士顿"         |
| capacity    | Int?          | -    | 名额             | 30                   |
| status      | ProductStatus | ✓    | 状态             | ACTIVE               |

### 工作区系统相关表

#### Tenant（租户表）

代表一个机构或个人规划师。

| 字段         | 类型         | 必填 | 说明       | 示例                  |
| ------------ | ------------ | ---- | ---------- | --------------------- |
| id           | String       | ✓    | 主键       | "clh1234567890"       |
| name         | String       | ✓    | 租户名称   | "优学教育"            |
| type         | TenantType   | ✓    | 租户类型   | INSTITUTION           |
| status       | TenantStatus | ✓    | 状态       | ACTIVE                |
| contactName  | String       | ✓    | 联系人     | "张三"                |
| contactPhone | String       | ✓    | 联系电话   | "13800138000"         |
| contactEmail | String       | ✓    | 联系邮箱   | "<EMAIL>" |
| customDomain | String?      | -    | 自定义域名 | "youxue.example.com"  |

#### Student（学生表）

租户管理的学生信息。

| 字段     | 类型      | 必填 | 说明       | 示例                     |
| -------- | --------- | ---- | ---------- | ------------------------ |
| id       | String    | ✓    | 主键       | "clh1234567890"          |
| tenantId | String    | ✓    | 所属租户ID | "clh1111111111"          |
| name     | String    | ✓    | 学生姓名   | "李四"                   |
| gender   | String?   | -    | 性别       | "男"                     |
| birthday | DateTime? | -    | 生日       | "2005-06-15"             |
| school   | String?   | -    | 学校       | "北京四中"               |
| grade    | String?   | -    | 年级       | "高二"                   |
| gpa      | Float?    | -    | GPA        | 3.8                      |
| tags     | String[]  | ✓    | 标签       | ["重点关注", "托福110+"] |
| status   | String    | ✓    | 状态       | "ACTIVE"                 |

### 订阅计费相关表

#### SubscriptionPlan（订阅计划表）

系统提供的订阅计划。

| 字段             | 类型     | 必填 | 说明             | 示例            |
| ---------------- | -------- | ---- | ---------------- | --------------- |
| id               | String   | ✓    | 主键             | "clh1234567890" |
| name             | String   | ✓    | 计划名称         | "个人专业版"    |
| code             | String   | ✓    | 计划代码（唯一） | "personal"      |
| type             | PlanType | ✓    | 计划类型         | PERSONAL        |
| maxUsers         | Int      | ✓    | 最大用户数       | 3               |
| maxStudents      | Int      | ✓    | 最大学生数       | 100             |
| maxAIRequests    | Int      | ✓    | 每月AI请求数     | 1000            |
| hasAITools       | Boolean  | ✓    | 是否有AI工具     | true            |
| hasDataDashboard | Boolean  | ✓    | 是否有数据看板   | true            |

#### SubscriptionPrice（价格表）

订阅计划的价格配置。

| 字段          | 类型         | 必填 | 说明       | 示例            |
| ------------- | ------------ | ---- | ---------- | --------------- |
| id            | String       | ✓    | 主键       | "clh1234567890" |
| planId        | String       | ✓    | 计划ID     | "clh0987654321" |
| billingCycle  | BillingCycle | ✓    | 计费周期   | YEARLY          |
| price         | Decimal      | ✓    | 价格       | 2599.00         |
| originalPrice | Decimal?     | -    | 原价       | 3999.00         |
| discount      | Int?         | -    | 折扣百分比 | 35              |
| trialDays     | Int          | ✓    | 试用天数   | 14              |

## 业务流程

### 1. 用户注册与认证流程

```mermaid
graph TD
    A[访问平台] --> B{选择入口}
    B --> C[社区注册]
    B --> D[申请试用]

    C --> E[创建User账号]
    E --> F[验证邮箱]
    F --> G[完善个人信息]
    G --> H[开始使用社区]

    D --> I[创建User账号]
    I --> J[创建试用租户]
    J --> K[创建TenantUser关系]
    K --> L[分配默认角色]
    L --> M[进入工作区]

    H --> N{需要专业工具?}
    N --> |是| D
    N --> |否| O[继续使用社区]
```

### 2. 课程学习流程

```mermaid
graph TD
    A[浏览课程列表] --> B[查看课程详情]
    B --> C{是否免费?}

    C --> |免费| D[直接报名]
    C --> |付费| E{是否登录?}

    E --> |未登录| F[提示登录]
    E --> |已登录| G[查看价格]

    F --> H[登录/注册]
    H --> G

    G --> I[支付]
    I --> J[支付成功]

    D --> K[开始学习]
    J --> K

    K --> L[观看视频]
    L --> M[记录进度]
    M --> N[完成课时]
    N --> O{还有课时?}

    O --> |是| L
    O --> |否| P[完成课程]
    P --> Q[课程评价]
```

### 3. 产品询价流程

```mermaid
graph TD
    A[浏览产品] --> B[查看详情]
    B --> C[提交询价单]
    C --> D{是否登录?}

    D --> |已登录| E[自动填充信息]
    D --> |未登录| F[手动填写信息]

    E --> G[补充需求信息]
    F --> G

    G --> H[提交成功]
    H --> I[系统通知运营]

    I --> J[运营查看询价]
    J --> K[分配负责人]
    K --> L[联系客户]

    L --> M{客户意向}
    M --> |有意向| N[详细洽谈]
    M --> |考虑中| O[定期跟进]
    M --> |无意向| P[记录原因]

    N --> Q[线下对接]
    O --> R[设置提醒]
```

### 4. 学生管理流程

```mermaid
graph TD
    A[规划师登录] --> B[进入工作区]
    B --> C[学生管理模块]

    C --> D{操作类型}
    D --> |新增| E[添加学生]
    D --> |导入| F[批量导入]
    D --> |管理| G[学生列表]

    E --> H[填写基本信息]
    H --> I[设置标签]
    I --> J[分配规划师]

    F --> K[下载模板]
    K --> L[填写数据]
    L --> M[上传文件]
    M --> N[数据验证]
    N --> O[导入确认]

    G --> P[筛选学生]
    P --> Q[查看详情]
    Q --> R{操作}
    R --> |规划| S[创建规划方案]
    R --> |预约| T[安排咨询]
    R --> |文档| U[上传资料]
```

### 5. AI工具使用流程

```mermaid
graph TD
    A[选择AI工具] --> B{检查权限}
    B --> |无权限| C[提示升级]
    B --> |有权限| D{检查配额}

    D --> |超限| E[提示限制]
    D --> |未超限| F[打开工具]

    F --> G[输入内容]
    G --> H[设置参数]
    H --> I[提交请求]

    I --> J[调用AI API]
    J --> K{处理状态}

    K --> |成功| L[返回结果]
    K --> |失败| M[错误提示]

    L --> N[展示结果]
    N --> O[保存/导出]

    M --> P[重试]
    P --> I

    O --> Q[记录使用]
    Q --> R[更新配额]
```

## 权限体系

### 1. 角色权限矩阵

#### 社区系统权限

| 功能           | 游客 | 注册用户 | 规划师 | 管理员 |
| -------------- | ---- | -------- | ------ | ------ |
| 浏览帖子       | ✓    | ✓        | ✓      | ✓      |
| 发布内容       | ✗    | ✓        | ✓      | ✓      |
| 评论互动       | ✗    | ✓        | ✓      | ✓      |
| 下载公开资源   | ✓    | ✓        | ✓      | ✓      |
| 下载需登录资源 | ✗    | ✓        | ✓      | ✓      |
| 观看免费课程   | ✓    | ✓        | ✓      | ✓      |
| 购买付费课程   | ✗    | ✓        | ✓      | ✓      |
| 提交产品询价   | ✓    | ✓        | ✓      | ✓      |

#### 工作区系统权限

| 功能           | 租户管理员 | 规划师 | 学生 | 家长 |
| -------------- | ---------- | ------ | ---- | ---- |
| 管理租户设置   | ✓          | ✗      | ✗    | ✗    |
| 管理用户       | ✓          | ✗      | ✗    | ✗    |
| 查看所有学生   | ✓          | ✗      | ✗    | ✗    |
| 管理分配的学生 | ✓          | ✓      | ✗    | ✗    |
| 创建规划方案   | ✓          | ✓      | ✗    | ✗    |
| 使用AI工具     | ✓          | ✓      | 限制 | ✗    |
| 查看数据报表   | ✓          | 部分   | ✗    | ✗    |
| 创建营销页面   | ✓          | ✗      | ✗    | ✗    |
| 查看自己信息   | ✓          | ✓      | ✓    | ✓    |
| 查看孩子信息   | ✗          | ✗      | ✗    | ✓    |

#### 管理后台权限

| 功能模块   | 超级管理员 | 平台管理员 | 运营人员 |
| ---------- | ---------- | ---------- | -------- |
| 系统配置   | ✓          | ✗          | ✗        |
| 管理员管理 | ✓          | ✗          | ✗        |
| 租户管理   | ✓          | ✓          | ✗        |
| 用户管理   | ✓          | ✓          | ✗        |
| 内容审核   | ✓          | ✓          | ✓        |
| 课程管理   | ✓          | ✓          | ✓        |
| 产品管理   | ✓          | ✓          | ✓        |
| 询价处理   | ✓          | ✓          | ✓        |
| 数据统计   | ✓          | ✓          | 只读     |
| 系统监控   | ✓          | ✓          | ✗        |
| 发布公告   | ✓          | ✓          | ✗        |

### 2. 权限控制实现

```typescript
// 权限定义
const permissions = {
  // 社区权限
  "community.post.create": "创建帖子",
  "community.post.edit": "编辑帖子",
  "community.post.delete": "删除帖子",
  "community.comment.create": "发表评论",
  "community.resource.download": "下载资源",

  // 课程权限
  "course.view": "查看课程",
  "course.enroll": "报名课程",
  "course.create": "创建课程",
  "course.edit": "编辑课程",
  "course.publish": "发布课程",

  // 工作区权限
  "workspace.student.view": "查看学生",
  "workspace.student.create": "创建学生",
  "workspace.student.edit": "编辑学生",
  "workspace.student.delete": "删除学生",
  "workspace.plan.create": "创建规划",
  "workspace.ai.use": "使用AI工具",

  // 管理后台权限
  "admin.system.config": "系统配置",
  "admin.tenant.manage": "租户管理",
  "admin.user.manage": "用户管理",
  "admin.content.audit": "内容审核",
};

// 角色权限映射
const rolePermissions = {
  SUPER_ADMIN: ["*"], // 所有权限
  ADMIN: [
    "admin.tenant.manage",
    "admin.user.manage",
    "admin.content.audit",
    "course.create",
    "course.edit",
    "course.publish",
  ],
  TENANT_ADMIN: ["workspace.*", "community.*"],
  PLANNER: [
    "workspace.student.*",
    "workspace.plan.*",
    "workspace.ai.use",
    "community.*",
  ],
  STUDENT: [
    "community.post.create",
    "community.comment.create",
    "course.view",
    "course.enroll",
  ],
};
```

## 开发指南

### 1. 环境设置

```bash
# 安装依赖
npm install prisma @prisma/client

# 初始化 Prisma
npx prisma init

# 生成 Prisma Client
npx prisma generate

# 运行数据库迁移
npx prisma migrate dev
```

### 2. 数据库连接

```typescript
// prisma/client.ts
import { PrismaClient } from "@prisma/client";

const globalForPrisma = global as unknown as { prisma: PrismaClient };

export const prisma =
  globalForPrisma.prisma ||
  new PrismaClient({
    log:
      process.env.NODE_ENV === "development"
        ? ["query", "error", "warn"]
        : ["error"],
  });

if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;
```

### 3. 租户隔离中间件

```typescript
// middleware/tenantIsolation.ts
import { Prisma } from "@prisma/client";

// 需要租户隔离的模型
const tenantScopedModels = [
  "Student",
  "Planner",
  "Plan",
  "Task",
  "LandingPage",
  "AIUsageRecord",
];

export function tenantIsolationMiddleware(tenantId: string): Prisma.Middleware {
  return async (params, next) => {
    // 只处理需要租户隔离的模型
    if (!tenantScopedModels.includes(params.model || "")) {
      return next(params);
    }

    // 创建操作：自动添加 tenantId
    if (params.action === "create") {
      params.args.data = {
        ...params.args.data,
        tenantId,
      };
    }

    // 查询操作：自动添加租户过滤
    if (
      [
        "findUnique",
        "findFirst",
        "findMany",
        "update",
        "updateMany",
        "delete",
        "deleteMany",
      ].includes(params.action)
    ) {
      if (!params.args) params.args = {};
      if (!params.args.where) params.args.where = {};

      params.args.where = {
        ...params.args.where,
        tenantId,
      };
    }

    return next(params);
  };
}

// 使用示例
prisma.$use(tenantIsolationMiddleware(currentTenantId));
```

### 4. 常用查询示例

```typescript
// 获取用户的所有租户身份
async function getUserTenants(userId: string) {
  return await prisma.tenantUser.findMany({
    where: { userId },
    include: {
      tenant: true,
    },
  });
}

// 社区帖子查询（无租户限制）
async function getCommunityPosts(page: number = 1, limit: number = 20) {
  return await prisma.post.findMany({
    where: {
      status: "PUBLISHED",
    },
    include: {
      author: true,
      category: true,
      tags: {
        include: {
          tag: true,
        },
      },
      _count: {
        select: {
          comments: true,
          likes: true,
        },
      },
    },
    orderBy: {
      publishedAt: "desc",
    },
    skip: (page - 1) * limit,
    take: limit,
  });
}

// 工作区学生查询（需要租户隔离）
async function getTenantStudents(tenantId: string, filters?: any) {
  return await prisma.student.findMany({
    where: {
      tenantId, // 必须包含租户ID
      ...filters,
    },
    include: {
      planners: {
        include: {
          planner: true,
        },
      },
      plans: {
        where: {
          status: "ACTIVE",
        },
      },
    },
  });
}

// 创建订阅
async function createSubscription(
  tenantId: string,
  planId: string,
  priceId: string,
) {
  return await prisma.$transaction(async (tx) => {
    // 获取价格信息
    const price = await tx.subscriptionPrice.findUnique({
      where: { id: priceId },
      include: { plan: true },
    });

    if (!price) throw new Error("价格方案不存在");

    // 创建订阅
    const subscription = await tx.subscription.create({
      data: {
        tenantId,
        planId,
        status: price.trialDays > 0 ? "TRIAL" : "ACTIVE",
        billingCycle: price.billingCycle,
        currentPrice: price.price,
        trialEndDate:
          price.trialDays > 0
            ? new Date(Date.now() + price.trialDays * 24 * 60 * 60 * 1000)
            : null,
        startDate: new Date(),
        endDate: calculateEndDate(new Date(), price.billingCycle),
      },
    });

    // 更新租户状态
    await tx.tenant.update({
      where: { id: tenantId },
      data: { status: "ACTIVE" },
    });

    return subscription;
  });
}
```

### 5. 性能优化建议

#### 5.1 查询优化

```typescript
// 使用 select 减少数据传输
const lightweightPosts = await prisma.post.findMany({
  select: {
    id: true,
    title: true,
    summary: true,
    authorName: true,
    publishedAt: true,
  },
});

// 使用游标分页替代 skip
const posts = await prisma.post.findMany({
  take: 20,
  cursor: {
    id: lastPostId,
  },
  orderBy: {
    id: "desc",
  },
});
```

#### 5.2 批量操作

```typescript
// 批量创建
await prisma.student.createMany({
  data: studentsData,
  skipDuplicates: true,
});

// 批量更新
await prisma.student.updateMany({
  where: {
    tenantId,
    tags: {
      has: "2024届",
    },
  },
  data: {
    status: "GRADUATED",
  },
});
```

#### 5.3 事务处理

```typescript
// 使用事务保证数据一致性
await prisma.$transaction(async (tx) => {
  // 创建学生
  const student = await tx.student.create({
    data: studentData,
  });

  // 分配规划师
  await tx.studentPlanner.create({
    data: {
      studentId: student.id,
      plannerId: plannerId,
      isPrimary: true,
    },
  });

  // 创建初始规划
  await tx.plan.create({
    data: {
      studentId: student.id,
      plannerId: plannerId,
      title: "初始规划方案",
      type: "综合规划",
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
    },
  });
});
```

## 部署与运维

### 1. 数据库部署

#### 1.1 生产环境配置

```env
# .env.production
DATABASE_URL="************************************/dbname?schema=public&connection_limit=10"
```

#### 1.2 数据库优化

```sql
-- 创建索引优化查询性能
CREATE INDEX idx_posts_published_at ON "Post"("publishedAt") WHERE status = 'PUBLISHED';
CREATE INDEX idx_students_tenant_status ON "Student"("tenantId", "status");
CREATE INDEX idx_ai_usage_tenant_date ON "AIUsageRecord"("tenantUserId", "createdAt");

-- 创建物化视图加速统计查询
CREATE MATERIALIZED VIEW mv_tenant_statistics AS
SELECT
  t.id as tenant_id,
  COUNT(DISTINCT s.id) as student_count,
  COUNT(DISTINCT p.id) as planner_count,
  COUNT(DISTINCT pl.id) as plan_count,
  MAX(s."createdAt") as last_student_created
FROM "Tenant" t
LEFT JOIN "Student" s ON t.id = s."tenantId"
LEFT JOIN "Planner" p ON t.id = p."tenantId"
LEFT JOIN "Plan" pl ON p.id = pl."plannerId"
GROUP BY t.id;

-- 定期刷新物化视图
CREATE OR REPLACE FUNCTION refresh_tenant_statistics()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY mv_tenant_statistics;
END;
$$ LANGUAGE plpgsql;
```

### 2. 备份策略

```bash
#!/bin/bash
# backup.sh - 数据库备份脚本

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/postgres"
DB_NAME="studyplan_saas"

# 全量备份
pg_dump -h localhost -U postgres -d $DB_NAME -F c -f "$BACKUP_DIR/full_backup_$DATE.dump"

# 仅结构备份
pg_dump -h localhost -U postgres -d $DB_NAME -s -f "$BACKUP_DIR/schema_backup_$DATE.sql"

# 清理30天前的备份
find $BACKUP_DIR -name "*.dump" -mtime +30 -delete
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
```

### 3. 监控指标

#### 3.1 业务指标

```typescript
// 每日活跃用户
async function getDailyActiveUsers(date: Date) {
  const start = new Date(date.setHours(0, 0, 0, 0));
  const end = new Date(date.setHours(23, 59, 59, 999));

  return await prisma.user.count({
    where: {
      lastLoginAt: {
        gte: start,
        lte: end,
      },
    },
  });
}

// 订阅转化率
async function getSubscriptionConversionRate(period: {
  start: Date;
  end: Date;
}) {
  const trials = await prisma.subscription.count({
    where: {
      status: "TRIAL",
      createdAt: {
        gte: period.start,
        lte: period.end,
      },
    },
  });

  const converted = await prisma.subscription.count({
    where: {
      status: "ACTIVE",
      trialEndDate: {
        not: null,
      },
      createdAt: {
        gte: period.start,
        lte: period.end,
      },
    },
  });

  return trials > 0 ? (converted / trials) * 100 : 0;
}
```

#### 3.2 系统指标

```typescript
// 记录系统监控数据
async function recordSystemMetrics() {
  const metrics = {
    activeUsers: await prisma.user.count({ where: { isActive: true } }),
    activeTenants: await prisma.tenant.count({ where: { status: "ACTIVE" } }),
    apiRequests: await getApiRequestCount(),
    errorCount: await getErrorCount(),
  };

  await prisma.systemMonitor.create({
    data: metrics,
  });
}
```

### 4. 安全建议

1. **数据加密**

   - 使用 bcrypt 加密密码
   - 敏感数据使用 AES 加密
   - 启用 SSL/TLS 连接

2. **访问控制**

   - 实施 IP 白名单
   - 使用强密码策略
   - 定期轮换数据库密码

3. **审计日志**

   - 记录所有敏感操作
   - 定期审查异常访问
   - 保留日志至少6个月

4. **数据隔离**
   - 使用 Row Level Security
   - 严格的租户数据隔离
   - 定期检查数据泄露

## 常见问题

### Q1: 如何处理大量学生数据的导入？

```typescript
// 使用流式处理和批量插入
import { Readable } from "stream";
import csv from "csv-parser";

async function importStudents(fileStream: Readable, tenantId: string) {
  const batch: any[] = [];
  const batchSize = 100;

  return new Promise((resolve, reject) => {
    fileStream
      .pipe(csv())
      .on("data", async (row) => {
        batch.push({
          tenantId,
          name: row.name,
          email: row.email,
          phone: row.phone,
          school: row.school,
          grade: row.grade,
          tags: row.tags ? row.tags.split(",") : [],
        });

        if (batch.length >= batchSize) {
          await prisma.student.createMany({
            data: batch.splice(0, batchSize),
            skipDuplicates: true,
          });
        }
      })
      .on("end", async () => {
        if (batch.length > 0) {
          await prisma.student.createMany({
            data: batch,
            skipDuplicates: true,
          });
        }
        resolve(true);
      })
      .on("error", reject);
  });
}
```

### Q2: 如何实现软删除？

```typescript
// 使用 Prisma 中间件实现软删除
prisma.$use(async (params, next) => {
  // 软删除模型列表
  const softDeleteModels = ["Post", "Comment", "Student"];

  if (!softDeleteModels.includes(params.model || "")) {
    return next(params);
  }

  // 删除操作改为更新
  if (params.action === "delete") {
    params.action = "update";
    params.args["data"] = { deletedAt: new Date() };
  }

  if (params.action === "deleteMany") {
    params.action = "updateMany";
    params.args["data"] = { deletedAt: new Date() };
  }

  // 查询时自动过滤已删除的记录
  if (params.action === "findUnique" || params.action === "findFirst") {
    params.action = "findFirst";
    params.args.where = {
      ...params.args.where,
      deletedAt: null,
    };
  }

  if (params.action === "findMany") {
    params.args.where = {
      ...params.args.where,
      deletedAt: null,
    };
  }

  return next(params);
});
```

### Q3: 如何优化复杂的统计查询？

```typescript
// 使用原生 SQL 查询复杂统计
interface TenantStatistics {
  tenantId: string;
  studentCount: number;
  activePlanCount: number;
  monthlyAIUsage: number;
  storageUsed: number;
}

async function getTenantStatistics(
  tenantId: string,
): Promise<TenantStatistics> {
  const result = await prisma.$queryRaw<TenantStatistics[]>`
    SELECT 
      t.id as "tenantId",
      COUNT(DISTINCT s.id) as "studentCount",
      COUNT(DISTINCT CASE WHEN p.status = 'ACTIVE' THEN p.id END) as "activePlanCount",
      COALESCE(SUM(
        CASE 
          WHEN au."createdAt" >= date_trunc('month', CURRENT_DATE) 
          THEN 1 
          ELSE 0 
        END
      ), 0) as "monthlyAIUsage",
      COALESCE(SUM(d."fileSize"), 0) as "storageUsed"
    FROM "Tenant" t
    LEFT JOIN "Student" s ON t.id = s."tenantId"
    LEFT JOIN "Plan" p ON s.id = p."studentId"
    LEFT JOIN "TenantUser" tu ON t.id = tu."tenantId"
    LEFT JOIN "AIUsageRecord" au ON tu.id = au."tenantUserId"
    LEFT JOIN "Document" d ON s.id = d."studentId"
    WHERE t.id = ${tenantId}
    GROUP BY t.id
  `;

  return result[0];
}
```

---

**文档版本**: 3.0  
**最后更新**: 2025-06-12  
**作者**: AI Assistant  
**维护者**: newObjectccc
