{"name": "@workspace/database", "version": "1.0.0", "description": "Database package with Prisma client for workspace", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./services": {"require": "./dist/services/index.js", "types": "./dist/services/index.d.ts"}}, "files": ["dist", "prisma", "src/generated"], "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "npm run clean && npm run build:prisma && npm run build:ts && npm run copy:generated", "build:prisma": "prisma generate", "build:ts": "tsc", "copy:generated": "cp -r src/generated dist/", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "npm run build:prisma && tsc --watch", "prebuild": "npm run clean", "postinstall": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx scripts/seed.ts", "studio": "prisma studio"}, "keywords": ["prisma", "database", "workspace"], "author": "", "license": "ISC", "devDependencies": {"@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "prisma": "^6.9.0", "prisma-docs-generator": "^0.8.0", "rimraf": "^5.0.10", "tsx": "^4.20.1", "typescript": "^5.8.3"}, "dependencies": {"@prisma/client": "^6.9.0", "@workspace/ioredis": "workspace:*", "bcrypt": "^6.0.0", "jsonwebtoken": "^9.0.2", "prisma-zod-generator": "^0.8.13", "zod": "^3.24.2"}}