// 认证相关常量定义
export const AUTH_CONSTANTS = {
  // JWT配置
  JWT: {
    SECRET:
      process.env.JWT_SECRET || "your-super-secret-key-change-in-production",
    ACCESS_EXPIRES_IN: process.env.JWT_EXPIRES_IN || "2h", // 访问令牌过期时间
    REFRESH_EXPIRES_IN: process.env.REFRESH_TOKEN_EXPIRES_IN || "7d", // 刷新令牌过期时间
    REMEMBER_EXPIRES_IN: process.env.REMEMBER_EXPIRES_IN || "30d", // 记住我过期时间
  },

  // Token类型
  TOKEN_TYPES: {
    ACCESS: "access" as const,
    REFRESH: "refresh" as const,
  },

  // 时间常量（毫秒）
  TOKEN: {
    ACCESS_EXPIRES_IN: 2 * 60 * 60 * 1000, // 2小时
    REFRESH_EXPIRES_IN: 7 * 24 * 60 * 60 * 1000, // 7天
    REMEMBER_EXPIRES_IN: 30 * 24 * 60 * 60 * 1000, // 30天记住我
    AUTO_REFRESH_INTERVAL: 30 * 60 * 1000, // 30分钟自动刷新间隔
  },

  // Cookie配置
  COOKIES: {
    REFRESH_TOKEN: "refresh_token",
    ACCESS_TOKEN: "access_token",
  },

  // LocalStorage键名
  STORAGE_KEYS: {
    ACCESS_TOKEN: "access_token",
    USER_INFO: "user_info",
    TENANT_INFO: "tenant_info",
    CURRENT_TENANT: "current_tenant",
  },

  // HTTP Headers
  HEADERS: {
    AUTHORIZATION: "Authorization",
    CONTENT_TYPE: "Content-Type",
    BEARER_PREFIX: "Bearer ",
  },

  // 会话配置
  SESSION: {
    DEFAULT_EXPIRES_DAYS: 1, // 默认会话过期天数
    REMEMBER_EXPIRES_DAYS: 30, // 记住我会话过期天数
  },

  // 安全配置
  SECURITY: {
    MAX_LOGIN_ATTEMPTS: 5, // 最大登录尝试次数
    LOCKOUT_DURATION: 15 * 60 * 1000, // 锁定时长15分钟
    PASSWORD_MIN_LENGTH: 6, // 密码最小长度
  },
} as const;

// 导出类型
export type TokenType =
  (typeof AUTH_CONSTANTS.TOKEN_TYPES)[keyof typeof AUTH_CONSTANTS.TOKEN_TYPES];
