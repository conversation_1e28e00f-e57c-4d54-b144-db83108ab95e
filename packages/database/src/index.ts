import { PrismaClient } from "./generated/prisma";

// 创建一个全局的 PrismaClient 实例，避免在开发环境中创建多个连接
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

// 如果已经存在实例就使用，否则创建新的
export const prisma =
  globalForPrisma.prisma ??
  new PrismaClient({
    log:
      process.env.NODE_ENV === "development"
        ? ["query", "error", "warn"]
        : ["error"],
  });

// 在非生产环境下，将实例存储到全局对象中
if (process.env.NODE_ENV !== "production") {
  globalForPrisma.prisma = prisma;
}

// 导出 PrismaClient 类型和实例
export { PrismaClient } from "./generated/prisma";
export * from "./generated/prisma";

// 导出服务
export * from "./services";

// 导出常量
export * from "./constants/client-auth.constants";

// 默认导出
export default prisma;
