import {
  ProductType,
  ProductStatus,
  InquiryStatus,
  Product,
  ProductCategory,
  Partner,
  ProductInquiry,
  AdminUser,
} from "../generated/prisma";
import { Decimal } from "../generated/prisma/runtime/library";
import { PaginatedResult } from "./course.types";

// ========== 基础输入类型 ==========

export interface CreateProductInput {
  name: string;
  code: string;
  type: ProductType;
  categoryId: string;
  description: string;
  features: string[];
  highlights?: any; // JSON类型
  images: string[];
  brochureUrl?: string;
  price?: number | null;
  priceUnit?: string;
  priceNote?: string;
  duration?: string;
  location?: string;
  startDate?: Date | null;
  endDate?: Date | null;
  capacity?: number | null;
  minParticipants?: number | null;
  targetAudience: string[];
  ageRange?: string;
  gradeRange?: string;
  partnerId?: string;
  status?: ProductStatus;
  priority?: number;
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords?: string[];
  createdById: string;
}

export interface UpdateProductInput extends Partial<CreateProductInput> {
  status?: ProductStatus;
}

export interface CreateProductCategoryInput {
  name: string;
  slug?: string;
  description?: string;
  type: ProductType;
  icon?: string;
  order?: number;
  isActive?: boolean;
}

export interface UpdateProductCategoryInput
  extends Partial<CreateProductCategoryInput> {}

export interface CreatePartnerInput {
  name: string;
  code: string;
  logo?: string;
  contactName: string;
  contactPhone: string;
  contactEmail: string;
  address?: string;
  cooperationType: string[];
  description?: string;
  isActive?: boolean;
}

export interface UpdatePartnerInput extends Partial<CreatePartnerInput> {}

export interface CreateProductInquiryInput {
  productId: string;
  tenantId?: string;
  userId?: string;
  contactName: string;
  contactPhone: string;
  contactEmail?: string;
  organization?: string;
  participants?: number;
  expectedDate?: Date;
  budget?: string;
  requirements?: string;
  message?: string;
  assignedToId?: string;
}

export interface UpdateProductInquiryInput
  extends Partial<CreateProductInquiryInput> {
  status?: InquiryStatus;
  followUpNotes?: any; // JSON类型
  lastContactAt?: Date;
  nextFollowUpAt?: Date;
}

// ========== 查询选项类型 ==========

export interface GetProductsOptions {
  page?: number;
  limit?: number;
  categoryId?: string;
  type?: ProductType;
  status?: ProductStatus;
  partnerId?: string;
  search?: string;
  sortBy?: "createdAt" | "updatedAt" | "name" | "price" | "priority";
  sortOrder?: "asc" | "desc";
}

export interface GetProductInquiriesOptions {
  page?: number;
  limit?: number;
  productId?: string;
  status?: InquiryStatus;
  assignedToId?: string;
  tenantId?: string;
  search?: string;
  sortBy?: "createdAt" | "updatedAt" | "lastContactAt";
  sortOrder?: "asc" | "desc";
}

// ========== 返回结果类型 ==========

export interface ProductWithBasicInfo extends Product {
  category: ProductCategory;
  partner?: Partner | null;
  _count: {
    inquiries: number;
  };
}

export interface ProductWithFullInfo extends ProductWithBasicInfo {
  inquiries: ProductInquiry[];
  createdBy: AdminUser;
}

export interface ProductCategoryWithCount extends ProductCategory {
  _count: {
    products: number;
  };
}

export interface PartnerWithProductCount extends Partner {
  _count: {
    products: number;
  };
}

export interface ProductInquiryWithDetails extends ProductInquiry {
  product: {
    id: string;
    name: string;
    code: string;
    type: ProductType;
    images: string[];
    price?: Decimal | null;
    priceUnit?: string | null;
  };
  user?: {
    id: string;
    name: string;
    email: string;
    avatar?: string | null;
  } | null;
  tenant?: {
    id: string;
    name: string;
    type: string;
    logo?: string | null;
  } | null;
  assignedTo?: {
    id: string;
    name: string;
    email: string;
    avatar?: string | null;
  } | null;
}

// ========== 统计相关类型 ==========

export interface ProductStats {
  totalProducts: number;
  totalInquiries: number;
  totalPartners: number;
  activeProducts: number;
  pendingInquiries: number;
  completedInquiries: number;
  conversionRate: number;
}

export interface ProductCategoryStats {
  categoryId: string;
  categoryName: string;
  productCount: number;
  inquiryCount: number;
  conversionRate: number;
}

export interface PartnerStats {
  partnerId: string;
  partnerName: string;
  productCount: number;
  inquiryCount: number;
  conversionRate: number;
}

export interface InquiryStats {
  totalInquiries: number;
  pendingInquiries: number;
  contactingInquiries: number;
  negotiatingInquiries: number;
  completedInquiries: number;
  cancelledInquiries: number;
  conversionRate: number;
  averageResponseTime: number; // 小时
}

// ========== 搜索和过滤类型 ==========

export interface ProductSearchFilters {
  categoryIds?: string[];
  types?: ProductType[];
  partnerIds?: string[];
  priceRange?: {
    min: number;
    max: number;
  };
  dateRange?: {
    from: Date;
    to: Date;
  };
  targetAudience?: string[];
  location?: string;
  hasCapacity?: boolean;
}

export interface ProductSearchOptions extends GetProductsOptions {
  filters?: ProductSearchFilters;
}

export interface ProductSearchResult
  extends PaginatedResult<ProductWithBasicInfo> {
  facets: {
    categories: Array<{ id: string; name: string; count: number }>;
    types: Array<{ type: ProductType; count: number }>;
    partners: Array<{ id: string; name: string; count: number }>;
    priceRanges: Array<{ range: string; count: number }>;
    locations: Array<{ location: string; count: number }>;
  };
}

// ========== Dashboard 类型 ==========

export interface ProductDashboardStats {
  totalProducts: number;
  totalInquiries: number;
  pendingInquiries: number;
  conversionRate: number;
  topPerformingProducts: Array<{
    id: string;
    name: string;
    inquiryCount: number;
    conversionRate: number;
  }>;
  inquiryTrends: Array<{
    date: string;
    inquiries: number;
    conversions: number;
  }>;
}

// ========== 事件类型 ==========

export interface ProductEvent {
  type:
    | "product.created"
    | "product.updated"
    | "product.deleted"
    | "product.published"
    | "product.unpublished";
  productId: string;
  userId: string;
  timestamp: Date;
  data?: any;
}

export interface InquiryEvent {
  type:
    | "inquiry.created"
    | "inquiry.updated"
    | "inquiry.assigned"
    | "inquiry.completed"
    | "inquiry.cancelled";
  inquiryId: string;
  productId: string;
  userId?: string;
  assignedToId?: string;
  timestamp: Date;
  data?: any;
}

// ========== 工具类型 ==========

export type ProductEntityType = "product" | "category" | "partner" | "inquiry";
export type ProductAction =
  | "create"
  | "update"
  | "delete"
  | "publish"
  | "unpublish"
  | "archive";
