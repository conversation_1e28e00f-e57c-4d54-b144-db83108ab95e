import {
  CourseLevel,
  CourseStatus,
  Course,
  CourseCategory,
  Course<PERSON>ha<PERSON>er,
  CourseLesson,
  LessonTimestamp,
} from "../generated/prisma";

// ========== 基础输入类型 ==========
export interface CreateCourseInput {
  title: string;
  subtitle: null | string;
  description: string;
  cover: string;
  categoryId: string;
  level: CourseLevel;
  price: number;
  originalPrice: null | number;
  isFree: boolean;
  status: CourseStatus | null;
  requireLogin: boolean;
  instructorName: string;
  instructorTitle: null | string;
  instructorAvatar: null | string;
  instructorBio: null | string;
  previewVideo: null | string;
  tags: string[];
  createdById: string;
  metaTitle: null | string;
  metaDescription: null | string;
  metaKeywords: string[];
}

export interface UpdateCourseInput extends Partial<CreateCourseInput> {
  status?: CourseStatus;
}

export interface CreateChapterInput {
  title: string;
  description: null | string;
  order: number;
}

export interface UpdateChapterInput extends Partial<CreateChapterInput> {}

export interface CreateLessonInput {
  title: string;
  description: null | string;
  videoUrl: string;
  videoDuration: number;
  videoSize: null | number;
  order: number;
  isFree: boolean;
}

export interface UpdateLessonInput extends Partial<CreateLessonInput> {}

export interface CreateTimestampInput {
  timestamp: number;
  title: string;
  description: null | string;
}

// ========== 复合输入类型 ==========

export interface CreateCourseWithContentInput extends CreateCourseInput {
  chapters: CreateChapterWithLessonsInput[];
}

export interface CreateChapterWithLessonsInput extends CreateChapterInput {
  lessons: CreateLessonInput[];
}

export interface UpdateCourseWithContentInput {
  courseData: null | Partial<CreateCourseInput>;
  chapters: null | UpdateChapterWithLessonsInput[];
}

export interface UpdateChapterWithLessonsInput {
  id: null | string;
  title: string;
  description: null | string;
  order: number;
  lessons: UpdateLessonWithIdInput[];
  _action: null | "create" | "update" | "delete";
}

export interface UpdateLessonWithIdInput extends CreateLessonInput {
  id: null | string;
  _action: null | "create" | "update" | "delete";
}

// ========== 排序相关类型 ==========

export interface ChapterOrder {
  id: string;
  order: number;
}

export interface LessonOrder {
  id: string;
  order: number;
}

export interface MoveChapterInput {
  id: string;
  newOrder: number;
  courseId: string;
}

export interface MoveLessonInput {
  id: string;
  newOrder: number;
  chapterId: string;
}

// ========== 批量操作类型 ==========

export interface BulkCreateChapterInput {
  courseId: string;
  chapters: CreateChapterInput[];
}

export interface BulkCreateLessonInput {
  chapterId: string;
  lessons: CreateLessonInput[];
}

export interface VideoUpdateInput {
  videoUrl: string;
  videoDuration: number;
  videoSize: null | number;
}

// ========== 查询选项类型 ==========

export interface GetCoursesOptions {
  page?: number;
  limit?: number;
  categoryId?: string;
  level?: CourseLevel;
  status?: CourseStatus;
  isFree?: boolean;
  search?: string;
  sortBy?: "createdAt" | "updatedAt" | "title" | "price";
  sortOrder?: "asc" | "desc";
}

// ========== 返回结果类型 ==========

export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface CourseWithBasicInfo extends Course {
  category: CourseCategory;
  _count: {
    chapters: number;
    enrollments: number;
  };
}

export interface CourseWithFullContent extends Course {
  category: CourseCategory;
  chapters: (CourseChapter & {
    lessons: CourseLesson[];
  })[];
  _count: {
    chapters: number;
    enrollments: number;
  };
  stats: CourseContentStats;
}

export interface ChapterWithLessons extends CourseChapter {
  lessons: CourseLesson[];
  _count: {
    lessons: number;
  };
}

export interface ChapterWithStats extends CourseChapter {
  lessons: CourseLesson[];
  _count: {
    lessons: number;
  };
  stats: {
    totalDuration: number;
    freeLessons: number;
    paidLessons: number;
  };
}

export interface LessonWithTimestamps extends CourseLesson {
  timestamps: LessonTimestamp[];
  _count: {
    timestamps: number;
  };
}

export interface LessonWithStats extends CourseLesson {
  timestamps: LessonTimestamp[];
  chapter: {
    id: string;
    title: string;
    courseId: string;
  };
  stats: {
    watchTime: number;
    completionRate: number;
    popularTimestamps: LessonTimestamp[];
  };
}

// ========== 统计相关类型 ==========

export interface CourseStats {
  totalStudents: number;
  totalRevenue: number;
  averageRating: number;
  completionRate: number;
}

export interface CourseContentStats {
  totalChapters: number;
  totalLessons: number;
  totalDuration: number;
  freeLessons: number;
  paidLessons: number;
  averageChapterLength: number;
  averageLessonDuration: number;
  completionRate: number;
}

export interface LessonAnalytics {
  totalDuration: number;
  averageDuration: number;
  freeLessonsCount: number;
  paidLessonsCount: number;
  totalTimestamps: number;
  mostPopularLesson: CourseLesson | null;
}

export interface ChapterAnalytics {
  totalChapters: number;
  totalLessons: number;
  totalDuration: number;
  averageLessonsPerChapter: number;
  chaptersWithoutLessons: number;
}

export interface CourseAnalytics {
  content: CourseContentStats;
  performance: {
    enrollmentCount: number;
    completionRate: number;
    averageRating: number;
    revenue: number;
  };
  engagement: {
    mostPopularChapter: string;
    mostPopularLesson: string;
    averageWatchTime: number;
  };
}

// ========== 验证相关类型 ==========

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  suggestions: string[];
}

export interface ValidationError {
  type: "COURSE" | "CHAPTER" | "LESSON";
  field: string;
  message: string;
  targetId?: null | string;
}

export interface ValidationWarning {
  type: "COURSE" | "CHAPTER" | "LESSON";
  field: string;
  message: string;
  targetId?: null | string;
}

export interface IntegrityCheckResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// ========== 导入导出类型 ==========

export interface ImportContentInput {
  chapters: ImportChapterInput[];
  replaceExisting: null | boolean;
}

export interface ImportChapterInput {
  title: string;
  description: null | string;
  lessons: ImportLessonInput[];
}

export interface ImportLessonInput {
  title: string;
  description: null | string;
  videoUrl: string;
  videoDuration: number;
  videoSize: null | number;
  isFree: null | boolean;
}

export interface ExportContentResult {
  course: {
    id: string;
    title: string;
    description: string;
    level: string;
    instructorName: string;
  };
  chapters: ExportChapterResult[];
  stats: CourseContentStats;
  exportedAt: Date;
}

export interface ExportChapterResult {
  title: string;
  description: null | string;
  order: number;
  lessons: ExportLessonResult[];
}

export interface ExportLessonResult {
  title: string;
  description: null | string;
  videoDuration: number;
  isFree: boolean;
  order: number;
}

// ========== 课程分类相关类型 ==========

export interface CreateCourseCategoryInput {
  name: string;
  slug?: string;
  description?: string;
  parentId?: string;
  icon?: string;
  order?: number;
  isActive?: boolean;
}

export interface UpdateCourseCategoryInput
  extends Partial<CreateCourseCategoryInput> {}

export interface CourseCategoryWithChildren extends CourseCategory {
  children: CourseCategory[];
  parent?: CourseCategory | null;
  _count: {
    courses: number;
    children: number;
  };
}

// ========== 模板和复制相关类型 ==========

export interface DuplicateCourseOptions {
  newTitle: string;
  includeContent?: boolean;
  copyEnrollments?: boolean;
  copyReviews?: boolean;
}

export interface CourseTemplate {
  id: string;
  name: string;
  description: string;
  structure: CreateCourseWithContentInput;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateTemplateInput {
  name: string;
  description: string;
  baseCourseId: string;
}

// ========== 搜索和过滤类型 ==========

export interface CourseSearchFilters {
  categoryIds: null | string[];
  levels: null | CourseLevel[];
  priceRange: null | {
    min: number;
    max: number;
  };
  duration: null | {
    min: number;
    max: number;
  };
  isFree: null | boolean;
  hasPreview: null | boolean;
  instructorIds: null | string[];
  tags: null | string[];
  rating: null | {
    min: number;
    max: number;
  };
}

export interface CourseSearchOptions extends GetCoursesOptions {
  filters: null | CourseSearchFilters;
}

export interface CourseSearchResult
  extends PaginatedResult<CourseWithBasicInfo> {
  facets: {
    categories: Array<{ id: string; name: string; count: number }>;
    levels: Array<{ level: CourseLevel; count: number }>;
    priceRanges: Array<{ range: string; count: number }>;
    instructors: Array<{ name: string; count: number }>;
  };
}

// ========== 进度和学习相关类型 ==========

export interface LessonProgressInput {
  lessonId: string;
  userId: string;
  watchedDuration: number;
  isCompleted: null | boolean;
  currentPosition: null | number;
}

export interface ChapterProgressSummary {
  chapterId: string;
  totalLessons: number;
  completedLessons: number;
  totalDuration: number;
  watchedDuration: number;
  progressPercentage: number;
}

export interface CourseProgressSummary {
  courseId: string;
  totalChapters: number;
  completedChapters: number;
  totalLessons: number;
  completedLessons: number;
  totalDuration: number;
  watchedDuration: number;
  progressPercentage: number;
  chapters: ChapterProgressSummary[];
}

// ========== 错误处理类型 ==========

export interface ServiceError {
  code: string;
  message: string;
  details: null | any;
}

export interface OperationResult<T = any> {
  success: boolean;
  data: null | T;
  error: null | ServiceError;
}

// ========== 事件类型 ==========

export interface CourseEvent {
  type:
    | "course.created"
    | "course.updated"
    | "course.deleted"
    | "course.published"
    | "course.unpublished";
  courseId: string;
  userId: string;
  timestamp: Date;
  data: null | any;
}

export interface ChapterEvent {
  type:
    | "chapter.created"
    | "chapter.updated"
    | "chapter.deleted"
    | "chapter.reordered";
  chapterId: string;
  courseId: string;
  userId: string;
  timestamp: Date;
  data: null | any;
}

export interface LessonEvent {
  type:
    | "lesson.created"
    | "lesson.updated"
    | "lesson.deleted"
    | "lesson.reordered";
  lessonId: string;
  chapterId: string;
  courseId: string;
  userId: string;
  timestamp: Date;
  data: null | any;
}

// ========== 工具类型 ==========

export type CourseEntityType = "course" | "chapter" | "lesson";

export type SortDirection = "asc" | "desc";

export type CourseAction =
  | "create"
  | "update"
  | "delete"
  | "publish"
  | "unpublish"
  | "archive";

export type ContentStatus = "draft" | "review" | "published" | "archived";

// ========== 响应包装类型 ==========

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: ValidationError[];
  warnings?: ValidationWarning[];
  meta?: {
    timestamp: Date;
    requestId: string;
    version: string;
  };
}

export interface PaginatedApiResponse<T = any>
  extends ApiResponse<PaginatedResult<T>> {
  pagination: null | {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// ========== 课程报名类型 ==========

export interface EnrollmentStats {
  totalEnrollments: number;
  paidEnrollments: number;
  freeEnrollments: number;
  completedEnrollments: number;
  averageProgress: number;
  totalRevenue: number;
  completionRate: number;
  recentEnrollments: number; // 最近7天
}

export interface EnrollmentWithUser {
  id: string;
  courseId: string;
  userId: string;
  enrolledAt: Date;
  completedAt: Date | null;
  progress: number;
  lastAccessAt: Date | null;
  isPaid: boolean;
  paidAmount: number | null;
  paidAt: Date | null;
  user: {
    id: string;
    name: string;
    email: string;
    avatar: string | null;
    username: string;
  };
}

export interface CourseEnrollmentData {
  course: Course;
  enrollments: EnrollmentWithUser[];
  stats: EnrollmentStats;
}

export interface EnrollmentFilters {
  isPaid?: boolean;
  isCompleted?: boolean;
  dateRange?: {
    from: Date;
    to: Date;
  };
  search?: string; // 搜索用户名或邮箱
}

// ========== Dashboard 类型 ==========

export interface DashboardStats {
  totalEnrollments: number;
  totalRevenue: number;
  totalCourses: number;
  averageCompletionRate: number;
}

export interface EnrollmentTrend {
  date: string;
  enrollments: number;
  revenue: number;
}

export interface CourseWithEnrollmentStats extends Course {
  category: CourseCategory;
  enrollmentCount: number;
  completionRate: number;
  revenue: number;
  recentEnrollments: number;
}

export interface EnrollmentDetail {
  id: string;
  courseId: string;
  userId: string;
  enrolledAt: Date;
  completedAt: Date | null;
  progress: number;
  lastAccessAt: Date | null;
  isPaid: boolean;
  paidAmount: number | null;
  paidAt: Date | null;
  user: {
    id: string;
    name: string;
    email: string;
    avatar: string | null;
    username: string;
    phone: string | null;
  };
  course: {
    id: string;
    title: string;
    cover: string;
    instructorName: string;
    price: number;
  };
  progressDetails: {
    totalLessons: number;
    completedLessons: number;
    totalDuration: number;
    watchedDuration: number;
    lastLessonId: string | null;
    lastLessonTitle: string | null;
  };
}

export interface DashboardResponse {
  stats: DashboardStats;
  trends: EnrollmentTrend[];
  topCourses: CourseWithEnrollmentStats[];
}
