import {
  ProductType,
  ProductStatus,
  InquiryStatus,
  Product,
  ProductCategory,
  Partner,
  ProductInquiry,
  PrismaClient,
  Prisma,
} from "../generated/prisma";
import { PaginatedResult } from "../types/course.types";
import {
  CreateProductInput,
  UpdateProductInput,
  CreateProductCategoryInput,
  UpdateProductCategoryInput,
  CreatePartnerInput,
  UpdatePartnerInput,
  CreateProductInquiryInput,
  UpdateProductInquiryInput,
  GetProductsOptions,
  GetProductInquiriesOptions,
  ProductWithBasicInfo,
  ProductWithFullInfo,
  ProductCategoryWithCount,
  PartnerWithProductCount,
  ProductInquiryWithDetails,
  ProductStats,
  ProductSearchOptions,
  ProductSearchResult,
  ProductDashboardStats,
} from "../types/product.types";

export class ProductsService {
  constructor(private prisma: PrismaClient) {}

  // ========== 产品基本管理 ==========

  /**
   * 创建产品
   */
  async createProduct(input: CreateProductInput): Promise<Product> {
    const {
      features,
      targetAudience,
      metaKeywords,
      createdById,
      ...productData
    } = input;

    return this.prisma.product.create({
      data: {
        ...productData,
        features: features || [],
        targetAudience: targetAudience || [],
        metaKeywords: metaKeywords || [],
        status: input.status || ProductStatus.DRAFT,
        priority: input.priority || 0,
        createdById,
      },
    });
  }

  /**
   * 更新产品
   */
  async updateProduct(id: string, input: UpdateProductInput): Promise<Product> {
    const { features, targetAudience, metaKeywords, ...updateData } = input;

    const product = await this.prisma.product.findUnique({
      where: { id },
    });

    if (!product) {
      throw new Error("产品不存在");
    }

    return this.prisma.product.update({
      where: { id },
      data: {
        ...updateData,
        ...(features && { features }),
        ...(targetAudience && { targetAudience }),
        ...(metaKeywords && { metaKeywords }),
        updatedAt: new Date(),
      },
    });
  }

  /**
   * 删除产品
   */
  async deleteProduct(id: string): Promise<void> {
    const product = await this.prisma.product.findUnique({
      where: { id },
      include: {
        inquiries: true,
      },
    });

    if (!product) {
      throw new Error("产品不存在");
    }

    // 使用事务删除产品及其相关数据
    await this.prisma.$transaction(async (tx) => {
      // 删除所有咨询
      if (product.inquiries.length > 0) {
        await tx.productInquiry.deleteMany({
          where: {
            productId: id,
          },
        });
      }

      // 删除产品
      await tx.product.delete({
        where: { id },
      });
    });
  }

  /**
   * 获取单个产品
   */
  async getProduct(id: string): Promise<Product | null> {
    return this.prisma.product.findUnique({
      where: { id },
    });
  }

  /**
   * 获取产品（包含基本关联信息）
   */
  async getProductWithBasicInfo(
    id: string,
  ): Promise<ProductWithBasicInfo | null> {
    return this.prisma.product.findUnique({
      where: { id },
      include: {
        category: true,
        partner: true,
        _count: {
          select: {
            inquiries: true,
          },
        },
      },
    });
  }

  /**
   * 获取产品（包含完整信息）
   */
  async getProductWithFullInfo(
    id: string,
  ): Promise<ProductWithFullInfo | null> {
    return this.prisma.product.findUnique({
      where: { id },
      include: {
        category: true,
        partner: true,
        inquiries: {
          orderBy: {
            createdAt: "desc",
          },
        },
        createdBy: true,
        _count: {
          select: {
            inquiries: true,
          },
        },
      },
    });
  }

  /**
   * 分页获取产品列表
   */
  async getProducts(
    options: GetProductsOptions = {},
  ): Promise<PaginatedResult<ProductWithBasicInfo>> {
    const {
      page = 1,
      limit = 10,
      categoryId,
      type,
      status,
      partnerId,
      search,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = options;

    const skip = (page - 1) * limit;

    // 构建查询条件
    const where: Prisma.ProductWhereInput = {};

    if (categoryId) {
      where.categoryId = categoryId;
    }

    if (type) {
      where.type = type;
    }

    if (status) {
      where.status = status;
    }

    if (partnerId) {
      where.partnerId = partnerId;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { code: { contains: search, mode: "insensitive" } },
        { location: { contains: search, mode: "insensitive" } },
      ];
    }

    // 执行查询
    const [products, total] = await Promise.all([
      this.prisma.product.findMany({
        where,
        include: {
          category: true,
          partner: true,
          _count: {
            select: {
              inquiries: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip,
        take: limit,
      }),
      this.prisma.product.count({ where }),
    ]);

    return {
      data: products,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  // ========== 产品状态管理 ==========

  /**
   * 发布产品
   */
  async publishProduct(id: string): Promise<Product> {
    const product = await this.getProductWithBasicInfo(id);

    if (!product) {
      throw new Error("产品不存在");
    }

    // 验证产品是否可以发布
    await this.validateProductForPublish(id);

    return this.prisma.product.update({
      where: { id },
      data: {
        status: ProductStatus.ACTIVE,
        updatedAt: new Date(),
      },
    });
  }

  /**
   * 下架产品
   */
  async unpublishProduct(id: string): Promise<Product> {
    return this.prisma.product.update({
      where: { id },
      data: {
        status: ProductStatus.DRAFT,
        updatedAt: new Date(),
      },
    });
  }

  /**
   * 售罄产品
   */
  async archiveProduct(id: string): Promise<Product> {
    return this.prisma.product.update({
      where: { id },
      data: {
        status: ProductStatus.SOLD_OUT,
        updatedAt: new Date(),
      },
    });
  }

  // ========== 产品分类管理 ==========

  /**
   * 创建产品分类
   */
  async createProductCategory(
    input: CreateProductCategoryInput,
  ): Promise<ProductCategory> {
    const { order = 0, isActive = true, ...categoryData } = input;

    return this.prisma.productCategory.create({
      data: {
        ...categoryData,
        order,
        isActive,
        slug: input.slug || this.generateSlug(input.name),
      },
    });
  }

  /**
   * 更新产品分类
   */
  async updateProductCategory(
    id: string,
    input: UpdateProductCategoryInput,
  ): Promise<ProductCategory> {
    const category = await this.prisma.productCategory.findUnique({
      where: { id },
    });

    if (!category) {
      throw new Error("产品分类不存在");
    }

    return this.prisma.productCategory.update({
      where: { id },
      data: {
        ...input,
        ...(input.name &&
          !input.slug && { slug: this.generateSlug(input.name) }),
        updatedAt: new Date(),
      },
    });
  }

  /**
   * 删除产品分类
   */
  async deleteProductCategory(id: string): Promise<void> {
    const category = await this.prisma.productCategory.findUnique({
      where: { id },
      include: {
        products: true,
      },
    });

    if (!category) {
      throw new Error("产品分类不存在");
    }

    if (category.products.length > 0) {
      throw new Error("该分类下还有产品，无法删除");
    }

    await this.prisma.productCategory.delete({
      where: { id },
    });
  }

  /**
   * 获取所有产品分类
   */
  async getProductCategories(): Promise<ProductCategoryWithCount[]> {
    return this.prisma.productCategory.findMany({
      where: {
        isActive: true,
      },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
      orderBy: {
        order: "asc",
      },
    });
  }

  /**
   * 按类型获取产品分类
   */
  async getProductCategoriesByType(
    type: ProductType,
  ): Promise<ProductCategoryWithCount[]> {
    return this.prisma.productCategory.findMany({
      where: {
        type,
        isActive: true,
      },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
      orderBy: {
        order: "asc",
      },
    });
  }

  // ========== 合作伙伴管理 ==========

  /**
   * 创建合作伙伴
   */
  async createPartner(input: CreatePartnerInput): Promise<Partner> {
    const { cooperationType, isActive = true, ...partnerData } = input;

    return this.prisma.partner.create({
      data: {
        ...partnerData,
        cooperationType: cooperationType || [],
        isActive,
      },
    });
  }

  /**
   * 更新合作伙伴
   */
  async updatePartner(id: string, input: UpdatePartnerInput): Promise<Partner> {
    const partner = await this.prisma.partner.findUnique({
      where: { id },
    });

    if (!partner) {
      throw new Error("合作伙伴不存在");
    }

    const { cooperationType, ...updateData } = input;

    return this.prisma.partner.update({
      where: { id },
      data: {
        ...updateData,
        ...(cooperationType && { cooperationType }),
        updatedAt: new Date(),
      },
    });
  }

  /**
   * 删除合作伙伴
   */
  async deletePartner(id: string): Promise<void> {
    const partner = await this.prisma.partner.findUnique({
      where: { id },
      include: {
        products: true,
      },
    });

    if (!partner) {
      throw new Error("合作伙伴不存在");
    }

    if (partner.products.length > 0) {
      throw new Error("该合作伙伴下还有产品，无法删除");
    }

    await this.prisma.partner.delete({
      where: { id },
    });
  }

  /**
   * 获取所有合作伙伴
   */
  async getPartners(): Promise<PartnerWithProductCount[]> {
    return this.prisma.partner.findMany({
      where: {
        isActive: true,
      },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });
  }

  /**
   * 获取单个合作伙伴
   */
  async getPartner(id: string): Promise<PartnerWithProductCount | null> {
    return this.prisma.partner.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
    });
  }

  // ========== 产品咨询管理 ==========

  /**
   * 创建产品咨询
   */
  async createProductInquiry(
    input: CreateProductInquiryInput,
  ): Promise<ProductInquiry> {
    return this.prisma.productInquiry.create({
      data: {
        ...input,
        status: InquiryStatus.PENDING,
      },
    });
  }

  /**
   * 更新产品咨询
   */
  async updateProductInquiry(
    id: string,
    input: UpdateProductInquiryInput,
  ): Promise<ProductInquiry> {
    const inquiry = await this.prisma.productInquiry.findUnique({
      where: { id },
    });

    if (!inquiry) {
      throw new Error("产品咨询不存在");
    }

    return this.prisma.productInquiry.update({
      where: { id },
      data: {
        ...input,
        updatedAt: new Date(),
      },
    });
  }

  /**
   * 删除产品咨询
   */
  async deleteProductInquiry(id: string): Promise<void> {
    await this.prisma.productInquiry.delete({
      where: { id },
    });
  }

  /**
   * 获取产品咨询详情
   */
  async getProductInquiry(
    id: string,
  ): Promise<ProductInquiryWithDetails | null> {
    return await this.prisma.productInquiry.findUnique({
      where: { id },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            code: true,
            type: true,
            images: true,
            price: true,
            priceUnit: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        tenant: {
          select: {
            id: true,
            name: true,
            type: true,
            logo: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
      },
    });
  }

  /**
   * 分页获取产品咨询列表
   */
  async getProductInquiries(
    options: GetProductInquiriesOptions = {},
  ): Promise<PaginatedResult<ProductInquiryWithDetails>> {
    const {
      page = 1,
      limit = 10,
      productId,
      status,
      assignedToId,
      tenantId,
      search,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = options;

    const skip = (page - 1) * limit;

    // 构建查询条件
    const where: Prisma.ProductInquiryWhereInput = {};

    if (productId) {
      where.productId = productId;
    }

    if (status) {
      where.status = status;
    }

    if (assignedToId) {
      where.assignedToId = assignedToId;
    }

    if (tenantId) {
      where.tenantId = tenantId;
    }

    if (search) {
      where.OR = [
        { contactName: { contains: search, mode: "insensitive" } },
        { contactPhone: { contains: search, mode: "insensitive" } },
        { contactEmail: { contains: search, mode: "insensitive" } },
        { organization: { contains: search, mode: "insensitive" } },
      ];
    }

    // 执行查询
    const [inquiries, total] = await Promise.all([
      this.prisma.productInquiry.findMany({
        where,
        include: {
          product: {
            select: {
              id: true,
              name: true,
              code: true,
              type: true,
              images: true,
              price: true,
              priceUnit: true,
            },
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          tenant: {
            select: {
              id: true,
              name: true,
              type: true,
              logo: true,
            },
          },
          assignedTo: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip,
        take: limit,
      }),
      this.prisma.productInquiry.count({ where }),
    ]);

    return {
      data: inquiries,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * 分配咨询给管理员
   */
  async assignInquiry(
    id: string,
    assignedToId: string,
  ): Promise<ProductInquiry> {
    return this.prisma.productInquiry.update({
      where: { id },
      data: {
        assignedToId,
        status: InquiryStatus.CONTACTING,
        updatedAt: new Date(),
      },
    });
  }

  // ========== 搜索功能 ==========

  /**
   * 高级搜索产品
   */
  async searchProducts(
    options: ProductSearchOptions,
  ): Promise<ProductSearchResult> {
    const { filters, ...baseOptions } = options;

    // 构建基础查询条件
    const where: Prisma.ProductWhereInput = {};

    // 添加基础筛选条件
    if (baseOptions.categoryId) where.categoryId = baseOptions.categoryId;
    if (baseOptions.type) where.type = baseOptions.type;
    if (baseOptions.status) where.status = baseOptions.status;
    if (baseOptions.partnerId) where.partnerId = baseOptions.partnerId;

    // 添加高级筛选条件
    if (filters) {
      if (filters.categoryIds?.length) {
        where.categoryId = { in: filters.categoryIds };
      }

      if (filters.types?.length) {
        where.type = { in: filters.types };
      }

      if (filters.partnerIds?.length) {
        where.partnerId = { in: filters.partnerIds };
      }

      if (filters.priceRange) {
        where.price = {
          gte: filters.priceRange.min,
          lte: filters.priceRange.max,
        };
      }

      if (filters.dateRange) {
        where.AND = [
          ...(Array.isArray(where.AND) ? where.AND : []),
          {
            OR: [
              {
                startDate: {
                  gte: filters.dateRange.from,
                  lte: filters.dateRange.to,
                },
              },
              {
                endDate: {
                  gte: filters.dateRange.from,
                  lte: filters.dateRange.to,
                },
              },
            ],
          },
        ];
      }

      if (filters.targetAudience?.length) {
        where.targetAudience = {
          hasSome: filters.targetAudience,
        };
      }

      if (filters.location) {
        where.location = { contains: filters.location, mode: "insensitive" };
      }

      if (filters.hasCapacity) {
        where.capacity = { gt: 0 };
      }
    }

    // 添加搜索条件
    if (baseOptions.search) {
      where.OR = [
        { name: { contains: baseOptions.search, mode: "insensitive" } },
        { description: { contains: baseOptions.search, mode: "insensitive" } },
        { code: { contains: baseOptions.search, mode: "insensitive" } },
        { location: { contains: baseOptions.search, mode: "insensitive" } },
      ];
    }

    // 获取分页结果
    const paginatedResult = await this.getProductsWithWhere(where, baseOptions);

    // 获取聚合统计数据（facets）
    const facets = await this.getProductSearchFacets(where);

    return {
      ...paginatedResult,
      facets,
    };
  }

  // ========== 统计功能 ==========

  /**
   * 获取产品统计信息
   */
  async getProductStats(): Promise<ProductStats> {
    const [
      totalProducts,
      totalInquiries,
      totalPartners,
      activeProducts,
      pendingInquiries,
      completedInquiries,
    ] = await Promise.all([
      this.prisma.product.count(),
      this.prisma.productInquiry.count(),
      this.prisma.partner.count({ where: { isActive: true } }),
      this.prisma.product.count({ where: { status: ProductStatus.ACTIVE } }),
      this.prisma.productInquiry.count({
        where: { status: InquiryStatus.PENDING },
      }),
      this.prisma.productInquiry.count({
        where: { status: InquiryStatus.COMPLETED },
      }),
    ]);

    const conversionRate =
      totalInquiries > 0 ? (completedInquiries / totalInquiries) * 100 : 0;

    return {
      totalProducts,
      totalInquiries,
      totalPartners,
      activeProducts,
      pendingInquiries,
      completedInquiries,
      conversionRate: Math.round(conversionRate * 100) / 100,
    };
  }

  /**
   * 获取仪表板统计数据
   */
  async getDashboardStats(): Promise<ProductDashboardStats> {
    const stats = await this.getProductStats();

    // 获取表现最好的产品
    const topPerformingProducts = await this.prisma.product.findMany({
      select: {
        id: true,
        name: true,
        _count: {
          select: {
            inquiries: true,
          },
        },
      },
      where: {
        status: ProductStatus.ACTIVE,
      },
      orderBy: {
        inquiries: {
          _count: "desc",
        },
      },
      take: 5,
    });

    // 计算每个产品的转化率
    const topProducts = await Promise.all(
      topPerformingProducts.map(async (product) => {
        const completedInquiries = await this.prisma.productInquiry.count({
          where: {
            productId: product.id,
            status: InquiryStatus.COMPLETED,
          },
        });

        const conversionRate =
          product._count.inquiries > 0
            ? (completedInquiries / product._count.inquiries) * 100
            : 0;

        return {
          id: product.id,
          name: product.name,
          inquiryCount: product._count.inquiries,
          conversionRate: Math.round(conversionRate * 100) / 100,
        };
      }),
    );

    // 获取咨询趋势（最近30天）
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const inquiryTrends = await this.getInquiryTrends(
      thirtyDaysAgo,
      new Date(),
    );

    return {
      totalProducts: stats.totalProducts,
      totalInquiries: stats.totalInquiries,
      pendingInquiries: stats.pendingInquiries,
      conversionRate: stats.conversionRate,
      topPerformingProducts: topProducts,
      inquiryTrends,
    };
  }

  // ========== 批量操作 ==========

  /**
   * 批量更新产品状态
   */
  async batchUpdateProductStatus(
    ids: string[],
    status: ProductStatus,
  ): Promise<number> {
    const result = await this.prisma.product.updateMany({
      where: {
        id: {
          in: ids,
        },
      },
      data: {
        status,
        updatedAt: new Date(),
      },
    });

    return result.count;
  }

  /**
   * 批量删除产品
   */
  async batchDeleteProducts(ids: string[]): Promise<number> {
    // 先删除相关的咨询
    await this.prisma.productInquiry.deleteMany({
      where: {
        productId: {
          in: ids,
        },
      },
    });

    // 再删除产品
    const result = await this.prisma.product.deleteMany({
      where: {
        id: {
          in: ids,
        },
      },
    });

    return result.count;
  }

  // ========== 私有工具方法 ==========

  /**
   * 生成URL友好的slug
   */
  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^\w\s-]/g, "") // 移除特殊字符
      .replace(/\s+/g, "-") // 空格替换为连字符
      .replace(/-+/g, "-") // 多个连字符合并为一个
      .trim();
  }

  /**
   * 验证产品是否可以发布
   */
  private async validateProductForPublish(id: string): Promise<void> {
    const product = await this.prisma.product.findUnique({
      where: { id },
      include: {
        category: true,
      },
    });

    if (!product) {
      throw new Error("产品不存在");
    }

    // 检查基本信息
    if (!product.name || !product.description || !product.images.length) {
      throw new Error("产品基本信息不完整");
    }

    // 检查分类
    if (!product.category || !product.category.isActive) {
      throw new Error("产品分类不存在或已禁用");
    }
  }

  /**
   * 根据where条件获取产品列表
   */
  private async getProductsWithWhere(
    where: Prisma.ProductWhereInput,
    options: GetProductsOptions,
  ): Promise<PaginatedResult<ProductWithBasicInfo>> {
    const {
      page = 1,
      limit = 10,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = options;

    const skip = (page - 1) * limit;

    const [products, total] = await Promise.all([
      this.prisma.product.findMany({
        where,
        include: {
          category: true,
          partner: true,
          _count: {
            select: {
              inquiries: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip,
        take: limit,
      }),
      this.prisma.product.count({ where }),
    ]);

    return {
      data: products,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * 获取搜索聚合统计数据
   */
  private async getProductSearchFacets(where: Prisma.ProductWhereInput) {
    const [categories, types, partners, priceRanges, locations] =
      await Promise.all([
        // 分类统计
        this.prisma.product
          .groupBy({
            by: ["categoryId"],
            where,
            _count: true,
          })
          .then((results) =>
            Promise.all(
              results.map(async (result) => {
                const category = await this.prisma.productCategory.findUnique({
                  where: { id: result.categoryId },
                  select: { id: true, name: true },
                });
                return {
                  id: result.categoryId,
                  name: category?.name || "未知分类",
                  count: result._count,
                };
              }),
            ),
          ),

        // 类型统计
        this.prisma.product
          .groupBy({
            by: ["type"],
            where,
            _count: true,
          })
          .then((results) =>
            results.map((result) => ({
              type: result.type,
              count: result._count,
            })),
          ),

        // 合作伙伴统计
        this.prisma.product
          .groupBy({
            by: ["partnerId"],
            where: { ...where, partnerId: { not: null } },
            _count: true,
          })
          .then((results) =>
            Promise.all(
              results.map(async (result) => {
                const partner = await this.prisma.partner.findUnique({
                  where: { id: result.partnerId! },
                  select: { id: true, name: true },
                });
                return {
                  id: result.partnerId!,
                  name: partner?.name || "未知合作伙伴",
                  count: result._count,
                };
              }),
            ),
          ),

        // 价格区间统计（简化实现）
        Promise.resolve([
          { range: "0-1000", count: 0 },
          { range: "1000-5000", count: 0 },
          { range: "5000-10000", count: 0 },
          { range: "10000+", count: 0 },
        ]),

        // 地点统计
        this.prisma.product
          .groupBy({
            by: ["location"],
            where: { ...where, location: { not: null } },
            _count: true,
          })
          .then((results) =>
            results.map((result) => ({
              location: result.location!,
              count: result._count,
            })),
          ),
      ]);

    return {
      categories,
      types,
      partners,
      priceRanges,
      locations,
    };
  }

  /**
   * 获取咨询趋势数据
   */
  private async getInquiryTrends(startDate: Date, endDate: Date) {
    // 这里需要根据实际需求实现具体的趋势统计逻辑
    // 简化实现，返回空数组
    return [];
  }
}
