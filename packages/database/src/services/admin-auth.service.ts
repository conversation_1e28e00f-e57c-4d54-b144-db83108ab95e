import { PrismaClient, AdminUser } from "../generated/prisma";
import { createHash } from "crypto";
import { redis, REDIS_KEYS, CACHE_TTL } from "@workspace/ioredis";
import jwt from "jsonwebtoken";

// JWT配置
const JWT_SECRET =
  process.env.JWT_SECRET || "your-super-secret-key-change-in-production";
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || "7d";

// JWT载荷接口
interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  sessionId: string;
  iat?: number;
  exp?: number;
}

// 登录输入
export interface LoginInput {
  email: string;
  password: string;
  remember?: boolean;
}

// 登录结果
export interface LoginResult {
  success: boolean;
  data?: {
    user: AdminUser & { permissions: string[]; allowedModules: string[] };
    token: string;
    expiresAt: string;
  };
  message?: string;
  errors?: Record<string, string>;
}

// 会话验证结果
export interface SessionResult {
  success: boolean;
  user?: AdminUser & { permissions: string[]; allowedModules: string[] };
  message?: string;
}

// 登出结果
export interface LogoutResult {
  success: boolean;
  message?: string;
}

// 管理员认证服务（包含所有业务逻辑）
export class AdminAuthService {
  constructor(private prisma: PrismaClient) {}

  /**
   * MD5加密
   */
  private hashPassword(password: string): string {
    return createHash("md5").update(password).digest("hex");
  }

  /**
   * 生成JWT token
   */
  private generateJWT(payload: Omit<JWTPayload, "iat" | "exp">): string {
    return jwt.sign(payload, JWT_SECRET, {
      expiresIn: JWT_EXPIRES_IN as any,
    });
  }

  /**
   * 验证JWT token
   */
  private verifyJWT(token: string): JWTPayload | null {
    try {
      const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload;
      return decoded;
    } catch (error) {
      console.error("JWT验证失败:", error);
      return null;
    }
  }

  /**
   * 完整的登录业务逻辑
   */
  async login(
    credentials: LoginInput,
    ip?: string,
    userAgent?: string,
  ): Promise<LoginResult> {
    try {
      // 1. 查找管理员用户
      const admin = await this.prisma.adminUser.findUnique({
        where: {
          email: credentials.email.toLowerCase(),
        },
      });

      if (!admin) {
        return {
          success: false,
          message: "用户名或密码错误",
        };
      }

      // 2. 检查用户状态
      if (admin.status !== "ACTIVE") {
        return {
          success: false,
          message: "账户已被禁用，请联系管理员",
        };
      }

      // 3. 验证密码
      const hashedPassword = this.hashPassword(credentials.password);
      if (admin.password !== hashedPassword) {
        return {
          success: false,
          message: "用户名或密码错误",
        };
      }

      // 4. 生成JWT token和会话ID
      const sessionId = `admin_${admin.id}_${Date.now()}`;
      const token = this.generateJWT({
        userId: admin.id,
        email: admin.email,
        role: admin.role,
        sessionId,
      });

      // 5. 创建数据库会话
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
      await this.prisma.adminSession.create({
        data: {
          adminId: admin.id,
          token,
          ip,
          userAgent,
          expiresAt,
        },
      });

      // 6. 缓存用户权限信息到Redis
      const permissionData = {
        userId: admin.id,
        permissions: admin.permissions,
        allowedModules: admin.allowedModules,
        role: admin.role,
        sessionId,
      };

      try {
        await redis.setex(
          REDIS_KEYS.SESSION(token),
          CACHE_TTL.SESSION,
          JSON.stringify(permissionData),
        );

        // 缓存用户权限
        await redis.setex(
          REDIS_KEYS.USER_PERMISSIONS(admin.id),
          CACHE_TTL.PERMISSIONS,
          JSON.stringify(admin.permissions),
        );
      } catch (redisError) {
        console.error("Redis缓存失败:", redisError);
        // Redis失败不影响登录，继续执行
      }

      // 7. 更新最后登录时间
      await this.prisma.adminUser.update({
        where: { id: admin.id },
        data: {
          lastLoginAt: new Date(),
          lastLoginIp: ip,
        },
      });

      // 8. 返回成功结果
      const { password: _, ...userWithoutPassword } = admin;
      return {
        success: true,
        data: {
          user: {
            ...userWithoutPassword,
            permissions: admin.permissions || [],
            allowedModules: admin.allowedModules || [],
          } as any,
          token,
          expiresAt: expiresAt.toISOString(),
        },
        message: "登录成功",
      };
    } catch (error) {
      console.error("登录服务错误:", error);
      return {
        success: false,
        message: "系统错误，请稍后重试",
      };
    }
  }

  /**
   * 验证管理员会话
   */
  async validateSession(token?: string): Promise<SessionResult> {
    if (!token) {
      return {
        success: false,
        message: "未找到认证信息",
      };
    }

    try {
      // 1. 先从Redis缓存中获取
      let permissionData: any = null;
      try {
        const cachedData = await redis.get(REDIS_KEYS.SESSION(token));
        if (cachedData) {
          permissionData = JSON.parse(cachedData);
        }
      } catch (redisError) {
        console.error("Redis读取失败:", redisError);
      }

      // 2. 如果缓存中没有，从数据库验证
      if (!permissionData) {
        const session = await this.prisma.adminSession.findUnique({
          where: { token },
          include: {
            admin: true,
          },
        });

        if (!session) {
          return {
            success: false,
            message: "无效的会话",
          };
        }

        // 检查会话是否过期
        if (session.expiresAt < new Date()) {
          // 删除过期会话
          await this.prisma.adminSession.delete({
            where: { id: session.id },
          });
          return {
            success: false,
            message: "会话已过期",
          };
        }

        // 检查用户状态
        if (session.admin.status !== "ACTIVE") {
          return {
            success: false,
            message: "账户已被禁用",
          };
        }

        permissionData = {
          userId: session.admin.id,
          permissions: session.admin.permissions,
          allowedModules: session.admin.allowedModules,
          role: session.admin.role,
        };

        // 重新缓存到Redis
        try {
          await redis.setex(
            REDIS_KEYS.SESSION(token),
            CACHE_TTL.SESSION,
            JSON.stringify(permissionData),
          );
        } catch (redisError) {
          console.error("Redis缓存失败:", redisError);
        }
      }

      // 3. 获取完整用户信息
      const admin = await this.prisma.adminUser.findUnique({
        where: { id: permissionData.userId },
      });

      if (!admin || admin.status !== "ACTIVE") {
        return {
          success: false,
          message: "用户不存在或已被禁用",
        };
      }

      const { password: _, ...userWithoutPassword } = admin;
      return {
        success: true,
        user: {
          ...userWithoutPassword,
          permissions: admin.permissions || [],
          allowedModules: admin.allowedModules || [],
        } as any,
      };
    } catch (error) {
      console.error("会话验证错误:", error);
      return {
        success: false,
        message: "系统错误",
      };
    }
  }

  /**
   * 管理员登出
   */
  async logout(token: string): Promise<LogoutResult> {
    try {
      // 1. 删除数据库会话
      await this.prisma.adminSession.deleteMany({
        where: { token },
      });

      // 2. 删除Redis缓存
      try {
        await redis.del(REDIS_KEYS.SESSION(token));
      } catch (redisError) {
        console.error("Redis删除失败:", redisError);
      }

      return {
        success: true,
        message: "退出成功",
      };
    } catch (error) {
      console.error("登出错误:", error);
      return {
        success: false,
        message: "退出失败",
      };
    }
  }

  /**
   * 根据ID获取管理员信息
   */
  async getAdminById(id: string): Promise<SessionResult> {
    try {
      const admin = await this.prisma.adminUser.findUnique({
        where: { id },
      });

      if (!admin) {
        return {
          success: false,
          message: "用户不存在",
        };
      }

      if (admin.status !== "ACTIVE") {
        return {
          success: false,
          message: "账户已被禁用",
        };
      }

      const { password: _, ...userWithoutPassword } = admin;
      return {
        success: true,
        user: {
          ...userWithoutPassword,
          permissions: admin.permissions || [],
          allowedModules: admin.allowedModules || [],
        } as any,
      };
    } catch (error) {
      console.error("Get admin by ID error:", error);
      return {
        success: false,
        message: "系统错误",
      };
    }
  }

  /**
   * 清理过期会话
   */
  async cleanupExpiredSessions(): Promise<void> {
    try {
      await this.prisma.adminSession.deleteMany({
        where: {
          expiresAt: {
            lt: new Date(),
          },
        },
      });
    } catch (error) {
      console.error("Cleanup expired sessions error:", error);
    }
  }
}
