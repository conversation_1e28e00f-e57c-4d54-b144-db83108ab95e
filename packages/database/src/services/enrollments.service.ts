import { PrismaClient, CourseEnrollment } from "../generated/prisma";
import {
  EnrollmentFilters,
  EnrollmentWithUser,
  EnrollmentStats,
  CourseEnrollmentData,
} from "../types/course.types";

export class EnrollmentsService {
  constructor(private prisma: PrismaClient) {}

  /**
   * 获取课程的所有报名信息
   */
  async getCourseEnrollments(
    courseId: string,
    filters?: EnrollmentFilters,
    page: number = 1,
    pageSize: number = 20,
  ): Promise<{
    enrollments: EnrollmentWithUser[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }> {
    const where: any = {
      courseId,
    };

    // 应用过滤条件
    if (filters) {
      if (filters.isPaid !== undefined) {
        where.isPaid = filters.isPaid;
      }

      if (filters.isCompleted !== undefined) {
        if (filters.isCompleted) {
          where.completedAt = { not: null };
        } else {
          where.completedAt = null;
        }
      }

      if (filters.dateRange) {
        where.enrolledAt = {
          gte: filters.dateRange.from,
          lte: filters.dateRange.to,
        };
      }

      if (filters.search) {
        where.user = {
          OR: [
            { name: { contains: filters.search, mode: "insensitive" } },
            { email: { contains: filters.search, mode: "insensitive" } },
            { username: { contains: filters.search, mode: "insensitive" } },
          ],
        };
      }
    }

    const [enrollments, total] = await Promise.all([
      this.prisma.courseEnrollment.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
              username: true,
            },
          },
        },
        orderBy: { enrolledAt: "desc" },
        skip: (page - 1) * pageSize,
        take: pageSize,
      }),
      this.prisma.courseEnrollment.count({ where }),
    ]);

    return {
      enrollments: enrollments as unknown as EnrollmentWithUser[],
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  /**
   * 获取课程报名统计信息
   */
  async getCourseEnrollmentStats(courseId: string): Promise<EnrollmentStats> {
    // 获取所有报名记录
    const enrollments = await this.prisma.courseEnrollment.findMany({
      where: { courseId },
    });

    const totalEnrollments = enrollments.length;
    const paidEnrollments = enrollments.filter((e) => e.isPaid).length;
    const freeEnrollments = totalEnrollments - paidEnrollments;
    const completedEnrollments = enrollments.filter(
      (e) => e.completedAt,
    ).length;

    // 计算平均进度
    const totalProgress = enrollments.reduce((sum, e) => sum + e.progress, 0);
    const averageProgress =
      totalEnrollments > 0 ? totalProgress / totalEnrollments : 0;

    // 计算总收入
    const totalRevenue = enrollments.reduce((sum, e) => {
      return sum + (e.paidAmount ? Number(e.paidAmount) : 0);
    }, 0);

    // 计算完成率
    const completionRate =
      totalEnrollments > 0
        ? (completedEnrollments / totalEnrollments) * 100
        : 0;

    // 计算最近7天报名数
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const recentEnrollments = enrollments.filter(
      (e) => e.enrolledAt >= sevenDaysAgo,
    ).length;

    return {
      totalEnrollments,
      paidEnrollments,
      freeEnrollments,
      completedEnrollments,
      averageProgress: Math.round(averageProgress * 100) / 100,
      totalRevenue,
      completionRate: Math.round(completionRate * 100) / 100,
      recentEnrollments,
    };
  }

  /**
   * 获取完整的课程报名数据
   */
  async getCourseEnrollmentData(
    courseId: string,
    filters?: EnrollmentFilters,
    page: number = 1,
    pageSize: number = 20,
  ): Promise<CourseEnrollmentData & { pagination: any }> {
    // 获取课程信息
    const course = await this.prisma.course.findUnique({
      where: { id: courseId },
    });

    if (!course) {
      throw new Error("课程不存在");
    }

    // 获取报名信息和统计数据
    const [enrollmentData, stats] = await Promise.all([
      this.getCourseEnrollments(courseId, filters, page, pageSize),
      this.getCourseEnrollmentStats(courseId),
    ]);

    return {
      course,
      enrollments: enrollmentData.enrollments,
      stats,
      pagination: {
        total: enrollmentData.total,
        page: enrollmentData.page,
        pageSize: enrollmentData.pageSize,
        totalPages: enrollmentData.totalPages,
      },
    };
  }

  /**
   * 更新报名状态
   */
  async updateEnrollmentStatus(
    enrollmentId: string,
    updates: {
      isPaid?: boolean;
      paidAmount?: number;
      paidAt?: Date;
      completedAt?: Date;
      progress?: number;
    },
  ): Promise<CourseEnrollment> {
    return await this.prisma.courseEnrollment.update({
      where: { id: enrollmentId },
      data: updates,
    });
  }

  /**
   * 删除报名记录
   */
  async deleteEnrollment(enrollmentId: string): Promise<void> {
    await this.prisma.courseEnrollment.delete({
      where: { id: enrollmentId },
    });
  }

  /**
   * 批量操作报名记录
   */
  async batchUpdateEnrollments(
    enrollmentIds: string[],
    updates: {
      isPaid?: boolean;
      paidAmount?: number;
      paidAt?: Date;
      completedAt?: Date;
      progress?: number;
    },
  ): Promise<void> {
    await this.prisma.courseEnrollment.updateMany({
      where: {
        id: { in: enrollmentIds },
      },
      data: updates,
    });
  }

  /**
   * 获取报名趋势数据（用于图表）
   */
  async getEnrollmentTrends(
    courseId: string,
    days: number = 30,
  ): Promise<Array<{ date: string; enrollments: number; revenue: number }>> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const enrollments = await this.prisma.courseEnrollment.findMany({
      where: {
        courseId,
        enrolledAt: {
          gte: startDate,
        },
      },
      orderBy: { enrolledAt: "asc" },
    });

    // 按日期分组统计
    const trendsMap = new Map<
      string,
      { enrollments: number; revenue: number }
    >();

    // 初始化所有日期
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split("T")[0];
      trendsMap.set(dateStr, { enrollments: 0, revenue: 0 });
    }

    // 统计实际数据
    enrollments.forEach((enrollment) => {
      const dateStr = enrollment.enrolledAt.toISOString().split("T")[0];
      const current = trendsMap.get(dateStr) || { enrollments: 0, revenue: 0 };
      current.enrollments += 1;
      current.revenue += enrollment.paidAmount
        ? Number(enrollment.paidAmount)
        : 0;
      trendsMap.set(dateStr, current);
    });

    // 转换为数组并排序
    return Array.from(trendsMap)
      .map(([date, data]) => ({ date, ...data }))
      .sort((a, b) => a.date.localeCompare(b.date));
  }
}
