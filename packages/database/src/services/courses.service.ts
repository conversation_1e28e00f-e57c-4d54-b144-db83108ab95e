import {
  CourseLevel,
  CourseStatus,
  Course,
  CourseCategory,
  PrismaClient,
  Prisma,
} from "../generated/prisma";
import {
  CreateCourseInput,
  UpdateCourseInput,
  GetCoursesOptions,
  PaginatedResult,
  CourseWithBasicInfo,
  CourseStats,
  CreateCourseCategoryInput,
  UpdateCourseCategoryInput,
  CourseCategoryWithChildren,
} from "../types/course.types";

export class CoursesService {
  constructor(private prisma: PrismaClient) {}

  // ========== 课程基本管理 ==========

  /**
   * 创建课程（仅基本信息）
   */
  async createCourse(input: CreateCourseInput): Promise<Course> {
    const { tags, metaKeywords, createdById, ...courseData } = input;

    return this.prisma.course.create({
      data: {
        ...courseData,
        tags: tags || [],
        metaKeywords: metaKeywords || [],
        status: CourseStatus.DRAFT,
        duration: 0,
        createdById,
      },
    });
  }

  /**
   * 更新课程基本信息
   */
  async updateCourse(id: string, input: UpdateCourseInput): Promise<Course> {
    const { tags, metaKeywords, ...updateData } = input;

    const course = await this.prisma.course.findUnique({
      where: { id },
    });

    if (!course) {
      throw new Error("课程不存在");
    }

    return this.prisma.course.update({
      where: { id },
      data: {
        ...updateData,
        ...(tags && { tags }),
        ...(metaKeywords && { metaKeywords }),
        ...(input.title && { slug: this.generateSlug(input.title) }),
        updatedAt: new Date(),
      },
    });
  }

  /**
   * 删除课程
   */
  async deleteCourse(id: string): Promise<void> {
    const course = await this.prisma.course.findUnique({
      where: { id },
      include: {
        chapters: {
          include: {
            lessons: true,
          },
        },
      },
    });

    if (!course) {
      throw new Error("课程不存在");
    }

    // 使用事务删除课程及其相关数据
    await this.prisma.$transaction(async (tx) => {
      // 删除所有课时
      for (const chapter of course.chapters) {
        if (chapter.lessons.length > 0) {
          await tx.courseLesson.deleteMany({
            where: {
              chapterId: chapter.id,
            },
          });
        }
      }

      // 删除所有章节
      if (course.chapters.length > 0) {
        await tx.courseChapter.deleteMany({
          where: {
            courseId: id,
          },
        });
      }

      // 删除课程
      await tx.course.delete({
        where: { id },
      });
    });
  }

  /**
   * 获取单个课程
   */
  async getCourse(id: string): Promise<Course | null> {
    return this.prisma.course.findUnique({
      where: { id },
    });
  }

  /**
   * 获取课程（包含基本关联信息）
   */
  async getCourseWithBasicInfo(
    id: string,
  ): Promise<CourseWithBasicInfo | null> {
    return this.prisma.course.findUnique({
      where: { id },
      include: {
        category: true,
        _count: {
          select: {
            chapters: true,
            enrollments: true,
          },
        },
      },
    });
  }

  /**
   * 分页获取课程列表
   */
  async getCourses(
    options: GetCoursesOptions = {},
  ): Promise<PaginatedResult<CourseWithBasicInfo>> {
    const {
      page = 1,
      limit = 10,
      categoryId,
      level,
      status,
      isFree,
      search,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = options;

    const skip = (page - 1) * limit;

    // 构建查询条件
    const where: Prisma.CourseWhereInput = {};

    if (categoryId) {
      where.categoryId = categoryId;
    }

    if (level) {
      where.level = level;
    }

    if (status) {
      where.status = status;
    }

    if (typeof isFree === "boolean") {
      where.isFree = isFree;
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { instructorName: { contains: search, mode: "insensitive" } },
      ];
    }

    // 执行查询
    const [courses, total] = await Promise.all([
      this.prisma.course.findMany({
        where,
        include: {
          category: true,
          _count: {
            select: {
              chapters: true,
              enrollments: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip,
        take: limit,
      }),
      this.prisma.course.count({ where }),
    ]);

    return {
      data: courses,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  // ========== 课程状态管理 ==========

  /**
   * 发布课程
   */
  async publishCourse(id: string): Promise<Course> {
    const course = await this.getCourseWithBasicInfo(id);

    if (!course) {
      throw new Error("课程不存在");
    }

    // 验证课程是否可以发布
    await this.validateCourseForPublish(id);

    return this.prisma.course.update({
      where: { id },
      data: {
        status: CourseStatus.PUBLISHED,
        publishedAt: new Date(),
        updatedAt: new Date(),
      },
    });
  }

  /**
   * 下架课程
   */
  async unpublishCourse(id: string): Promise<Course> {
    return this.prisma.course.update({
      where: { id },
      data: {
        status: CourseStatus.DRAFT,
        updatedAt: new Date(),
      },
    });
  }

  /**
   * 设为离线
   */
  async offlineCourse(id: string): Promise<Course> {
    return this.prisma.course.update({
      where: { id },
      data: {
        status: CourseStatus.OFFLINE,
        updatedAt: new Date(),
      },
    });
  }

  // ========== 课程统计 ==========

  /**
   * 获取课程统计信息
   */
  async getCourseStats(id: string): Promise<CourseStats> {
    const course = await this.prisma.course.findUnique({
      where: { id },
      include: {
        enrollments: true,
        reviews: true,
      },
    });

    if (!course) {
      throw new Error("课程不存在");
    }

    const totalStudents = course.enrollments.length;
    const totalRevenue = course.enrollments.reduce((sum, enrollment) => {
      const amount = enrollment.paidAmount;
      return sum + (amount ? Number(amount) : 0);
    }, 0);

    const reviews = course.reviews;
    const averageRating =
      reviews.length > 0
        ? reviews.reduce((sum, review) => sum + review.rating, 0) /
          reviews.length
        : 0;

    // 计算完成率（这里需要根据实际业务逻辑调整）
    const completedEnrollments = course.enrollments.filter(
      (enrollment) => enrollment.completedAt,
    ).length;
    const completionRate =
      totalStudents > 0 ? (completedEnrollments / totalStudents) * 100 : 0;

    return {
      totalStudents,
      totalRevenue,
      averageRating: Math.round(averageRating * 100) / 100,
      completionRate: Math.round(completionRate * 100) / 100,
    };
  }

  // ========== 课程分类管理 ==========

  /**
   * 创建课程分类
   */
  async createCourseCategory(
    input: CreateCourseCategoryInput,
  ): Promise<CourseCategory> {
    const { order = 0, isActive = true, ...categoryData } = input;

    return this.prisma.courseCategory.create({
      data: {
        ...categoryData,
        order,
        isActive,
        slug: this.generateSlug(input.name),
      },
    });
  }

  /**
   * 更新课程分类
   */
  async updateCourseCategory(
    id: string,
    input: UpdateCourseCategoryInput,
  ): Promise<CourseCategory> {
    const category = await this.prisma.courseCategory.findUnique({
      where: { id },
    });

    if (!category) {
      throw new Error("课程分类不存在");
    }

    return this.prisma.courseCategory.update({
      where: { id },
      data: {
        ...input,
        ...(input.name && { slug: this.generateSlug(input.name) }),
        updatedAt: new Date(),
      },
    });
  }

  /**
   * 删除课程分类
   */
  async deleteCourseCategory(id: string): Promise<void> {
    const category = await this.prisma.courseCategory.findUnique({
      where: { id },
      include: {
        courses: true,
        children: true,
      },
    });

    if (!category) {
      throw new Error("课程分类不存在");
    }

    if (category.courses.length > 0) {
      throw new Error("该分类下还有课程，无法删除");
    }

    if (category.children.length > 0) {
      throw new Error("该分类下还有子分类，无法删除");
    }

    await this.prisma.courseCategory.delete({
      where: { id },
    });
  }

  /**
   * 获取所有课程分类（树形结构）
   */
  async getCourseCategoriesTree(): Promise<CourseCategoryWithChildren[]> {
    const categories = await this.prisma.courseCategory.findMany({
      where: {
        isActive: true,
      },
      include: {
        children: {
          where: {
            isActive: true,
          },
          include: {
            _count: {
              select: {
                courses: true,
                children: true,
              },
            },
          },
          orderBy: {
            order: "asc",
          },
        },
        parent: true,
        _count: {
          select: {
            courses: true,
            children: true,
          },
        },
      },
      orderBy: {
        order: "asc",
      },
    });

    // 只返回顶级分类（没有父分类的）
    return categories.filter((category) => !category.parentId);
  }

  /**
   * 获取单个课程分类
   */
  async getCourseCategory(
    id: string,
  ): Promise<CourseCategoryWithChildren | null> {
    return this.prisma.courseCategory.findUnique({
      where: { id },
      include: {
        children: {
          where: {
            isActive: true,
          },
          orderBy: {
            order: "asc",
          },
        },
        parent: true,
        _count: {
          select: {
            courses: true,
            children: true,
          },
        },
      },
    });
  }

  // ========== 私有工具方法 ==========

  /**
   * 生成URL友好的slug
   */
  private generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, "") // 移除特殊字符
      .replace(/\s+/g, "-") // 空格替换为连字符
      .replace(/-+/g, "-") // 多个连字符合并为一个
      .trim();
  }

  /**
   * 验证课程是否可以发布
   */
  private async validateCourseForPublish(id: string): Promise<void> {
    const course = await this.prisma.course.findUnique({
      where: { id },
      include: {
        chapters: {
          include: {
            lessons: true,
          },
        },
      },
    });

    if (!course) {
      throw new Error("课程不存在");
    }

    // 检查基本信息
    if (!course.title || !course.description || !course.cover) {
      throw new Error("课程基本信息不完整");
    }

    // 检查是否有章节
    if (course.chapters.length === 0) {
      throw new Error("课程至少需要包含一个章节");
    }

    // 检查是否有课时
    const hasLessons = course.chapters.some(
      (chapter) => chapter.lessons.length > 0,
    );
    if (!hasLessons) {
      throw new Error("课程至少需要包含一个课时");
    }

    // 检查讲师信息
    if (!course.instructorName) {
      throw new Error("课程需要指定讲师信息");
    }
  }

  /**
   * 批量更新课程状态
   */
  async batchUpdateCourseStatus(
    ids: string[],
    status: CourseStatus,
  ): Promise<number> {
    const result = await this.prisma.course.updateMany({
      where: {
        id: {
          in: ids,
        },
      },
      data: {
        status,
        updatedAt: new Date(),
      },
    });

    return result.count;
  }

  /**
   * 获取热门课程
   */
  async getPopularCourses(limit: number = 10): Promise<CourseWithBasicInfo[]> {
    return this.prisma.course.findMany({
      where: {
        status: CourseStatus.PUBLISHED,
      },
      include: {
        category: true,
        _count: {
          select: {
            chapters: true,
            enrollments: true,
          },
        },
      },
      orderBy: {
        enrollments: {
          _count: "desc",
        },
      },
      take: limit,
    });
  }

  /**
   * 按分类获取课程
   */
  async getCoursesByCategory(
    categoryId: string,
    options: GetCoursesOptions = {},
  ): Promise<PaginatedResult<CourseWithBasicInfo>> {
    return this.getCourses({
      ...options,
      categoryId,
    });
  }
}
