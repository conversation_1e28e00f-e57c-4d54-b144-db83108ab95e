import { PrismaClient, PostType, PostStatus, Post } from "../generated/prisma";

// Additional types needed for the service
export interface GetPostsOptions {
  page?: number;
  limit?: number;
  categoryId?: string;
  type?: PostType;
  status?: PostStatus;
  authorId?: string;
  search?: string;
  tags?: string[];
  isTop?: boolean;
  isRecommended?: boolean;
  sortBy?: "createdAt" | "publishedAt" | "viewCount" | "likes" | "comments";
  sortOrder?: "asc" | "desc";
  includeAuthor?: boolean;
  includeCategory?: boolean;
  includeTags?: boolean;
  includeStats?: boolean;
}

export interface CreatePostInput {
  title: string;
  content: string;
  summary?: string;
  cover?: string;
  type: PostType;
  status?: PostStatus;
  categoryId?: string;
  tags?: string[];
  isOriginal?: boolean;
  isTop?: boolean;
  isRecommended?: boolean;
  authorId: string;
}

export interface UpdatePostInput {
  title?: string;
  content?: string;
  summary?: string;
  cover?: string;
  type?: PostType;
  status?: PostStatus;
  categoryId?: string;
  tags?: string[];
  isOriginal?: boolean;
  isTop?: boolean;
  isRecommended?: boolean;
  publishedAt?: Date;
}

export interface PostWithRelations extends Post {
  author: {
    id: string;
    name: string;
    username: string;
    avatar?: string;
  };
  category?: {
    id: string;
    name: string;
    slug: string;
  };
  tags: Array<{
    tag: {
      id: string;
      name: string;
      slug: string;
    };
  }>;
  _count: {
    likes: number;
    comments: number;
    favorites: number;
  };
  comments?: Array<{
    id: string;
    content: string;
    user: {
      id: string;
      name: string;
      username: string;
      avatar?: string;
    };
    replies?: Array<{
      id: string;
      content: string;
      user: {
        id: string;
        name: string;
        username: string;
        avatar?: string;
      };
      createdAt: Date;
    }>;
    createdAt: Date;
  }>;
}

export interface PostsListResponse {
  posts: PostWithRelations[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PostStatsResponse {
  totalPosts: number;
  publishedPosts: number;
  draftPosts: number;
  totalViews: number;
  totalLikes: number;
  totalComments: number;
  popularTags: Array<{
    name: string;
    count: number;
  }>;
}

export class PostsService {
  constructor(private prisma: PrismaClient) {}

  /**
   * 获取帖子列表
   */
  async getPosts(options: GetPostsOptions = {}): Promise<PostsListResponse> {
    const {
      page = 1,
      limit = 10,
      categoryId,
      type,
      status,
      authorId,
      search,
      tags,
      isTop,
      isRecommended,
      sortBy = "createdAt",
      sortOrder = "desc",
      includeAuthor = true,
      includeCategory = true,
      includeTags = true,
      includeStats = true,
    } = options;

    const skip = (page - 1) * limit;

    // 构建查询条件
    const where: any = {};

    if (categoryId) {
      where.categoryId = categoryId;
    }

    if (type) {
      where.type = type;
    }

    if (status) {
      where.status = status;
    } else {
      // 默认只显示已发布的帖子
      where.status = PostStatus.PUBLISHED;
    }

    if (authorId) {
      where.authorId = authorId;
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { content: { contains: search, mode: "insensitive" } },
        { summary: { contains: search, mode: "insensitive" } },
      ];
    }

    if (tags && tags.length > 0) {
      where.tags = {
        some: {
          tag: {
            slug: { in: tags },
          },
        },
      };
    }

    if (typeof isTop === "boolean") {
      where.isTop = isTop;
    }

    if (typeof isRecommended === "boolean") {
      where.isRecommended = isRecommended;
    }

    // 构建排序
    let orderBy: any;
    switch (sortBy) {
      case "viewCount":
        orderBy = { viewCount: sortOrder };
        break;
      case "likes":
        orderBy = { likes: { _count: sortOrder } };
        break;
      case "comments":
        orderBy = { comments: { _count: sortOrder } };
        break;
      case "publishedAt":
        orderBy = { publishedAt: sortOrder };
        break;
      default:
        orderBy = { [sortBy]: sortOrder };
    }

    // 构建包含关系
    const include: any = {};

    if (includeAuthor) {
      include.author = {
        select: {
          id: true,
          name: true,
          username: true,
          avatar: true,
        },
      };
    }

    if (includeCategory) {
      include.category = {
        select: {
          id: true,
          name: true,
          slug: true,
        },
      };
    }

    if (includeTags) {
      include.tags = {
        include: {
          tag: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      };
    }

    if (includeStats) {
      include._count = {
        select: {
          likes: true,
          comments: true,
          favorites: true,
        },
      };
    }

    // 执行查询
    const [posts, total] = await Promise.all([
      this.prisma.post.findMany({
        where,
        include,
        orderBy: [
          { isTop: "desc" }, // 置顶帖子优先
          { isRecommended: "desc" }, // 推荐帖子其次
          orderBy,
        ],
        skip,
        take: limit,
      }),
      this.prisma.post.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      posts: posts as unknown as PostWithRelations[],
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * 根据ID获取帖子详情
   */
  async getPostById(
    id: string,
    includeComments = false,
  ): Promise<PostWithRelations | null> {
    const include: any = {
      author: {
        select: {
          id: true,
          name: true,
          username: true,
          avatar: true,
        },
      },
      category: {
        select: {
          id: true,
          name: true,
          slug: true,
        },
      },
      tags: {
        include: {
          tag: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      },
      _count: {
        select: {
          likes: true,
          comments: true,
          favorites: true,
        },
      },
    };

    if (includeComments) {
      include.comments = {
        where: { isDeleted: false },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              username: true,
              avatar: true,
            },
          },
          replies: {
            where: { isDeleted: false },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  username: true,
                  avatar: true,
                },
              },
            },
            orderBy: { createdAt: "asc" },
          },
        },
        orderBy: { createdAt: "desc" },
      };
    }

    const post = await this.prisma.post.findUnique({
      where: { id },
      include,
    });

    return post as PostWithRelations | null;
  }

  /**
   * 创建帖子
   */
  async createPost(input: CreatePostInput): Promise<PostWithRelations> {
    const {
      tags = [],
      status = PostStatus.DRAFT,
      isOriginal = true,
      isTop = false,
      isRecommended = false,
      ...postData
    } = input;

    // 处理标签
    const tagConnections = [];
    for (const tagName of tags) {
      // 创建或获取标签
      const tag = await this.prisma.tag.upsert({
        where: { name: tagName },
        update: { usageCount: { increment: 1 } },
        create: {
          name: tagName,
          slug: tagName.toLowerCase().replace(/\s+/g, "-"),
          usageCount: 1,
        },
      });
      tagConnections.push({ tagId: tag.id });
    }

    // 创建帖子
    const post = await this.prisma.post.create({
      data: {
        ...postData,
        status,
        isOriginal,
        isTop,
        isRecommended,
        publishedAt: status === PostStatus.PUBLISHED ? new Date() : null,
        tags: {
          create: tagConnections,
        },
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            username: true,
            avatar: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        tags: {
          include: {
            tag: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
        },
        _count: {
          select: {
            likes: true,
            comments: true,
            favorites: true,
          },
        },
      },
    });

    return post as PostWithRelations;
  }

  /**
   * 更新帖子
   */
  async updatePost(
    id: string,
    input: UpdatePostInput,
  ): Promise<PostWithRelations | null> {
    const { tags, ...updateData } = input;

    // 检查帖子是否存在
    const existingPost = await this.prisma.post.findUnique({
      where: { id },
      include: { tags: true },
    });

    if (!existingPost) {
      return null;
    }

    // 处理标签更新
    let tagOperations = {};
    if (tags !== undefined) {
      // 删除现有标签关联
      await this.prisma.postTag.deleteMany({
        where: { postId: id },
      });

      // 减少旧标签使用计数
      for (const oldTag of existingPost.tags) {
        await this.prisma.tag.update({
          where: { id: oldTag.tagId },
          data: { usageCount: { decrement: 1 } },
        });
      }

      // 创建新标签关联
      const tagConnections = [];
      for (const tagName of tags) {
        const tag = await this.prisma.tag.upsert({
          where: { name: tagName },
          update: { usageCount: { increment: 1 } },
          create: {
            name: tagName,
            slug: tagName.toLowerCase().replace(/\s+/g, "-"),
            usageCount: 1,
          },
        });
        tagConnections.push({ tagId: tag.id });
      }

      tagOperations = {
        tags: {
          create: tagConnections,
        },
      };
    }

    // 更新发布时间
    if (input.status === PostStatus.PUBLISHED && !existingPost.publishedAt) {
      updateData.publishedAt = new Date();
    }

    // 更新帖子
    const post = await this.prisma.post.update({
      where: { id },
      data: {
        ...updateData,
        ...tagOperations,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            username: true,
            avatar: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        tags: {
          include: {
            tag: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
        },
        _count: {
          select: {
            likes: true,
            comments: true,
            favorites: true,
          },
        },
      },
    });

    return post as PostWithRelations;
  }

  /**
   * 删除帖子
   */
  async deletePost(id: string): Promise<boolean> {
    try {
      // 先删除相关数据
      await this.prisma.$transaction([
        // 删除标签关联
        this.prisma.postTag.deleteMany({ where: { postId: id } }),
        // 删除评论
        this.prisma.comment.deleteMany({ where: { postId: id } }),
        // 删除点赞
        this.prisma.like.deleteMany({ where: { postId: id } }),
        // 删除收藏
        this.prisma.favorite.deleteMany({ where: { postId: id } }),
        // 删除帖子
        this.prisma.post.delete({ where: { id } }),
      ]);

      return true;
    } catch (error) {
      console.error("删除帖子失败:", error);
      return false;
    }
  }

  /**
   * 增加浏览量
   */
  async incrementViewCount(id: string): Promise<void> {
    await this.prisma.post.update({
      where: { id },
      data: { viewCount: { increment: 1 } },
    });
  }

  /**
   * 点赞/取消点赞
   */
  async toggleLike(postId: string, userId: string): Promise<boolean> {
    const existingLike = await this.prisma.like.findUnique({
      where: {
        userId_postId: {
          userId,
          postId,
        },
      },
    });

    if (existingLike) {
      // 取消点赞
      await this.prisma.like.delete({
        where: {
          userId_postId: {
            userId,
            postId,
          },
        },
      });
      return false;
    } else {
      // 点赞
      await this.prisma.like.create({
        data: {
          userId,
          postId,
        },
      });
      return true;
    }
  }

  /**
   * 获取帖子统计数据
   */
  async getPostStats(): Promise<PostStatsResponse> {
    const [
      totalPosts,
      publishedPosts,
      draftPosts,
      totalViews,
      totalLikes,
      totalComments,
      popularTags,
    ] = await Promise.all([
      this.prisma.post.count(),
      this.prisma.post.count({ where: { status: PostStatus.PUBLISHED } }),
      this.prisma.post.count({ where: { status: PostStatus.DRAFT } }),
      this.prisma.post.aggregate({ _sum: { viewCount: true } }),
      this.prisma.like.count(),
      this.prisma.comment.count(),
      this.prisma.tag.findMany({
        orderBy: { usageCount: "desc" },
        take: 10,
        select: { name: true, usageCount: true },
      }),
    ]);

    return {
      totalPosts,
      publishedPosts,
      draftPosts,
      totalViews: totalViews._sum.viewCount || 0,
      totalLikes,
      totalComments,
      popularTags: popularTags.map((tag) => ({
        name: tag.name,
        count: tag.usageCount,
      })),
    };
  }

  /**
   * 收藏/取消收藏
   */
  async toggleFavorite(postId: string, userId: string): Promise<boolean> {
    const existingFavorite = await this.prisma.favorite.findUnique({
      where: {
        userId_postId: {
          userId,
          postId,
        },
      },
    });

    if (existingFavorite) {
      // 取消收藏
      await this.prisma.favorite.delete({
        where: {
          userId_postId: {
            userId,
            postId,
          },
        },
      });
      return false;
    } else {
      // 收藏
      await this.prisma.favorite.create({
        data: {
          userId,
          postId,
        },
      });
      return true;
    }
  }
}
