import { PrismaClient, CourseStatus } from "../generated/prisma";
import {
  DashboardStats,
  EnrollmentTrend,
  CourseWithEnrollmentStats,
  DashboardResponse,
  EnrollmentDetail,
} from "../types/course.types";

export class DashboardService {
  constructor(private prisma: PrismaClient) {}

  /**
   * 获取 Dashboard 总体统计数据
   */
  async getDashboardStats(): Promise<DashboardStats> {
    // 并行获取各项统计数据
    const [totalEnrollments, totalRevenue, totalCourses, completionData] =
      await Promise.all([
        // 总报名数
        this.prisma.courseEnrollment.count(),

        // 总收入
        this.prisma.courseEnrollment.aggregate({
          _sum: {
            paidAmount: true,
          },
          where: {
            isPaid: true,
          },
        }),

        // 课程总数
        this.prisma.course.count({
          where: {
            status: {
              not: CourseStatus.DRAFT,
            },
          },
        }),

        // 完成率数据
        this.prisma.courseEnrollment.aggregate({
          _avg: {
            progress: true,
          },
        }),
      ]);

    return {
      totalEnrollments,
      totalRevenue: Number(totalRevenue._sum.paidAmount) || 0,
      totalCourses,
      averageCompletionRate:
        Math.round((completionData._avg.progress || 0) * 100) / 100,
    };
  }

  /**
   * 获取报名趋势数据
   */
  async getEnrollmentTrends(days: number = 30): Promise<EnrollmentTrend[]> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const enrollments = await this.prisma.courseEnrollment.findMany({
      where: {
        enrolledAt: {
          gte: startDate,
        },
      },
      select: {
        enrolledAt: true,
        paidAmount: true,
        isPaid: true,
      },
      orderBy: {
        enrolledAt: "asc",
      },
    });

    // 按日期分组统计
    const trendsMap = new Map<
      string,
      { enrollments: number; revenue: number }
    >();

    // 初始化所有日期
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() - (days - 1 - i));
      const dateStr = date.toISOString().split("T")[0];
      trendsMap.set(dateStr, { enrollments: 0, revenue: 0 });
    }

    // 统计实际数据
    enrollments.forEach((enrollment) => {
      const dateStr = enrollment.enrolledAt.toISOString().split("T")[0];
      const current = trendsMap.get(dateStr) || { enrollments: 0, revenue: 0 };
      current.enrollments += 1;
      if (enrollment.isPaid && enrollment.paidAmount) {
        current.revenue += Number(enrollment.paidAmount);
      }
      trendsMap.set(dateStr, current);
    });

    // 转换为数组并排序
    return Array.from(trendsMap)
      .map(([date, data]) => ({ date, ...data }))
      .sort((a, b) => a.date.localeCompare(b.date));
  }

  /**
   * 获取热门课程列表（按报名数排序）
   */
  async getTopCourses(
    limit: number = 10,
  ): Promise<CourseWithEnrollmentStats[]> {
    const courses = await this.prisma.course.findMany({
      where: {
        status: {
          not: CourseStatus.DRAFT,
        },
      },
      include: {
        category: true,
        enrollments: {
          select: {
            isPaid: true,
            paidAmount: true,
            completedAt: true,
            enrolledAt: true,
          },
        },
      },
      orderBy: {
        enrollCount: "desc",
      },
      take: limit,
    });

    // 计算统计数据
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    return courses.map((course) => {
      const enrollments = course.enrollments;
      const enrollmentCount = enrollments.length;
      const completedCount = enrollments.filter((e) => e.completedAt).length;
      const completionRate =
        enrollmentCount > 0 ? (completedCount / enrollmentCount) * 100 : 0;
      const revenue = enrollments.reduce((sum, e) => {
        return sum + (e.isPaid && e.paidAmount ? Number(e.paidAmount) : 0);
      }, 0);
      const recentEnrollments = enrollments.filter(
        (e) => e.enrolledAt >= sevenDaysAgo,
      ).length;

      return {
        id: course.id,
        title: course.title,
        subtitle: course.subtitle,
        description: course.description,
        cover: course.cover,
        previewVideo: course.previewVideo,
        categoryId: course.categoryId,
        category: course.category,
        tags: course.tags,
        level: course.level,
        duration: course.duration,
        lessonsCount: course.lessonsCount,
        instructorName: course.instructorName,
        instructorTitle: course.instructorTitle,
        instructorAvatar: course.instructorAvatar,
        instructorBio: course.instructorBio,
        price: Number(course.price),
        originalPrice: course.originalPrice
          ? Number(course.originalPrice)
          : null,
        isFree: course.isFree,
        requireLogin: course.requireLogin,
        status: course.status,
        viewCount: course.viewCount,
        enrollCount: course.enrollCount,
        rating: course.rating,
        metaTitle: course.metaTitle,
        metaDescription: course.metaDescription,
        metaKeywords: course.metaKeywords,
        publishedAt: course.publishedAt,
        createdAt: course.createdAt,
        updatedAt: course.updatedAt,
        createdById: course.createdById,
        enrollmentCount,
        completionRate: Math.round(completionRate * 100) / 100,
        revenue,
        recentEnrollments,
      };
    }) as any;
  }

  /**
   * 获取完整的 Dashboard 数据
   */
  async getDashboardData(
    trendDays: number = 30,
    topCoursesLimit: number = 10,
  ): Promise<DashboardResponse> {
    const [stats, trends, topCourses] = await Promise.all([
      this.getDashboardStats(),
      this.getEnrollmentTrends(trendDays),
      this.getTopCourses(topCoursesLimit),
    ]);

    return {
      stats,
      trends,
      topCourses,
    };
  }

  /**
   * 获取报名详情
   */
  async getEnrollmentDetail(
    enrollmentId: string,
  ): Promise<EnrollmentDetail | null> {
    const enrollment = await this.prisma.courseEnrollment.findUnique({
      where: { id: enrollmentId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
            username: true,
            phone: true,
          },
        },
        course: {
          select: {
            id: true,
            title: true,
            cover: true,
            instructorName: true,
            price: true,
            chapters: {
              include: {
                lessons: {
                  select: {
                    id: true,
                    title: true,
                    videoDuration: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!enrollment) {
      return null;
    }

    // 计算学习进度详情
    const lessons = enrollment.course.chapters.flatMap(
      (chapter) => chapter.lessons,
    );
    const totalLessons = lessons.length;
    const totalDuration = lessons.reduce(
      (sum, lesson) => sum + lesson.videoDuration,
      0,
    );

    // 获取用户的课时进度
    const lessonProgress = await this.prisma.lessonProgress.findMany({
      where: {
        userId: enrollment.userId,
        lesson: {
          chapter: {
            courseId: enrollment.courseId,
          },
        },
      },
      include: {
        lesson: {
          select: {
            id: true,
            title: true,
          },
        },
      },
      orderBy: {
        updatedAt: "desc",
      },
    });

    const completedLessons = lessonProgress.filter((p) => p.isCompleted).length;
    const watchedDuration = lessonProgress.reduce(
      (sum, p) => sum + p.watchedDuration,
      0,
    );
    const lastProgress = lessonProgress[0];

    return {
      id: enrollment.id,
      courseId: enrollment.courseId,
      userId: enrollment.userId,
      enrolledAt: enrollment.enrolledAt,
      completedAt: enrollment.completedAt,
      progress: enrollment.progress,
      lastAccessAt: enrollment.lastAccessAt,
      isPaid: enrollment.isPaid,
      paidAmount: enrollment.paidAmount ? Number(enrollment.paidAmount) : null,
      paidAt: enrollment.paidAt,
      user: enrollment.user,
      course: {
        id: enrollment.course.id,
        title: enrollment.course.title,
        cover: enrollment.course.cover,
        instructorName: enrollment.course.instructorName,
        price: Number(enrollment.course.price),
      },
      progressDetails: {
        totalLessons,
        completedLessons,
        totalDuration,
        watchedDuration,
        lastLessonId: lastProgress?.lesson.id || null,
        lastLessonTitle: lastProgress?.lesson.title || null,
      },
    };
  }

  /**
   * 获取课程报名排行榜
   */
  async getCourseEnrollmentRanking(
    limit: number = 20,
    dateRange?: { from: Date; to: Date },
  ): Promise<
    Array<{
      courseId: string;
      courseTitle: string;
      instructorName: string;
      cover: string;
      categoryName: string;
      enrollmentCount: number;
      revenue: number;
      completionRate: number;
    }>
  > {
    const whereClause: any = {
      status: {
        not: CourseStatus.DRAFT,
      },
    };

    // 如果有日期范围，添加到查询条件
    let enrollmentWhere: any = {};
    if (dateRange) {
      enrollmentWhere.enrolledAt = {
        gte: dateRange.from,
        lte: dateRange.to,
      };
    }

    const courses = await this.prisma.course.findMany({
      where: whereClause,
      include: {
        category: {
          select: {
            name: true,
          },
        },
        enrollments: {
          where: enrollmentWhere,
          select: {
            isPaid: true,
            paidAmount: true,
            completedAt: true,
          },
        },
      },
    });

    // 计算统计并排序
    const courseStats = courses
      .map((course) => {
        const enrollments = course.enrollments;
        const enrollmentCount = enrollments.length;
        const completedCount = enrollments.filter((e) => e.completedAt).length;
        const completionRate =
          enrollmentCount > 0 ? (completedCount / enrollmentCount) * 100 : 0;
        const revenue = enrollments.reduce((sum, e) => {
          return sum + (e.isPaid && e.paidAmount ? Number(e.paidAmount) : 0);
        }, 0);

        return {
          courseId: course.id,
          courseTitle: course.title,
          instructorName: course.instructorName,
          cover: course.cover,
          categoryName: course.category?.name || "未分类",
          enrollmentCount,
          revenue,
          completionRate: Math.round(completionRate * 100) / 100,
        };
      })
      .sort((a, b) => b.enrollmentCount - a.enrollmentCount)
      .slice(0, limit);

    return courseStats;
  }

  /**
   * 获取收入统计
   */
  async getRevenueStats(dateRange?: { from: Date; to: Date }): Promise<{
    totalRevenue: number;
    paidEnrollments: number;
    averageOrderValue: number;
    dailyRevenue: Array<{ date: string; revenue: number; orders: number }>;
  }> {
    let whereClause: any = {
      isPaid: true,
    };

    if (dateRange) {
      whereClause.paidAt = {
        gte: dateRange.from,
        lte: dateRange.to,
      };
    }

    const [revenueData, enrollments] = await Promise.all([
      this.prisma.courseEnrollment.aggregate({
        _sum: {
          paidAmount: true,
        },
        _count: {
          id: true,
        },
        where: whereClause,
      }),
      this.prisma.courseEnrollment.findMany({
        where: whereClause,
        select: {
          paidAt: true,
          paidAmount: true,
        },
        orderBy: {
          paidAt: "asc",
        },
      }),
    ]);

    const totalRevenue = Number(revenueData._sum.paidAmount) || 0;
    const paidEnrollments = revenueData._count.id;
    const averageOrderValue =
      paidEnrollments > 0 ? totalRevenue / paidEnrollments : 0;

    // 按日期分组计算每日收入
    const dailyRevenueMap = new Map<
      string,
      { revenue: number; orders: number }
    >();

    enrollments.forEach((enrollment) => {
      if (enrollment.paidAt && enrollment.paidAmount) {
        const dateStr = enrollment.paidAt.toISOString().split("T")[0];
        const current = dailyRevenueMap.get(dateStr) || {
          revenue: 0,
          orders: 0,
        };
        current.revenue += Number(enrollment.paidAmount);
        current.orders += 1;
        dailyRevenueMap.set(dateStr, current);
      }
    });

    const dailyRevenue = Array.from(dailyRevenueMap)
      .map(([date, data]) => ({ date, ...data }))
      .sort((a, b) => a.date.localeCompare(b.date));

    return {
      totalRevenue,
      paidEnrollments,
      averageOrderValue: Math.round(averageOrderValue * 100) / 100,
      dailyRevenue,
    };
  }
}
