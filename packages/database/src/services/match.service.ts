import {
  PrismaClient,
  MatchRequestType,
  RequirementCategory,
  MatchMode,
  MatchRequestStatus,
  Prisma,
  PlannerMatchProfile,
} from "../generated/prisma";

export class MatchService {
  constructor(private prisma: PrismaClient) {}

  /**
   * 检查和创建规划师匹配配置（延迟初始化）
   */
  async ensurePlannerMatchProfile(
    tenantUserId: string,
    plannerId: string,
  ): Promise<{
    success: boolean;
    profile?: PlannerMatchProfile;
    needsSetup?: boolean;
    message?: string;
  }> {
    try {
      // 1. 检查是否已有匹配配置
      const existingProfile = await this.prisma.plannerMatchProfile.findUnique({
        where: { plannerId },
      });

      if (existingProfile) {
        return {
          success: true,
          profile: existingProfile,
          needsSetup: false,
        };
      }

      // 2. 获取规划师信息
      const planner = await this.prisma.planner.findUnique({
        where: { tenantUserId },
        include: {
          tenantUser: {
            include: {
              user: true,
              tenant: true,
            },
          },
        },
      });

      if (!planner) {
        return {
          success: false,
          message: "未找到规划师信息",
        };
      }

      // 3. 返回需要设置的标识
      return {
        success: true,
        needsSetup: true,
        message: "需要完成匹配配置才能使用匹配功能",
      };
    } catch (error) {
      console.error("检查规划师匹配配置失败:", error);
      return {
        success: false,
        message: "检查配置失败",
      };
    }
  }

  /**
   * 创建默认的规划师匹配配置
   */
  async createDefaultPlannerMatchProfile(
    plannerId: string,
    setupData?: Partial<PlannerMatchProfile>,
  ): Promise<{
    success: boolean;
    data?: PlannerMatchProfile;
    message?: string;
  }> {
    try {
      // 1. 获取规划师信息
      const planner = await this.prisma.planner.findUnique({
        where: { id: plannerId },
        include: {
          tenantUser: {
            include: {
              user: true,
              tenant: true,
            },
          },
        },
      });

      if (!planner) {
        return {
          success: false,
          message: "未找到规划师信息",
        };
      }

      // 2. 创建默认配置
      const defaultProfile = await this.prisma.plannerMatchProfile.create({
        data: {
          plannerId: plannerId,
          isAcceptingMatch: false, // 默认不接受匹配，需要用户手动开启
          maxConcurrent: 10, // 默认最多同时服务10个学生
          preferredGrades: ["高一", "高二", "高三"], // 默认所有高中年级
          preferredSubjects: [], // 空数组，让用户自己设置
          preferredLocations: [], // 空数组，让用户自己设置
          expertise: {
            specialties: planner.specialties || [],
            certifications: [],
            languages: ["中文"],
          } as any,
          availability: {
            weekdays: ["周一", "周二", "周三", "周四", "周五"],
            timeSlots: ["14:00-17:00", "19:00-21:00"],
            timezone: "Asia/Shanghai",
          } as any,
          responseTime: 24, // 默认24小时内响应
          basePrice: 0, // 默认价格为0，让用户自己设置
          priceRange: {
            consultationFee: { min: 0, max: 1000 },
            planningFee: { min: 0, max: 10000 },
            currency: "CNY",
          } as any,
          matchScore: planner.rating || 4.0,
          successRate: planner.successRate || 0.8,
          ...setupData, // 覆盖用户提供的配置
        },
      });

      return {
        success: true,
        data: defaultProfile,
        message: "匹配配置创建成功",
      };
    } catch (error) {
      console.error("创建规划师匹配配置失败:", error);
      return {
        success: false,
        message: "创建配置失败",
      };
    }
  }

  /**
   * 检查用户是否可以创建匹配请求
   */
  async checkMatchRequestEligibility(
    userId: string,
    tenantId: string,
  ): Promise<{
    success: boolean;
    canCreate: boolean;
    message?: string;
    setupRequired?: string; // 'student' | 'planner' | 'tenant'
  }> {
    try {
      // 1. 检查用户在租户中的角色
      const tenantUser = await this.prisma.tenantUser.findUnique({
        where: {
          userId_tenantId: {
            userId,
            tenantId,
          },
        },
        include: {
          user: true,
          tenant: true,
          planner: true,
        },
      });

      if (!tenantUser) {
        return {
          success: false,
          canCreate: false,
          message: "用户不属于当前租户",
        };
      }

      // 2. 检查租户状态
      if (tenantUser.tenant.status !== "ACTIVE") {
        return {
          success: false,
          canCreate: false,
          message: "租户状态异常，无法创建匹配请求",
        };
      }

      // 3. 检查用户角色和对应的配置
      const role = tenantUser.role;

      if (role === "STUDENT" || role === "PARENT" || role === "TENANT_ADMIN") {
        // 学生/家长可以直接创建匹配请求
        return {
          success: true,
          canCreate: true,
        };
      } else if (role === "PLANNER") {
        if (!tenantUser.planner?.id) {
          return {
            success: true,
            canCreate: false,
            message: "规划师需要检查是否有匹配配置",
          };
        }
        // 规划师需要检查是否有匹配配置
        const plannerProfile = await this.ensurePlannerMatchProfile(
          tenantUser.id,
          tenantUser.planner.id,
        );
        if (plannerProfile.needsSetup) {
          return {
            success: true,
            canCreate: false,
            message: "需要先完成规划师匹配配置",
            setupRequired: "planner",
          };
        }
        return {
          success: true,
          canCreate: true,
        };
      } else {
        return {
          success: false,
          canCreate: false,
          message: "当前角色无法创建匹配请求",
        };
      }
    } catch (error) {
      console.error("检查匹配请求资格失败:", error);
      return {
        success: false,
        canCreate: false,
        message: "检查资格失败",
      };
    }
  }
  /**
   * 创建匹配请求
   */
  async createMatchRequest(data: {
    requesterId: string;
    requesterType: MatchRequestType;
    tenantId?: string;
    requirements: any;
    category?: RequirementCategory;
    urgency?: number;
    mode?: MatchMode;
    expiredAt?: Date;
    metadata?: any;
  }) {
    return await this.prisma.matchRequest.create({
      data: {
        requesterId: data.requesterId,
        requesterType: data.requesterType,
        tenantId: data.tenantId,
        requirements: data.requirements,
        category: data.category,
        urgency: data.urgency || 0,
        mode: data.mode || MatchMode.SMART_MATCH,
        expiredAt: data.expiredAt,
        metadata: data.metadata,
        status: MatchRequestStatus.PENDING,
      },
      include: {
        tenant: true,
        matchedPlanner: {
          include: {
            tenantUser: {
              include: {
                user: true,
              },
            },
          },
        },
        responses: {
          include: {
            planner: {
              include: {
                tenantUser: {
                  include: {
                    user: true,
                  },
                },
              },
            },
          },
        },
      },
    });
  }

  /**
   * 获取匹配请求列表
   */
  async getMatchRequests(params: {
    tenantId?: string;
    requesterId?: string;
    status?: MatchRequestStatus;
    mode?: MatchMode;
    category?: RequirementCategory;
    page?: number;
    pageSize?: number;
  }) {
    const {
      tenantId,
      requesterId,
      status,
      mode,
      category,
      page = 1,
      pageSize = 10,
    } = params;

    const where: Prisma.MatchRequestWhereInput = {};

    if (tenantId) where.tenantId = tenantId;
    if (requesterId) where.requesterId = requesterId;
    if (status) where.status = status;
    if (mode) where.mode = mode;
    if (category) where.category = category;

    // 默认不返回已取消的匹配请求，除非明确指定状态为 CANCELLED
    if (!status) {
      where.status = {
        not: MatchRequestStatus.CANCELLED,
      };
    }

    const [total, items] = await Promise.all([
      this.prisma.matchRequest.count({ where }),
      this.prisma.matchRequest.findMany({
        where,
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: {
          createdAt: "desc",
        },
        include: {
          tenant: true,
          matchedPlanner: {
            include: {
              tenantUser: {
                include: {
                  user: true,
                },
              },
            },
          },
          responses: {
            include: {
              planner: {
                include: {
                  tenantUser: {
                    include: {
                      user: true,
                    },
                  },
                },
              },
            },
          },
          evaluations: true,
        },
      }),
    ]);

    return {
      items,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  /**
   * 获取匹配大厅列表（公开抢单池）
   */
  async getMatchPool(params: {
    tenantUserId: string;
    page?: number;
    pageSize?: number;
  }) {
    const { tenantUserId, page = 1, pageSize = 10 } = params;

    const tenantUser = await this.prisma.tenantUser.findUnique({
      where: { id: tenantUserId },
      include: {
        planner: true,
      },
    });

    if (!tenantUser) throw new Error("规划师不存在或未关联租户");

    const where: Prisma.MatchRequestWhereInput = {
      status: MatchRequestStatus.PENDING,
      mode: MatchMode.PUBLIC_POOL,
      OR: [{ expiredAt: null }, { expiredAt: { gt: new Date() } }],
    };

    const plannerProfile = await this.prisma.plannerMatchProfile.findUnique({
      where: { plannerId: tenantUser.planner?.id },
    });

    if (!plannerProfile) throw new Error("规划师匹配配置不存在");

    // 过滤已经响应过的请求
    const respondedRequestIds = await this.prisma.matchResponse.findMany({
      where: { plannerId: tenantUser.planner?.id },
      select: { matchRequestId: true },
    });

    if (respondedRequestIds.length > 0) {
      where.id = {
        notIn: respondedRequestIds.map((r) => r.matchRequestId),
      };
    }

    const [total, items] = await Promise.all([
      this.prisma.matchRequest.count({ where }),
      this.prisma.matchRequest.findMany({
        where,
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: [{ urgency: "desc" }, { createdAt: "desc" }],
        include: {
          tenant: true,
          responses: {
            select: {
              id: true,
              plannerId: true,
            },
          },
        },
      }),
    ]);

    // 计算每个请求的匹配度
    const itemsWithMatchScore = items.map((item) => {
      const requirements = item.requirements as any;
      let matchScore = 0.5; // 基础匹配度

      // 根据规划师偏好计算匹配度
      if (plannerProfile.preferredGrades?.length && requirements.grade) {
        if (plannerProfile.preferredGrades.includes(requirements.grade)) {
          matchScore += 0.2;
        }
      }

      if (
        plannerProfile.preferredSubjects?.length &&
        requirements.subjects?.length
      ) {
        const matchedSubjects = requirements.subjects.filter((s: string) =>
          plannerProfile.preferredSubjects?.includes(s),
        );
        matchScore +=
          (matchedSubjects.length / requirements.subjects.length) * 0.2;
      }

      if (plannerProfile.preferredLocations?.length && requirements.location) {
        if (plannerProfile.preferredLocations.includes(requirements.location)) {
          matchScore += 0.1;
        }
      }

      return {
        ...item,
        matchScore,
        responseCount: item.responses.length,
      };
    });

    // 根据匹配度排序
    itemsWithMatchScore.sort((a, b) => {
      if (a.urgency !== b.urgency) {
        return b.urgency - a.urgency;
      }
      return b.matchScore - a.matchScore;
    });

    return {
      items: itemsWithMatchScore,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  /**
   * 规划师响应匹配请求
   */
  async respondToMatch(data: {
    matchRequestId: string;
    plannerId: string;
    message?: string;
    availability?: any;
    quote?: number;
  }) {
    // 检查请求状态
    const matchRequest = await this.prisma.matchRequest.findUnique({
      where: { id: data.matchRequestId },
    });

    if (!matchRequest) {
      throw new Error("匹配请求不存在");
    }

    if (matchRequest.status !== MatchRequestStatus.PENDING) {
      throw new Error("该请求已不再接受响应");
    }

    // 检查是否已经响应过
    const existingResponse = await this.prisma.matchResponse.findUnique({
      where: {
        matchRequestId_plannerId: {
          matchRequestId: data.matchRequestId,
          plannerId: data.plannerId,
        },
      },
    });

    if (existingResponse) {
      throw new Error("您已经响应过该请求");
    }

    // 创建响应
    const response = await this.prisma.matchResponse.create({
      data: {
        matchRequestId: data.matchRequestId,
        plannerId: data.plannerId,
        message: data.message,
        availability: data.availability,
        quote: data.quote,
      },
      include: {
        planner: {
          include: {
            tenantUser: {
              include: {
                user: true,
              },
            },
          },
        },
        matchRequest: true,
      },
    });

    // 记录历史
    await this.prisma.matchHistory.create({
      data: {
        matchRequestId: data.matchRequestId,
        plannerId: data.plannerId,
        action: "RESPONDED",
        details: {
          message: data.message,
          quote: data.quote,
        },
      },
    });

    // 如果是智能匹配模式，自动更新状态
    if (matchRequest.mode === MatchMode.SMART_MATCH) {
      await this.prisma.matchRequest.update({
        where: { id: data.matchRequestId },
        data: { status: MatchRequestStatus.MATCHING },
      });
    }

    return response;
  }

  /**
   * 接受/拒绝匹配响应
   */
  async handleMatchResponse(data: {
    responseId: string;
    requesterId: string;
    action: "accept" | "reject";
  }) {
    const response = await this.prisma.matchResponse.findUnique({
      where: { id: data.responseId },
      include: {
        matchRequest: true,
        planner: true,
      },
    });

    if (!response) {
      throw new Error("响应不存在");
    }

    if (response.matchRequest.requesterId !== data.requesterId) {
      throw new Error("无权操作此响应");
    }

    if (
      response.matchRequest.status !== MatchRequestStatus.PENDING &&
      response.matchRequest.status !== MatchRequestStatus.MATCHING
    ) {
      throw new Error("该请求已完成匹配");
    }

    if (data.action === "accept") {
      // 接受匹配
      await this.prisma.$transaction(async (tx) => {
        // 更新响应状态
        await tx.matchResponse.update({
          where: { id: data.responseId },
          data: { isAccepted: true },
        });

        // 更新匹配请求
        await tx.matchRequest.update({
          where: { id: response.matchRequestId },
          data: {
            status: MatchRequestStatus.MATCHED,
            matchedPlannerId: response.plannerId,
            matchedAt: new Date(),
          },
        });

        // 拒绝其他响应
        await tx.matchResponse.updateMany({
          where: {
            matchRequestId: response.matchRequestId,
            id: { not: data.responseId },
          },
          data: { isRejected: true },
        });

        // 记录历史
        await tx.matchHistory.create({
          data: {
            matchRequestId: response.matchRequestId,
            plannerId: response.plannerId,
            action: "MATCHED",
            details: { responseId: data.responseId },
          },
        });
      });
    } else {
      // 拒绝响应
      await this.prisma.matchResponse.update({
        where: { id: data.responseId },
        data: { isRejected: true },
      });

      await this.prisma.matchHistory.create({
        data: {
          matchRequestId: response.matchRequestId,
          plannerId: response.plannerId,
          action: "REJECTED",
          details: { responseId: data.responseId },
        },
      });
    }

    return response;
  }

  /**
   * 确认匹配
   */
  async confirmMatch(matchRequestId: string, requesterId: string) {
    const matchRequest = await this.prisma.matchRequest.findUnique({
      where: { id: matchRequestId },
      include: {
        matchedPlanner: true,
      },
    });

    if (!matchRequest) {
      throw new Error("匹配请求不存在");
    }

    if (matchRequest.requesterId !== requesterId) {
      throw new Error("无权操作此请求");
    }

    if (matchRequest.status !== MatchRequestStatus.MATCHED) {
      throw new Error("请求状态不正确");
    }

    const updated = await this.prisma.matchRequest.update({
      where: { id: matchRequestId },
      data: {
        status: MatchRequestStatus.CONFIRMED,
        confirmedAt: new Date(),
      },
      include: {
        matchedPlanner: {
          include: {
            tenantUser: {
              include: {
                user: true,
              },
            },
          },
        },
      },
    });

    // 记录历史
    await this.prisma.matchHistory.create({
      data: {
        matchRequestId: matchRequestId,
        plannerId: matchRequest.matchedPlannerId!,
        action: "CONFIRMED",
      },
    });

    return updated;
  }

  /**
   * 取消匹配请求
   */
  async cancelMatchRequest(matchRequestId: string, requesterId: string) {
    const matchRequest = await this.prisma.matchRequest.findUnique({
      where: { id: matchRequestId },
    });

    if (!matchRequest) {
      throw new Error("匹配请求不存在");
    }

    if (matchRequest.requesterId !== requesterId) {
      throw new Error("无权操作此请求");
    }

    if (matchRequest.status === MatchRequestStatus.CONFIRMED) {
      throw new Error("已确认的匹配无法取消");
    }

    return await this.prisma.matchRequest.update({
      where: { id: matchRequestId },
      data: {
        status: MatchRequestStatus.CANCELLED,
      },
    });
  }

  /**
   * 评价匹配
   */
  async evaluateMatch(data: {
    matchRequestId: string;
    evaluatorId: string;
    evaluatorType: string;
    rating: number;
    comment?: string;
    tags?: string[];
  }) {
    // 验证评分范围
    if (data.rating < 1 || data.rating > 5) {
      throw new Error("评分必须在1-5之间");
    }

    // 检查是否已评价
    const existing = await this.prisma.matchEvaluation.findFirst({
      where: {
        matchRequestId: data.matchRequestId,
        evaluatorId: data.evaluatorId,
      },
    });

    if (existing) {
      throw new Error("您已评价过此匹配");
    }

    const evaluation = await this.prisma.matchEvaluation.create({
      data: {
        matchRequestId: data.matchRequestId,
        evaluatorId: data.evaluatorId,
        evaluatorType: data.evaluatorType,
        rating: data.rating,
        comment: data.comment,
        tags: data.tags || [],
      },
    });

    // 更新规划师的成功率和评分
    const matchRequest = await this.prisma.matchRequest.findUnique({
      where: { id: data.matchRequestId },
      include: {
        matchedPlanner: true,
      },
    });

    if (matchRequest?.matchedPlanner) {
      // 计算新的评分
      const allEvaluations = await this.prisma.matchEvaluation.findMany({
        where: {
          matchRequest: {
            matchedPlannerId: matchRequest.matchedPlannerId,
          },
        },
      });

      const avgRating =
        allEvaluations.reduce((acc, _eval) => acc + _eval.rating, 0) /
        allEvaluations.length;

      await this.prisma.planner.update({
        where: { id: matchRequest.matchedPlannerId! },
        data: {
          rating: avgRating,
        },
      });

      // 更新匹配配置中的成功率
      const successfulMatches = await this.prisma.matchRequest.count({
        where: {
          matchedPlannerId: matchRequest.matchedPlannerId,
          status: MatchRequestStatus.CONFIRMED,
        },
      });

      const totalMatches = await this.prisma.matchRequest.count({
        where: {
          matchedPlannerId: matchRequest.matchedPlannerId,
          status: {
            in: [MatchRequestStatus.MATCHED, MatchRequestStatus.CONFIRMED],
          },
        },
      });

      if (totalMatches > 0) {
        await this.prisma.plannerMatchProfile.update({
          where: { plannerId: matchRequest.matchedPlannerId! },
          data: {
            successRate: successfulMatches / totalMatches,
          },
        });
      }
    }

    return evaluation;
  }

  /**
   * 获取规划师匹配配置
   */
  async getPlannerMatchProfile(plannerId: string) {
    let profile = await this.prisma.plannerMatchProfile.findUnique({
      where: { plannerId },
      include: {
        planner: {
          include: {
            tenantUser: {
              include: {
                user: true,
              },
            },
          },
        },
      },
    });

    // 如果不存在，先验证规划师是否存在，然后创建默认配置
    if (!profile) {
      // 验证规划师是否存在
      const planner = await this.prisma.planner.findUnique({
        where: { id: plannerId },
        include: {
          tenantUser: {
            include: {
              user: true,
            },
          },
        },
      });

      if (!planner) {
        throw new Error(`规划师不存在: ${plannerId}`);
      }

      profile = await this.prisma.plannerMatchProfile.create({
        data: {
          plannerId,
          isAcceptingMatch: true,
          maxConcurrent: 10,
          preferredGrades: [],
          preferredSubjects: [],
          preferredLocations: [],
          responseTime: 24,
          matchScore: 0.5,
          successRate: 0.0,
        },
        include: {
          planner: {
            include: {
              tenantUser: {
                include: {
                  user: true,
                },
              },
            },
          },
        },
      });
    }

    return profile;
  }

  /**
   * 更新规划师匹配配置
   */
  async updatePlannerMatchProfile(
    plannerId: string,
    data: Partial<{
      isAcceptingMatch: boolean;
      maxConcurrent: number;
      preferredGrades: string[];
      preferredSubjects: string[];
      preferredLocations: string[];
      expertise: any;
      availability: any;
      responseTime: number;
      basePrice: number;
      priceRange: any;
    }>,
  ) {
    // 确保配置存在
    await this.getPlannerMatchProfile(plannerId);

    return await this.prisma.plannerMatchProfile.update({
      where: { plannerId },
      data,
      include: {
        planner: {
          include: {
            tenantUser: {
              include: {
                user: true,
              },
            },
          },
        },
      },
    });
  }

  /**
   * 获取匹配统计
   */
  async getMatchStatistics(params: {
    plannerId?: string;
    tenantId?: string;
    startDate?: Date;
    endDate?: Date;
  }) {
    const { plannerId, tenantId, startDate, endDate } = params;

    const where: Prisma.MatchRequestWhereInput = {};

    if (plannerId) {
      where.matchedPlannerId = plannerId;
    }

    if (tenantId) {
      where.tenantId = tenantId;
    }

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = startDate;
      if (endDate) where.createdAt.lte = endDate;
    }

    const [
      totalRequests,
      pendingRequests,
      matchedRequests,
      confirmedRequests,
      cancelledRequests,
    ] = await Promise.all([
      this.prisma.matchRequest.count({ where }),
      this.prisma.matchRequest.count({
        where: { ...where, status: MatchRequestStatus.PENDING },
      }),
      this.prisma.matchRequest.count({
        where: { ...where, status: MatchRequestStatus.MATCHED },
      }),
      this.prisma.matchRequest.count({
        where: { ...where, status: MatchRequestStatus.CONFIRMED },
      }),
      this.prisma.matchRequest.count({
        where: { ...where, status: MatchRequestStatus.CANCELLED },
      }),
    ]);

    // 计算平均响应时间
    let avgResponseTime = 0;
    if (plannerId) {
      const responses = await this.prisma.matchResponse.findMany({
        where: { plannerId },
        include: {
          matchRequest: true,
        },
      });

      if (responses.length > 0) {
        const totalTime = responses.reduce((acc, response) => {
          const requestTime = response.matchRequest.createdAt.getTime();
          const responseTime = response.respondedAt.getTime();
          return acc + (responseTime - requestTime);
        }, 0);

        avgResponseTime = totalTime / responses.length / (1000 * 60 * 60); // 转换为小时
      }
    }

    // 获取评价统计
    const evaluationWhere: Prisma.MatchEvaluationWhereInput = {};

    if (plannerId) {
      evaluationWhere.matchRequest = {
        matchedPlannerId: plannerId,
      };
    }

    const evaluations = await this.prisma.matchEvaluation.findMany({
      where: evaluationWhere,
    });

    const avgRating =
      evaluations.length > 0
        ? evaluations.reduce((acc, _eval) => acc + _eval.rating, 0) /
          evaluations.length
        : 0;

    return {
      total: totalRequests,
      pending: pendingRequests,
      matched: matchedRequests,
      confirmed: confirmedRequests,
      cancelled: cancelledRequests,
      successRate: totalRequests > 0 ? confirmedRequests / totalRequests : 0,
      avgResponseTime: Math.round(avgResponseTime),
      avgRating,
      totalEvaluations: evaluations.length,
    };
  }

  /**
   * 智能匹配推荐（支持动态算法配置）
   */
  async getSmartMatchRecommendations(matchRequestId: string) {
    const matchRequest = await this.prisma.matchRequest.findUnique({
      where: { id: matchRequestId },
    });

    if (!matchRequest) {
      throw new Error("匹配请求不存在");
    }

    // 获取算法配置
    const algorithmConfig = await this.getAlgorithmConfig(
      matchRequest.category!,
    );
    const requirements = matchRequest.requirements as any;

    // 获取所有活跃的规划师
    const planners = await this.prisma.planner.findMany({
      where: {
        matchProfile: {
          isAcceptingMatch: true,
        },
      },
      include: {
        matchProfile: true,
        tenantUser: {
          include: {
            user: true,
          },
        },
        students: true,
      },
    });

    // 使用动态权重计算每个规划师的匹配得分
    const recommendations = planners.map((planner) => {
      return this.calculateMatchScore(
        planner,
        requirements,
        matchRequest,
        algorithmConfig,
      );
    });

    // 按匹配度排序
    recommendations.sort((a, b) => b.score - a.score);

    // 返回前10个推荐
    return recommendations.slice(0, 10);
  }

  /**
   * 获取算法配置
   */
  private async getAlgorithmConfig(category?: RequirementCategory) {
    // 优先查找特定分类的配置
    let config = null;

    if (category) {
      config = await this.prisma.matchAlgorithmConfig.findFirst({
        where: {
          category,
          isActive: true,
        },
      });
    }

    // 如果没有特定配置，使用默认配置
    if (!config) {
      config = await this.prisma.matchAlgorithmConfig.findFirst({
        where: {
          isDefault: true,
          isActive: true,
        },
      });
    }

    // 如果还没有，返回硬编码的默认配置
    if (!config) {
      return {
        gradeMatchWeight: 0.2,
        subjectMatchWeight: 0.2,
        locationMatchWeight: 0.1,
        ratingWeight: 0.2,
        successRateWeight: 0.2,
        capacityWeight: 0.05,
        responseTimeWeight: 0.05,
        priceMatchWeight: 0.0,
        urgencyMultiplier: 1.0,
        newPlannerBoost: 0.0,
        repeatCustomerBoost: 0.1,
        minRating: null,
        minSuccessRate: null,
        maxResponseTime: null,
      };
    }

    return config;
  }

  /**
   * 计算匹配得分
   */
  private calculateMatchScore(
    planner: any,
    requirements: any,
    matchRequest: any,
    config: any,
  ) {
    const profile = planner.matchProfile!;
    let score = 0;

    // 1. 基础评分和成功率
    if (planner.rating) {
      score += (planner.rating / 5) * config.ratingWeight;
    }
    if (profile.successRate) {
      score += profile.successRate * config.successRateWeight;
    }

    // 2. 偏好匹配
    if (
      requirements.grade &&
      profile.preferredGrades?.includes(requirements.grade)
    ) {
      score += config.gradeMatchWeight;
    }

    if (requirements.subjects?.length && profile.preferredSubjects?.length) {
      const matchedSubjects = requirements.subjects.filter((s: string) =>
        profile.preferredSubjects?.includes(s),
      );
      const subjectMatchRatio =
        matchedSubjects.length / requirements.subjects.length;
      score += subjectMatchRatio * config.subjectMatchWeight;
    }

    if (
      requirements.location &&
      profile.preferredLocations?.includes(requirements.location)
    ) {
      score += config.locationMatchWeight;
    }

    // 3. 容量检查
    const currentLoad = planner.students.length / profile.maxConcurrent;
    const capacityScore = Math.max(0, 1 - currentLoad);
    score += capacityScore * config.capacityWeight;

    // 4. 响应时间
    const responseTimeScore = Math.max(0, 1 - profile.responseTime / 48); // 48小时为基准
    score += responseTimeScore * config.responseTimeWeight;

    // 5. 价格匹配（如果有预算要求）
    if (
      requirements.budget &&
      profile.basePrice &&
      config.priceMatchWeight > 0
    ) {
      const priceScore = requirements.budget >= profile.basePrice ? 1 : 0.5;
      score += priceScore * config.priceMatchWeight;
    }

    // 6. 应用附加因子
    if (matchRequest.urgency > 5) {
      score *= config.urgencyMultiplier;
    }

    // 新规划师加成
    if (profile.successRate === 0 && config.newPlannerBoost > 0) {
      score += config.newPlannerBoost;
    }

    // 回头客加成（检查是否服务过同一机构）
    if (matchRequest.tenantId && config.repeatCustomerBoost > 0) {
      // 这里可以检查历史服务记录
      score += config.repeatCustomerBoost;
    }

    // 应用筛选条件
    if (config.minRating && planner.rating < config.minRating) {
      score = 0;
    }
    if (config.minSuccessRate && profile.successRate < config.minSuccessRate) {
      score = 0;
    }
    if (
      config.maxResponseTime &&
      profile.responseTime > config.maxResponseTime
    ) {
      score = 0;
    }

    return {
      planner,
      score: Math.min(1, Math.max(0, score)),
      matchReasons: {
        gradeMatch:
          requirements.grade &&
          profile.preferredGrades?.includes(requirements.grade),
        subjectMatch: requirements.subjects?.some((s: string) =>
          profile.preferredSubjects?.includes(s),
        ),
        locationMatch:
          requirements.location &&
          profile.preferredLocations?.includes(requirements.location),
        hasCapacity: currentLoad < 0.8,
        fastResponse: profile.responseTime <= 12,
        priceMatch: requirements.budget
          ? requirements.budget >= (profile.basePrice || 0)
          : true,
      },
      details: {
        currentLoad,
        responseTimeScore: Math.round(responseTimeScore * 100),
        capacityScore: Math.round(capacityScore * 100),
      },
    };
  }

  /**
   * 创建算法配置
   */
  async createAlgorithmConfig(data: {
    name: string;
    description?: string;
    category?: RequirementCategory;
    gradeMatchWeight: number;
    subjectMatchWeight: number;
    locationMatchWeight: number;
    ratingWeight: number;
    successRateWeight: number;
    capacityWeight: number;
    responseTimeWeight: number;
    priceMatchWeight?: number;
    urgencyMultiplier?: number;
    newPlannerBoost?: number;
    repeatCustomerBoost?: number;
    minRating?: number;
    minSuccessRate?: number;
    maxResponseTime?: number;
    isDefault?: boolean;
  }) {
    // 如果设置为默认配置，先取消其他默认配置
    if (data.isDefault) {
      await this.prisma.matchAlgorithmConfig.updateMany({
        where: { isDefault: true },
        data: { isDefault: false },
      });
    }

    return await this.prisma.matchAlgorithmConfig.create({
      data: {
        name: data.name,
        description: data.description,
        category: data.category,
        gradeMatchWeight: data.gradeMatchWeight,
        subjectMatchWeight: data.subjectMatchWeight,
        locationMatchWeight: data.locationMatchWeight,
        ratingWeight: data.ratingWeight,
        successRateWeight: data.successRateWeight,
        capacityWeight: data.capacityWeight,
        responseTimeWeight: data.responseTimeWeight,
        priceMatchWeight: data.priceMatchWeight || 0,
        urgencyMultiplier: data.urgencyMultiplier || 1.0,
        newPlannerBoost: data.newPlannerBoost || 0,
        repeatCustomerBoost: data.repeatCustomerBoost || 0,
        minRating: data.minRating,
        minSuccessRate: data.minSuccessRate,
        maxResponseTime: data.maxResponseTime,
        isDefault: data.isDefault || false,
        isActive: true,
      },
    });
  }

  /**
   * 更新算法配置
   */
  async updateAlgorithmConfig(
    configId: string,
    data: Partial<{
      name: string;
      description: string;
      gradeMatchWeight: number;
      subjectMatchWeight: number;
      locationMatchWeight: number;
      ratingWeight: number;
      successRateWeight: number;
      capacityWeight: number;
      responseTimeWeight: number;
      priceMatchWeight: number;
      urgencyMultiplier: number;
      newPlannerBoost: number;
      repeatCustomerBoost: number;
      minRating: number;
      minSuccessRate: number;
      maxResponseTime: number;
      isActive: boolean;
      isDefault: boolean;
    }>,
    adjustedBy: string,
    reason?: string,
  ) {
    // 获取当前配置
    const currentConfig = await this.prisma.matchAlgorithmConfig.findUnique({
      where: { id: configId },
    });

    if (!currentConfig) {
      throw new Error("算法配置不存在");
    }

    // 如果设置为默认配置，先取消其他默认配置
    if (data.isDefault) {
      await this.prisma.matchAlgorithmConfig.updateMany({
        where: {
          isDefault: true,
          id: { not: configId },
        },
        data: { isDefault: false },
      });
    }

    // 记录调整历史
    const oldWeights = {
      gradeMatchWeight: currentConfig.gradeMatchWeight,
      subjectMatchWeight: currentConfig.subjectMatchWeight,
      locationMatchWeight: currentConfig.locationMatchWeight,
      ratingWeight: currentConfig.ratingWeight,
      successRateWeight: currentConfig.successRateWeight,
      capacityWeight: currentConfig.capacityWeight,
      responseTimeWeight: currentConfig.responseTimeWeight,
      priceMatchWeight: currentConfig.priceMatchWeight,
      urgencyMultiplier: currentConfig.urgencyMultiplier,
      newPlannerBoost: currentConfig.newPlannerBoost,
      repeatCustomerBoost: currentConfig.repeatCustomerBoost,
    };

    const newWeights = { ...oldWeights, ...data };

    await this.prisma.weightAdjustmentHistory.create({
      data: {
        configId,
        adjustedBy,
        adjustmentType: "MANUAL",
        reason,
        oldWeights,
        newWeights,
      },
    });

    // 更新配置
    return await this.prisma.matchAlgorithmConfig.update({
      where: { id: configId },
      data,
    });
  }

  /**
   * 获取算法配置列表
   */
  async getAlgorithmConfigs(params?: {
    category?: RequirementCategory;
    isActive?: boolean;
    page?: number;
    pageSize?: number;
  }) {
    const { category, isActive, page = 1, pageSize = 10 } = params || {};

    const where: any = {};
    if (category) where.category = category;
    if (isActive !== undefined) where.isActive = isActive;

    const [total, items] = await Promise.all([
      this.prisma.matchAlgorithmConfig.count({ where }),
      this.prisma.matchAlgorithmConfig.findMany({
        where,
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: [{ isDefault: "desc" }, { createdAt: "desc" }],
        include: {
          adjustmentHistory: {
            take: 5,
            orderBy: { createdAt: "desc" },
          },
        },
      }),
    ]);

    return {
      items,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  /**
   * 删除算法配置
   */
  async deleteAlgorithmConfig(configId: string) {
    const config = await this.prisma.matchAlgorithmConfig.findUnique({
      where: { id: configId },
    });

    if (!config) {
      throw new Error("算法配置不存在");
    }

    if (config.isDefault) {
      throw new Error("无法删除默认配置");
    }

    return await this.prisma.matchAlgorithmConfig.delete({
      where: { id: configId },
    });
  }

  /**
   * 获取权重调整历史
   */
  async getWeightAdjustmentHistory(params: {
    configId?: string;
    adjustedBy?: string;
    startDate?: Date;
    endDate?: Date;
    page?: number;
    pageSize?: number;
  }) {
    const {
      configId,
      adjustedBy,
      startDate,
      endDate,
      page = 1,
      pageSize = 20,
    } = params;

    const where: any = {};
    if (configId) where.configId = configId;
    if (adjustedBy) where.adjustedBy = adjustedBy;
    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = startDate;
      if (endDate) where.createdAt.lte = endDate;
    }

    const [total, items] = await Promise.all([
      this.prisma.weightAdjustmentHistory.count({ where }),
      this.prisma.weightAdjustmentHistory.findMany({
        where,
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: { createdAt: "desc" },
        include: {
          config: {
            select: {
              name: true,
              category: true,
            },
          },
        },
      }),
    ]);

    return {
      items,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  /**
   * 获取规划师列表
   */
  async getPlannersList(params: {
    page?: number;
    pageSize?: number;
    searchQuery?: string;
    subjects?: string[];
    location?: string;
    gradeLevel?: string;
    minRating?: number;
    maxPrice?: number;
    availability?: string;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
    tenantId?: string;
  }) {
    const {
      page = 1,
      pageSize = 10,
      searchQuery = "",
      subjects = [],
      location = "",
      gradeLevel = "",
      minRating,
      maxPrice,
      availability = "",
      sortBy = "rating",
      sortOrder = "desc",
      tenantId,
    } = params;

    // 构建 matchProfile 条件
    const matchProfileConditions: Prisma.PlannerMatchProfileWhereInput = {
      isAcceptingMatch: true,
    };

    // 学科筛选
    if (subjects.length > 0) {
      matchProfileConditions.preferredSubjects = {
        hasSome: subjects,
      };
    }

    // 地区筛选
    if (location) {
      matchProfileConditions.preferredLocations = {
        has: location,
      };
    }

    // 年级筛选
    if (gradeLevel) {
      matchProfileConditions.preferredGrades = {
        has: gradeLevel,
      };
    }

    // 价格筛选
    if (maxPrice !== undefined) {
      matchProfileConditions.basePrice = {
        lte: maxPrice,
      };
    }

    // 可用性筛选
    if (availability) {
      matchProfileConditions.availability = {
        path: ["timeSlots"],
        array_contains: [availability],
      } as any; // 使用 any 因为 Prisma 的 JSON 查询类型限制
    }

    const where: Prisma.PlannerWhereInput = {
      matchProfile: matchProfileConditions,
    };

    // 租户筛选
    if (tenantId) {
      where.tenantId = tenantId;
    }

    // 搜索查询
    if (searchQuery) {
      where.OR = [
        {
          tenantUser: {
            user: {
              name: {
                contains: searchQuery,
                mode: "insensitive",
              },
            },
          },
        },
        {
          tenantUser: {
            user: {
              email: {
                contains: searchQuery,
                mode: "insensitive",
              },
            },
          },
        },
        {
          specialties: {
            hasSome: [searchQuery],
          },
        },
      ];
    }

    // 评分筛选
    if (minRating !== undefined) {
      where.rating = {
        gte: minRating,
      };
    }

    // 排序配置
    const orderBy: Prisma.PlannerOrderByWithRelationInput = {};
    switch (sortBy) {
      case "rating":
        orderBy.rating = sortOrder;
        break;
      case "successRate":
        orderBy.matchProfile = {
          successRate: sortOrder,
        };
        break;
      case "experience":
        orderBy.experience = sortOrder;
        break;
      case "responseTime":
        orderBy.matchProfile = {
          responseTime: sortOrder === "asc" ? "asc" : "desc",
        };
        break;
      case "students":
        orderBy.students = {
          _count: sortOrder,
        };
        break;
      default:
        orderBy.rating = "desc";
    }

    const [totalCount, items] = await Promise.all([
      this.prisma.planner.count({ where }),
      this.prisma.planner.findMany({
        where,
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy,
        include: {
          matchProfile: true,
          tenantUser: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatar: true,
                },
              },
            },
          },
          students: {
            select: {
              studentId: true,
              isPrimary: true,
              assignedAt: true,
            },
          },
          _count: {
            select: {
              students: true,
            },
          },
        },
      }),
    ]);

    // 计算总页数
    const totalPages = Math.ceil(totalCount / pageSize);

    // 格式化返回数据
    const formattedItems = items.map((planner) => ({
      id: planner.id,
      name: planner.tenantUser?.user?.name || "",
      email: planner.tenantUser?.user?.email || "",
      avatar: planner.tenantUser?.user?.avatar || "",
      rating: planner.rating || 0,
      studentCount: planner._count.students,
      matchProfile: planner.matchProfile,
      specialties: planner.specialties,
      experience: planner.experience,
      title: planner.title,
      introduction: planner.introduction,
      createdAt: planner.createdAt,
      updatedAt: planner.updatedAt,
    }));

    return {
      items: formattedItems,
      totalCount,
      totalPages,
      currentPage: page,
      pageSize,
    };
  }

  /**
   * 按服务分类获取匹配请求统计
   */
  async getMatchStatisticsByCategory(params: {
    tenantId?: string;
    startDate?: Date;
    endDate?: Date;
  }) {
    const { tenantId, startDate, endDate } = params;

    const where: Prisma.MatchRequestWhereInput = {};
    if (tenantId) where.tenantId = tenantId;
    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = startDate;
      if (endDate) where.createdAt.lte = endDate;
    }

    // 按分类分组统计
    const categoryStats = await this.prisma.matchRequest.groupBy({
      by: ["category"],
      where,
      _count: {
        id: true,
      },
      _avg: {
        urgency: true,
      },
    });

    // 按分类和状态分组统计
    const statusStats = await this.prisma.matchRequest.groupBy({
      by: ["category", "status"],
      where,
      _count: {
        id: true,
      },
    });

    // 格式化结果
    const result = categoryStats.map((stat) => {
      const categoryStatusStats = statusStats.filter(
        (s) => s.category === stat.category,
      );

      const statusCounts = {
        PENDING: 0,
        MATCHING: 0,
        MATCHED: 0,
        CONFIRMED: 0,
        CANCELLED: 0,
        EXPIRED: 0,
      };

      categoryStatusStats.forEach((s) => {
        statusCounts[s.status as keyof typeof statusCounts] = s._count.id;
      });

      return {
        category: stat.category,
        total: stat._count.id,
        avgUrgency: stat._avg.urgency || 0,
        successRate: statusCounts.CONFIRMED / stat._count.id,
        statusBreakdown: statusCounts,
      };
    });

    return result;
  }

  /**
   * 获取规划师在不同服务分类下的表现
   */
  async getPlannerPerformanceByCategory(plannerId: string) {
    const stats = await this.prisma.matchRequest.groupBy({
      by: ["category", "status"],
      where: {
        matchedPlannerId: plannerId,
      },
      _count: {
        id: true,
      },
      _avg: {
        urgency: true,
      },
    });

    // 获取评价统计
    const evaluations = await this.prisma.matchEvaluation.findMany({
      where: {
        matchRequest: {
          matchedPlannerId: plannerId,
        },
      },
      include: {
        matchRequest: {
          select: {
            category: true,
          },
        },
      },
    });

    // 按分类分组评价
    const evaluationsByCategory = evaluations.reduce(
      (acc, _eval) => {
        const category = _eval.matchRequest.category || "UNKNOWN";
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category].push(_eval.rating);
        return acc;
      },
      {} as Record<string, number[]>,
    );

    // 格式化结果
    const categoryPerformance = Object.values(RequirementCategory).map(
      (category) => {
        const categoryStats = stats.filter((s) => s.category === category);
        const categoryEvaluations = evaluationsByCategory[category] || [];

        const totalRequests = categoryStats.reduce(
          (sum, s) => sum + s._count.id,
          0,
        );
        const confirmedRequests = categoryStats
          .filter((s) => s.status === MatchRequestStatus.CONFIRMED)
          .reduce((sum, s) => sum + s._count.id, 0);

        const avgRating =
          categoryEvaluations.length > 0
            ? categoryEvaluations.reduce((sum, rating) => sum + rating, 0) /
              categoryEvaluations.length
            : 0;

        return {
          category,
          totalRequests,
          confirmedRequests,
          successRate:
            totalRequests > 0 ? confirmedRequests / totalRequests : 0,
          avgRating,
          evaluationCount: categoryEvaluations.length,
        };
      },
    );

    return categoryPerformance.filter((perf) => perf.totalRequests > 0);
  }

  /**
   * 获取单个规划师详情
   */
  async getPlannerDetail(plannerId: string) {
    const planner = await this.prisma.planner.findUnique({
      where: { id: plannerId },
      include: {
        matchProfile: true,
        tenantUser: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                avatar: true,
              },
            },
            tenant: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
          },
        },
        students: {
          select: {
            studentId: true,
            isPrimary: true,
            assignedAt: true,
          },
        },
        _count: {
          select: {
            students: true,
          },
        },
      },
    });

    if (!planner) {
      return null;
    }

    // 获取匹配统计
    const matchStats = await this.getMatchStatistics({
      plannerId: planner.id,
    });

    // 获取最近的评价
    const recentEvaluations = await this.prisma.matchEvaluation.findMany({
      where: {
        matchRequest: {
          matchedPlannerId: planner.id,
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 5,
      include: {
        matchRequest: {
          select: {
            requirements: true,
            category: true,
          },
        },
      },
    });

    return {
      id: planner.id,
      tenantUser: planner.tenantUser,
      matchProfile: planner.matchProfile,
      specialties: planner.specialties,
      experience: planner.experience,
      title: planner.title,
      introduction: planner.introduction,
      rating: planner.rating,
      successRate: planner.successRate,
      totalStudents: planner._count.students,
      createdAt: planner.createdAt,
      updatedAt: planner.updatedAt,
      matchStats,
      recentEvaluations: recentEvaluations.map((_eval) => ({
        id: _eval.id,
        rating: _eval.rating,
        comment: _eval.comment,
        tags: _eval.tags,
        createdAt: _eval.createdAt,
        matchRequest: {
          category: _eval.matchRequest.category,
          requirements: _eval.matchRequest.requirements,
        },
      })),
    };
  }
}
