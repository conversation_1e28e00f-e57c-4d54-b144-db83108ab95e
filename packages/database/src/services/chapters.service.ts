import {
  CourseChapter,
  CourseLesson,
  PrismaClient,
  Prisma,
} from "../generated/prisma";
import { SharedUtils } from "../utils/course.utils";
import {
  CreateChapterInput,
  UpdateChapterInput,
  ChapterOrder,
  ChapterWithLessons,
  ChapterWithStats,
  BulkCreateChapterInput,
  MoveChapterInput,
  ChapterAnalytics,
  IntegrityCheckResult,
} from "../types/course.types";

export class ChaptersService {
  constructor(private prisma: PrismaClient) {}

  // ========== 章节基本管理 ==========

  /**
   * 创建章节
   */
  async createChapter(
    courseId: string,
    input: CreateChapterInput,
  ): Promise<CourseChapter> {
    // 验证课程是否存在
    const course = await this.prisma.course.findUnique({
      where: { id: courseId },
    });

    if (!course) {
      throw new Error("课程不存在");
    }

    // 如果没有指定order，自动计算
    let order = input.order;
    if (order === undefined || order === null) {
      const lastChapter = await this.prisma.courseChapter.findFirst({
        where: { courseId },
        orderBy: { order: "desc" },
      });
      order = lastChapter ? lastChapter.order + 1 : 0;
    }

    return this.prisma.courseChapter.create({
      data: {
        ...input,
        order,
        courseId,
      },
    });
  }

  /**
   * 更新章节
   */
  async updateChapter(
    id: string,
    input: UpdateChapterInput,
  ): Promise<CourseChapter> {
    const chapter = await this.prisma.courseChapter.findUnique({
      where: { id },
    });

    if (!chapter) {
      throw new Error("章节不存在");
    }

    return this.prisma.courseChapter.update({
      where: { id },
      data: {
        ...input,
        updatedAt: new Date(),
      },
    });
  }

  /**
   * 删除章节
   */
  async deleteChapter(id: string): Promise<void> {
    const chapter = await this.prisma.courseChapter.findUnique({
      where: { id },
      include: {
        lessons: true,
      },
    });

    if (!chapter) {
      throw new Error("章节不存在");
    }

    await this.prisma.$transaction(async (tx) => {
      // 删除所有课时
      if (chapter.lessons.length > 0) {
        await tx.courseLesson.deleteMany({
          where: { chapterId: id },
        });
      }

      // 删除章节
      await tx.courseChapter.delete({
        where: { id },
      });

      // 重新排序其他章节
      await this.reorderChaptersAfterDelete(
        tx,
        chapter.courseId,
        chapter.order,
      );
    });
  }

  /**
   * 获取单个章节
   */
  async getChapter(id: string): Promise<CourseChapter | null> {
    return this.prisma.courseChapter.findUnique({
      where: { id },
    });
  }

  /**
   * 获取章节（包含课时）
   */
  async getChapterWithLessons(id: string): Promise<ChapterWithLessons | null> {
    return this.prisma.courseChapter.findUnique({
      where: { id },
      include: {
        lessons: {
          orderBy: { order: "asc" },
        },
        _count: {
          select: {
            lessons: true,
          },
        },
      },
    });
  }

  /**
   * 获取章节（包含统计信息）
   */
  async getChapterWithStats(id: string): Promise<ChapterWithStats | null> {
    const chapter = await this.prisma.courseChapter.findUnique({
      where: { id },
      include: {
        lessons: {
          orderBy: { order: "asc" },
        },
        _count: {
          select: {
            lessons: true,
          },
        },
      },
    });

    if (!chapter) {
      return null;
    }

    // 计算统计信息
    const stats = {
      totalDuration: chapter.lessons.reduce(
        (sum, lesson) => sum + lesson.videoDuration,
        0,
      ),
      freeLessons: chapter.lessons.filter((lesson) => lesson.isFree).length,
      paidLessons: chapter.lessons.filter((lesson) => !lesson.isFree).length,
    };

    return {
      ...chapter,
      stats,
    };
  }

  /**
   * 获取课程的所有章节
   */
  async getChaptersByCourse(courseId: string): Promise<CourseChapter[]> {
    return this.prisma.courseChapter.findMany({
      where: { courseId },
      orderBy: { order: "asc" },
    });
  }

  /**
   * 获取课程的所有章节（包含课时）
   */
  async getChaptersWithLessonsByCourse(
    courseId: string,
  ): Promise<ChapterWithLessons[]> {
    return this.prisma.courseChapter.findMany({
      where: { courseId },
      include: {
        lessons: {
          orderBy: { order: "asc" },
        },
        _count: {
          select: {
            lessons: true,
          },
        },
      },
      orderBy: { order: "asc" },
    });
  }

  // ========== 章节排序管理 ==========

  /**
   * 重新排序章节
   */
  async reorderChapters(
    courseId: string,
    orders: ChapterOrder[],
  ): Promise<CourseChapter[]> {
    // 验证所有章节都属于同一个课程
    const chapters = await this.prisma.courseChapter.findMany({
      where: {
        courseId,
        id: { in: orders.map((o) => o.id) },
      },
    });

    if (chapters.length !== orders.length) {
      throw new Error("部分章节不存在或不属于指定课程");
    }

    // 使用事务更新排序
    await this.prisma.$transaction(
      orders.map(({ id, order }) =>
        this.prisma.courseChapter.update({
          where: { id },
          data: { order },
        }),
      ),
    );

    // 返回更新后的章节列表
    return this.getChaptersByCourse(courseId);
  }

  /**
   * 移动章节到指定位置
   */
  async moveChapter(input: MoveChapterInput): Promise<CourseChapter[]> {
    const { id, newOrder, courseId } = input;

    const chapter = await this.prisma.courseChapter.findUnique({
      where: { id },
    });

    if (!chapter || chapter.courseId !== courseId) {
      throw new Error("章节不存在或不属于指定课程");
    }

    const oldOrder = chapter.order;

    await this.prisma.$transaction(async (tx) => {
      if (newOrder > oldOrder) {
        // 向后移动：将中间的章节向前移动
        await tx.courseChapter.updateMany({
          where: {
            courseId,
            order: {
              gt: oldOrder,
              lte: newOrder,
            },
          },
          data: {
            order: {
              decrement: 1,
            },
          },
        });
      } else if (newOrder < oldOrder) {
        // 向前移动：将中间的章节向后移动
        await tx.courseChapter.updateMany({
          where: {
            courseId,
            order: {
              gte: newOrder,
              lt: oldOrder,
            },
          },
          data: {
            order: {
              increment: 1,
            },
          },
        });
      }

      // 更新目标章节的位置
      await tx.courseChapter.update({
        where: { id },
        data: { order: newOrder },
      });
    });

    return this.getChaptersByCourse(courseId);
  }

  // ========== 批量操作 ==========

  /**
   * 批量创建章节
   */
  async createChapters(
    input: BulkCreateChapterInput,
  ): Promise<CourseChapter[]> {
    const { courseId, chapters } = input;

    // 验证课程是否存在
    const course = await this.prisma.course.findUnique({
      where: { id: courseId },
    });

    if (!course) {
      throw new Error("课程不存在");
    }

    // 获取当前最大order值
    const lastChapter = await this.prisma.courseChapter.findFirst({
      where: { courseId },
      orderBy: { order: "desc" },
    });

    let baseOrder = lastChapter ? lastChapter.order + 1 : 0;

    // 为每个章节分配order
    const chaptersWithOrder = chapters.map((chapter, index) => ({
      ...chapter,
      courseId,
      order: chapter.order !== undefined ? chapter.order : baseOrder + index,
    }));

    // 批量创建
    const createdChapters = await this.prisma.$transaction(
      chaptersWithOrder.map((chapterData) =>
        this.prisma.courseChapter.create({
          data: chapterData,
        }),
      ),
    );

    return createdChapters;
  }

  /**
   * 批量删除章节
   */
  async deleteChaptersByIds(ids: string[]): Promise<void> {
    if (ids.length === 0) {
      return;
    }

    await this.prisma.$transaction(async (tx) => {
      // 删除所有相关课时
      await tx.courseLesson.deleteMany({
        where: {
          chapterId: { in: ids },
        },
      });

      // 删除章节
      await tx.courseChapter.deleteMany({
        where: {
          id: { in: ids },
        },
      });
    });
  }

  /**
   * 复制章节到另一个课程
   */
  async duplicateChapter(
    chapterId: string,
    targetCourseId: string,
  ): Promise<CourseChapter> {
    const sourceChapter = await this.prisma.courseChapter.findUnique({
      where: { id: chapterId },
      include: {
        lessons: {
          orderBy: { order: "asc" },
        },
      },
    });

    if (!sourceChapter) {
      throw new Error("源章节不存在");
    }

    // 验证目标课程是否存在
    const targetCourse = await this.prisma.course.findUnique({
      where: { id: targetCourseId },
    });

    if (!targetCourse) {
      throw new Error("目标课程不存在");
    }

    // 获取目标课程的最大order
    const lastChapter = await this.prisma.courseChapter.findFirst({
      where: { courseId: targetCourseId },
      orderBy: { order: "desc" },
    });

    const newOrder = lastChapter ? lastChapter.order + 1 : 0;

    return this.prisma.$transaction(async (tx) => {
      // 创建新章节
      const newChapter = await tx.courseChapter.create({
        data: {
          title: `${sourceChapter.title}（复制）`,
          description: sourceChapter.description,
          order: newOrder,
          courseId: targetCourseId,
        },
      });

      // 复制课时
      if (sourceChapter.lessons.length > 0) {
        await tx.courseLesson.createMany({
          data: sourceChapter.lessons.map((lesson) => ({
            title: lesson.title,
            description: lesson.description,
            videoUrl: lesson.videoUrl,
            videoDuration: lesson.videoDuration,
            videoSize: lesson.videoSize,
            order: lesson.order,
            isFree: lesson.isFree,
            chapterId: newChapter.id,
          })),
        });
      }

      return newChapter;
    });
  }

  // ========== 统计和验证 ==========

  /**
   * 获取章节统计信息
   */
  async getChapterStats(courseId: string): Promise<{
    totalChapters: number;
    totalLessons: number;
    totalDuration: number;
    averageLessonsPerChapter: number;
    chaptersWithoutLessons: number;
  }> {
    const chapters = await this.prisma.courseChapter.findMany({
      where: { courseId },
      include: {
        lessons: true,
      },
    });

    const totalChapters = chapters.length;
    const totalLessons = chapters.reduce(
      (sum, chapter) => sum + chapter.lessons.length,
      0,
    );
    const totalDuration = chapters.reduce(
      (sum, chapter) =>
        sum +
        chapter.lessons.reduce(
          (chapterSum, lesson) => chapterSum + lesson.videoDuration,
          0,
        ),
      0,
    );
    const averageLessonsPerChapter =
      totalChapters > 0 ? totalLessons / totalChapters : 0;
    const chaptersWithoutLessons = chapters.filter(
      (chapter) => chapter.lessons.length === 0,
    ).length;

    return {
      totalChapters,
      totalLessons,
      totalDuration,
      averageLessonsPerChapter:
        Math.round(averageLessonsPerChapter * 100) / 100,
      chaptersWithoutLessons,
    };
  }

  /**
   * 验证章节数据完整性
   */
  async validateChapterIntegrity(courseId: string): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    const chapters = await this.prisma.courseChapter.findMany({
      where: { courseId },
      include: {
        lessons: true,
      },
      orderBy: { order: "asc" },
    });

    // 检查章节顺序
    const expectedOrders = chapters.map((_, index) => index);
    const actualOrders = chapters.map((chapter) => chapter.order);

    if (!SharedUtils.arraysEqual(expectedOrders, actualOrders)) {
      errors.push("章节排序不连续");
    }

    // 检查空章节
    const emptyChapters = chapters.filter(
      (chapter) => chapter.lessons.length === 0,
    );
    if (emptyChapters.length > 0) {
      warnings.push(`发现 ${emptyChapters.length} 个空章节`);
    }

    // 检查章节标题
    const chaptersWithoutTitle = chapters.filter(
      (chapter) => !chapter.title || chapter.title.trim() === "",
    );
    if (chaptersWithoutTitle.length > 0) {
      errors.push(`发现 ${chaptersWithoutTitle.length} 个没有标题的章节`);
    }

    // 检查重复标题
    const titles = chapters.map((chapter) => chapter.title.toLowerCase());
    const duplicateTitles = titles.filter(
      (title, index) => titles.indexOf(title) !== index,
    );
    if (duplicateTitles.length > 0) {
      warnings.push("发现重复的章节标题");
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  // ========== 私有工具方法 ==========

  /**
   * 删除章节后重新排序其他章节
   */
  private async reorderChaptersAfterDelete(
    tx: Prisma.TransactionClient,
    courseId: string,
    deletedOrder: number,
  ): Promise<void> {
    await tx.courseChapter.updateMany({
      where: {
        courseId,
        order: { gt: deletedOrder },
      },
      data: {
        order: { decrement: 1 },
      },
    });
  }


  /**
   * 修复章节排序
   */
  async repairChapterOrder(courseId: string): Promise<CourseChapter[]> {
    const chapters = await this.prisma.courseChapter.findMany({
      where: { courseId },
      orderBy: { order: "asc" },
    });

    // 重新分配连续的order值
    await this.prisma.$transaction(
      chapters.map((chapter, index) =>
        this.prisma.courseChapter.update({
          where: { id: chapter.id },
          data: { order: index },
        }),
      ),
    );

    return this.getChaptersByCourse(courseId);
  }

  /**
   * 获取章节的下一个可用order值
   */
  async getNextChapterOrder(courseId: string): Promise<number> {
    const lastChapter = await this.prisma.courseChapter.findFirst({
      where: { courseId },
      orderBy: { order: "desc" },
    });

    return lastChapter ? lastChapter.order + 1 : 0;
  }
}
