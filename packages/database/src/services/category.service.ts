import { CourseCategory, PrismaClient, Prisma } from "../generated/prisma";
import {
  CreateCourseCategoryInput,
  UpdateCourseCategoryInput,
  CourseCategoryWithChildren,
} from "../types/course.types";

export class CategoryService {
  constructor(private prisma: PrismaClient) {}

  // ========== 分类基本管理 ==========

  /**
   * 创建课程分类
   */
  async createCategory(input: CreateCourseCategoryInput): Promise<{
    success: boolean;
    data?: CourseCategory;
    message?: string;
  }> {
    try {
      // 检查 slug 是否已存在
      if (input.slug) {
        const existingCategory = await this.prisma.courseCategory.findUnique({
          where: { slug: input.slug },
        });

        if (existingCategory) {
          return {
            success: false,
            message: "标识符已存在，请使用其他标识符",
          };
        }
      }

      // 如果指定了父分类，验证父分类是否存在
      if (input.parentId) {
        const parentCategory = await this.prisma.courseCategory.findUnique({
          where: { id: input.parentId },
        });

        if (!parentCategory) {
          return {
            success: false,
            message: "指定的父分类不存在",
          };
        }
      }

      // 如果没有指定 order，自动计算
      let order = input.order;
      if (order === undefined || order === null) {
        const lastCategory = await this.prisma.courseCategory.findFirst({
          where: { parentId: input.parentId || null },
          orderBy: { order: "desc" },
        });
        order = lastCategory ? lastCategory.order + 1 : 0;
      }

      const category = await this.prisma.courseCategory.create({
        data: {
          name: input.name,
          slug: input.slug || this.generateSlug(input.name),
          description: input.description || null,
          parentId: input.parentId || null,
          icon: input.icon || null,
          order: order,
          isActive: input.isActive ?? true,
        },
      });

      return {
        success: true,
        data: category,
        message: "分类创建成功",
      };
    } catch (error) {
      console.error("创建分类失败:", error);
      return {
        success: false,
        message: error instanceof Error ? error.message : "创建分类失败",
      };
    }
  }

  /**
   * 更新课程分类
   */
  async updateCategory(
    input: UpdateCourseCategoryInput & { id: string },
  ): Promise<{
    success: boolean;
    data?: CourseCategory;
    message?: string;
  }> {
    try {
      const { id, ...updateData } = input;

      // 检查分类是否存在
      const existingCategory = await this.prisma.courseCategory.findUnique({
        where: { id },
      });

      if (!existingCategory) {
        return {
          success: false,
          message: "分类不存在",
        };
      }

      // 检查 slug 是否与其他分类冲突
      if (updateData.slug && updateData.slug !== existingCategory.slug) {
        const conflictCategory = await this.prisma.courseCategory.findUnique({
          where: { slug: updateData.slug },
        });

        if (conflictCategory && conflictCategory.id !== id) {
          return {
            success: false,
            message: "标识符已存在，请使用其他标识符",
          };
        }
      }

      // 如果更新了父分类，验证父分类是否存在且不会造成循环引用
      if (updateData.parentId !== undefined) {
        if (updateData.parentId) {
          // 检查父分类是否存在
          const parentCategory = await this.prisma.courseCategory.findUnique({
            where: { id: updateData.parentId },
          });

          if (!parentCategory) {
            return {
              success: false,
              message: "指定的父分类不存在",
            };
          }

          // 检查是否会造成循环引用
          if (
            await this.wouldCreateCircularReference(id, updateData.parentId)
          ) {
            return {
              success: false,
              message: "不能将分类设置为其子分类的子分类",
            };
          }
        }
      }

      const category = await this.prisma.courseCategory.update({
        where: { id },
        data: {
          ...updateData,
          updatedAt: new Date(),
        },
      });

      return {
        success: true,
        data: category,
        message: "分类更新成功",
      };
    } catch (error) {
      console.error("更新分类失败:", error);
      return {
        success: false,
        message: error instanceof Error ? error.message : "更新分类失败",
      };
    }
  }

  /**
   * 删除课程分类
   */
  async deleteCategory(id: string): Promise<{
    success: boolean;
    message?: string;
  }> {
    try {
      // 检查分类是否存在
      const category = await this.prisma.courseCategory.findUnique({
        where: { id },
        include: {
          children: true,
          courses: true,
        },
      });

      if (!category) {
        return {
          success: false,
          message: "分类不存在",
        };
      }

      // 检查是否有子分类
      if (category.children.length > 0) {
        return {
          success: false,
          message: `该分类下还有 ${category.children.length} 个子分类，无法删除`,
        };
      }

      // 检查是否有关联的课程
      if (category.courses.length > 0) {
        return {
          success: false,
          message: `该分类下还有 ${category.courses.length} 门课程，无法删除`,
        };
      }

      await this.prisma.courseCategory.delete({
        where: { id },
      });

      return {
        success: true,
        message: "分类删除成功",
      };
    } catch (error) {
      console.error("删除分类失败:", error);
      return {
        success: false,
        message: error instanceof Error ? error.message : "删除分类失败",
      };
    }
  }

  /**
   * 获取单个分类详情
   */
  async getCategoryById(id: string): Promise<{
    success: boolean;
    data?: CourseCategoryWithChildren;
    message?: string;
  }> {
    try {
      const category = await this.prisma.courseCategory.findUnique({
        where: { id },
        include: {
          parent: true,
          children: {
            orderBy: { order: "asc" },
          },
          _count: {
            select: {
              courses: true,
              children: true,
            },
          },
        },
      });

      if (!category) {
        return {
          success: false,
          message: "课程分类不存在",
        };
      }

      return {
        success: true,
        data: category,
      };
    } catch (error) {
      console.error("获取分类详情失败:", error);
      return {
        success: false,
        message: "获取分类详情失败",
      };
    }
  }

  /**
   * 获取分类列表
   */
  async getCategories(
    options: {
      parentId?: string | null;
      isActive?: boolean;
      includeChildren?: boolean;
      includeParent?: boolean;
      search?: string;
      page?: number;
      limit?: number;
    } = {},
  ): Promise<{
    success: boolean;
    data?: any[];
    total?: number;
    message?: string;
  }> {
    try {
      const {
        parentId,
        isActive,
        includeChildren = false,
        includeParent = false,
        search,
        page,
        limit,
      } = options;

      // 构建查询条件
      const where: Prisma.CourseCategoryWhereInput = {};

      if (parentId !== undefined) {
        where.parentId = parentId;
      }

      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      if (search) {
        where.OR = [
          { name: { contains: search, mode: "insensitive" } },
          { description: { contains: search, mode: "insensitive" } },
        ];
      }

      // 构建查询选项
      const queryOptions: Prisma.CourseCategoryFindManyArgs = {
        where,
        include: {
          parent: includeParent,
          children: includeChildren
            ? {
                orderBy: { order: "asc" },
              }
            : false,
          _count: {
            select: {
              courses: true,
              children: true,
            },
          },
        },
        orderBy: { order: "asc" },
      };

      // 如果指定了分页参数
      if (page !== undefined && limit !== undefined) {
        const skip = (page - 1) * limit;
        queryOptions.skip = skip;
        queryOptions.take = limit;

        const [categories, total] = await Promise.all([
          this.prisma.courseCategory.findMany(queryOptions),
          this.prisma.courseCategory.count({ where }),
        ]);

        return {
          success: true,
          data: categories,
          total,
        };
      } else {
        const categories =
          await this.prisma.courseCategory.findMany(queryOptions);

        return {
          success: true,
          data: categories,
        };
      }
    } catch (error) {
      console.error("获取分类列表失败:", error);
      return {
        success: false,
        message: "获取分类列表失败",
      };
    }
  }

  /**
   * 获取分类树结构
   */
  async getCategoryTree(): Promise<{
    success: boolean;
    data?: any[];
    message?: string;
  }> {
    try {
      // 获取所有分类
      const allCategories = await this.prisma.courseCategory.findMany({
        where: { isActive: true },
        include: {
          _count: {
            select: {
              courses: true,
              children: true,
            },
          },
        },
        orderBy: { order: "asc" },
      });

      // 构建树形结构
      const buildTree = (parentId: string | null = null): any[] => {
        return allCategories
          .filter((cat) => cat.parentId === parentId)
          .map((cat) => ({
            ...cat,
            children: buildTree(cat.id),
          }));
      };

      const tree = buildTree();

      return {
        success: true,
        data: tree,
      };
    } catch (error) {
      console.error("获取分类树失败:", error);
      return {
        success: false,
        message: "获取分类树失败",
      };
    }
  }

  // ========== 私有工具方法 ==========

  /**
   * 生成 slug
   */
  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-+|-+$/g, "");
  }

  /**
   * 检查是否会创建循环引用
   */
  private async wouldCreateCircularReference(
    categoryId: string,
    newParentId: string,
  ): Promise<boolean> {
    if (categoryId === newParentId) {
      return true;
    }

    // 获取新父分类的所有祖先
    const ancestors = await this.getCategoryAncestors(newParentId);
    return ancestors.includes(categoryId);
  }

  /**
   * 获取分类的所有祖先ID
   */
  private async getCategoryAncestors(categoryId: string): Promise<string[]> {
    const ancestors: string[] = [];
    let currentId: string | null = categoryId;

    while (currentId) {
      const category = (await this.prisma.courseCategory.findUnique({
        where: { id: currentId },
        select: { parentId: true },
      })) as any;

      if (!category || !category.parentId) {
        break;
      }

      ancestors.push(category.parentId);
      currentId = category.parentId;
    }

    return ancestors;
  }

  /**
   * 修复分类排序
   */
  async repairCategoryOrder(parentId: string | null = null): Promise<{
    success: boolean;
    message?: string;
  }> {
    try {
      const categories = await this.prisma.courseCategory.findMany({
        where: { parentId },
        orderBy: { order: "asc" },
      });

      // 重新分配连续的order值
      await this.prisma.$transaction(
        categories.map((category, index) =>
          this.prisma.courseCategory.update({
            where: { id: category.id },
            data: { order: index },
          }),
        ),
      );

      return {
        success: true,
        message: "分类排序修复成功",
      };
    } catch (error) {
      console.error("修复分类排序失败:", error);
      return {
        success: false,
        message: "修复分类排序失败",
      };
    }
  }

  /**
   * 移动分类到新的父分类
   */
  async moveCategory(
    categoryId: string,
    newParentId: string | null,
    newOrder?: number,
  ): Promise<{
    success: boolean;
    data?: CourseCategory;
    message?: string;
  }> {
    try {
      // 检查分类是否存在
      const category = await this.prisma.courseCategory.findUnique({
        where: { id: categoryId },
      });

      if (!category) {
        return {
          success: false,
          message: "分类不存在",
        };
      }

      // 如果指定了新父分类，验证其存在性和循环引用
      if (newParentId) {
        const newParent = await this.prisma.courseCategory.findUnique({
          where: { id: newParentId },
        });

        if (!newParent) {
          return {
            success: false,
            message: "目标父分类不存在",
          };
        }

        if (await this.wouldCreateCircularReference(categoryId, newParentId)) {
          return {
            success: false,
            message: "不能将分类移动到其子分类下",
          };
        }
      }

      // 如果没有指定新order，放到目标位置的最后
      if (newOrder === undefined) {
        const lastCategory = await this.prisma.courseCategory.findFirst({
          where: { parentId: newParentId },
          orderBy: { order: "desc" },
        });
        newOrder = lastCategory ? lastCategory.order + 1 : 0;
      }

      const updatedCategory = await this.prisma.courseCategory.update({
        where: { id: categoryId },
        data: {
          parentId: newParentId,
          order: newOrder,
        },
      });

      return {
        success: true,
        data: updatedCategory,
        message: "分类移动成功",
      };
    } catch (error) {
      console.error("移动分类失败:", error);
      return {
        success: false,
        message: "移动分类失败",
      };
    }
  }
}
