import {
  CourseLesson,
  LessonTimestamp,
  PrismaClient,
  Prisma,
} from "../generated/prisma";
import { SharedUtils } from "../utils/course.utils";
import {
  CreateLessonInput,
  UpdateLessonInput,
  LessonOrder,
  VideoUpdateInput,
  CreateTimestampInput,
  LessonWithTimestamps,
  LessonWithStats,
  BulkCreateLessonInput,
  MoveLessonInput,
  LessonAnalytics,
} from "../types/course.types";

export class LessonsService {
  constructor(private prisma: PrismaClient) {}

  // ========== 课时基本管理 ==========

  /**
   * 创建课时
   */
  async createLesson(
    chapterId: string,
    input: CreateLessonInput,
  ): Promise<CourseLesson> {
    // 验证章节是否存在
    const chapter = await this.prisma.courseChapter.findUnique({
      where: { id: chapterId },
    });

    if (!chapter) {
      throw new Error("章节不存在");
    }

    // 如果没有指定order，自动计算
    let order = input.order;
    if (order === undefined || order === null) {
      const lastLesson = await this.prisma.courseLesson.findFirst({
        where: { chapterId },
        orderBy: { order: "desc" },
      });
      order = lastLesson ? lastLesson.order + 1 : 0;
    }

    return this.prisma.courseLesson.create({
      data: {
        ...input,
        order,
        chapterId,
        isFree: input.isFree ?? false,
      },
    });
  }

  /**
   * 更新课时
   */
  async updateLesson(
    id: string,
    input: UpdateLessonInput,
  ): Promise<CourseLesson> {
    const lesson = await this.prisma.courseLesson.findUnique({
      where: { id },
    });

    if (!lesson) {
      throw new Error("课时不存在");
    }

    return this.prisma.courseLesson.update({
      where: { id },
      data: {
        ...input,
        updatedAt: new Date(),
      },
    });
  }

  /**
   * 删除课时
   */
  async deleteLesson(id: string): Promise<void> {
    const lesson = await this.prisma.courseLesson.findUnique({
      where: { id },
      include: {
        timestamps: true,
      },
    });

    if (!lesson) {
      throw new Error("课时不存在");
    }

    await this.prisma.$transaction(async (tx) => {
      // 删除所有时间戳
      if (lesson.timestamps.length > 0) {
        await tx.lessonTimestamp.deleteMany({
          where: { lessonId: id },
        });
      }

      // 删除课时
      await tx.courseLesson.delete({
        where: { id },
      });

      // 重新排序其他课时
      await this.reorderLessonsAfterDelete(tx, lesson.chapterId, lesson.order);
    });
  }

  /**
   * 获取单个课时
   */
  async getLesson(id: string): Promise<CourseLesson | null> {
    return this.prisma.courseLesson.findUnique({
      where: { id },
    });
  }

  /**
   * 获取课时（包含时间戳）
   */
  async getLessonWithTimestamps(
    id: string,
  ): Promise<LessonWithTimestamps | null> {
    return this.prisma.courseLesson.findUnique({
      where: { id },
      include: {
        timestamps: {
          orderBy: { timestamp: "asc" },
        },
        _count: {
          select: {
            timestamps: true,
          },
        },
      },
    });
  }

  /**
   * 获取课时（包含统计信息）
   */
  async getLessonWithStats(id: string): Promise<LessonWithStats | null> {
    const lesson = await this.prisma.courseLesson.findUnique({
      where: { id },
      include: {
        timestamps: {
          orderBy: { timestamp: "asc" },
        },
        chapter: {
          select: {
            id: true,
            title: true,
            courseId: true,
          },
        },
        progress: true, // 学习进度
      },
    });

    if (!lesson) {
      return null;
    }

    // 计算统计信息
    const totalProgress = lesson.progress?.length || 0;
    const completedProgress =
      lesson.progress?.filter((p) => p.isCompleted).length || 0;
    const watchTime =
      lesson.progress?.reduce((sum, p) => sum + p.watchedDuration, 0) || 0;
    const completionRate =
      totalProgress > 0 ? (completedProgress / totalProgress) * 100 : 0;

    // 获取热门时间戳（这里可以根据实际需求调整逻辑）
    const popularTimestamps = lesson.timestamps.slice(0, 5);

    const stats = {
      watchTime,
      completionRate: Math.round(completionRate * 100) / 100,
      popularTimestamps,
    };

    return {
      ...lesson,
      stats,
    };
  }

  /**
   * 获取章节的所有课时
   */
  async getLessonsByChapter(chapterId: string): Promise<CourseLesson[]> {
    return this.prisma.courseLesson.findMany({
      where: { chapterId },
      orderBy: { order: "asc" },
    });
  }

  /**
   * 获取课程的所有课时
   */
  async getLessonsByCourse(courseId: string): Promise<CourseLesson[]> {
    return this.prisma.courseLesson.findMany({
      where: {
        chapter: {
          courseId,
        },
      },
      include: {
        chapter: {
          select: {
            id: true,
            title: true,
            order: true,
          },
        },
      },
      orderBy: [{ chapter: { order: "asc" } }, { order: "asc" }],
    });
  }

  // ========== 课时排序管理 ==========

  /**
   * 重新排序课时
   */
  async reorderLessons(
    chapterId: string,
    orders: LessonOrder[],
  ): Promise<CourseLesson[]> {
    // 验证所有课时都属于同一个章节
    const lessons = await this.prisma.courseLesson.findMany({
      where: {
        chapterId,
        id: { in: orders.map((o) => o.id) },
      },
    });

    if (lessons.length !== orders.length) {
      throw new Error("部分课时不存在或不属于指定章节");
    }

    // 使用事务更新排序
    await this.prisma.$transaction(
      orders.map(({ id, order }) =>
        this.prisma.courseLesson.update({
          where: { id },
          data: { order },
        }),
      ),
    );

    // 返回更新后的课时列表
    return this.getLessonsByChapter(chapterId);
  }

  /**
   * 移动课时到指定位置
   */
  async moveLesson(input: MoveLessonInput): Promise<CourseLesson[]> {
    const { id, newOrder, chapterId } = input;

    const lesson = await this.prisma.courseLesson.findUnique({
      where: { id },
    });

    if (!lesson || lesson.chapterId !== chapterId) {
      throw new Error("课时不存在或不属于指定章节");
    }

    const oldOrder = lesson.order;

    await this.prisma.$transaction(async (tx) => {
      if (newOrder > oldOrder) {
        // 向后移动：将中间的课时向前移动
        await tx.courseLesson.updateMany({
          where: {
            chapterId,
            order: {
              gt: oldOrder,
              lte: newOrder,
            },
          },
          data: {
            order: {
              decrement: 1,
            },
          },
        });
      } else if (newOrder < oldOrder) {
        // 向前移动：将中间的课时向后移动
        await tx.courseLesson.updateMany({
          where: {
            chapterId,
            order: {
              gte: newOrder,
              lt: oldOrder,
            },
          },
          data: {
            order: {
              increment: 1,
            },
          },
        });
      }

      // 更新目标课时的位置
      await tx.courseLesson.update({
        where: { id },
        data: { order: newOrder },
      });
    });

    return this.getLessonsByChapter(chapterId);
  }

  /**
   * 移动课时到不同章节
   */
  async moveLessonToChapter(
    lessonId: string,
    targetChapterId: string,
    newOrder?: number,
  ): Promise<CourseLesson> {
    const lesson = await this.prisma.courseLesson.findUnique({
      where: { id: lessonId },
    });

    if (!lesson) {
      throw new Error("课时不存在");
    }

    // 验证目标章节是否存在
    const targetChapter = await this.prisma.courseChapter.findUnique({
      where: { id: targetChapterId },
    });

    if (!targetChapter) {
      throw new Error("目标章节不存在");
    }

    // 如果没有指定新order，放到目标章节的最后
    if (newOrder === undefined) {
      const lastLesson = await this.prisma.courseLesson.findFirst({
        where: { chapterId: targetChapterId },
        orderBy: { order: "desc" },
      });
      newOrder = lastLesson ? lastLesson.order + 1 : 0;
    }

    return this.prisma.$transaction(async (tx) => {
      // 重新排序原章节中的其他课时
      await this.reorderLessonsAfterDelete(tx, lesson.chapterId, lesson.order);

      // 为目标章节的课时让出位置
      await tx.courseLesson.updateMany({
        where: {
          chapterId: targetChapterId,
          order: { gte: newOrder },
        },
        data: {
          order: { increment: 1 },
        },
      });

      // 移动课时到目标章节
      return tx.courseLesson.update({
        where: { id: lessonId },
        data: {
          chapterId: targetChapterId,
          order: newOrder,
        },
      });
    });
  }

  // ========== 批量操作 ==========

  /**
   * 批量创建课时
   */
  async createLessons(input: BulkCreateLessonInput): Promise<CourseLesson[]> {
    const { chapterId, lessons } = input;

    // 验证章节是否存在
    const chapter = await this.prisma.courseChapter.findUnique({
      where: { id: chapterId },
    });

    if (!chapter) {
      throw new Error("章节不存在");
    }

    // 获取当前最大order值
    const lastLesson = await this.prisma.courseLesson.findFirst({
      where: { chapterId },
      orderBy: { order: "desc" },
    });

    let baseOrder = lastLesson ? lastLesson.order + 1 : 0;

    // 为每个课时分配order
    const lessonsWithOrder = lessons.map((lesson, index) => ({
      ...lesson,
      chapterId,
      order: lesson.order !== undefined ? lesson.order : baseOrder + index,
      isFree: lesson.isFree ?? false,
    }));

    // 批量创建
    const createdLessons = await this.prisma.$transaction(
      lessonsWithOrder.map((lessonData) =>
        this.prisma.courseLesson.create({
          data: lessonData,
        }),
      ),
    );

    return createdLessons;
  }

  /**
   * 批量删除课时
   */
  async deleteLessonsByIds(ids: string[]): Promise<void> {
    if (ids.length === 0) {
      return;
    }

    await this.prisma.$transaction(async (tx) => {
      // 删除所有相关时间戳
      await tx.lessonTimestamp.deleteMany({
        where: {
          lessonId: { in: ids },
        },
      });

      // 删除课时
      await tx.courseLesson.deleteMany({
        where: {
          id: { in: ids },
        },
      });
    });
  }

  /**
   * 复制课时到另一个章节
   */
  async duplicateLesson(
    lessonId: string,
    targetChapterId: string,
  ): Promise<CourseLesson> {
    const sourceLesson = await this.prisma.courseLesson.findUnique({
      where: { id: lessonId },
      include: {
        timestamps: {
          orderBy: { timestamp: "asc" },
        },
      },
    });

    if (!sourceLesson) {
      throw new Error("源课时不存在");
    }

    // 验证目标章节是否存在
    const targetChapter = await this.prisma.courseChapter.findUnique({
      where: { id: targetChapterId },
    });

    if (!targetChapter) {
      throw new Error("目标章节不存在");
    }

    // 获取目标章节的最大order
    const lastLesson = await this.prisma.courseLesson.findFirst({
      where: { chapterId: targetChapterId },
      orderBy: { order: "desc" },
    });

    const newOrder = lastLesson ? lastLesson.order + 1 : 0;

    return this.prisma.$transaction(async (tx) => {
      // 创建新课时
      const newLesson = await tx.courseLesson.create({
        data: {
          title: `${sourceLesson.title}（复制）`,
          description: sourceLesson.description,
          videoUrl: sourceLesson.videoUrl,
          videoDuration: sourceLesson.videoDuration,
          videoSize: sourceLesson.videoSize,
          order: newOrder,
          isFree: sourceLesson.isFree,
          chapterId: targetChapterId,
        },
      });

      // 复制时间戳
      if (sourceLesson.timestamps.length > 0) {
        await tx.lessonTimestamp.createMany({
          data: sourceLesson.timestamps.map((timestamp) => ({
            timestamp: timestamp.timestamp,
            title: timestamp.title,
            description: timestamp.description,
            lessonId: newLesson.id,
          })),
        });
      }

      return newLesson;
    });
  }

  // ========== 视频管理 ==========

  /**
   * 更新课时视频信息
   */
  async updateLessonVideo(
    id: string,
    videoData: VideoUpdateInput,
  ): Promise<CourseLesson> {
    const lesson = await this.prisma.courseLesson.findUnique({
      where: { id },
    });

    if (!lesson) {
      throw new Error("课时不存在");
    }

    return this.prisma.courseLesson.update({
      where: { id },
      data: {
        ...videoData,
        updatedAt: new Date(),
      },
    });
  }

  /**
   * 批量更新视频信息
   */
  async batchUpdateVideoInfo(
    updates: Array<{ id: string; videoData: VideoUpdateInput }>,
  ): Promise<CourseLesson[]> {
    const results = await this.prisma.$transaction(
      updates.map(({ id, videoData }) =>
        this.prisma.courseLesson.update({
          where: { id },
          data: {
            ...videoData,
            updatedAt: new Date(),
          },
        }),
      ),
    );

    return results;
  }

  // ========== 时间戳管理 ==========

  /**
   * 为课时添加时间戳
   */
  async createTimestamp(
    lessonId: string,
    input: CreateTimestampInput,
  ): Promise<LessonTimestamp> {
    const lesson = await this.prisma.courseLesson.findUnique({
      where: { id: lessonId },
    });

    if (!lesson) {
      throw new Error("课时不存在");
    }

    // 验证时间戳是否在有效范围内
    if (input.timestamp < 0 || input.timestamp > lesson.videoDuration) {
      throw new Error("时间戳超出视频时长范围");
    }

    return this.prisma.lessonTimestamp.create({
      data: {
        ...input,
        lessonId,
      },
    });
  }

  /**
   * 更新时间戳
   */
  async updateTimestamp(
    id: string,
    input: Partial<CreateTimestampInput>,
  ): Promise<LessonTimestamp> {
    const timestamp = await this.prisma.lessonTimestamp.findUnique({
      where: { id },
      include: { lesson: true },
    });

    if (!timestamp) {
      throw new Error("时间戳不存在");
    }

    // 如果更新了时间戳值，验证是否在有效范围内
    if (input.timestamp !== undefined) {
      if (
        input.timestamp < 0 ||
        input.timestamp > timestamp.lesson.videoDuration
      ) {
        throw new Error("时间戳超出视频时长范围");
      }
    }

    return this.prisma.lessonTimestamp.update({
      where: { id },
      data: input,
    });
  }

  /**
   * 删除时间戳
   */
  async deleteTimestamp(id: string): Promise<void> {
    const timestamp = await this.prisma.lessonTimestamp.findUnique({
      where: { id },
    });

    if (!timestamp) {
      throw new Error("时间戳不存在");
    }

    await this.prisma.lessonTimestamp.delete({
      where: { id },
    });
  }

  /**
   * 获取课时的所有时间戳
   */
  async getTimestampsByLesson(lessonId: string): Promise<LessonTimestamp[]> {
    return this.prisma.lessonTimestamp.findMany({
      where: { lessonId },
      orderBy: { timestamp: "asc" },
    });
  }

  // ========== 统计和分析 ==========

  /**
   * 获取章节的课时分析
   */
  async getLessonAnalytics(chapterId: string): Promise<LessonAnalytics> {
    const lessons = await this.prisma.courseLesson.findMany({
      where: { chapterId },
      include: {
        timestamps: true,
        progress: true,
      },
    });

    const totalDuration = lessons.reduce(
      (sum, lesson) => sum + lesson.videoDuration,
      0,
    );
    const averageDuration =
      lessons.length > 0 ? totalDuration / lessons.length : 0;
    const freeLessonsCount = lessons.filter((lesson) => lesson.isFree).length;
    const paidLessonsCount = lessons.filter((lesson) => !lesson.isFree).length;
    const totalTimestamps = lessons.reduce(
      (sum, lesson) => sum + lesson.timestamps.length,
      0,
    );

    // 找到最受欢迎的课时（基于学习进度数量）
    const mostPopularLesson = lessons.reduce((prev, current) => {
      const prevProgress = prev?.progress?.length || 0;
      const currentProgress = current.progress?.length || 0;
      return currentProgress > prevProgress ? current : prev;
    }, lessons[0] || null);

    return {
      totalDuration,
      averageDuration: Math.round(averageDuration),
      freeLessonsCount,
      paidLessonsCount,
      totalTimestamps,
      mostPopularLesson,
    };
  }

  /**
   * 获取课程的课时统计
   */
  async getCourseLeasonStats(courseId: string): Promise<{
    totalLessons: number;
    totalDuration: number;
    freeLessons: number;
    paidLessons: number;
    averageLessonDuration: number;
    chaptersWithLessons: number;
    totalTimestamps: number;
  }> {
    const lessons = await this.getLessonsByCourse(courseId);
    const timestamps = await this.prisma.lessonTimestamp.findMany({
      where: {
        lesson: {
          chapter: {
            courseId,
          },
        },
      },
    });

    const totalLessons = lessons.length;
    const totalDuration = lessons.reduce(
      (sum, lesson) => sum + lesson.videoDuration,
      0,
    );
    const freeLessons = lessons.filter((lesson) => lesson.isFree).length;
    const paidLessons = lessons.filter((lesson) => !lesson.isFree).length;
    const averageLessonDuration =
      totalLessons > 0 ? totalDuration / totalLessons : 0;

    // 统计有课时的章节数
    const chapterIds = new Set(lessons.map((lesson) => lesson.chapterId));
    const chaptersWithLessons = chapterIds.size;

    return {
      totalLessons,
      totalDuration,
      freeLessons,
      paidLessons,
      averageLessonDuration: Math.round(averageLessonDuration),
      chaptersWithLessons,
      totalTimestamps: timestamps.length,
    };
  }

  /**
   * 验证课时数据完整性
   */
  async validateLessonIntegrity(chapterId: string): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    const lessons = await this.prisma.courseLesson.findMany({
      where: { chapterId },
      include: {
        timestamps: true,
      },
      orderBy: { order: "asc" },
    });

    // 检查课时顺序
    const expectedOrders = lessons.map((_, index) => index);
    const actualOrders = lessons.map((lesson) => lesson.order);

    if (!SharedUtils.arraysEqual(expectedOrders, actualOrders)) {
      errors.push("课时排序不连续");
    }

    // 检查视频信息
    const lessonsWithoutVideo = lessons.filter(
      (lesson) => !lesson.videoUrl || lesson.videoDuration <= 0,
    );
    if (lessonsWithoutVideo.length > 0) {
      errors.push(`发现 ${lessonsWithoutVideo.length} 个没有有效视频的课时`);
    }

    // 检查课时标题
    const lessonsWithoutTitle = lessons.filter(
      (lesson) => !lesson.title || lesson.title.trim() === "",
    );
    if (lessonsWithoutTitle.length > 0) {
      errors.push(`发现 ${lessonsWithoutTitle.length} 个没有标题的课时`);
    }

    // 检查重复标题
    const titles = lessons.map((lesson) => lesson.title.toLowerCase());
    const duplicateTitles = titles.filter(
      (title, index) => titles.indexOf(title) !== index,
    );
    if (duplicateTitles.length > 0) {
      warnings.push("发现重复的课时标题");
    }

    // 检查时间戳有效性
    const invalidTimestamps = lessons.filter((lesson) =>
      lesson.timestamps.some(
        (ts) => ts.timestamp < 0 || ts.timestamp > lesson.videoDuration,
      ),
    );
    if (invalidTimestamps.length > 0) {
      errors.push(`发现 ${invalidTimestamps.length} 个课时包含无效时间戳`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  // ========== 私有工具方法 ==========

  /**
   * 删除课时后重新排序其他课时
   */
  private async reorderLessonsAfterDelete(
    tx: Prisma.TransactionClient,
    chapterId: string,
    deletedOrder: number,
  ): Promise<void> {
    await tx.courseLesson.updateMany({
      where: {
        chapterId,
        order: { gt: deletedOrder },
      },
      data: {
        order: { decrement: 1 },
      },
    });
  }


  /**
   * 修复课时排序
   */
  async repairLessonOrder(chapterId: string): Promise<CourseLesson[]> {
    const lessons = await this.prisma.courseLesson.findMany({
      where: { chapterId },
      orderBy: { order: "asc" },
    });

    // 重新分配连续的order值
    await this.prisma.$transaction(
      lessons.map((lesson, index) =>
        this.prisma.courseLesson.update({
          where: { id: lesson.id },
          data: { order: index },
        }),
      ),
    );

    return this.getLessonsByChapter(chapterId);
  }

  /**
   * 获取课时的下一个可用order值
   */
  async getNextLessonOrder(chapterId: string): Promise<number> {
    const lastLesson = await this.prisma.courseLesson.findFirst({
      where: { chapterId },
      orderBy: { order: "desc" },
    });

    return lastLesson ? lastLesson.order + 1 : 0;
  }

  /**
   * 格式化时长为可读格式
   */
  formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    }
    return `${minutes}:${secs.toString().padStart(2, "0")}`;
  }
}
