import { CoursesService } from "./courses.service";
import { ChaptersService } from "./chapters.service";
import { LessonsService } from "./lessons.service";
import { SharedUtils } from "../utils/course.utils";
import {
  Course,
  CourseCategory,
  CourseChapter,
  CourseLesson,
  PrismaClient,
} from "../generated/prisma";
import {
  CreateCourseInput,
  CreateChapterInput,
  CreateLessonInput,
  CreateCourseWithContentInput,
  CreateChapterWithLessonsInput,
  UpdateCourseWithContentInput,
  UpdateChapterWithLessonsInput,
  UpdateLessonWithIdInput,
  CourseWithFullContent,
  CourseContentStats,
  ValidationResult,
  ValidationError,
  ValidationWarning,
  ImportContentInput,
  ImportChapterInput,
  ImportLessonInput,
  ExportContentResult,
  ExportChapterResult,
  ExportLessonResult,
} from "../types/course.types";

export class CourseContentService {
  constructor(
    private prisma: PrismaClient,
    private coursesService: CoursesService,
    private chaptersService: ChaptersService,
    private lessonsService: LessonsService,
  ) {}

  // ========== 完整课程创建 ==========

  /**
   * 创建包含完整内容的课程
   */
  async createCourseWithContent(
    input: CreateCourseWithContentInput,
  ): Promise<CourseWithFullContent> {
    const { chapters, ...courseData } = input;

    return this.prisma.$transaction(async (tx) => {
      // 1. 创建课程基本信息
      const course = await this.coursesService.createCourse(courseData);

      // 2. 创建章节和课时
      if (chapters && chapters.length > 0) {
        for (let i = 0; i < chapters.length; i++) {
          const chapterInput = chapters[i];
          const { lessons, ...chapterData } = chapterInput;

          // 创建章节
          const chapter = await this.chaptersService.createChapter(course.id, {
            ...chapterData,
            order: chapterData.order ?? i,
          });

          // 为章节创建课时
          if (lessons && lessons.length > 0) {
            const lessonsWithOrder = lessons.map((lesson, index) => ({
              ...lesson,
              order: lesson.order ?? index,
              isFree: lesson.isFree ?? false,
            }));

            await this.lessonsService.createLessons({
              chapterId: chapter.id,
              lessons: lessonsWithOrder,
            });
          }
        }
      }

      // 3. 返回完整的课程信息
      const result = await this.getCourseWithFullContent(course.id);
      if (!result) {
        throw new Error("创建课程后无法获取完整信息");
      }
      return result;
    });
  }

  // ========== 完整课程更新 ==========

  /**
   * 更新包含完整内容的课程
   */
  async updateCourseWithContent(
    id: string,
    input: UpdateCourseWithContentInput,
  ): Promise<CourseWithFullContent> {
    const { courseData, chapters } = input;

    return this.prisma.$transaction(async (tx) => {
      // 1. 更新课程基本信息
      if (courseData) {
        await this.coursesService.updateCourse(id, courseData as any);
      }

      // 2. 处理章节更新
      if (chapters) {
        await this.updateCourseChapters(id, chapters);
      }

      // 3. 返回更新后的完整课程信息
      const result = await this.getCourseWithFullContent(id);
      if (!result) {
        throw new Error("更新课程后无法获取完整信息");
      }
      return result;
    });
  }

  /**
   * 处理章节的批量更新
   */
  private async updateCourseChapters(
    courseId: string,
    chapters: UpdateChapterWithLessonsInput[],
  ): Promise<void> {
    // 获取现有章节
    const existingChapters =
      await this.chaptersService.getChaptersByCourse(courseId);
    const existingChapterIds = new Set(existingChapters.map((c) => c.id));

    // 处理每个章节
    for (const chapterInput of chapters) {
      const { id, lessons, _action, ...chapterData } = chapterInput;

      if (_action === "delete" && id) {
        // 删除章节
        await this.chaptersService.deleteChapter(id);
      } else if (id && existingChapterIds.has(id)) {
        // 更新现有章节
        await this.chaptersService.updateChapter(id, chapterData);

        // 处理章节下的课时
        if (lessons) {
          await this.updateChapterLessons(id, lessons);
        }
      } else {
        // 创建新章节
        const newChapter = await this.chaptersService.createChapter(
          courseId,
          chapterData,
        );

        // 为新章节创建课时
        if (lessons && lessons.length > 0) {
          const lessonsToCreate = lessons
            .filter((lesson) => !lesson._action || lesson._action === "create")
            .map(({ _action, id, ...lessonData }) => lessonData);

          if (lessonsToCreate.length > 0) {
            await this.lessonsService.createLessons({
              chapterId: newChapter.id,
              lessons: lessonsToCreate,
            });
          }
        }
      }
    }
  }

  /**
   * 处理章节下课时的批量更新
   */
  private async updateChapterLessons(
    chapterId: string,
    lessons: UpdateLessonWithIdInput[],
  ): Promise<void> {
    // 获取现有课时
    const existingLessons =
      await this.lessonsService.getLessonsByChapter(chapterId);
    const existingLessonIds = new Set(existingLessons.map((l) => l.id));

    // 分类处理课时
    const lessonsToCreate: CreateLessonInput[] = [];
    const lessonsToUpdate: Array<{
      id: string;
      data: Partial<CreateLessonInput>;
    }> = [];
    const lessonsToDelete: string[] = [];

    for (const lessonInput of lessons) {
      const { id, _action, ...lessonData } = lessonInput;

      if (_action === "delete" && id) {
        lessonsToDelete.push(id);
      } else if (id && existingLessonIds.has(id)) {
        lessonsToUpdate.push({ id, data: lessonData });
      } else {
        lessonsToCreate.push(lessonData);
      }
    }

    // 执行批量操作
    if (lessonsToDelete.length > 0) {
      await this.lessonsService.deleteLessonsByIds(lessonsToDelete);
    }

    if (lessonsToCreate.length > 0) {
      await this.lessonsService.createLessons({
        chapterId,
        lessons: lessonsToCreate,
      });
    }

    for (const { id, data } of lessonsToUpdate) {
      await this.lessonsService.updateLesson(id, data);
    }
  }

  // ========== 复制和模板 ==========

  /**
   * 复制整个课程（包含所有内容）
   */
  async duplicateCourse(
    sourceId: string,
    newTitle: string,
    includeContent: boolean = true,
  ): Promise<CourseWithFullContent> {
    const sourceCourse = await this.getCourseWithFullContent(sourceId);

    if (!sourceCourse) {
      throw new Error("源课程不存在");
    }

    // 准备新课程数据
    const newCourseData: CreateCourseInput = {
      title: newTitle,
      subtitle: sourceCourse.subtitle ?? null,
      description: sourceCourse.description,
      cover: sourceCourse.cover,
      categoryId: sourceCourse.categoryId,
      level: sourceCourse.level,
      price: sourceCourse.price.toNumber(),
      originalPrice: sourceCourse.originalPrice?.toNumber() ?? null,
      isFree: sourceCourse.isFree,
      status: sourceCourse.status ?? null,
      requireLogin: sourceCourse.requireLogin,
      instructorName: sourceCourse.instructorName,
      instructorTitle: sourceCourse.instructorTitle ?? null,
      instructorAvatar: sourceCourse.instructorAvatar ?? null,
      instructorBio: sourceCourse.instructorBio ?? null,
      previewVideo: sourceCourse.previewVideo ?? null,
      createdById: sourceCourse.createdById,
      tags: [...sourceCourse.tags],
      metaTitle: sourceCourse.metaTitle ?? null,
      metaDescription: sourceCourse.metaDescription ?? null,
      metaKeywords: [...sourceCourse.metaKeywords],
    };

    if (!includeContent) {
      // 仅复制课程基本信息
      const newCourse = await this.coursesService.createCourse(newCourseData);
      const result = await this.getCourseWithFullContent(newCourse.id);
      if (!result) {
        throw new Error("复制课程后无法获取完整信息");
      }
      return result;
    }

    // 复制完整内容
    const chaptersInput: CreateChapterWithLessonsInput[] =
      sourceCourse.chapters.map((chapter) => ({
        title: chapter.title,
        description: chapter.description,
        order: chapter.order,
        lessons: chapter.lessons.map((lesson) => ({
          title: lesson.title,
          description: lesson.description,
          videoUrl: lesson.videoUrl,
          videoDuration: lesson.videoDuration,
          videoSize: lesson.videoSize,
          order: lesson.order,
          isFree: lesson.isFree,
        })),
      }));

    return this.createCourseWithContent({
      ...newCourseData,
      chapters: chaptersInput,
    });
  }

  /**
   * 从模板创建课程
   */
  async createCourseFromTemplate(
    templateId: string,
    courseData: CreateCourseInput,
  ): Promise<CourseWithFullContent> {
    const template = await this.getCourseWithFullContent(templateId);

    if (!template) {
      throw new Error("模板课程不存在");
    }

    // 使用模板的结构但替换为新的课程数据
    const chaptersInput: CreateChapterWithLessonsInput[] =
      template.chapters.map((chapter) => ({
        title: chapter.title,
        description: chapter.description,
        order: chapter.order,
        lessons: chapter.lessons.map((lesson) => ({
          title: lesson.title,
          description: lesson.description,
          videoUrl: "", // 模板中视频URL需要重新设置
          videoDuration: lesson.videoDuration,
          videoSize: lesson.videoSize,
          order: lesson.order,
          isFree: lesson.isFree,
        })),
      }));

    return this.createCourseWithContent({
      ...courseData,
      chapters: chaptersInput,
    });
  }

  // ========== 完整查询 ==========

  /**
   * 获取包含完整内容的课程
   */
  async getCourseWithFullContent(
    id: string,
  ): Promise<CourseWithFullContent | null> {
    const course = await this.prisma.course.findUnique({
      where: { id },
      include: {
        category: true,
        chapters: {
          include: {
            lessons: {
              orderBy: { order: "asc" },
            },
          },
          orderBy: { order: "asc" },
        },
        _count: {
          select: {
            chapters: true,
            enrollments: true,
          },
        },
      },
    });

    if (!course) {
      return null;
    }

    // 计算统计信息
    const stats = await this.calculateCourseStats(course);

    return {
      ...course,
      stats,
    };
  }

  /**
   * 计算课程统计信息
   */
  private async calculateCourseStats(course: any): Promise<CourseContentStats> {
    const totalChapters = course.chapters.length;
    const totalLessons = course.chapters.reduce(
      (sum: number, chapter: any) => sum + chapter.lessons.length,
      0,
    );
    const totalDuration = course.chapters.reduce(
      (sum: number, chapter: any) =>
        sum +
        chapter.lessons.reduce(
          (chapterSum: number, lesson: any) =>
            chapterSum + lesson.videoDuration,
          0,
        ),
      0,
    );
    const freeLessons = course.chapters.reduce(
      (sum: number, chapter: any) =>
        sum + chapter.lessons.filter((lesson: any) => lesson.isFree).length,
      0,
    );
    const paidLessons = totalLessons - freeLessons;
    const averageChapterLength =
      totalChapters > 0 ? totalLessons / totalChapters : 0;
    const averageLessonDuration =
      totalLessons > 0 ? totalDuration / totalLessons : 0;

    // 计算完成率（基于报名用户的完成情况）
    const enrollments = await this.prisma.courseEnrollment.findMany({
      where: { courseId: course.id },
    });
    const completedEnrollments = enrollments.filter(
      (e) => e.completedAt,
    ).length;
    const completionRate =
      enrollments.length > 0
        ? (completedEnrollments / enrollments.length) * 100
        : 0;

    return {
      totalChapters,
      totalLessons,
      totalDuration,
      freeLessons,
      paidLessons,
      averageChapterLength: Math.round(averageChapterLength * 100) / 100,
      averageLessonDuration: Math.round(averageLessonDuration),
      completionRate: Math.round(completionRate * 100) / 100,
    };
  }

  // ========== 验证和修复 ==========

  /**
   * 验证课程内容完整性
   */
  async validateCourseContent(id: string): Promise<ValidationResult> {
    const course = await this.getCourseWithFullContent(id);

    if (!course) {
      throw new Error("课程不存在");
    }

    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const suggestions: string[] = [];

    // 验证课程基本信息
    this.validateCourseBasicInfo(course, errors, warnings);

    // 验证章节
    this.validateChapters(course.chapters, errors, warnings, suggestions);

    // 验证课时
    this.validateLessons(course.chapters, errors, warnings, suggestions);

    // 生成改进建议
    this.generateSuggestions(course, suggestions);

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
    };
  }

  /**
   * 验证课程基本信息
   */
  private validateCourseBasicInfo(
    course: CourseWithFullContent,
    errors: ValidationError[],
    warnings: ValidationWarning[],
  ): void {
    if (!course.title || course.title.trim().length === 0) {
      errors.push({
        type: "COURSE",
        field: "title",
        message: "课程标题不能为空",
        targetId: course.id,
      });
    }

    if (!course.description || course.description.trim().length < 10) {
      errors.push({
        type: "COURSE",
        field: "description",
        message: "课程描述至少需要10个字符",
        targetId: course.id,
      });
    }

    if (!course.cover) {
      warnings.push({
        type: "COURSE",
        field: "cover",
        message: "建议添加课程封面图片",
        targetId: course.id,
      });
    }

    if (!course.instructorName) {
      errors.push({
        type: "COURSE",
        field: "instructorName",
        message: "必须指定讲师姓名",
        targetId: course.id,
      });
    }

    if (course.chapters.length === 0) {
      errors.push({
        type: "COURSE",
        field: "chapters",
        message: "课程至少需要包含一个章节",
        targetId: course.id,
      });
    }
  }

  /**
   * 验证章节
   */
  private validateChapters(
    chapters: any[],
    errors: ValidationError[],
    warnings: ValidationWarning[],
    suggestions: string[],
  ): void {
    if (chapters.length === 0) {
      return;
    }

    // 检查章节顺序
    const orders = chapters.map((c) => c.order);
    const expectedOrders = chapters.map((_, index) => index);
    if (!SharedUtils.arraysEqual(orders, expectedOrders)) {
      errors.push({
        type: "CHAPTER",
        field: "order",
        message: "章节排序不连续",
      });
    }

    // 检查每个章节
    chapters.forEach((chapter, index) => {
      if (!chapter.title || chapter.title.trim().length === 0) {
        errors.push({
          type: "CHAPTER",
          field: "title",
          message: `第${index + 1}章缺少标题`,
          targetId: chapter.id,
        });
      }

      if (chapter.lessons.length === 0) {
        warnings.push({
          type: "CHAPTER",
          field: "lessons",
          message: `第${index + 1}章"${chapter.title}"没有课时`,
          targetId: chapter.id,
        });
      }
    });

    // 检查重复标题
    const titles = chapters.map((c) => c.title.toLowerCase().trim());
    const duplicates = titles.filter(
      (title, index) => titles.indexOf(title) !== index,
    );
    if (duplicates.length > 0) {
      warnings.push({
        type: "CHAPTER",
        field: "title",
        message: "存在重复的章节标题",
      });
    }
  }

  /**
   * 验证课时
   */
  private validateLessons(
    chapters: any[],
    errors: ValidationError[],
    warnings: ValidationWarning[],
    suggestions: string[],
  ): void {
    let totalLessons = 0;
    let lessonsWithoutVideo = 0;

    chapters.forEach((chapter, chapterIndex) => {
      const lessons = chapter.lessons || [];
      totalLessons += lessons.length;

      // 检查课时顺序
      const orders = lessons.map((l: any) => l.order);
      const expectedOrders = lessons.map((_: any, index: number) => index);
      if (!SharedUtils.arraysEqual(orders, expectedOrders)) {
        errors.push({
          type: "LESSON",
          field: "order",
          message: `第${chapterIndex + 1}章的课时排序不连续`,
        });
      }

      // 检查每个课时
      lessons.forEach((lesson: any, lessonIndex: number) => {
        const lessonLabel = `第${chapterIndex + 1}章第${lessonIndex + 1}课时`;

        if (!lesson.title || lesson.title.trim().length === 0) {
          errors.push({
            type: "LESSON",
            field: "title",
            message: `${lessonLabel}缺少标题`,
            targetId: lesson.id,
          });
        }

        if (!lesson.videoUrl || lesson.videoUrl.trim().length === 0) {
          errors.push({
            type: "LESSON",
            field: "videoUrl",
            message: `${lessonLabel}"${lesson.title}"缺少视频地址`,
            targetId: lesson.id,
          });
          lessonsWithoutVideo++;
        }

        if (lesson.videoDuration <= 0) {
          errors.push({
            type: "LESSON",
            field: "videoDuration",
            message: `${lessonLabel}"${lesson.title}"视频时长无效`,
            targetId: lesson.id,
          });
        }

        if (lesson.videoDuration > 7200) {
          // 2小时
          warnings.push({
            type: "LESSON",
            field: "videoDuration",
            message: `${lessonLabel}"${lesson.title}"视频时长过长，建议拆分`,
            targetId: lesson.id,
          });
        }
      });
    });

    if (totalLessons === 0) {
      errors.push({
        type: "LESSON",
        field: "count",
        message: "课程至少需要包含一个课时",
      });
    }
  }

  /**
   * 生成改进建议
   */
  private generateSuggestions(
    course: CourseWithFullContent,
    suggestions: string[],
  ): void {
    // 基于统计数据生成建议
    if (course.stats.totalLessons < 5) {
      suggestions.push("建议增加更多课时内容，提高课程价值");
    }

    if (course.stats.totalDuration < 1800) {
      // 30分钟
      suggestions.push("课程总时长较短，建议增加更多详细内容");
    }

    if (course.stats.freeLessons === 0) {
      suggestions.push("建议提供一些免费试看课时，吸引更多学生");
    }

    if (course.stats.averageChapterLength > 10) {
      suggestions.push("部分章节课时过多，建议拆分为更小的章节");
    }

    if (!course.previewVideo) {
      suggestions.push("建议添加课程预览视频，提高转化率");
    }

    if (course.tags.length === 0) {
      suggestions.push("建议添加相关标签，提高课程可发现性");
    }
  }

  /**
   * 修复课程内容顺序
   */
  async repairCourseOrder(id: string): Promise<void> {
    const course = await this.getCourseWithFullContent(id);

    if (!course) {
      throw new Error("课程不存在");
    }

    await this.prisma.$transaction(async (tx) => {
      // 修复章节顺序
      const chapters = course.chapters.sort((a, b) => a.order - b.order);
      for (let i = 0; i < chapters.length; i++) {
        await tx.courseChapter.update({
          where: { id: chapters[i].id },
          data: { order: i },
        });

        // 修复该章节下的课时顺序
        const lessons = chapters[i].lessons.sort((a, b) => a.order - b.order);
        for (let j = 0; j < lessons.length; j++) {
          await tx.courseLesson.update({
            where: { id: lessons[j].id },
            data: { order: j },
          });
        }
      }
    });
  }

  // ========== 导入导出 ==========

  /**
   * 导入课程内容
   */
  async importCourseContent(
    courseId: string,
    content: ImportContentInput,
  ): Promise<CourseWithFullContent | null> {
    const { chapters, replaceExisting = false } = content;

    const course = await this.coursesService.getCourse(courseId);
    if (!course) {
      throw new Error("课程不存在");
    }

    return this.prisma.$transaction(async (tx) => {
      // 如果要替换现有内容，先删除所有章节和课时
      if (replaceExisting) {
        const existingChapters =
          await this.chaptersService.getChaptersByCourse(courseId);
        if (existingChapters.length > 0) {
          await this.chaptersService.deleteChaptersByIds(
            existingChapters.map((c) => c.id),
          );
        }
      }

      // 导入新内容
      for (let i = 0; i < chapters.length; i++) {
        const chapterInput = chapters[i];
        const { lessons, ...chapterData } = chapterInput;

        // 创建章节
        const chapter = await this.chaptersService.createChapter(courseId, {
          ...chapterData,
          order: i,
        });

        // 创建课时
        if (lessons && lessons.length > 0) {
          const lessonsWithOrder = lessons.map((lesson, index) => ({
            ...lesson,
            order: index,
            isFree: lesson.isFree ?? false,
          }));

          await this.lessonsService.createLessons({
            chapterId: chapter.id,
            lessons: lessonsWithOrder,
          });
        }
      }

      return this.getCourseWithFullContent(courseId);
    });
  }

  /**
   * 导出课程内容
   */
  async exportCourseContent(id: string): Promise<ExportContentResult> {
    const course = await this.getCourseWithFullContent(id);

    if (!course) {
      throw new Error("课程不存在");
    }

    const chapters: ExportChapterResult[] = course.chapters.map((chapter) => ({
      title: chapter.title,
      description: chapter.description,
      order: chapter.order,
      lessons: chapter.lessons.map((lesson) => ({
        title: lesson.title,
        description: lesson.description,
        videoDuration: lesson.videoDuration,
        isFree: lesson.isFree,
        order: lesson.order,
      })),
    }));

    return {
      course: {
        id: course.id,
        title: course.title,
        description: course.description,
        level: course.level,
        instructorName: course.instructorName,
      },
      chapters,
      stats: course.stats,
      exportedAt: new Date(),
    };
  }

  // ========== 私有工具方法 ==========

  /**
   * 获取课程的完整统计信息
   */
  async getCourseAnalytics(id: string): Promise<{
    content: CourseContentStats;
    performance: {
      enrollmentCount: number;
      completionRate: number;
      averageRating: number;
      revenue: number;
    };
    engagement: {
      mostPopularChapter: string;
      mostPopularLesson: string;
      averageWatchTime: number;
    };
  }> {
    const course = await this.getCourseWithFullContent(id);

    if (!course) {
      throw new Error("课程不存在");
    }

    // 获取报名和完成数据
    const enrollments = await this.prisma.courseEnrollment.findMany({
      where: { courseId: id },
    });

    // 获取评价数据
    const reviews = await this.prisma.courseReview.findMany({
      where: { courseId: id },
    });

    // 获取学习进度数据
    const progress = await this.prisma.lessonProgress.findMany({
      where: {
        lesson: {
          chapter: {
            courseId: id,
          },
        },
      },
      include: {
        lesson: {
          include: {
            chapter: true,
          },
        },
      },
    });

    // 计算性能数据
    const enrollmentCount = enrollments.length;
    const completedCount = enrollments.filter((e) => e.completedAt).length;
    const completionRate =
      enrollmentCount > 0 ? (completedCount / enrollmentCount) * 100 : 0;
    const averageRating =
      reviews.length > 0
        ? reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length
        : 0;
    const revenue = enrollments.reduce(
      (sum, e) => sum + ((e.paidAmount as unknown as number) || 0),
      0,
    );

    // 计算参与度数据
    const chapterProgress = new Map<string, number>();
    const lessonProgress = new Map<string, number>();
    let totalWatchTime = 0;

    progress.forEach((p) => {
      const chapterId = p.lesson.chapterId;
      const lessonId = p.lesson.id;

      chapterProgress.set(chapterId, (chapterProgress.get(chapterId) || 0) + 1);
      lessonProgress.set(lessonId, (lessonProgress.get(lessonId) || 0) + 1);
      totalWatchTime += p.watchedDuration;
    });

    const mostPopularChapterId = Array.from(chapterProgress.entries()).sort(
      ([, a], [, b]) => b - a,
    )[0]?.[0];
    const mostPopularLessonId = Array.from(lessonProgress.entries()).sort(
      ([, a], [, b]) => b - a,
    )[0]?.[0];

    const mostPopularChapter =
      course.chapters.find((c) => c.id === mostPopularChapterId)?.title || "";
    const mostPopularLesson =
      course.chapters
        .flatMap((c) => c.lessons)
        .find((l) => l.id === mostPopularLessonId)?.title || "";

    const averageWatchTime =
      progress.length > 0 ? totalWatchTime / progress.length : 0;

    return {
      content: course.stats,
      performance: {
        enrollmentCount,
        completionRate: Math.round(completionRate * 100) / 100,
        averageRating: Math.round(averageRating * 100) / 100,
        revenue,
      },
      engagement: {
        mostPopularChapter,
        mostPopularLesson,
        averageWatchTime: Math.round(averageWatchTime),
      },
    };
  }
}
