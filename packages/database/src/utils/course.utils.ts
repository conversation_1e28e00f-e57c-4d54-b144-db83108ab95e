import { CourseLevel, CourseStatus } from "../generated/prisma";
import { CourseWithFullContent, ValidationResult } from "../services";

/**
 * 共享工具函数
 */
export class SharedUtils {
  /**
   * 比较两个数组是否相等
   */
  static arraysEqual<T>(a: T[], b: T[]): boolean {
    if (a.length !== b.length) return false;
    return a.every((val, index) => val === b[index]);
  }

  /**
   * 生成URL友好的slug
   */
  static generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, "") // 移除特殊字符
      .replace(/\s+/g, "-") // 空格替换为连字符
      .replace(/-+/g, "-") // 多个连字符合并为一个
      .trim();
  }

  /**
   * 验证实体是否存在
   */
  static validateExists(entity: any, entityName: string): void {
    if (!entity) {
      throw new Error(`${entityName}不存在`);
    }
  }

  /**
   * 获取下一个order值
   */
  static getNextOrder(items: Array<{ order: number }>): number {
    if (items.length === 0) return 0;
    const maxOrder = Math.max(...items.map(item => item.order));
    return maxOrder + 1;
  }

  /**
   * 重新排序删除后的项目
   */
  static async reorderAfterDelete(
    updateFn: (where: any, data: any) => Promise<any>,
    where: any,
    deletedOrder: number
  ): Promise<void> {
    await updateFn(
      {
        ...where,
        order: { gt: deletedOrder },
      },
      {
        order: { decrement: 1 },
      }
    );
  }

  /**
   * 批量删除相关数据
   */
  static async batchDelete(
    deleteFn: (where: any) => Promise<any>,
    where: any
  ): Promise<void> {
    await deleteFn(where);
  }
}

/**
 * 课程相关的工具函数
 */
export class CourseUtils extends SharedUtils {
  /**
   * 格式化课程时长（秒转换为可读格式）
   */
  static formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟${secs}秒`;
    } else {
      return `${secs}秒`;
    }
  }

  /**
   * 格式化详细时长（包含秒）
   */
  static formatDetailedDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    }
    return `${minutes}:${secs.toString().padStart(2, "0")}`;
  }

  /**
   * 生成课程的URL slug
   */
  static generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, "") // 移除特殊字符
      .replace(/\s+/g, "-") // 空格替换为连字符
      .replace(/-+/g, "-") // 多个连字符合并为一个
      .trim();
  }

  /**
   * 获取课程级别的中文描述
   */
  static getLevelLabel(level: CourseLevel): string {
    const labels: Record<string, string> = {
      BEGINNER: "初级",
      INTERMEDIATE: "中级",
      ADVANCED: "高级",
      ALL: "全部",
    };
    return labels[level] || "未知";
  }

  /**
   * 获取课程状态的中文描述
   */
  static getStatusLabel(status: CourseStatus): string {
    const labels: Record<string, string> = {
      DRAFT: "草稿",
      PUBLISHED: "已发布",
      REVIEWING: "审核中",
    };
    return labels[status] || "未知";
  }

  /**
   * 获取课程状态的颜色（用于UI显示）
   */
  static getStatusColor(status: CourseStatus): string {
    const colors: Record<string, string> = {
      DRAFT: "gray",
      PUBLISHED: "green",
      REVIEWING: "blue",
    };
    return colors[status] || "gray";
  }

  /**
   * 计算课程的总时长
   */
  static calculateTotalDuration(course: CourseWithFullContent): number {
    return course.chapters.reduce((total, chapter) => {
      return (
        total +
        chapter.lessons.reduce((chapterTotal, lesson) => {
          return chapterTotal + lesson.videoDuration;
        }, 0)
      );
    }, 0);
  }

  /**
   * 计算课程的免费课时数量
   */
  static calculateFreeLessons(course: CourseWithFullContent): number {
    return course.chapters.reduce((total, chapter) => {
      return total + chapter.lessons.filter((lesson) => lesson.isFree).length;
    }, 0);
  }

  /**
   * 检查课程是否可以发布
   */
  static canPublish(course: CourseWithFullContent): {
    canPublish: boolean;
    reasons: string[];
  } {
    const reasons: string[] = [];

    // 检查基本信息
    if (!course.title) reasons.push("课程标题不能为空");
    if (!course.description) reasons.push("课程描述不能为空");
    if (!course.cover) reasons.push("课程封面不能为空");
    if (!course.instructorName) reasons.push("讲师姓名不能为空");

    // 检查内容
    if (course.chapters.length === 0) {
      reasons.push("课程至少需要包含一个章节");
    } else {
      const hasLessons = course.chapters.some(
        (chapter) => chapter.lessons.length > 0,
      );
      if (!hasLessons) {
        reasons.push("课程至少需要包含一个课时");
      }
    }

    return {
      canPublish: reasons.length === 0,
      reasons,
    };
  }

  /**
   * 生成课程的SEO友好描述
   */
  static generateSeoDescription(
    course: CourseWithFullContent,
    maxLength: number = 160,
  ): string {
    const stats = course.stats;
    const levelLabel = this.getLevelLabel(course.level);
    const duration = this.formatDuration(stats.totalDuration);

    let description = `${course.title} - ${levelLabel}课程，共${stats.totalChapters}章${stats.totalLessons}课时，总时长${duration}。`;

    if (course.description) {
      const remainingLength = maxLength - description.length;
      if (remainingLength > 0) {
        const truncatedDesc = course.description.substring(
          0,
          remainingLength - 3,
        );
        description +=
          truncatedDesc +
          (course.description.length > truncatedDesc.length ? "..." : "");
      }
    }

    return description.substring(0, maxLength);
  }

  /**
   * 验证课程数据的完整性
   */
  static validateCourseData(course: CourseWithFullContent): ValidationResult {
    const errors: any[] = [];
    const warnings: any[] = [];
    const suggestions: string[] = [];

    // 基本信息验证
    if (!course.title) {
      errors.push({
        type: "COURSE",
        field: "title",
        message: "课程标题不能为空",
      });
    }

    if (!course.description || course.description.length < 10) {
      errors.push({
        type: "COURSE",
        field: "description",
        message: "课程描述至少需要10个字符",
      });
    }

    // 内容验证
    if (course.chapters.length === 0) {
      errors.push({
        type: "COURSE",
        field: "chapters",
        message: "课程至少需要包含一个章节",
      });
    }

    // 生成建议
    if (course.stats.totalLessons < 5) {
      suggestions.push("建议增加更多课时内容");
    }

    if (course.stats.freeLessons === 0) {
      suggestions.push("建议提供一些免费试看课时");
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
    };
  }

  /**
   * 计算课程的价值评分（用于推荐算法）
   */
  static calculateCourseScore(course: CourseWithFullContent): number {
    let score = 0;

    // 内容丰富度 (40%)
    const contentScore = Math.min(course.stats.totalLessons / 20, 1) * 40;
    score += contentScore;

    // 时长合理性 (20%)
    const durationScore =
      course.stats.totalDuration > 1800
        ? 20
        : (course.stats.totalDuration / 1800) * 20;
    score += durationScore;

    // 免费内容比例 (20%)
    const freeRatio = course.stats.freeLessons / course.stats.totalLessons;
    const freeScore = freeRatio > 0.1 && freeRatio < 0.3 ? 20 : 10;
    score += freeScore;

    // 信息完整度 (20%)
    let infoScore = 0;
    if (course.cover) infoScore += 5;
    if (course.previewVideo) infoScore += 5;
    if (course.instructorAvatar) infoScore += 5;
    if (course.tags.length > 0) infoScore += 5;
    score += infoScore;

    return Math.round(score);
  }

  /**
   * 生成课程的标签建议
   */
  static suggestTags(course: CourseWithFullContent): string[] {
    const suggestions: string[] = [];

    // 基于级别
    suggestions.push(this.getLevelLabel(course.level));

    // 基于分类
    if (course.category) {
      suggestions.push(course.category.name);
    }

    // 基于内容长度
    if (course.stats.totalDuration > 7200) {
      // 2小时
      suggestions.push("深度学习");
    } else if (course.stats.totalDuration < 1800) {
      // 30分钟
      suggestions.push("快速入门");
    }

    // 基于价格
    if (course.isFree) {
      suggestions.push("免费课程");
    } else if (course.price.toNumber() < 100) {
      suggestions.push("经济实惠");
    }

    return suggestions;
  }

  /**
   * 格式化价格显示
   */
  static formatPrice(price: number, originalPrice?: number): string {
    if (price === 0) {
      return "免费";
    }

    const priceStr = `¥${price}`;

    if (originalPrice && originalPrice > price) {
      const discount = Math.round((1 - price / originalPrice) * 100);
      return `${priceStr} (原价¥${originalPrice}，${discount}折)`;
    }

    return priceStr;
  }

  /**
   * 检查课程内容的质量
   */
  static assessContentQuality(course: CourseWithFullContent): {
    score: number;
    level: "poor" | "fair" | "good" | "excellent";
    feedback: string[];
  } {
    const feedback: string[] = [];
    let score = 0;

    // 检查章节数量 (25分)
    if (course.stats.totalChapters >= 5) {
      score += 25;
    } else if (course.stats.totalChapters >= 3) {
      score += 15;
      feedback.push("建议增加更多章节来组织内容");
    } else {
      score += 5;
      feedback.push("章节数量过少，建议至少包含3个章节");
    }

    // 检查课时数量 (25分)
    if (course.stats.totalLessons >= 20) {
      score += 25;
    } else if (course.stats.totalLessons >= 10) {
      score += 15;
      feedback.push("可以考虑增加更多课时内容");
    } else {
      score += 5;
      feedback.push("课时数量较少，建议增加到至少10个课时");
    }

    // 检查总时长 (25分)
    if (course.stats.totalDuration >= 7200) {
      // 2小时
      score += 25;
    } else if (course.stats.totalDuration >= 3600) {
      // 1小时
      score += 15;
      feedback.push("课程时长适中，可以考虑增加更多深度内容");
    } else {
      score += 5;
      feedback.push("课程时长较短，建议增加更多详细内容");
    }

    // 检查结构合理性 (25分)
    const avgLessonsPerChapter = course.stats.averageChapterLength;
    if (avgLessonsPerChapter >= 3 && avgLessonsPerChapter <= 8) {
      score += 25;
    } else if (avgLessonsPerChapter >= 2 && avgLessonsPerChapter <= 10) {
      score += 15;
      feedback.push("章节结构基本合理，可以进一步优化");
    } else {
      score += 5;
      feedback.push("建议调整章节结构，每章3-8个课时比较合适");
    }

    // 确定质量等级
    let level: "poor" | "fair" | "good" | "excellent";
    if (score >= 80) {
      level = "excellent";
    } else if (score >= 60) {
      level = "good";
    } else if (score >= 40) {
      level = "fair";
    } else {
      level = "poor";
    }

    return { score, level, feedback };
  }
}

/**
 * 常量定义
 */
export const COURSE_CONSTANTS = {
  MAX_TITLE_LENGTH: 100,
  MAX_DESCRIPTION_LENGTH: 2000,
  MAX_CHAPTER_TITLE_LENGTH: 100,
  MAX_LESSON_TITLE_LENGTH: 100,
  MIN_DESCRIPTION_LENGTH: 10,
  MAX_VIDEO_DURATION: 14400, // 4小时
  MAX_TAGS: 10,
  MAX_KEYWORDS: 20,
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
} as const;

/**
 * 正则表达式常量
 */
export const COURSE_PATTERNS = {
  SLUG: /^[a-z0-9-]+$/,
  VIDEO_URL: /^https?:\/\/.+\.(mp4|avi|mov|wmv|flv|webm)$/i,
  IMAGE_URL: /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i,
} as const;
