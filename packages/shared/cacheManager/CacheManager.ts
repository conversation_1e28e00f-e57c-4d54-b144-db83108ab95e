/**
 * 缓存管理器
 * 用于管理认证结果的缓存，支持TTL（生存时间）
 */
export class CacheManager {
  private cache = new Map<
    string,
    { value: boolean; timestamp: number; ttl: number }
  >();

  /**
   * 设置缓存项
   * @param key 缓存键
   * @param value 缓存值
   * @param ttl 生存时间（毫秒），默认5分钟
   */
  set(key: string, value: boolean, ttl: number = 5 * 60 * 1000) {
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl,
    });
  }

  /**
   * 获取缓存项
   * @param key 缓存键
   * @returns 缓存值或null（如果不存在或已过期）
   */
  get(key: string): boolean | null {
    const item = this.cache.get(key);
    if (!item) return null;

    const now = Date.now();
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.value;
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.cache.clear();
  }

  /**
   * 删除指定的缓存项
   * @param key 缓存键
   */
  delete(key: string) {
    this.cache.delete(key);
  }

  /**
   * 获取缓存大小
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * 清理过期的缓存项
   */
  cleanup() {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// 全局缓存实例
export const globalCacheManager = new CacheManager();

