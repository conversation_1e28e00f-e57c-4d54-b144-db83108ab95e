{"name": "@workspace/shared", "version": "0.0.0", "type": "module", "private": true, "exports": {"./index": {"types": "./index.d.ts", "import": "./index.js"}}, "main": "index.js", "types": "index.d.ts", "devDependencies": {"@next/eslint-plugin-next": "^15.1.7", "@typescript-eslint/eslint-plugin": "^8.24.1", "@typescript-eslint/parser": "^8.24.1", "eslint": "^9.20.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-turbo": "^2.4.2", "globals": "^15.15.0", "typescript": "^5.7.3", "typescript-eslint": "^8.24.1"}}