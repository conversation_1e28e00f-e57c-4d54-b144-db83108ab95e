import { AuthComposerBuilder } from "./AuthComposerBuilder.js";
import { LogicType } from "./AuthComposerBuilder.js";

// 定义用户类型
type User = {
  role: string;
  permissions: string[];
  active: boolean;
};

// 示例认证函数
const isAdmin = async (user?: User) => {
  console.log("检查管理员权限");
  return user?.role === "admin";
};

const hasPermission = async (user?: User) => {
  console.log("检查特定权限");
  return user?.permissions.includes("read") || false;
};

const isActive = async (user?: User) => {
  console.log("检查用户是否激活");
  return user?.active || false;
};

// 使用示例
async function example() {
  const user = {
    role: "admin",
    permissions: ["read", "write"],
    active: true,
  };

  // AND 逻辑示例 - 需要同时满足所有条件
  console.log("=== AND 逻辑示例 ===");
  const andResult = await AuthComposerBuilder.create(user)
    .add(isAdmin, hasPermission, isActive)
    .cache(60000) // 缓存1分钟
    .execute(LogicType.AND);

  console.log("AND 结果:", andResult);

  // OR 逻辑示例 - 满足任一条件即可
  console.log("\n=== OR 逻辑示例 ===");
  const orResult = await AuthComposerBuilder.create(user)
    .add(isAdmin, hasPermission)
    .cache()
    .execute(LogicType.OR);

  console.log("OR 结果:", orResult);

  // 测试缓存效果
  console.log("\n=== 测试缓存效果 ===");
  const cachedResult = await AuthComposerBuilder.create(user)
    .add(isAdmin, hasPermission)
    .cache()
    .execute(LogicType.OR);

  console.log("缓存结果:", cachedResult);
}

// 运行示例
example().catch(console.error);
