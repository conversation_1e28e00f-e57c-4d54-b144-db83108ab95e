import { globalCacheManager } from "../cacheManager/CacheManager.js";

export type AuthFunction<T = any> = (data?: T) => boolean | Promise<boolean>;
export enum LogicType {
  AND = "AND",
  OR = "OR",
}

// 生成缓存键
const generateCacheKey = (
  functions: AuthFunction[],
  context: any,
  logicType: LogicType,
): string => {
  const functionNames = functions.map(
    (fn) => fn.name || fn.toString().slice(0, 50),
  );
  const contextHash = JSON.stringify(context);
  return `auth_${logicType}_${functionNames.join("_")}_${contextHash}`;
};

// 创建认证组合器
const createAuthComposer = <T = any>(
  logicType: LogicType,
  functions: AuthFunction<T>[],
  options: { cache?: boolean; cacheTTL?: number; shortCircuit?: boolean } = {},
) => {
  return async (context?: T): Promise<boolean> => {
    const {
      cache = false,
      cacheTTL = 5 * 60 * 1000,
      shortCircuit = false,
    } = options;

    // 如果启用缓存，先检查缓存
    if (cache) {
      const cacheKey = generateCacheKey(functions, context, logicType);
      const cachedResult = globalCacheManager.get(cacheKey);
      if (cachedResult !== null) {
        return cachedResult;
      }
    }

    let result: boolean;

    if (logicType === LogicType.AND) {
      // AND逻辑：所有函数都必须返回true，短路优化
      result = true;
      for (const fn of functions) {
        const fnResult = await fn(context);
        if (!fnResult) {
          // 短路：一旦有一个函数返回false，立即返回false
          result = false;
          if (shortCircuit) {
            break;
          }
        }
      }
    } else {
      // OR逻辑：至少一个函数返回true，短路优化
      result = false;
      for (const fn of functions) {
        const fnResult = await fn(context);
        if (fnResult) {
          // 短路：一旦有一个函数返回true，立即返回true
          result = true;
          if (shortCircuit) {
            break;
          }
        }
      }
    }

    // 如果启用缓存，保存结果
    if (cache) {
      const cacheKey = generateCacheKey(functions, context, logicType);
      globalCacheManager.set(cacheKey, result, cacheTTL);
    }

    return result;
  };
};

export class AuthComposerBuilder<T = any> {
  private functions: AuthFunction<T>[] = [];
  private options: { cache?: boolean; cacheTTL?: number } = {};

  constructor(private initialContext?: T) {}

  static create<T = any>(context?: T) {
    return new AuthComposerBuilder<T>(context);
  }

  add(...fns: AuthFunction<T>[]) {
    this.functions.push(...fns);
    return this;
  }

  cache(ttl?: number) {
    this.options.cache = true;
    if (ttl) this.options.cacheTTL = ttl;
    return this;
  }

  async execute(logicType: LogicType = LogicType.AND): Promise<boolean> {
    const composer = createAuthComposer(logicType, this.functions, {
      ...this.options,
      shortCircuit: true,
    });
    return composer(this.initialContext);
  }
}
