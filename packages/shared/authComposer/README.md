# Auth Composer 认证组合器

一个功能强大的认证组合器，支持灵活的权限验证逻辑组合、缓存优化和短路求值。

## 功能特性

- 🚀 **灵活的逻辑组合**: 支持 AND 和 OR 逻辑组合多个认证函数
- ⚡ **短路求值优化**: 自动短路求值，提高执行效率
- 💾 **智能缓存系统**: 内置缓存机制，支持 TTL 设置
- 🎯 **类型安全**: 完整的 TypeScript 类型支持
- 🔧 **链式调用**: 流畅的 Builder 模式 API

## 核心组件

### 1. CacheManager - 缓存管理器

独立的缓存管理模块，支持：
- TTL（生存时间）管理
- 自动过期清理
- 缓存统计和控制

### 2. AuthComposerBuilder - 认证组合器构建器

主要的 API 入口，提供链式调用接口：

```typescript
AuthComposerBuilder.create(context)
  .add(fn1, fn2, fn3)
  .cache(ttl)
  .execute(LogicType.AND | LogicType.OR)
```

## 使用示例

### 基本用法

```typescript
import { AuthComposerBuilder, LogicType } from './AuthComposerBuilder.js';

// 定义认证函数
const isAdmin = async (user) => user?.role === 'admin';
const hasPermission = async (user) => user?.permissions.includes('read');
const isActive = async (user) => user?.active;

const user = {
  role: 'admin',
  permissions: ['read', 'write'],
  active: true
};

// AND 逻辑 - 所有条件都必须满足
const result = await AuthComposerBuilder.create(user)
  .add(isAdmin, hasPermission, isActive)
  .execute(LogicType.AND);

// OR 逻辑 - 任一条件满足即可
const result2 = await AuthComposerBuilder.create(user)
  .add(isAdmin, hasPermission)
  .execute(LogicType.OR);
```

### 启用缓存

```typescript
// 启用缓存，默认 5 分钟 TTL
const result = await AuthComposerBuilder.create(user)
  .add(isAdmin, hasPermission)
  .cache()
  .execute(LogicType.AND);

// 自定义缓存时间（毫秒）
const result2 = await AuthComposerBuilder.create(user)
  .add(isAdmin, hasPermission)
  .cache(60000) // 1分钟
  .execute(LogicType.OR);
```

## 性能优化

### 短路求值

- **AND 逻辑**: 遇到第一个返回 `false` 的函数时立即停止执行
- **OR 逻辑**: 遇到第一个返回 `true` 的函数时立即停止执行

### 缓存机制

- 基于函数组合、上下文和逻辑类型生成唯一缓存键
- 支持 TTL 自动过期
- 内存高效的 Map 存储

## API 参考

### AuthFunction

```typescript
type AuthFunction<T = any> = (data?: T) => boolean | Promise<boolean>;
```

### LogicType

```typescript
enum LogicType {
  AND = "AND",
  OR = "OR"
}
```

### AuthComposerBuilder 方法

- `create<T>(context?: T)`: 创建新的构建器实例
- `add(...fns: AuthFunction<T>[])`: 添加认证函数
- `cache(ttl?: number)`: 启用缓存，可选 TTL
- `execute(logicType: LogicType)`: 执行认证逻辑

### CacheManager 方法

- `set(key: string, value: boolean, ttl?: number)`: 设置缓存
- `get(key: string)`: 获取缓存
- `clear()`: 清空所有缓存
- `delete(key: string)`: 删除指定缓存
- `size()`: 获取缓存大小
- `cleanup()`: 清理过期缓存

## 最佳实践

1. **函数命名**: 为认证函数提供有意义的名称，便于调试和缓存键生成
2. **错误处理**: 认证函数应该处理异常并返回布尔值
3. **缓存策略**: 根据数据变化频率合理设置 TTL
4. **性能考虑**: 将耗时的认证函数放在后面，利用短路求值优化

