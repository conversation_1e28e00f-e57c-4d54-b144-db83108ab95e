# Workspace Packages

本项目包含两个核心的基础设施包：

## @workspace/database

提供 Prisma 数据库客户端和相关服务。

### 安装依赖

```bash
cd packages/database
bun install
```

### 构建

```bash
# 生成 Prisma Client 并编译 TypeScript
bun run build

# 开发模式（监听文件变化）
bun run dev
```

### 数据库操作

```bash
# 推送 schema 到数据库（不创建迁移）
bun run db:push

# 创建并运行迁移
bun run db:migrate

# 运行种子数据
bun run db:seed

# 打开 Prisma Studio
bun run studio
```

### 使用方式

```typescript
import { prisma } from "@workspace/database";
import { AdminAuthService } from "@workspace/database/services";

// 使用 Prisma Client
const users = await prisma.user.findMany();

// 使用服务
const authService = new AdminAuthService();
const token = await authService.generateToken(user);
```

## @workspace/ioredis

Redis 客户端封装，提供缓存和会话管理功能。

### 安装依赖

```bash
cd packages/ioredis
bun install
```

### 构建

```bash
# 编译 TypeScript
bun run build

# 开发模式
bun run dev
```

### 使用方式

```typescript
import { redis, redisHelpers, REDIS_KEYS, CACHE_TTL } from "@workspace/ioredis";

// 直接使用 redis 客户端
await redis.set("key", "value");
const value = await redis.get("key");

// 使用 helper 函数
await redisHelpers.setJSON(
  "user:1",
  { id: 1, name: "John" },
  CACHE_TTL.DEFAULT,
);
const user = await redisHelpers.getJSON("user:1");

// 使用预定义的键前缀
const sessionKey = `${REDIS_KEYS.SESSION}:${sessionId}`;
await redis.setex(sessionKey, CACHE_TTL.SESSION, token);
```

## 注意事项

1. **环境变量**：

   - `DATABASE_URL`：PostgreSQL 连接字符串
   - `REDIS_URL`：Redis 连接字符串（可选）

2. **开发建议**：

   - 在根目录运行 `bun install` 会自动安装所有 workspace 包
   - 修改包后需要重新构建才能在其他项目中看到更新
   - 使用 `workspace:*` 协议确保总是使用本地版本

3. **构建顺序**：
   - 先构建 `ioredis`（因为 `database` 依赖它）
   - 再构建 `database`
