{"name": "@workspace/ioredis", "version": "1.0.0", "description": "Redis client wrapper using ioredis for workspace", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"build": "npm run build:ts", "build:ts": "tsc && tsc --module esnext --outDir dist/esm --declaration false && mv dist/esm/index.js dist/index.mjs && rm -rf dist/esm", "dev": "tsc --watch", "prebuild": "<PERSON><PERSON><PERSON> dist", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["redis", "i<PERSON>is", "cache", "workspace"], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^20", "rimraf": "^5.0.10", "typescript": "^5.8.3"}, "dependencies": {"ioredis": "^5.6.1"}}