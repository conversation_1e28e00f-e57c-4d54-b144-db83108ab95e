import { Redis as RedisClient, RedisOptions } from "ioredis";

export { RedisClient, RedisOptions };

export declare const redis: RedisClient;
export declare function createRedisClient(options?: RedisOptions): RedisClient;

export declare const REDIS_KEYS: {
  SESSION: string;
  USER_PERMISSIONS: string;
  RATE_LIMIT: string;
  CACHE: string;
};

export declare const CACHE_TTL: {
  SESSION: number;
  PERMISSIONS: number;
  DEFAULT: number;
};

export declare const redisHelpers: {
  setJSON: (key: string, value: any, ttl?: number) => Promise<string | null>;
  getJSON: <T = any>(key: string) => Promise<T | null>;
  deletePattern: (pattern: string) => Promise<number>;
  exists: (key: string) => Promise<boolean>;
};
