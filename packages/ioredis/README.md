# @workspace/ioredis

Redis 客户端封装，提供了预配置的 Redis 实例和常用的工具函数。

## 安装

在 monorepo 的其他包中使用：

```json
{
  "dependencies": {
    "@workspace/ioredis": "workspace:*"
  }
}
```

## 构建

```bash
# 在 packages/ioredis 目录下
pnpm build

# 或在项目根目录
pnpm --filter @workspace/ioredis build
```

## 使用方法

### 基础使用

```typescript
import { redis } from "@workspace/ioredis";

// 使用默认的 Redis 实例
await redis.set("key", "value");
const value = await redis.get("key");
```

### 创建新实例

```typescript
import { createRedisClient } from "@workspace/ioredis";

// 创建带自定义配置的新实例
const customRedis = createRedisClient({
  host: "custom-host",
  port: 6380,
  db: 2,
});
```

### 使用类型

```typescript
import { Redis, RedisClient, RedisOptions } from "@workspace/ioredis";

// 使用 Redis 构造函数
const myRedis = new Redis({
  host: "localhost",
  port: 6379,
});

// 使用类型定义
function processWithRedis(client: RedisClient) {
  // ...
}
```

### 使用预定义的键和 TTL

```typescript
import { redis, REDIS_KEYS, CACHE_TTL } from "@workspace/ioredis";

// 使用预定义的键模板
const sessionKey = REDIS_KEYS.SESSION("user-token-123");
await redis.setex(sessionKey, CACHE_TTL.SESSION, "session-data");

// 权限缓存
const permKey = REDIS_KEYS.USER_PERMISSIONS("user-123");
await redis.setex(
  permKey,
  CACHE_TTL.PERMISSIONS,
  JSON.stringify(["read", "write"]),
);
```

### 使用助手函数

```typescript
import { redisHelpers } from "@workspace/ioredis";

// 存储 JSON 数据
await redisHelpers.setWithExpiry("user:123", { name: "John", age: 30 }, 3600);

// 获取 JSON 数据
const user = await redisHelpers.getJSON<{ name: string; age: number }>(
  "user:123",
);

// 批量获取
const values = await redisHelpers.batchGet(["key1", "key2", "key3"]);

// 删除匹配的键
await redisHelpers.deletePattern("temp:*");
```

## 环境变量

包支持以下环境变量：

- `REDIS_HOST` - Redis 主机地址（默认：localhost）
- `REDIS_PORT` - Redis 端口（默认：6379）
- `REDIS_PASSWORD` - Redis 密码（可选）
- `REDIS_DB` - Redis 数据库索引（默认：1）

## 导出内容

- `redis` - 预配置的 Redis 客户端实例
- `Redis` - ioredis 的 Redis 类
- `RedisClient` - Redis 客户端类型
- `RedisOptions` - Redis 配置选项类型
- `createRedisClient()` - 创建新 Redis 实例的工厂函数
- `REDIS_KEYS` - 预定义的键生成函数
- `CACHE_TTL` - 预定义的缓存过期时间
- `redisHelpers` - 常用的 Redis 操作助手函数

## 注意事项

1. 默认实例会自动处理连接错误和重连
2. 在生产环境请确保设置正确的环境变量
3. 助手函数中的 JSON 操作会自动处理序列化/反序列化
