services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:16-alpine
    container_name: qyqm-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: qyqm_teachers
      POSTGRES_USER: qyqm_user
      POSTGRES_PASSWORD: qyqm_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    networks:
      - qyqm-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U qyqm_user -d qyqm_teachers"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: qyqm-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass qyqm_redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - qyqm-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Redis Commander (可选的 Redis 管理界面)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: qyqm-redis-commander
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis:6379:0:qyqm_redis_password
    ports:
      - "8081:8081"
    networks:
      - qyqm-network
    depends_on:
      - redis
    profiles:
      - tools

  # pgAdmin (可选的 PostgreSQL 管理界面)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: qyqm-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - qyqm-network
    depends_on:
      - postgres
    profiles:
      - tools

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  qyqm-network:
    driver: bridge
