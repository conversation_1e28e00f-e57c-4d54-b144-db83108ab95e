{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "build:prisma": {"cache": false, "outputs": ["src/generated/**"]}, "build:ts": {"dependsOn": ["build:prisma"], "outputs": ["dist/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true, "env": ["DATABASE_URL", "REDIS_URL", "REDIS_HOST", "REDIS_PORT", "REDIS_PASSWORD", "REDIS_DB", "NODE_ENV", "JWT_SECRET", "JWT_EXPIRES_IN", "LOG_LEVEL"]}, "db:push": {"cache": false}, "db:migrate": {"cache": false}, "db:seed": {"cache": false}, "studio": {"cache": false, "persistent": true}}}