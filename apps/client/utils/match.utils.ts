/**
 * 匹配模块通用工具函数
 */

import { 
  MatchRequestStatus, 
  RequirementCategory, 
  MatchMode,
  type MatchRequest,
  type PlannerProfile,
  type MatchFilters,
  type PlannerFilters,
} from "@/types/match.types";
import { 
  CATEGORY_CONFIG, 
  STATUS_CONFIG, 
  MODE_CONFIG,
  URGENCY_LABELS,
  getUrgencyColor,
} from "@/constants/match-constants";

// ========== 数据格式化工具 ==========

/**
 * 格式化日期
 */
export function formatDate(
  dateString: string | Date, 
  options: Intl.DateTimeFormatOptions = {}
): string {
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  };
  
  const mergedOptions = { ...defaultOptions, ...options };
  const date = typeof dateString === "string" ? new Date(dateString) : dateString;
  return date.toLocaleDateString("zh-CN", mergedOptions);
}

/**
 * 格式化相对时间
 */
export function formatRelativeTime(dateString: string | Date): string {
  const date = typeof dateString === "string" ? new Date(dateString) : dateString;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) {
    return "刚刚";
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}分钟前`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}小时前`;
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}天前`;
  } else {
    return formatDate(date, { month: "short", day: "numeric" });
  }
}

/**
 * 格式化价格
 */
export function formatPrice(price?: number): string {
  if (!price || price === 0) return "面议";
  return `¥${price.toLocaleString()}`;
}

/**
 * 格式化百分比
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  return `${value.toFixed(decimals)}%`;
}

/**
 * 格式化评分
 */
export function formatRating(rating?: number): string {
  if (!rating) return "暂无评分";
  return rating.toFixed(1);
}

// ========== 状态和配置获取工具 ==========

/**
 * 获取分类配置
 */
export function getCategoryConfig(category: RequirementCategory) {
  return CATEGORY_CONFIG[category];
}

/**
 * 获取状态配置
 */
export function getStatusConfig(status: MatchRequestStatus) {
  return STATUS_CONFIG[status];
}

/**
 * 获取模式配置
 */
export function getModeConfig(mode: MatchMode) {
  return MODE_CONFIG[mode];
}

/**
 * 获取紧急程度标签
 */
export function getUrgencyLabel(urgency: number): string {
  return URGENCY_LABELS[urgency as keyof typeof URGENCY_LABELS] || "未知";
}

/**
 * 获取紧急程度配置
 */
export function getUrgencyConfig(urgency: number) {
  if (urgency <= 3) {
    return {
      label: getUrgencyLabel(urgency),
      color: "bg-green-500",
      textColor: "text-green-700",
      bgColor: "bg-green-50",
      level: "low" as const,
    };
  } else if (urgency <= 6) {
    return {
      label: getUrgencyLabel(urgency),
      color: "bg-yellow-500",
      textColor: "text-yellow-700",
      bgColor: "bg-yellow-50",
      level: "medium" as const,
    };
  } else {
    return {
      label: getUrgencyLabel(urgency),
      color: "bg-red-500",
      textColor: "text-red-700",
      bgColor: "bg-red-50",
      level: "high" as const,
    };
  }
}

// ========== 数据验证工具 ==========

/**
 * 验证匹配请求是否有效
 */
export function isValidMatchRequest(request: Partial<MatchRequest>): boolean {
  return !!(
    request.requirements?.title &&
    request.requirements?.description &&
    request.requirements?.grade &&
    request.requirements?.subjects?.length &&
    request.requirements?.location &&
    request.category &&
    request.urgency &&
    request.mode
  );
}

/**
 * 验证规划师档案是否完整
 */
export function isValidPlannerProfile(planner: Partial<PlannerProfile>): boolean {
  return !!(
    planner.specialties?.length &&
    planner.matchProfile?.preferredGrades?.length &&
    planner.matchProfile?.preferredSubjects?.length &&
    planner.matchProfile?.preferredLocations?.length
  );
}

/**
 * 检查匹配请求是否过期
 */
export function isRequestExpired(request: MatchRequest): boolean {
  if (!request.expiredAt) return false;
  return new Date(request.expiredAt) < new Date();
}

/**
 * 检查规划师是否可接单
 */
export function isPlannerAvailable(planner: PlannerProfile): boolean {
  return !!(
    planner.matchProfile?.isAcceptingMatch &&
    (!planner.matchProfile?.maxActiveMatches || 
     (planner.activeMatches || 0) < planner.matchProfile.maxActiveMatches)
  );
}

// ========== 数据处理工具 ==========

/**
 * 构建筛选参数
 */
export function buildFilterParams(filters: MatchFilters): URLSearchParams {
  const params = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      if (Array.isArray(value)) {
        value.forEach(v => params.append(key, v));
      } else {
        params.append(key, value.toString());
      }
    }
  });
  
  return params;
}

/**
 * 构建规划师筛选参数
 */
export function buildPlannerFilterParams(filters: PlannerFilters): URLSearchParams {
  const params = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      if (Array.isArray(value)) {
        value.forEach(v => params.append(key, v));
      } else {
        params.append(key, value.toString());
      }
    }
  });
  
  return params;
}

/**
 * 计算匹配度分数
 */
export function calculateMatchScore(
  request: MatchRequest, 
  planner: PlannerProfile
): number {
  let score = 0;
  let maxScore = 0;
  
  // 年级匹配 (权重: 20)
  maxScore += 20;
  if (planner.matchProfile?.preferredGrades?.includes(request.requirements.grade)) {
    score += 20;
  }
  
  // 科目匹配 (权重: 25)
  maxScore += 25;
  const subjectMatches = request.requirements.subjects.filter(subject =>
    planner.matchProfile?.preferredSubjects?.includes(subject)
  );
  score += (subjectMatches.length / request.requirements.subjects.length) * 25;
  
  // 地区匹配 (权重: 15)
  maxScore += 15;
  if (planner.matchProfile?.preferredLocations?.includes(request.requirements.location)) {
    score += 15;
  }
  
  // 评分 (权重: 20)
  maxScore += 20;
  if (planner.rating) {
    score += (planner.rating / 5) * 20;
  }
  
  // 成功率 (权重: 10)
  maxScore += 10;
  if (planner.successRate) {
    score += (planner.successRate / 100) * 10;
  }
  
  // 响应时间 (权重: 10)
  maxScore += 10;
  if (planner.matchProfile?.responseTime) {
    // 响应时间越短分数越高
    const responseScore = Math.max(0, 10 - (planner.matchProfile.responseTime / 24) * 10);
    score += responseScore;
  }
  
  return maxScore > 0 ? (score / maxScore) * 100 : 0;
}

/**
 * 排序匹配请求
 */
export function sortMatchRequests(
  requests: MatchRequest[], 
  sortBy: string = "createdAt", 
  sortOrder: "asc" | "desc" = "desc"
): MatchRequest[] {
  return [...requests].sort((a, b) => {
    let aValue: any;
    let bValue: any;
    
    switch (sortBy) {
      case "urgency":
        aValue = a.urgency;
        bValue = b.urgency;
        break;
      case "createdAt":
        aValue = new Date(a.createdAt).getTime();
        bValue = new Date(b.createdAt).getTime();
        break;
      case "budget":
        aValue = a.requirements.budget || 0;
        bValue = b.requirements.budget || 0;
        break;
      case "responses":
        aValue = a._count?.responses || 0;
        bValue = b._count?.responses || 0;
        break;
      default:
        aValue = a.createdAt;
        bValue = b.createdAt;
    }
    
    if (sortOrder === "asc") {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });
}

/**
 * 排序规划师
 */
export function sortPlanners(
  planners: PlannerProfile[], 
  sortBy: string = "rating", 
  sortOrder: "asc" | "desc" = "desc"
): PlannerProfile[] {
  return [...planners].sort((a, b) => {
    let aValue: any;
    let bValue: any;
    
    switch (sortBy) {
      case "rating":
        aValue = a.rating || 0;
        bValue = b.rating || 0;
        break;
      case "successRate":
        aValue = a.successRate || 0;
        bValue = b.successRate || 0;
        break;
      case "experience":
        aValue = a.experience || 0;
        bValue = b.experience || 0;
        break;
      case "responseTime":
        aValue = a.matchProfile?.responseTime || 999;
        bValue = b.matchProfile?.responseTime || 999;
        break;
      case "students":
        aValue = a._count?.students || 0;
        bValue = b._count?.students || 0;
        break;
      default:
        aValue = a.rating || 0;
        bValue = b.rating || 0;
    }
    
    if (sortOrder === "asc") {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });
}
