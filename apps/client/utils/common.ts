// 从主机名中提取租户子域名
export function extractTenantFromHostname(hostname: string): string | null {
  // 移除端口号
  const host = hostname.split(":")[0];

  // 定义主域名列表 (生产环境和开发环境)
  const mainDomains = [
    "localhost",
    "qyqm-teachers.com",
    "your-domain.com", // 替换为你的实际域名
  ];

  // 检查是否为子域名
  for (const mainDomain of mainDomains) {
    if (host === mainDomain) {
      // 主域名，不是子域名
      return null;
    }

    if (host?.endsWith(`.${mainDomain}`)) {
      // 是子域名，提取子域名部分
      const subdomain = host!.substring(
        0,
        host!.length - mainDomain.length - 1,
      );

      // 排除常见的系统子域名
      const systemSubdomains = ["www", "api", "admin", "app", "cdn", "static"];
      if (systemSubdomains.includes(subdomain)) {
        return null;
      }

      return subdomain;
    }
  }

  return null;
}
