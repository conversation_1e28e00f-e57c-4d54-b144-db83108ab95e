// 认证相关配置常量（直接定义，避免客户端引用数据库包）
// 但是必须确认和后端保持一致 @workflow/database/src/constants/client-auth.constants.ts
export const AUTH_CONFIG = {
  // Token配置
  TOKEN: {
    ACCESS_EXPIRES_IN: 2 * 60 * 60 * 1000, // 2小时
    REFRESH_EXPIRES_IN: 7 * 24 * 60 * 60 * 1000, // 7天
    REMEMBER_EXPIRES_IN: 30 * 24 * 60 * 60 * 1000, // 30天记住我
    AUTO_REFRESH_INTERVAL: 30 * 60 * 1000, // 30分钟自动刷新间隔
  },

  // JWT配置
  JWT: {
    SECRET:
      process.env.JWT_SECRET || "your-super-secret-key-change-in-production",
    ACCESS_EXPIRES_IN: process.env.JWT_EXPIRES_IN || "2h",
    REFRESH_EXPIRES_IN: process.env.REFRESH_TOKEN_EXPIRES_IN || "7d",
    REMEMBER_EXPIRES_IN: process.env.REMEMBER_EXPIRES_IN || "30d",
  },

  // Cookie配置
  COOKIES: {
    REFRESH_TOKEN: "refresh_token",
    ACCESS_TOKEN: "access_token",
  },

  // LocalStorage键名
  STORAGE_KEYS: {
    ACCESS_TOKEN: "access_token",
    USER_INFO: "user_info",
    TENANT_INFO: "tenant_info",
    CURRENT_TENANT: "current_tenant",
  },

  // HTTP Headers
  HEADERS: {
    AUTHORIZATION: "Authorization",
    CONTENT_TYPE: "Content-Type",
    BEARER_PREFIX: "Bearer ",
  },

  // 安全配置
  SECURITY: {
    MAX_LOGIN_ATTEMPTS: 5, // 最大登录尝试次数
    LOCKOUT_DURATION: 15 * 60 * 1000, // 锁定时长15分钟
    PASSWORD_MIN_LENGTH: 6, // 密码最小长度
  },

  // Cookie选项
  COOKIE_OPTIONS: {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict" as const,
    path: "/",
  },
} as const;

// 获取一级域名的辅助函数
export const getTopLevelDomain = (host: string): string => {
  // 如果是 localhost 或 IP 地址，不设置 domain
  if (host.includes("localhost") || /^\d+\.\d+\.\d+\.\d+/.test(host)) {
    return host;
  }

  // 获取一级域名 (例如: tenant.example.com -> .example.com)
  const parts = host.split(".");
  if (parts.length >= 2) {
    return `.${parts.slice(-2).join(".")}`;
  }

  return host;
};

// 获取Cookie选项的辅助函数 (支持多租户子域名)
export const getCookieOptions = (maxAge?: number, host?: string) => {
  const baseOptions = {
    ...AUTH_CONFIG.COOKIE_OPTIONS,
    ...(maxAge && { maxAge }),
  };

  // 如果提供了 host，设置 domain 为一级域名以支持子域名共享
  if (host) {
    const topLevelDomain = getTopLevelDomain(host);
    // 只有当不是 localhost 或 IP 时才设置 domain
    if (topLevelDomain.startsWith(".")) {
      return {
        ...baseOptions,
        domain: topLevelDomain,
      };
    }
  }

  return baseOptions;
};

// 获取Bearer Token的辅助函数
export const getBearerToken = (token: string) => {
  return `${AUTH_CONFIG.HEADERS.BEARER_PREFIX}${token}`;
};

// 从Authorization header中提取token的辅助函数
export const extractTokenFromHeader = (
  authHeader: string | null,
): string | null => {
  if (
    !authHeader ||
    !authHeader.startsWith(AUTH_CONFIG.HEADERS.BEARER_PREFIX)
  ) {
    return null;
  }
  return authHeader.substring(AUTH_CONFIG.HEADERS.BEARER_PREFIX.length);
};
