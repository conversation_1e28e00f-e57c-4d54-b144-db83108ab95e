// Post-related types that strictly match schema.prisma

// Enums from schema.prisma
export enum PostType {
  ARTICLE = "ARTICLE",
  SHARE = "SHARE",
  QUESTION = "QUESTION",
  ANNOUNCEMENT = "ANNOUNCEMENT",
  EXPERIENCE = "EXPERIENCE",
}

export enum PostStatus {
  DRAFT = "DRAFT",
  PENDING_REVIEW = "PENDING_REVIEW",
  PUBLISHED = "PUBLISHED",
  HIDDEN = "HIDDEN",
  DELETED = "DELETED",
}

// User interface (simplified for post context)
export interface User {
  id: string;
  username: string;
  name: string;
  avatar?: string;
  email: string;
}

// Category interface
export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  parentId?: string;
  parent?: Category;
  children?: Category[];
  order: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Tag interface
export interface Tag {
  id: string;
  name: string;
  slug: string;
  description?: string;
  usageCount: number;
  createdAt: Date;
}

// PostTag relation interface
export interface PostTag {
  postId: string;
  tagId: string;
  tag: Tag;
}

// Comment interface
export interface Comment {
  id: string;
  content: string;
  postId: string;
  userId: string;
  user: User;
  userName?: string;
  userAvatar?: string;
  parentId?: string;
  parent?: Comment;
  replies?: Comment[];
  isDeleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Like interface
export interface Like {
  userId: string;
  user: User;
  postId: string;
  createdAt: Date;
}

// Favorite interface
export interface Favorite {
  userId: string;
  user: User;
  postId: string;
  createdAt: Date;
}

// Main Post interface - strictly matching schema.prisma
export interface Post {
  id: string;
  title: string;
  content: string; // @db.Text
  summary?: string;
  cover?: string;
  type: PostType;
  status: PostStatus;
  viewCount: number; // @default(0)
  isTop: boolean; // @default(false)
  isRecommended: boolean; // @default(false)
  isOriginal: boolean; // @default(true)

  // Author information
  authorId: string;
  author: User;
  authorName?: string; // redundant field for performance
  authorAvatar?: string; // redundant field for performance

  // Category
  categoryId?: string;
  category?: Category;

  // Timestamps
  publishedAt?: Date;
  createdAt: Date; // @default(now())
  updatedAt: Date; // @updatedAt

  // Relations
  tags: PostTag[];
  comments: Comment[];
  likes: Like[];
  favorites: Favorite[];

  // Computed fields (for UI)
  _count?: {
    comments: number;
    likes: number;
    favorites: number;
  };
}

// Create Post DTO
export interface CreatePostDto {
  title: string;
  content: string;
  summary?: string;
  cover?: string;
  type: PostType;
  categoryId?: string;
  tagIds?: string[];
  isOriginal?: boolean;
}

// Update Post DTO
export interface UpdatePostDto {
  title?: string;
  content?: string;
  summary?: string;
  cover?: string;
  type?: PostType;
  categoryId?: string;
  tagIds?: string[];
  isOriginal?: boolean;
}

// Post filters for API
export interface PostFilters {
  type?: PostType;
  status?: PostStatus;
  categoryId?: string;
  authorId?: string;
  isTop?: boolean;
  isRecommended?: boolean;
  tags?: string[];
  search?: string;
}

// Post list response
export interface PostListResponse {
  data: Post[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}
