// 用户角色映射
export const UserRoleLabels = {
  SUPER_ADMIN: "超级管理员",
  ADMIN: "平台管理员",
  OPERATOR: "运营人员",
  TENANT_ADMIN: "租户管理员",
  PLANNER: "规划师",
  STUDENT: "学生",
  PARENT: "家长",
  COMMUNITY_USER: "社区用户",
  GUEST: "访客",
};

// 租户类型映射
export const TenantTypeLabels = {
  INSTITUTION: "机构",
  INDIVIDUAL: "个人规划师",
};

// 租户状态映射
export const TenantStatusLabels = {
  PENDING: "待审核",
  ACTIVE: "激活",
  SUSPENDED: "暂停",
  EXPIRED: "过期",
};

// 订阅计划类型映射
export const PlanTypeLabels = {
  FREE: "免费版",
  PERSONAL: "个人版",
  INSTITUTION: "机构版",
  CUSTOM: "定制版",
};

// 计费周期映射
export const BillingCycleLabels = {
  MONTHLY: "月付",
  QUARTERLY: "季付",
  YEARLY: "年付",
  LIFETIME: "终身",
};

// 订阅状态映射
export const SubscriptionStatusLabels = {
  TRIAL: "试用中",
  ACTIVE: "生效中",
  PAST_DUE: "逾期",
  CANCELLED: "已取消",
  EXPIRED: "已过期",
};

// 帖子类型映射
export const PostTypeLabels = {
  ARTICLE: "文章",
  SHARE: "分享",
  QUESTION: "问题",
  ANNOUNCEMENT: "公告",
  EXPERIENCE: "经验分享",
};

// 帖子状态映射
export const PostStatusLabels = {
  DRAFT: "草稿",
  PENDING_REVIEW: "待审核",
  PUBLISHED: "已发布",
  HIDDEN: "隐藏",
  DELETED: "已删除",
};

// 课程状态映射
export const CourseStatusLabels = {
  DRAFT: "草稿",
  REVIEWING: "审核中",
  PUBLISHED: "已发布",
  OFFLINE: "已下架",
};

// 课程等级映射
export const CourseLevelLabels = {
  BEGINNER: "初级",
  INTERMEDIATE: "中级",
  ADVANCED: "高级",
  ALL: "全部级别",
};

// 产品类型映射
export const ProductTypeLabels = {
  STUDY_CAMP: "研学营",
  CERTIFICATE: "证书",
  BACKGROUND: "背景提升",
  SCHOOL_LINK: "名校链接",
  INSTITUTION: "机构提升",
  OTHER: "其他",
};

// 产品状态映射
export const ProductStatusLabels = {
  DRAFT: "草稿",
  ACTIVE: "上架",
  INACTIVE: "下架",
  SOLD_OUT: "售罄",
};

// 询价状态映射
export const InquiryStatusLabels = {
  PENDING: "待处理",
  CONTACTING: "联系中",
  NEGOTIATING: "洽谈中",
  COMPLETED: "已完成",
  CANCELLED: "已取消",
};

// 任务状态映射
export const TaskStatusLabels = {
  PENDING: "待处理",
  PROCESSING: "处理中",
  COMPLETED: "已完成",
  FAILED: "失败",
  CANCELLED: "已取消",
};

// 任务类型映射
export const TaskTypeLabels = {
  EMAIL_CAMPAIGN: "邮件营销",
  SMS_CAMPAIGN: "短信营销",
  REPORT_GENERATION: "报告生成",
  DATA_EXPORT: "数据导出",
  SYSTEM_MAINTENANCE: "系统维护",
};

// 通知类型映射
export const NotificationTypeLabels = {
  SYSTEM: "系统通知",
  APPOINTMENT: "预约提醒",
  TASK: "任务通知",
  COMMENT: "评论通知",
  LIKE: "点赞通知",
  FOLLOW: "关注通知",
  POST: "帖子相关",
};

// 管理员状态映射
export const AdminStatusLabels = {
  ACTIVE: "活跃",
  INACTIVE: "非活跃",
  SUSPENDED: "暂停",
};

// 通用标签映射对象
export const LabelMaps = {
  UserRole: UserRoleLabels,
  TenantType: TenantTypeLabels,
  TenantStatus: TenantStatusLabels,
  PlanType: PlanTypeLabels,
  BillingCycle: BillingCycleLabels,
  SubscriptionStatus: SubscriptionStatusLabels,
  PostType: PostTypeLabels,
  PostStatus: PostStatusLabels,
  CourseStatus: CourseStatusLabels,
  CourseLevel: CourseLevelLabels,
  ProductType: ProductTypeLabels,
  ProductStatus: ProductStatusLabels,
  InquiryStatus: InquiryStatusLabels,
  TaskStatus: TaskStatusLabels,
  TaskType: TaskTypeLabels,
  NotificationType: NotificationTypeLabels,
  AdminStatus: AdminStatusLabels,
};

// 获取标签的工具函数
export function getLabel<T extends keyof typeof LabelMaps>(
  enumType: T,
  value: string,
): string {
  return (
    (LabelMaps[enumType][value as keyof (typeof LabelMaps)[T]] as string) ||
    String(value)
  );
}

// 获取枚举选项列表的工具函数
export function getEnumOptions<T extends keyof typeof LabelMaps>(
  enumType: T,
): Array<{ value: keyof (typeof LabelMaps)[T]; label: string }> {
  const enumObj = LabelMaps[enumType];
  return Object.keys(enumObj).map((key) => ({
    value: key as keyof (typeof LabelMaps)[T],
    label: enumObj[key as keyof typeof enumObj] as string,
  }));
}
