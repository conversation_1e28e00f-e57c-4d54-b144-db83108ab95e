# 双系统SaaS

# 1. 学业规划师在线学习平台 - UI设计文档

## 项目概述

本项目是一个专为学业规划师设计的在线学习和产品展示平台，提供优雅的用户界面，支持PC和移动端H5访问。

## 设计理念

### 设计原则

1. **专业性**: 界面设计简洁大方，符合教育行业的专业形象
2. **易用性**: 清晰的导航结构，让学业规划师快速找到所需内容
3. **响应式**: 完美适配PC端和移动端，确保良好的跨设备体验
4. **可访问性**: 高对比度设计，确保内容的可读性
5. 优先使用shadcnui，在我的monorepo的packages/ui子项目中
6. 一定要注意组件封装，代码解耦

## 页面结构

## 三大功能模块

### 1. 网课模块 (Courses)

**功能定位**: 学业规划师的核心学习区域

**主要功能**:

- 课程分类浏览（高，中，初级）
- 课程详情展示
- 视频播放器
- 学习进度追踪

**UI设计要点**:

- 使用卡片式布局展示课程
- 清晰的课程分类导航
- 突出显示学习进度
- 视频播放器采用沉浸式设计

### 2. 产品模块 (Products)

**功能定位**: 研学营和线下公开课展示

**主要功能**:

- 产品分类（研学营/公开课）
- 产品详情介绍
- 图文并茂的展示
- 报名入口（引导联系规划师）
- 产品日期和地点信息

**UI设计要点**:

- 大图配合文字的展示形式
- 时间轴展示即将开始的活动
- 醒目的"咨询报名"按钮
- 产品特色标签化展示

### 3. 个人中心 (Profile)

**功能定位**: 个人信息和学习管理

**主要功能**:

- 个人信息展示
- 学习记录查看
- 收藏的课程/产品
- 系统设置
- 关于我们
- 联系客服

**UI设计要点**:

- 简洁的个人信息卡片
- 清晰的功能入口列表
- 学习数据可视化展示

## 组件设计规范

### 卡片组件

```tsx
// 课程卡片示例
<Card className="hover:shadow-lg transition-shadow">
  <CardHeader>
    <img src="course-cover.jpg" className="rounded-lg" />
  </CardHeader>
  <CardContent>
    <h3 className="font-semibold">课程标题</h3>
    <p className="text-muted-foreground">课程简介</p>
    <Progress value={75} className="mt-2" />
  </CardContent>
</Card>
```

### 按钮规范

- **主要按钮**: 使用primary色，用于主要操作
- **次要按钮**: 使用secondary色，用于次要操作
- **文字按钮**: 用于不太重要的操作

### 表单设计

- 输入框使用圆角设计
- 聚焦时显示主题色边框
- 错误状态使用destructive色

## 响应式断点

- **移动端**: < 768px
- **平板**: 768px - 1024px
- **桌面端**: > 1024px

## 技术实现

### 动画库

使用motion来实现动画，要尽可能多的使用动画，包括页面过渡效果等。

### 使用的UI组件库

本项目基于 `@workspace/ui` 共享组件库，包含以下组件：

- Button（按钮）
- Card（卡片）
- Badge（徽章）
- Avatar（头像）
- Alert Dialog（对话框）
- 更多组件...

### 样式管理

- 使用 Tailwind CSS 进行样式管理
- 主题色定义在 `packages/ui/src/styles/globals.css`
- 支持亮色/暗色主题切换

### 图标库

使用 `lucide-react` 提供的图标集

## 页面路由规划

```
/                    # 首页（展示热门课程和最新产品）
/courses             # 网课列表页
/courses/[id]        # 课程详情页
/products            # 产品列表页
/products/[id]       # 产品详情页
/profile             # 个人中心
/profile/settings    # 设置页面
/profile/records     # 学习记录
/profile/favorites   # 我的收藏
```

## 开发规范

1. 所有组件使用 TypeScript 编写
2. 遵循 React 19 最佳实践
3. 使用 Next.js 15 的 App Router
4. 组件命名使用 PascalCase
5. 文件命名使用 kebab-case

# 2. EduPlanner CRM

EduPlanner CRM 是一款面向 **6-22 岁学生学业规划师 / 规划机构** 的一体化云端 CRM SaaS。  
它帮助规划师：

- 统一管理学生档案与学情数据
- 标准化咨询流程与自动提醒，降低漏跟进率
- 一键生成家长报告、课程/营地推荐书，提升专业度
- 通过可视化 KPI 面板量化服务效果
- 结合付费订阅与分销抽佣，实现收入增长

> MVP 已聚焦「能卖、能用、能看效果」的核心闭环，目标 3-4 个月上线收付费用户。

---

## 核心特性 (MVP)

| 模块       | 主要功能                                                  |
| ---------- | --------------------------------------------------------- |
| 学生档案   | 基础信息、家长信息、自定义兴趣/目标标签、成绩/测评附件    |
| 咨询流程   | 流程模板、自定义节点、到期提醒 (Email / 企业微信 Webhook) |
| 进展日志   | 内部记录、家长可视化周报 (PDF / H5)                       |
| 产品推荐库 | 课程 / 夏校 / 竞赛条目管理，按标签 & 预算智能筛选         |
| 付费订阅   | 14 天试用 → 个人版 / 团队版年费，内置微信/支付宝收银台    |

---

## 技术栈

| 层级        | 技术                                 | 说明                                    |
| ----------- | ------------------------------------ | --------------------------------------- |
| 前端        | **Next.js 14 (app router, SSR+CSR)** | React UI、鉴权、国际化                  |
| 后端        | **Hono.js**                          | 轻量 TypeScript Web 框架，Edge-friendly |
| ORM         | **Prisma**                           | 与 MySQL/PlanetScale 适配               |
| 数据库      | **MySQL 8**                          | 主数据存储                              |
| 缓存 / 会话 | **Redis**                            | 热数据与任务提醒队列                    |
| 对象存储    | OSS / S3 兼容                        | 附件 / 报告 PDF                         |
| 部署        | Vercel + Fly.io / 自托管 K8s         | 前后端分离或同域部署均可                |

---

## 快速开始

```bash
git clone https://github.com/your-org/eduplanner-crm
cd eduplanner-crm

# 安装依赖
pnpm i

# 配置环境变量
cp .env.example .env.local
# 修改数据库 / Redis / OSS 等连接串

# 数据库迁移 & 种子数据
npx prisma migrate dev
npx prisma db seed

# 启动开发
pnpm dev
```

访问 http://localhost:3000 即可进入管理台 (默认账号见 seed)。

---

## 目录结构

```
apps/
  web/             # Next.js (前端 + SSR API routes)
packages/
  api/             # Hono.js API (可独立部署到 edge)
  db/              # Prisma schema & migrations
  ui/              # 可复用的设计系统组件
docs/              # 需求/技术/运维文档
```

---

## 部署说明

1. **数据库**: 初始化 MySQL，并执行 `prisma migrate deploy`
2. **Redis**: 启动 Redis，配置 `REDIS_URL`
3. **API**: 将 `packages/api` 部署至 Vercel Edge / Cloudflare Workers / Fly.io
4. **WEB**: 将 Next.js `apps/web` 部署至 Vercel 或自托管容器
5. **环境变量**: 请参见 `.env.example`

---

## 路线图 (Roadmap)

- ✅ MVP 核心功能 (v0.1)
- 🔜 家长端小程序 (v0.2)
- 🔜 AI 面谈纪要 & 推荐关键词生成 (v0.3)
- 🔜 市场分销 & 佣金结算 (v0.4)

---

# 2.1 EduPlanner CRM 需求规格说明书 (MVP v0.1)

版本：1.0  
更新日期：2025-06-11  
作者：产品团队

---

## 1. 项目概述

EduPlanner CRM 旨在帮助学业规划师提升服务交付效率、标准化流程并强化价值感知，从而实现更高的客户满意度与收入。

---

## 2. 术语与角色

| 名称             | 描述                         |
| ---------------- | ---------------------------- |
| 规划师 (Advisor) | 购买 SaaS 的专业顾问         |
| 助理 (Assistant) | 受邀协同的子账号             |
| 学生 (Student)   | 服务对象 (6-22 岁)           |
| 家长 (Parent)    | 学生监护人，接收报告         |
| 产品 (Product)   | 可推荐的课程 / 夏校 / 竞赛等 |
| 流程 (Workflow)  | 咨询服务的标准步骤集合       |

---

## 3. 功能需求

### 3.1 账号与权限

F-1 注册 / 登录  
• 支持邮箱 + 密码 / 手机号 + 验证码  
F-2 订阅管理  
• 14 天试用 → 年费套餐  
• 套餐限制：学生档案数量、子账号数量  
F-3 角色 / 权限  
• 规划师 (Owner)、助理 (Editor)、只读 (Viewer)

### 3.2 学生档案

F-4 创建 / 编辑学生  
• 基础字段：姓名、性别、出生日期、学校、年级、联系方式  
• 家长信息：姓名、电话、邮箱  
• 自定义标签：兴趣爱好、目标国家、预算等  
F-5 附件管理  
• 支持上传成绩单、测评报告 (PDF/JPG)  
F-6 历史记录  
• 面谈纪要 (富文本)、系统日志 (自动)

### 3.3 咨询流程 & 任务

F-7 流程模板库  
• 系统预置：初访 → 测评 → 方案 → 跟进  
• 用户可自定义节点名称、顺序、负责人、时间  
F-8 任务提醒  
• 到期前 n 天邮件 & 企业微信推送  
• 支持自定义提醒频次  
F-9 看板视图  
• 按学生 / 节点状态展示进行中、逾期、已完成

### 3.4 进展日志 & 报告

F-10 内部日志  
• 规划师记录当日重点、下一步计划  
F-11 家长报告生成  
• 选定时间范围 → 生成 PDF / H5 链接  
• 模板包含：已完成节点、下一阶段目标、推荐产品清单  
F-12 已读回执  
• 家长查看后，系统记录时间戳并在后台展示

### 3.5 产品推荐库

F-13 产品管理  
• 字段：名称、类型、适合年龄、费用区间、关联标签、外部链接  
F-14 智能筛选  
• 基于学生标签与预算区间匹配  
F-15 推荐书导出  
• 多选产品 → 生成带 Logo PDF

### 3.6 支付与计费

F-16 在线支付  
• 支持微信 JSAPI & 支付宝 WAP  
F-17 发票信息填写 & 下载 (未来版本)

---

## 4. 非功能需求

| 分类     | 目标                                       |
| -------- | ------------------------------------------ |
| 性能     | 页面首屏 < 1.2s (Vercel Edge SSR)          |
| 并发     | 目标 2k 并发连接，Redis 缓存热点数据       |
| 安全     | JWT + 双因子登录 (可选)，GDPR/中国数据合规 |
| 可用性   | SLA 99.9%，多 AZ MySQL                     |
| 可维护性 | 单体 Repo + Turbo/PNPM，CI 提供 E2E 测试   |
| 可扩展   | 微前端 & Hono Edge API，可水平扩展         |

---

## 5. 数据模型 (简化 ER)

```
Student (1) --- (N) WorkflowRecord
Student (1) --- (N) Log
Student (1) --- (M) Product (via Recommendation)
User (1) --- (N) Student
User (1) --- (N) Subscription
```

---

## 6. 关键业务流程

### 6.1 学生入库流程

1. 规划师创建学生档案 → 系统生成空流程实例
2. 规划师选择流程模板 → 节点自动创建
3. 系统根据节点截止日期写入 Redis 延迟队列
4. 到期前 n 天触发邮件 / 企业微信提醒

### 6.2 报告生成流程

1. 规划师在学生详情点击「生成报告」
2. 后端汇总节点状态、日志、推荐清单 → 渲染 HTML
3. Puppeteer 转 PDF → OSS 保存 → 返回下载 / H5 链接
4. 系统推送给家长并记录已读

---

## 7. MVP 范围剪裁

- 不做家长端小程序，只提供 H5 链接
- 不做分销佣金，仅记录推荐产品
- 不含 AI 摘要、OCR，采用纯人工输入

---

## 8. 里程碑

| Sprint | 周期    | 交付                     |
| ------ | ------- | ------------------------ |
| S1     | 0-2 周  | 账号体系、学生档案 CRUD  |
| S2     | 3-4 周  | 流程模板、任务提醒       |
| S3     | 5-6 周  | 产品库、推荐书、在线支付 |
| Beta   | 7-8 周  | 试用反馈 & Bugfix        |
| GA     | 9-10 周 | 正式计费上线             |

---

## 9. 风险与应对

| 风险                    | 影响     | 缓解措施                         |
| ----------------------- | -------- | -------------------------------- |
| 微信/支付宝支付审核延迟 | 上线推迟 | 提前 3 周提交申请                |
| 邮件推送被屏蔽          | 提醒失效 | 备用企业微信群机器人             |
| 数据合规 (学生未成年人) | 法律风险 | 数据脱敏、国内机房、家长授权协议 |

---

## 10. 附录

- UI 原型：见 Figma 链接
- API 契约：`docs/openapi.yaml`
- 设计系统：`packages/ui` Storybook
