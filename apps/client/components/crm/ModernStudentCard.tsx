"use client";

import { Badge } from "@workspace/ui/components/badge";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Card, CardContent, CardHeader } from "@workspace/ui/components/card";
import { Avatar, AvatarFallback, AvatarImage } from "@workspace/ui/components/avatar";
import { 
  MoreHorizontal, 
  Phone, 
  Mail, 
  MapPin, 
  Calendar,
  GraduationCap,
  Star,
  TrendingUp,
  MessageCircle,
  FileText,
  Edit,
  Trash2,
  Eye
} from "lucide-react";
import { useState } from "react";
import { motion } from "framer-motion";
import { Student } from "@/hooks/useCRMStudents";

interface ModernStudentCardProps {
  student: Student;
  onView: (student: Student) => void;
  onEdit: (student: Student) => void;
  onDelete: (student: Student) => void;
  onMessage: (student: Student) => void;
  isSelected?: boolean;
  onSelect?: (student: Student, selected: boolean) => void;
}

// 状态配置
const getStatusConfig = (status: string) => {
  switch (status) {
    case 'ACTIVE':
      return {
        label: '在读',
        color: 'bg-green-100 text-green-800 border-green-200',
        dot: 'bg-green-500'
      };
    case 'GRADUATED':
      return {
        label: '已毕业',
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        dot: 'bg-blue-500'
      };
    case 'INACTIVE':
      return {
        label: '暂停',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        dot: 'bg-gray-500'
      };
    default:
      return {
        label: '未知',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        dot: 'bg-gray-500'
      };
  }
};

// 计算年龄
const calculateAge = (birthday?: string | null) => {
  if (!birthday) return null;
  const today = new Date();
  const birthDate = new Date(birthday);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
};

export function ModernStudentCard({
  student,
  onView,
  onEdit,
  onDelete,
  onMessage,
  isSelected = false,
  onSelect
}: ModernStudentCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const statusConfig = getStatusConfig(student.status);
  const age = calculateAge(student.birthday);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Card className={`relative overflow-hidden transition-all duration-200 hover:shadow-lg ${
        isSelected ? 'ring-2 ring-primary ring-offset-2' : ''
      }`}>
        {/* 选择指示器 */}
        {onSelect && (
          <div className="absolute top-3 left-3 z-10">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={(e) => onSelect(student, e.target.checked)}
              className="w-4 h-4 text-primary bg-white border-gray-300 rounded focus:ring-primary focus:ring-2"
            />
          </div>
        )}

        {/* 状态指示条 */}
        <div className={`absolute top-0 left-0 right-0 h-1 ${statusConfig.dot}`} />

        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <div className="relative">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={student.avatar || undefined} />
                  <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-semibold">
                    {student.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                {/* 在线状态指示器 */}
                <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${statusConfig.dot}`} />
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="font-semibold text-lg truncate">{student.name}</h3>
                  {student.gender && (
                    <span className="text-xs text-muted-foreground">
                      {student.gender}
                      {age && ` · ${age}岁`}
                    </span>
                  )}
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <GraduationCap className="w-4 h-4" />
                  <span>{student.grade || '未设置'}</span>
                  {student.school && (
                    <>
                      <span>·</span>
                      <span className="truncate">{student.school}</span>
                    </>
                  )}
                </div>
              </div>
            </div>
            
            <Badge variant="outline" className={statusConfig.color}>
              {statusConfig.label}
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* 学业信息 */}
          {(student.gpa || student.rank) && (
            <div className="flex items-center gap-4 p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-2">
                <Star className="w-4 h-4 text-yellow-500" />
                <span className="text-sm font-medium">
                  GPA: {student.gpa?.toFixed(1) || 'N/A'}
                </span>
              </div>
              {student.rank && (
                <div className="flex items-center gap-2">
                  <TrendingUp className="w-4 h-4 text-green-500" />
                  <span className="text-sm font-medium">
                    排名: {student.rank}
                  </span>
                </div>
              )}
            </div>
          )}

          {/* 联系信息 */}
          <div className="space-y-2">
            {student.parentName && (
              <div className="flex items-center gap-2 text-sm">
                <div className="w-2 h-2 rounded-full bg-primary/60" />
                <span className="font-medium">家长:</span>
                <span>{student.parentName}</span>
                {student.parentPhone && (
                  <a 
                    href={`tel:${student.parentPhone}`}
                    className="ml-auto text-primary hover:text-primary/80 transition-colors"
                  >
                    <Phone className="w-4 h-4" />
                  </a>
                )}
              </div>
            )}
            
            {student.email && (
              <div className="flex items-center gap-2 text-sm">
                <div className="w-2 h-2 rounded-full bg-blue-500/60" />
                <span className="truncate">{student.email}</span>
                <a 
                  href={`mailto:${student.email}`}
                  className="ml-auto text-blue-500 hover:text-blue-600 transition-colors"
                >
                  <Mail className="w-4 h-4" />
                </a>
              </div>
            )}
          </div>

          {/* 标签 */}
          {student.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {student.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {student.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{student.tags.length - 3}
                </Badge>
              )}
            </div>
          )}

          {/* 备注预览 */}
          {student.notes && (
            <div className="p-2 bg-muted/30 rounded text-xs text-muted-foreground line-clamp-2">
              {student.notes}
            </div>
          )}

          {/* 操作按钮 */}
          <div className={`flex items-center gap-2 transition-all duration-200 ${
            isHovered ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'
          }`}>
            <Button
              size="sm"
              variant="outline"
              onClick={() => onView(student)}
              className="flex-1"
            >
              <Eye className="w-4 h-4 mr-1" />
              查看
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => onMessage(student)}
            >
              <MessageCircle className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => onEdit(student)}
            >
              <Edit className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => onDelete(student)}
              className="text-destructive hover:text-destructive"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </CardContent>

        {/* 最后更新时间 */}
        <div className="absolute bottom-2 right-2 text-xs text-muted-foreground">
          <Calendar className="w-3 h-3 inline mr-1" />
          {new Date(student.updatedAt).toLocaleDateString()}
        </div>
      </Card>
    </motion.div>
  );
}
