"use client";

import { Badge } from "@workspace/ui/components/badge";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@workspace/ui/components/card";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle
} from "@workspace/ui/components/dialog";
import { Progress } from "@workspace/ui/components/progress";
import { AnimatePresence, motion } from "framer-motion";
import {
    AlertCircle,
    Bell,
    Calendar,
    CheckCircle,
    Clock,
    Edit,
    Eye,
    FileText,
    MessageSquare,
    Plus,
    Target,
    TrendingUp,
    User,
} from "lucide-react";
import { useState } from "react";

// 服务阶段定义
interface ServicePhase {
  id: string;
  name: string;
  description: string;
  duration: number; // 预期天数
  order: number;
  isRequired: boolean;
  color: string;
}

// 里程碑定义
interface Milestone {
  id: string;
  phaseId: string;
  title: string;
  description: string;
  targetDate: string;
  completedDate?: string;
  status: "pending" | "in_progress" | "completed" | "overdue";
  assignedTo?: string;
  documents?: string[];
  notes?: string;
  parentVisible: boolean; // 家长是否可见
}

// 服务记录
interface ServiceRecord {
  id: string;
  milestoneId: string;
  type: "meeting" | "call" | "document" | "note" | "task";
  title: string;
  content: string;
  createdAt: string;
  createdBy: string;
  attachments?: string[];
}

interface ServiceTimelineProps {
  studentId: string;
  phases: ServicePhase[];
  milestones: Milestone[];
  records: ServiceRecord[];
  onAddMilestone: (phaseId: string) => void;
  onUpdateMilestone: (milestone: Milestone) => void;
  onAddRecord: (milestoneId: string, record: Partial<ServiceRecord>) => void;
}

const phaseColors = {
  consultation: "from-blue-500 to-blue-600",
  assessment: "from-purple-500 to-purple-600", 
  planning: "from-green-500 to-green-600",
  execution: "from-orange-500 to-orange-600",
  review: "from-indigo-500 to-indigo-600",
};

const statusIcons = {
  pending: { icon: Clock, color: "text-gray-400" },
  in_progress: { icon: TrendingUp, color: "text-blue-500" },
  completed: { icon: CheckCircle, color: "text-green-500" },
  overdue: { icon: AlertCircle, color: "text-red-500" },
};

export function ServiceTimeline({ 
  studentId, 
  phases, 
  milestones, 
  records, 
  onAddMilestone, 
  onUpdateMilestone, 
  onAddRecord 
}: ServiceTimelineProps) {
  const [selectedPhase, setSelectedPhase] = useState<string | null>(null);
  const [selectedMilestone, setSelectedMilestone] = useState<Milestone | null>(null);
  const [isAddingRecord, setIsAddingRecord] = useState(false);

  // 计算整体进度
  const overallProgress = milestones.length > 0 
    ? (milestones.filter(m => m.status === 'completed').length / milestones.length) * 100 
    : 0;

  // 按阶段分组里程碑
  const milestonesByPhase = phases.map(phase => ({
    ...phase,
    milestones: milestones.filter(m => m.phaseId === phase.id),
    progress: milestones.filter(m => m.phaseId === phase.id).length > 0
      ? (milestones.filter(m => m.phaseId === phase.id && m.status === 'completed').length / 
         milestones.filter(m => m.phaseId === phase.id).length) * 100
      : 0
  }));

  return (
    <div className="space-y-6">
      {/* 整体进度概览 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              服务进度概览
            </CardTitle>
            <Badge variant="outline" className="px-3 py-1">
              {Math.round(overallProgress)}% 完成
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Progress value={overallProgress} className="h-3" />
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {milestones.filter(m => m.status === 'completed').length}
                </div>
                <div className="text-sm text-muted-foreground">已完成</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {milestones.filter(m => m.status === 'in_progress').length}
                </div>
                <div className="text-sm text-muted-foreground">进行中</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {milestones.filter(m => m.status === 'pending').length}
                </div>
                <div className="text-sm text-muted-foreground">待开始</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {milestones.filter(m => m.status === 'overdue').length}
                </div>
                <div className="text-sm text-muted-foreground">已逾期</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 服务阶段时间线 */}
      <div className="relative">
        {/* 时间线主轴 */}
        <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary/50 via-primary/30 to-primary/10" />

        {milestonesByPhase.map((phase, phaseIndex) => (
          <motion.div
            key={phase.id}
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: phaseIndex * 0.1 }}
            className="relative mb-8"
          >
            {/* 阶段标题 */}
            <div className="flex items-center gap-4 mb-6">
              <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${phase.color} flex items-center justify-center text-white font-bold text-lg shadow-lg`}>
                {phaseIndex + 1}
              </div>
              
              <div className="flex-1">
                <h3 className="text-xl font-bold">{phase.name}</h3>
                <p className="text-muted-foreground">{phase.description}</p>
                <div className="flex items-center gap-4 mt-2">
                  <div className="flex items-center gap-1 text-sm">
                    <Clock className="w-4 h-4" />
                    预期 {phase.duration} 天
                  </div>
                  <div className="flex items-center gap-1 text-sm">
                    <TrendingUp className="w-4 h-4" />
                    进度 {Math.round(phase.progress)}%
                  </div>
                </div>
                <Progress value={phase.progress} className="w-48 h-2 mt-2" />
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => onAddMilestone(phase.id)}
                className="shrink-0"
              >
                <Plus className="w-4 h-4 mr-1" />
                添加里程碑
              </Button>
            </div>

            {/* 里程碑列表 */}
            <div className="ml-20 space-y-4">
              <AnimatePresence>
                {phase.milestones.map((milestone, milestoneIndex) => {
                  const StatusIcon = statusIcons[milestone.status].icon;
                  const milestoneRecords = records.filter(r => r.milestoneId === milestone.id);
                  
                  return (
                    <motion.div
                      key={milestone.id}
                      layout
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ delay: milestoneIndex * 0.05 }}
                    >
                      <Card className={`border-l-4 ${
                        milestone.status === 'completed' ? 'border-l-green-500' :
                        milestone.status === 'in_progress' ? 'border-l-blue-500' :
                        milestone.status === 'overdue' ? 'border-l-red-500' :
                        'border-l-gray-300'
                      } hover:shadow-md transition-all duration-200`}>
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <StatusIcon className={`w-5 h-5 ${statusIcons[milestone.status].color}`} />
                                <h4 className="font-semibold">{milestone.title}</h4>
                                <Badge 
                                  variant={milestone.parentVisible ? "default" : "secondary"}
                                  className="text-xs"
                                >
                                  {milestone.parentVisible ? "家长可见" : "内部"}
                                </Badge>
                              </div>
                              
                              <p className="text-sm text-muted-foreground mb-3">
                                {milestone.description}
                              </p>

                              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                <span>目标日期: {milestone.targetDate}</span>
                                {milestone.completedDate && (
                                  <span>完成日期: {milestone.completedDate}</span>
                                )}
                                {milestone.assignedTo && (
                                  <span className="flex items-center gap-1">
                                    <User className="w-3 h-3" />
                                    {milestone.assignedTo}
                                  </span>
                                )}
                              </div>
                            </div>

                            <div className="flex items-center gap-1 ml-4">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setSelectedMilestone(milestone)}
                              >
                                <Eye className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => onUpdateMilestone(milestone)}
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setIsAddingRecord(true)}
                              >
                                <Plus className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>

                          {/* 服务记录预览 */}
                          {milestoneRecords.length > 0 && (
                            <div className="mt-4 pt-4 border-t">
                              <div className="flex items-center gap-2 mb-2">
                                <FileText className="w-4 h-4" />
                                <span className="text-sm font-medium">
                                  最近记录 ({milestoneRecords.length})
                                </span>
                              </div>
                              
                              <div className="space-y-2">
                                {milestoneRecords.slice(0, 2).map(record => (
                                  <div key={record.id} className="bg-muted/50 rounded p-2">
                                    <div className="flex items-center justify-between">
                                      <span className="text-sm font-medium">{record.title}</span>
                                      <span className="text-xs text-muted-foreground">
                                        {record.createdAt}
                                      </span>
                                    </div>
                                    <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                                      {record.content}
                                    </p>
                                  </div>
                                ))}
                                
                                {milestoneRecords.length > 2 && (
                                  <Button variant="link" size="sm" className="h-auto p-0 text-xs">
                                    查看更多 {milestoneRecords.length - 2} 条记录
                                  </Button>
                                )}
                              </div>
                            </div>
                          )}

                          {/* 快速操作 */}
                          <div className="flex gap-2 mt-4">
                            <Button variant="outline" size="sm">
                              <MessageSquare className="w-4 h-4 mr-1" />
                              添加备注
                            </Button>
                            <Button variant="outline" size="sm">
                              <Calendar className="w-4 h-4 mr-1" />
                              安排会议
                            </Button>
                            <Button variant="outline" size="sm">
                              <Bell className="w-4 h-4 mr-1" />
                              设置提醒
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  );
                })}
              </AnimatePresence>

              {/* 空状态 */}
              {phase.milestones.length === 0 && (
                <Card className="border-dashed">
                  <CardContent className="p-6 text-center">
                    <div className="text-muted-foreground">
                      <Target className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">该阶段还没有里程碑</p>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onAddMilestone(phase.id)}
                        className="mt-2"
                      >
                        <Plus className="w-4 h-4 mr-1" />
                        添加第一个里程碑
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </motion.div>
        ))}
      </div>

      {/* 里程碑详情弹窗 */}
      <Dialog open={!!selectedMilestone} onOpenChange={() => setSelectedMilestone(null)}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>里程碑详情</DialogTitle>
          </DialogHeader>
          
          {selectedMilestone && (
            <div className="space-y-6">
              {/* 基本信息 */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">标题</label>
                  <p className="mt-1">{selectedMilestone.title}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">状态</label>
                  <div className="mt-1">
                    <Badge variant={
                      selectedMilestone.status === 'completed' ? 'default' :
                      selectedMilestone.status === 'in_progress' ? 'secondary' :
                      selectedMilestone.status === 'overdue' ? 'destructive' :
                      'outline'
                    }>
                      {selectedMilestone.status}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* 服务记录时间线 */}
              <div>
                <h4 className="font-medium mb-4">服务记录</h4>
                <div className="space-y-3">
                  {records
                    .filter(r => r.milestoneId === selectedMilestone.id)
                    .map(record => (
                      <div key={record.id} className="flex gap-3 p-3 bg-muted/50 rounded">
                        <div className="w-2 h-2 bg-primary rounded-full mt-2 shrink-0" />
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h5 className="font-medium">{record.title}</h5>
                            <span className="text-xs text-muted-foreground">
                              {record.createdAt} by {record.createdBy}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">
                            {record.content}
                          </p>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
} 