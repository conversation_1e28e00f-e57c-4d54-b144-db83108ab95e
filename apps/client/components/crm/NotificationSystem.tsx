"use client";

import { useNotifications, type Notification, type NotificationPreferences } from "@/hooks/useNotifications";
import { Badge } from "@workspace/ui/components/badge";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@workspace/ui/components/dialog";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@workspace/ui/components/select";
import { Separator } from "@workspace/ui/components/separator";
import { Switch } from "@workspace/ui/components/switch";
import { cn } from "@workspace/ui/lib/utils";
import { AlertCircle, AlertTriangle, Bell, Check, CheckCircle2, Clock, Filter, Info, Search, Settings, Star, X } from "lucide-react";
import { useState } from "react";

// 通知图标映射
const NotificationIcons = {
  info: Info,
  success: CheckCircle2,
  warning: AlertTriangle,
  error: AlertCircle,
  reminder: Clock
};

// 优先级颜色映射
const PriorityColors = {
  low: "bg-gray-100 text-gray-800",
  medium: "bg-blue-100 text-blue-800",
  high: "bg-orange-100 text-orange-800",
  urgent: "bg-red-100 text-red-800"
};

// 类型颜色映射
const TypeColors = {
  info: "border-l-blue-500",
  success: "border-l-green-500",
  warning: "border-l-orange-500", 
  error: "border-l-red-500",
  reminder: "border-l-purple-500"
};

// Toast 通知组件
interface ToastNotificationProps {
  notification: Notification;
  onDismiss: (id: string) => void;
}

function ToastNotification({ notification, onDismiss }: ToastNotificationProps) {
  const Icon = NotificationIcons[notification.type];
  
  return (
    <div className={cn(
      "fixed top-4 right-4 z-50 w-96 bg-white rounded-lg shadow-lg border-l-4 p-4 animate-in slide-in-from-top-full",
      TypeColors[notification.type]
    )}>
      <div className="flex items-start gap-3">
        <Icon className={cn(
          "h-5 w-5 mt-0.5",
          notification.type === 'success' && "text-green-500",
          notification.type === 'warning' && "text-orange-500",
          notification.type === 'error' && "text-red-500",
          notification.type === 'info' && "text-blue-500",
          notification.type === 'reminder' && "text-purple-500"
        )} />
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm text-gray-900 truncate">
              {notification.title}
            </h4>
            <Badge className={cn("text-xs", PriorityColors[notification.priority])}>
              {notification.priority}
            </Badge>
          </div>
          <p className="text-sm text-gray-600 mt-1 line-clamp-2">
            {notification.message}
          </p>
          {notification.actionUrl && (
            <Button 
              variant="link" 
              size="sm" 
              className="p-0 h-auto text-blue-600 hover:text-blue-800"
              onClick={() => window.location.href = notification.actionUrl!}
            >
              查看详情
            </Button>
          )}
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0 hover:bg-gray-100"
          onClick={() => onDismiss(notification.id)}
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}

// 通知列表项组件
interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (id: string) => void;
  onToggleStar: (id: string) => void;
  onDelete: (id: string) => void;
}

function NotificationItem({ notification, onMarkAsRead, onToggleStar, onDelete }: NotificationItemProps) {
  const Icon = NotificationIcons[notification.type];
  const timeAgo = getTimeAgo(notification.timestamp);
  
  return (
    <div className={cn(
      "p-4 border-l-4 hover:bg-gray-50 transition-colors",
      TypeColors[notification.type],
      !notification.isRead && "bg-blue-50/30"
    )}>
      <div className="flex items-start gap-3">
        <Icon className={cn(
          "h-5 w-5 mt-1 flex-shrink-0",
          notification.type === 'success' && "text-green-500",
          notification.type === 'warning' && "text-orange-500",
          notification.type === 'error' && "text-red-500",
          notification.type === 'info' && "text-blue-500",
          notification.type === 'reminder' && "text-purple-500"
        )} />
        
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h4 className={cn(
                  "text-sm leading-5",
                  notification.isRead ? "text-gray-700" : "text-gray-900 font-medium"
                )}>
                  {notification.title}
                </h4>
                {!notification.isRead && (
                  <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0" />
                )}
              </div>
              <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                {notification.message}
              </p>
              <div className="flex items-center gap-3 text-xs text-gray-500">
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {timeAgo}
                </span>
                {notification.category && (
                  <Badge variant="secondary" className="text-xs">
                    {getCategoryLabel(notification.category)}
                  </Badge>
                )}
                <Badge className={cn("text-xs", PriorityColors[notification.priority])}>
                  {notification.priority}
                </Badge>
              </div>
            </div>
            
            <div className="flex items-center gap-1 flex-shrink-0">
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => onToggleStar(notification.id)}
              >
                <Star className={cn(
                  "h-4 w-4",
                  notification.isStarred ? "fill-yellow-400 text-yellow-400" : "text-gray-400"
                )} />
              </Button>
              {!notification.isRead && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => onMarkAsRead(notification.id)}
                >
                  <Check className="h-4 w-4 text-gray-400" />
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                onClick={() => onDelete(notification.id)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          {notification.actionUrl && (
            <Button 
              variant="link" 
              size="sm" 
              className="p-0 h-auto text-blue-600 hover:text-blue-800 mt-2"
              onClick={() => window.location.href = notification.actionUrl!}
            >
              查看详情 →
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}

// 通知设置面板
interface NotificationSettingsProps {
  preferences: NotificationPreferences;
  onUpdatePreferences: (preferences: Partial<NotificationPreferences>) => void;
}

function NotificationSettings({ preferences, onUpdatePreferences }: NotificationSettingsProps) {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-4">通知设置</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-sm font-medium">启用通知</Label>
              <p className="text-xs text-gray-500">接收所有类型的通知</p>
            </div>
            <Switch
              checked={preferences.enabled}
              onCheckedChange={(enabled) => onUpdatePreferences({ enabled })}
            />
          </div>
          
          <Separator />
          
          <div className="space-y-3">
            <Label className="text-sm font-medium">通知方式</Label>
            
            <div className="flex items-center justify-between">
              <Label className="text-sm">浏览器推送</Label>
              <Switch
                checked={preferences.pushNotifications}
                onCheckedChange={(pushNotifications) => onUpdatePreferences({ pushNotifications })}
                disabled={!preferences.enabled}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label className="text-sm">邮件通知</Label>
              <Switch
                checked={preferences.emailNotifications}
                onCheckedChange={(emailNotifications) => onUpdatePreferences({ emailNotifications })}
                disabled={!preferences.enabled}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label className="text-sm">短信通知</Label>
              <Switch
                checked={preferences.smsNotifications}
                onCheckedChange={(smsNotifications) => onUpdatePreferences({ smsNotifications })}
                disabled={!preferences.enabled}
              />
            </div>
          </div>
          
          <Separator />
          
          <div className="space-y-3">
            <Label className="text-sm font-medium">通知类别</Label>
            
            {Object.entries(preferences.categories).map(([category, enabled]) => (
              <div key={category} className="flex items-center justify-between">
                <Label className="text-sm">{getCategoryLabel(category)}</Label>
                <Switch
                  checked={enabled}
                  onCheckedChange={(checked) => 
                    onUpdatePreferences({
                      categories: { ...preferences.categories, [category]: checked }
                    })
                  }
                  disabled={!preferences.enabled}
                />
              </div>
            ))}
          </div>
          
          <Separator />
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">勿扰时间</Label>
                <p className="text-xs text-gray-500">在指定时间段内静音通知</p>
              </div>
              <Switch
                checked={preferences.quietHours.enabled}
                onCheckedChange={(enabled) => 
                  onUpdatePreferences({
                    quietHours: { ...preferences.quietHours, enabled }
                  })
                }
                disabled={!preferences.enabled}
              />
            </div>
            
            {preferences.quietHours.enabled && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-xs text-gray-500">开始时间</Label>
                  <Input
                    type="time"
                    value={preferences.quietHours.start}
                    onChange={(e) => 
                      onUpdatePreferences({
                        quietHours: { ...preferences.quietHours, start: e.target.value }
                      })
                    }
                    className="h-8"
                  />
                </div>
                <div>
                  <Label className="text-xs text-gray-500">结束时间</Label>
                  <Input
                    type="time"
                    value={preferences.quietHours.end}
                    onChange={(e) => 
                      onUpdatePreferences({
                        quietHours: { ...preferences.quietHours, end: e.target.value }
                      })
                    }
                    className="h-8"
                  />
                </div>
              </div>
            )}
          </div>
          
          <Separator />
          
          <div className="space-y-3">
            <Label className="text-sm font-medium">通知频率</Label>
            <Select
              value={preferences.frequency}
              onValueChange={(frequency) => 
                onUpdatePreferences({ 
                  frequency: frequency as NotificationPreferences['frequency'] 
                })
              }
              disabled={!preferences.enabled}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="realtime">实时</SelectItem>
                <SelectItem value="hourly">每小时</SelectItem>
                <SelectItem value="daily">每日</SelectItem>
                <SelectItem value="weekly">每周</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
    </div>
  );
}

// 主通知系统组件
export function NotificationSystem() {
  const {
    notifications,
    preferences,
    activeToasts,
    stats,
    isLoading,
    markAsRead,
    markAllAsRead,
    toggleStar,
    deleteNotification,
    updatePreferences,
    dismissToast,
    requestNotificationPermission
  } = useNotifications();

  const [search, setSearch] = useState("");
  const [filter, setFilter] = useState<string>("all");
  const [activeTab, setActiveTab] = useState("all");

  // 过滤通知
  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = notification.title.toLowerCase().includes(search.toLowerCase()) ||
                         notification.message.toLowerCase().includes(search.toLowerCase());
    
    const matchesFilter = filter === "all" || 
                         (filter === "unread" && !notification.isRead) ||
                         (filter === "starred" && notification.isStarred) ||
                         (filter === notification.category);
    
    return matchesSearch && matchesFilter;
  });

  const handleRequestPermission = async () => {
    const granted = await requestNotificationPermission();
    if (granted) {
      updatePreferences({ pushNotifications: true });
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Toast 通知 */}
      {activeToasts.map(toast => (
        <ToastNotification
          key={toast.id}
          notification={toast}
          onDismiss={dismissToast}
        />
      ))}

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Bell className="h-6 w-6 text-blue-600" />
              <div>
                <CardTitle className="text-xl">通知中心</CardTitle>
                <p className="text-sm text-gray-500 mt-1">
                  {stats.total} 条通知，{stats.unread} 条未读
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {stats.unread > 0 && (
                <Button variant="outline" size="sm" onClick={markAllAsRead}>
                  全部标记已读
                </Button>
              )}
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Settings className="h-4 w-4 mr-2" />
                    设置
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>通知设置</DialogTitle>
                  </DialogHeader>
                  <NotificationSettings
                    preferences={preferences}
                    onUpdatePreferences={updatePreferences}
                  />
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {/* 统计卡片 */}
          <div className="grid grid-cols-4 gap-4 mb-6">
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <Bell className="h-4 w-4 text-blue-500" />
                <div>
                  <p className="text-2xl font-bold">{stats.total}</p>
                  <p className="text-xs text-gray-500">总计</p>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full" />
                <div>
                  <p className="text-2xl font-bold">{stats.unread}</p>
                  <p className="text-xs text-gray-500">未读</p>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-500" />
                <div>
                  <p className="text-2xl font-bold">{stats.starred}</p>
                  <p className="text-xs text-gray-500">星标</p>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-red-500" />
                <div>
                  <p className="text-2xl font-bold">{stats.urgent}</p>
                  <p className="text-xs text-gray-500">紧急</p>
                </div>
              </div>
            </Card>
          </div>

          {/* 搜索和过滤 */}
          <div className="flex items-center gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                placeholder="搜索通知..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={filter} onValueChange={setFilter}>
              <SelectTrigger className="w-40">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="unread">未读</SelectItem>
                <SelectItem value="starred">星标</SelectItem>
                <SelectItem value="student">学生</SelectItem>
                <SelectItem value="task">任务</SelectItem>
                <SelectItem value="system">系统</SelectItem>
                <SelectItem value="reminder">提醒</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 通知列表 */}
          <div className="space-y-1">
            {isLoading ? (
              <div className="text-center py-8 text-gray-500">
                加载中...
              </div>
            ) : filteredNotifications.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                {search || filter !== "all" ? "没有找到符合条件的通知" : "暂无通知"}
              </div>
            ) : (
              filteredNotifications.map(notification => (
                <NotificationItem
                  key={notification.id}
                  notification={notification}
                  onMarkAsRead={(id) => markAsRead([id])}
                  onToggleStar={toggleStar}
                  onDelete={deleteNotification}
                />
              ))
            )}
          </div>

          {/* 权限提醒 */}
          {!preferences.pushNotifications && (
            <Card className="mt-6 border-blue-200 bg-blue-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Bell className="h-5 w-5 text-blue-600" />
                  <div className="flex-1">
                    <h4 className="font-medium text-blue-900">启用推送通知</h4>
                    <p className="text-sm text-blue-700">
                      开启浏览器推送通知，及时接收重要提醒
                    </p>
                  </div>
                  <Button onClick={handleRequestPermission} size="sm">
                    启用
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// 工具函数
function getTimeAgo(timestamp: Date): string {
  const now = new Date();
  const diff = now.getTime() - timestamp.getTime();
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (minutes < 1) return "刚刚";
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;
  return timestamp.toLocaleDateString('zh-CN');
}

function getCategoryLabel(category: string): string {
  const labels = {
    student: "学生",
    task: "任务",
    system: "系统",
    marketing: "营销",
    reminder: "提醒"
  };
  return labels[category as keyof typeof labels] || category;
} 