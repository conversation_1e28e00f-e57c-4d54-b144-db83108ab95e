"use client";

import { Bad<PERSON> } from "@workspace/ui/components/badge";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@workspace/ui/components/select";
import { Textarea } from "@workspace/ui/components/textarea";
import {
    AlertCircle,
    Brain,
    Camera,
    CheckCircle,
    FileText,
    Loader2,
    Mail,
    Mic,
    Phone,
    School,
    Sparkles,
    Tag,
    User,
    X
} from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";

// 类型定义
interface SmartFormField {
  id: string;
  type: 'text' | 'textarea' | 'select' | 'number' | 'date' | 'phone' | 'email' | 'tags';
  label: string;
  placeholder?: string;
  required?: boolean;
  options?: string[];
  validation?: (value: any) => string | null;
  aiSuggestions?: boolean;
  voiceInput?: boolean;
  icon?: React.ElementType;
}

interface VoiceRecognition {
  isSupported: boolean;
  isListening: boolean;
  transcript: string;
  confidence: number;
}

interface OCRResult {
  text: string;
  confidence: number;
  fields: Record<string, string>;
}

// 语音识别Hook
function useVoiceRecognition() {
  const [voiceState, setVoiceState] = useState<VoiceRecognition>({
    isSupported: false,
    isListening: false,
    transcript: '',
    confidence: 0
  });

  const recognitionRef = useRef<any>(null);

  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      
      recognitionRef.current.continuous = true;
      recognitionRef.current.interimResults = true;
      recognitionRef.current.lang = 'zh-CN';

      recognitionRef.current.onresult = (event: any) => {
        let finalTranscript = '';
        let confidence = 0;
        
        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i];
          if (result.isFinal) {
            finalTranscript += result[0].transcript;
            confidence = result[0].confidence;
          }
        }

        if (finalTranscript) {
          setVoiceState(prev => ({
            ...prev,
            transcript: finalTranscript,
            confidence
          }));
        }
      };

      recognitionRef.current.onerror = (event: any) => {
        console.error('语音识别错误:', event.error);
        setVoiceState(prev => ({ ...prev, isListening: false }));
      };

      recognitionRef.current.onend = () => {
        setVoiceState(prev => ({ ...prev, isListening: false }));
      };

      setVoiceState(prev => ({ ...prev, isSupported: true }));
    }
  }, []);

  const startListening = useCallback(() => {
    if (recognitionRef.current && !voiceState.isListening) {
      recognitionRef.current.start();
      setVoiceState(prev => ({ ...prev, isListening: true, transcript: '' }));
    }
  }, [voiceState.isListening]);

  const stopListening = useCallback(() => {
    if (recognitionRef.current && voiceState.isListening) {
      recognitionRef.current.stop();
      setVoiceState(prev => ({ ...prev, isListening: false }));
    }
  }, [voiceState.isListening]);

  return {
    ...voiceState,
    startListening,
    stopListening
  };
}

// OCR处理Hook
function useOCRProcessor() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<OCRResult | null>(null);

  const processImage = useCallback(async (file: File): Promise<OCRResult> => {
    setIsProcessing(true);
    
    try {
      // 模拟OCR API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 模拟OCR结果
      const mockResult: OCRResult = {
        text: "张小明 男 2008年5月15日 北京四中 高二年级 父母：张父 13600136000",
        confidence: 0.95,
        fields: {
          name: "张小明",
          gender: "男",
          birthday: "2008年5月15日",
          school: "北京四中",
          grade: "高二年级",
          parentName: "张父",
          parentPhone: "13600136000"
        }
      };
      
      setResult(mockResult);
      return mockResult;
    } catch (error) {
      throw new Error('OCR处理失败');
    } finally {
      setIsProcessing(false);
    }
  }, []);

  return {
    processImage,
    isProcessing,
    result,
    clearResult: () => setResult(null)
  };
}

// 智能建议Hook
function useAISuggestions(fieldType: string, value: string) {
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const getSuggestions = useCallback(async (input: string) => {
    if (!input || input.length < 2) {
      setSuggestions([]);
      return;
    }

    setIsLoading(true);
    
    try {
      // 模拟AI建议API
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const mockSuggestions: Record<string, string[]> = {
        school: ['北京四中', '清华附中', '人大附中', '北师大附中', '北京八中'],
        tags: ['理科强', '数学竞赛', '编程爱好', '英语优秀', '艺术特长', '体育特长'],
        servicePhase: ['初次咨询', '需求分析', '方案制定', '执行跟踪', '成果验收'],
        notes: ['学习成绩优秀', '需要重点关注', '家长期望较高', '学习自主性强']
      };
      
      const fieldSuggestions = mockSuggestions[fieldType] || [];
      const filtered = fieldSuggestions.filter(s => 
        s.toLowerCase().includes(input.toLowerCase())
      );
      
      setSuggestions(filtered);
    } catch (error) {
      console.error('获取建议失败:', error);
    } finally {
      setIsLoading(false);
    }
  }, [fieldType]);

  useEffect(() => {
    const timer = setTimeout(() => getSuggestions(value), 300);
    return () => clearTimeout(timer);
  }, [value, getSuggestions]);

  return { suggestions, isLoading };
}

// 语音输入组件
function VoiceInput({ onTranscript, className = "" }: { 
  onTranscript: (text: string) => void;
  className?: string;
}) {
  const { isSupported, isListening, transcript, startListening, stopListening } = useVoiceRecognition();

  useEffect(() => {
    if (transcript) {
      onTranscript(transcript);
    }
  }, [transcript, onTranscript]);

  if (!isSupported) return null;

  return (
    <Button
      type="button"
      variant={isListening ? "destructive" : "outline"}
      size="sm"
      className={className}
      onMouseDown={startListening}
      onMouseUp={stopListening}
      onMouseLeave={stopListening}
    >
      <Mic className={`w-4 h-4 ${isListening ? 'animate-pulse' : ''}`} />
      {isListening ? '松开结束' : '按住说话'}
    </Button>
  );
}

// 拍照识别组件
function CameraInput({ onResult, className = "" }: {
  onResult: (result: OCRResult) => void;
  className?: string;
}) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { processImage, isProcessing } = useOCRProcessor();

  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const result = await processImage(file);
      onResult(result);
    } catch (error) {
      console.error('文档识别失败:', error);
    }
  }, [processImage, onResult]);

  return (
    <>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        capture="environment"
        onChange={handleFileSelect}
        className="hidden"
      />
      <Button
        type="button"
        variant="outline"
        size="sm"
        className={className}
        onClick={() => fileInputRef.current?.click()}
        disabled={isProcessing}
      >
        {isProcessing ? (
          <Loader2 className="w-4 h-4 animate-spin" />
        ) : (
          <Camera className="w-4 h-4" />
        )}
        {isProcessing ? '识别中...' : '拍照识别'}
      </Button>
    </>
  );
}

// 标签输入组件
function TagsInput({ value = [], onChange, suggestions = [] }: {
  value: string[];
  onChange: (tags: string[]) => void;
  suggestions?: string[];
}) {
  const [inputValue, setInputValue] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);

  const addTag = useCallback((tag: string) => {
    if (tag && !value.includes(tag)) {
      onChange([...value, tag]);
      setInputValue('');
      setShowSuggestions(false);
    }
  }, [value, onChange]);

  const removeTag = useCallback((tagToRemove: string) => {
    onChange(value.filter(tag => tag !== tagToRemove));
  }, [value, onChange]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      addTag(inputValue.trim());
    } else if (e.key === 'Backspace' && !inputValue && value.length > 0) {
      removeTag(value[value.length - 1]);
    }
  }, [inputValue, addTag, removeTag, value]);

  const filteredSuggestions = suggestions.filter(s => 
    s.toLowerCase().includes(inputValue.toLowerCase()) && !value.includes(s)
  );

  return (
    <div className="relative">
      <div className="flex flex-wrap gap-1 p-2 border rounded-md min-h-[40px] focus-within:ring-2 focus-within:ring-primary">
        {value.map(tag => (
          <Badge key={tag} variant="secondary" className="flex items-center gap-1">
            {tag}
            <X 
              className="w-3 h-3 cursor-pointer hover:text-destructive" 
              onClick={() => removeTag(tag)}
            />
          </Badge>
        ))}
        <input
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => setShowSuggestions(true)}
          onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
          placeholder={value.length === 0 ? "输入标签，按Enter添加" : ""}
          className="flex-1 min-w-[120px] bg-transparent outline-none"
        />
      </div>

      {showSuggestions && filteredSuggestions.length > 0 && (
        <div className="absolute z-10 w-full mt-1 bg-white border rounded-md shadow-md max-h-40 overflow-y-auto">
          {filteredSuggestions.map(suggestion => (
            <div
              key={suggestion}
              className="px-3 py-2 cursor-pointer hover:bg-muted"
              onClick={() => addTag(suggestion)}
            >
              {suggestion}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// 智能字段组件
function SmartField({ field, value, onChange, className = "" }: {
  field: SmartFormField;
  value: any;
  onChange: (value: any) => void;
  className?: string;
}) {
  const { suggestions } = useAISuggestions(field.id, value?.toString() || '');
  const [showSuggestions, setShowSuggestions] = useState(false);

  const handleVoiceTranscript = useCallback((transcript: string) => {
    onChange(transcript);
  }, [onChange]);

  const handleOCRResult = useCallback((result: OCRResult) => {
    // 尝试从OCR结果中提取对应字段
    const extractedValue = result.fields[field.id];
    if (extractedValue) {
      onChange(extractedValue);
    }
  }, [field.id, onChange]);

  const Icon = field.icon;

  return (
    <div className={`space-y-2 ${className}`}>
      <Label className="flex items-center gap-2">
        {Icon && <Icon className="w-4 h-4" />}
        {field.label}
        {field.required && <span className="text-destructive">*</span>}
      </Label>

      <div className="relative">
        <div className="flex gap-2">
          {field.type === 'tags' ? (
            <TagsInput
              value={value || []}
              onChange={onChange}
              suggestions={suggestions}
            />
          ) : field.type === 'select' ? (
            <Select value={value} onValueChange={onChange}>
              <SelectTrigger>
                <SelectValue placeholder={field.placeholder} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map(option => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : field.type === 'textarea' ? (
            <Textarea
              value={value || ''}
              onChange={(e) => onChange(e.target.value)}
              placeholder={field.placeholder}
              className="min-h-[80px]"
              onFocus={() => setShowSuggestions(true)}
              onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
            />
          ) : (
            <Input
              type={field.type === 'phone' ? 'tel' : field.type}
              value={value || ''}
              onChange={(e) => onChange(e.target.value)}
              placeholder={field.placeholder}
              onFocus={() => setShowSuggestions(true)}
              onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
            />
          )}

          {/* 智能功能按钮 */}
          <div className="flex gap-1">
            {field.voiceInput && (
              <VoiceInput onTranscript={handleVoiceTranscript} />
            )}
            <CameraInput onResult={handleOCRResult} />
          </div>
        </div>

        {/* AI建议下拉 */}
        {showSuggestions && suggestions.length > 0 && field.type !== 'tags' && (
          <div className="absolute z-10 w-full mt-1 bg-white border rounded-md shadow-md max-h-40 overflow-y-auto">
            {suggestions.map(suggestion => (
              <div
                key={suggestion}
                className="px-3 py-2 cursor-pointer hover:bg-muted flex items-center gap-2"
                onClick={() => {
                  onChange(suggestion);
                  setShowSuggestions(false);
                }}
              >
                <Sparkles className="w-3 h-3 text-primary" />
                {suggestion}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

// 主要的智能表单组件
export function SmartStudentForm({ onSubmit, initialData, isLoading }: {
  onSubmit: (data: any) => void;
  initialData?: any;
  isLoading?: boolean;
}) {
  const [formData, setFormData] = useState(initialData || {});
  const [errors, setErrors] = useState<Record<string, string>>({});

  const formFields: SmartFormField[] = [
    {
      id: 'name',
      type: 'text',
      label: '学生姓名',
      placeholder: '请输入学生姓名',
      required: true,
      voiceInput: true,
      icon: User
    },
    {
      id: 'grade',
      type: 'select',
      label: '年级',
      placeholder: '选择年级',
      required: true,
      options: ['初一', '初二', '初三', '高一', '高二', '高三'],
      icon: School
    },
    {
      id: 'school',
      type: 'text',
      label: '学校',
      placeholder: '请输入学校名称',
      aiSuggestions: true,
      voiceInput: true,
      icon: School
    },
    {
      id: 'parentName',
      type: 'text',
      label: '家长姓名',
      placeholder: '请输入家长姓名',
      voiceInput: true,
      icon: User
    },
    {
      id: 'parentPhone',
      type: 'phone',
      label: '家长电话',
      placeholder: '请输入家长联系电话',
      required: true,
      icon: Phone
    },
    {
      id: 'parentEmail',
      type: 'email',
      label: '家长邮箱',
      placeholder: '请输入家长邮箱（可选）',
      icon: Mail
    },
    {
      id: 'tags',
      type: 'tags',
      label: '学生标签',
      placeholder: '添加学生特点标签',
      aiSuggestions: true,
      icon: Tag
    },
    {
      id: 'notes',
      type: 'textarea',
      label: '备注信息',
      placeholder: '记录学生的其他重要信息...',
      voiceInput: true,
      icon: FileText
    }
  ];

  const updateField = useCallback((fieldId: string, value: any) => {
    setFormData(prev => ({ ...prev, [fieldId]: value }));
    // 清除该字段的错误
    if (errors[fieldId]) {
      setErrors(prev => ({ ...prev, [fieldId]: '' }));
    }
  }, [errors]);

  const validateForm = useCallback(() => {
    const newErrors: Record<string, string> = {};

    formFields.forEach(field => {
      if (field.required && !formData[field.id]) {
        newErrors[field.id] = `${field.label}是必填项`;
      }

      if (field.validation) {
        const error = field.validation(formData[field.id]);
        if (error) {
          newErrors[field.id] = error;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData, formFields]);

  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
    }
  }, [formData, validateForm, onSubmit]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="w-5 h-5" />
          智能学生档案录入
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {formFields.map(field => (
              <div key={field.id} className={field.type === 'textarea' ? 'md:col-span-2' : ''}>
                <SmartField
                  field={field}
                  value={formData[field.id]}
                  onChange={(value) => updateField(field.id, value)}
                />
                {errors[field.id] && (
                  <div className="flex items-center gap-1 mt-1 text-sm text-destructive">
                    <AlertCircle className="w-3 h-3" />
                    {errors[field.id]}
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="flex justify-end gap-3 pt-6 border-t">
            <Button type="button" variant="outline">
              取消
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  保存中...
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  保存学生信息
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
} 