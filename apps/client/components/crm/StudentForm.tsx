"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Textarea } from "@workspace/ui/components/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@workspace/ui/components/avatar";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { 
  User, 
  School, 
  Home, 
  Phone, 
  Mail, 
  Calendar,
  Tag,
  FileText,
  Save,
  X,
  Upload
} from "lucide-react";
import { Student } from "@/hooks/useCRMStudents";

// 表单验证 schema
const studentFormSchema = z.object({
  name: z.string().min(1, "学生姓名不能为空"),
  gender: z.string().optional(),
  birthday: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email("请输入有效的邮箱地址").optional().or(z.literal("")),
  avatar: z.string().optional(),
  
  // 学业信息
  school: z.string().optional(),
  grade: z.string().optional(),
  major: z.string().optional(),
  gpa: z.number().min(0).max(4).optional(),
  rank: z.number().min(1).optional(),
  
  // 家庭信息
  parentName: z.string().optional(),
  parentPhone: z.string().min(1, "家长电话不能为空"),
  parentEmail: z.string().email("请输入有效的邮箱地址").optional().or(z.literal("")),
  familyBackground: z.string().optional(),
  
  // 标签和备注
  tags: z.array(z.string()).default([]),
  notes: z.string().optional(),
  
  // 状态
  status: z.enum(["ACTIVE", "GRADUATED", "INACTIVE"]).default("ACTIVE")
});

type StudentFormData = z.infer<typeof studentFormSchema>;

interface StudentFormProps {
  student?: Student;
  onSubmit: (data: StudentFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

const gradeOptions = [
  "小学一年级", "小学二年级", "小学三年级", "小学四年级", "小学五年级", "小学六年级",
  "初一", "初二", "初三",
  "高一", "高二", "高三",
  "大一", "大二", "大三", "大四",
  "研一", "研二", "研三"
];

const statusOptions = [
  { value: "ACTIVE", label: "在读" },
  { value: "GRADUATED", label: "已毕业" },
  { value: "INACTIVE", label: "暂停" }
];

export function StudentForm({ student, onSubmit, onCancel, isLoading = false }: StudentFormProps) {
  const [tagInput, setTagInput] = useState("");
  const [tags, setTags] = useState<string[]>(student?.tags || []);

  const form = useForm<StudentFormData>({
    resolver: zodResolver(studentFormSchema),
    defaultValues: {
      name: student?.name || "",
      gender: student?.gender || "",
      birthday: student?.birthday || "",
      phone: student?.phone || "",
      email: student?.email || "",
      avatar: student?.avatar || "",
      school: student?.school || "",
      grade: student?.grade || "",
      major: student?.major || "",
      gpa: student?.gpa || undefined,
      rank: student?.rank || undefined,
      parentName: student?.parentName || "",
      parentPhone: student?.parentPhone || "",
      parentEmail: student?.parentEmail || "",
      familyBackground: student?.familyBackground || "",
      tags: student?.tags || [],
      notes: student?.notes || "",
      status: (student?.status as "ACTIVE" | "GRADUATED" | "INACTIVE") || "ACTIVE"
    }
  });

  const handleSubmit = async (data: StudentFormData) => {
    try {
      await onSubmit({ ...data, tags });
    } catch (error) {
      console.error("提交表单失败:", error);
    }
  };

  const addTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      const newTags = [...tags, tagInput.trim()];
      setTags(newTags);
      form.setValue("tags", newTags);
      setTagInput("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    const newTags = tags.filter(tag => tag !== tagToRemove);
    setTags(newTags);
    form.setValue("tags", newTags);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addTag();
    }
  };

  return (
    <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 基本信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5" />
              基本信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 头像上传 */}
            <div className="flex flex-col items-center gap-4">
              <Avatar className="h-20 w-20">
                <AvatarImage src={form.watch("avatar") || undefined} />
                <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-semibold text-xl">
                  {form.watch("name")?.charAt(0) || "?"}
                </AvatarFallback>
              </Avatar>
              <Button type="button" variant="outline" size="sm">
                <Upload className="w-4 h-4 mr-2" />
                上传头像
              </Button>
            </div>

            <div className="space-y-2">
              <Label htmlFor="name">学生姓名 *</Label>
              <Input
                id="name"
                {...form.register("name")}
                placeholder="请输入学生姓名"
              />
              {form.formState.errors.name && (
                <p className="text-sm text-destructive">{form.formState.errors.name.message}</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="gender">性别</Label>
                <Select value={form.watch("gender")} onValueChange={(value) => form.setValue("gender", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择性别" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="男">男</SelectItem>
                    <SelectItem value="女">女</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="birthday">出生日期</Label>
                <Input
                  id="birthday"
                  type="date"
                  {...form.register("birthday")}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">联系电话</Label>
              <Input
                id="phone"
                {...form.register("phone")}
                placeholder="请输入联系电话"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">邮箱</Label>
              <Input
                id="email"
                type="email"
                {...form.register("email")}
                placeholder="请输入邮箱地址"
              />
              {form.formState.errors.email && (
                <p className="text-sm text-destructive">{form.formState.errors.email.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">状态</Label>
              <Select value={form.watch("status")} onValueChange={(value) => form.setValue("status", value as "ACTIVE" | "GRADUATED" | "INACTIVE")}>
                <SelectTrigger>
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* 学业信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <School className="w-5 h-5" />
              学业信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="school">学校</Label>
              <Input
                id="school"
                {...form.register("school")}
                placeholder="请输入学校名称"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="grade">年级</Label>
              <Select value={form.watch("grade")} onValueChange={(value) => form.setValue("grade", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择年级" />
                </SelectTrigger>
                <SelectContent>
                  {gradeOptions.map(grade => (
                    <SelectItem key={grade} value={grade}>
                      {grade}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="major">专业</Label>
              <Input
                id="major"
                {...form.register("major")}
                placeholder="请输入专业名称"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="gpa">GPA</Label>
                <Input
                  id="gpa"
                  type="number"
                  step="0.01"
                  min="0"
                  max="4"
                  {...form.register("gpa", { valueAsNumber: true })}
                  placeholder="0.00"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="rank">排名</Label>
                <Input
                  id="rank"
                  type="number"
                  min="1"
                  {...form.register("rank", { valueAsNumber: true })}
                  placeholder="请输入排名"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 家庭信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Home className="w-5 h-5" />
              家庭信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="parentName">家长姓名</Label>
              <Input
                id="parentName"
                {...form.register("parentName")}
                placeholder="请输入家长姓名"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="parentPhone">家长电话 *</Label>
              <Input
                id="parentPhone"
                {...form.register("parentPhone")}
                placeholder="请输入家长联系电话"
              />
              {form.formState.errors.parentPhone && (
                <p className="text-sm text-destructive">{form.formState.errors.parentPhone.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="parentEmail">家长邮箱</Label>
              <Input
                id="parentEmail"
                type="email"
                {...form.register("parentEmail")}
                placeholder="请输入家长邮箱"
              />
              {form.formState.errors.parentEmail && (
                <p className="text-sm text-destructive">{form.formState.errors.parentEmail.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="familyBackground">家庭背景</Label>
              <Textarea
                id="familyBackground"
                {...form.register("familyBackground")}
                placeholder="请输入家庭背景信息"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 标签和备注 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Tag className="w-5 h-5" />
              标签
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <Input
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="输入标签后按回车添加"
              />
              <Button type="button" onClick={addTag} variant="outline">
                添加
              </Button>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {tags.map((tag, index) => (
                <Badge key={index} variant="secondary" className="gap-1">
                  {tag}
                  <X 
                    className="w-3 h-3 cursor-pointer hover:text-destructive" 
                    onClick={() => removeTag(tag)}
                  />
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5" />
              备注
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              {...form.register("notes")}
              placeholder="请输入备注信息..."
              rows={6}
            />
          </CardContent>
        </Card>
      </div>

      {/* 操作按钮 */}
      <div className="flex justify-end gap-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          取消
        </Button>
        <Button type="submit" disabled={isLoading}>
          <Save className="w-4 h-4 mr-2" />
          {isLoading ? "保存中..." : "保存"}
        </Button>
      </div>
    </form>
  );
}
