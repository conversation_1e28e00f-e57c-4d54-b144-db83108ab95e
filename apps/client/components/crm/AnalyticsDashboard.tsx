"use client";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@workspace/ui/components/card";
import { Progress } from "@workspace/ui/components/progress";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@workspace/ui/components/select";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@workspace/ui/components/tabs";
import { motion } from "framer-motion";
import {
    AlertCircle,
    Award,
    BarChart3,
    Brain,
    CheckCircle,
    Clock,
    DollarSign,
    Download,
    Filter,
    Heart,
    PieChart,
    Target,
    TrendingDown,
    TrendingUp,
    Users,
    Zap
} from "lucide-react";
import { useMemo, useState } from "react";

// 数据类型定义
interface MetricCard {
  id: string;
  title: string;
  value: string | number;
  change: number;
  trend: 'up' | 'down' | 'stable';
  icon: React.ElementType;
  color: string;
  description?: string;
}

interface TimeSeriesData {
  date: string;
  value: number;
  target?: number;
  label?: string;
}

interface DistributionData {
  name: string;
  value: number;
  color: string;
  percentage: number;
}

interface StudentMetrics {
  totalStudents: number;
  activeStudents: number;
  newThisMonth: number;
  graduatedThisMonth: number;
  averageSatisfaction: number;
  retentionRate: number;
}

interface ServiceMetrics {
  completedSessions: number;
  averageSessionTime: number;
  responseTime: number;
  milestoneCompletionRate: number;
  reportGeneratedCount: number;
  parentSatisfaction: number;
}

interface PerformanceData {
  students: StudentMetrics;
  service: ServiceMetrics;
  revenue: {
    thisMonth: number;
    lastMonth: number;
    yearToDate: number;
    averagePerStudent: number;
  };
  efficiency: {
    tasksCompleted: number;
    onTimeRate: number;
    productivityScore: number;
    workloadBalance: number;
  };
}

const AnalyticsDashboard = ({ data, dateRange, onDateRangeChange }) => {
  const [selectedTab, setSelectedTab] = useState("overview");
  const [timeFrame, setTimeFrame] = useState("month");
  const [isLoading, setIsLoading] = useState(false);

  // 核心指标卡片数据
  const metricCards: MetricCard[] = useMemo(() => [
    {
      id: 'total-students',
      title: '管理学生',
      value: data.students.totalStudents,
      change: data.students.newThisMonth,
      trend: data.students.newThisMonth > 0 ? 'up' : 'stable',
      icon: Users,
      color: 'bg-blue-500',
      description: `本月新增 ${data.students.newThisMonth} 人`
    },
    {
      id: 'satisfaction',
      title: '满意度',
      value: `${data.students.averageSatisfaction.toFixed(1)}`,
      change: 0.3,
      trend: 'up',
      icon: Heart,
      color: 'bg-red-500',
      description: '较上月提升 0.3 分'
    },
    {
      id: 'completion-rate',
      title: '里程碑完成率',
      value: `${data.service.milestoneCompletionRate}%`,
      change: 5,
      trend: 'up',
      icon: Target,
      color: 'bg-green-500',
      description: '较上月提升 5%'
    },
    {
      id: 'response-time',
      title: '平均响应时间',
      value: `${data.service.responseTime}h`,
      change: -2,
      trend: 'up',
      icon: Clock,
      color: 'bg-purple-500',
      description: '响应速度提升 2 小时'
    },
    {
      id: 'revenue',
      title: '本月收入',
      value: `¥${data.revenue.thisMonth.toLocaleString()}`,
      change: ((data.revenue.thisMonth - data.revenue.lastMonth) / data.revenue.lastMonth * 100),
      trend: data.revenue.thisMonth > data.revenue.lastMonth ? 'up' : 'down',
      icon: DollarSign,
      color: 'bg-yellow-500',
      description: `环比${data.revenue.thisMonth > data.revenue.lastMonth ? '增长' : '下降'}`
    },
    {
      id: 'productivity',
      title: '生产力评分',
      value: data.efficiency.productivityScore,
      change: 12,
      trend: 'up',
      icon: Zap,
      color: 'bg-indigo-500',
      description: '工作效率持续提升'
    }
  ], [data]);

  // 学生阶段分布数据
  const studentPhaseDistribution: DistributionData[] = [
    { name: '初次咨询', value: 15, color: '#3b82f6', percentage: 25 },
    { name: '评估分析', value: 12, color: '#8b5cf6', percentage: 20 },
    { name: '方案制定', value: 18, color: '#10b981', percentage: 30 },
    { name: '执行跟踪', value: 9, color: '#f59e0b', percentage: 15 },
    { name: '成果验收', value: 6, color: '#ef4444', percentage: 10 },
  ];

  // 时间趋势数据
  const performanceTrend: TimeSeriesData[] = [
    { date: '1月', value: 85, target: 80 },
    { date: '2月', value: 88, target: 80 },
    { date: '3月', value: 92, target: 80 },
    { date: '4月', value: 89, target: 80 },
    { date: '5月', value: 94, target: 80 },
    { date: '6月', value: 96, target: 80 },
  ];

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return TrendingUp;
      case 'down': return TrendingDown;
      default: return BarChart3;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'text-green-600';
      case 'down': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* 顶部控制栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">数据分析</h1>
          <p className="text-muted-foreground mt-1">
            全面分析您的服务表现和学生进展
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <Select value={timeFrame} onValueChange={setTimeFrame}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">本周</SelectItem>
              <SelectItem value="month">本月</SelectItem>
              <SelectItem value="quarter">本季度</SelectItem>
              <SelectItem value="year">本年</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            筛选
          </Button>
          
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            导出报告
          </Button>
        </div>
      </div>

      {/* 关键指标卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {metricCards.map((metric, index) => {
          const TrendIcon = getTrendIcon(metric.trend);
          
          return (
            <motion.div
              key={metric.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="hover:shadow-md transition-all duration-200">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className={`p-2 rounded-lg ${metric.color} text-white`}>
                      <metric.icon className="w-5 h-5" />
                    </div>
                    <div className={`flex items-center gap-1 text-sm ${getTrendColor(metric.trend)}`}>
                      <TrendIcon className="w-4 h-4" />
                      <span>{Math.abs(metric.change)}</span>
                    </div>
                  </div>
                  
                  <div className="mt-3">
                    <div className="text-2xl font-bold">{metric.value}</div>
                    <div className="text-sm text-muted-foreground">{metric.title}</div>
                    {metric.description && (
                      <div className="text-xs text-muted-foreground mt-1">
                        {metric.description}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* 详细分析标签页 */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="students">学生分析</TabsTrigger>
          <TabsTrigger value="service">服务质量</TabsTrigger>
          <TabsTrigger value="performance">效能分析</TabsTrigger>
        </TabsList>

        {/* 概览标签页 */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 学生阶段分布 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="w-5 h-5" />
                  学生服务阶段分布
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {studentPhaseDistribution.map((phase, index) => (
                    <motion.div
                      key={phase.name}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-3">
                        <div 
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: phase.color }}
                        />
                        <span className="text-sm">{phase.name}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">{phase.value}人</span>
                        <span className="text-xs text-muted-foreground">
                          {phase.percentage}%
                        </span>
                      </div>
                    </motion.div>
                  ))}
                </div>
                
                <div className="mt-4 pt-4 border-t">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">总计</span>
                    <span className="font-medium">
                      {studentPhaseDistribution.reduce((sum, phase) => sum + phase.value, 0)} 人
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 月度表现趋势 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  月度表现趋势
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {performanceTrend.slice(-3).map((item, index) => (
                    <div key={item.date} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>{item.date}</span>
                        <span className="font-medium">{item.value}分</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Progress value={item.value} className="flex-1" />
                        <span className="text-xs text-muted-foreground">
                          目标: {item.target}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="mt-4 pt-4 border-t text-center">
                  <div className="text-sm text-muted-foreground mb-1">
                    综合评分
                  </div>
                  <div className="text-2xl font-bold text-green-600">
                    {performanceTrend[performanceTrend.length - 1].value}分
                  </div>
                  <div className="text-xs text-muted-foreground">
                    超出目标 {performanceTrend[performanceTrend.length - 1].value - performanceTrend[performanceTrend.length - 1].target} 分
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 预警和建议 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="w-5 h-5" />
                智能洞察与建议
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-start gap-3 p-3 bg-red-50 rounded-lg">
                    <AlertCircle className="w-5 h-5 text-red-500 mt-0.5" />
                    <div>
                      <div className="font-medium text-red-800">注意</div>
                      <div className="text-sm text-red-700">
                        3名学生的服务进度明显滞后，建议主动跟进
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg">
                    <Clock className="w-5 h-5 text-yellow-500 mt-0.5" />
                    <div>
                      <div className="font-medium text-yellow-800">提醒</div>
                      <div className="text-sm text-yellow-700">
                        本周有8个里程碑即将到期，记得及时完成
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <div>
                      <div className="font-medium text-green-800">优势</div>
                      <div className="text-sm text-green-700">
                        家长满意度评分持续提升，服务质量得到认可
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                    <Award className="w-5 h-5 text-blue-500 mt-0.5" />
                    <div>
                      <div className="font-medium text-blue-800">成就</div>
                      <div className="text-sm text-blue-700">
                        本月完成率达96%，创历史新高！
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 学生分析标签页 */}
        <TabsContent value="students" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>学生生命周期分析</CardTitle>
              </CardHeader>
              <CardContent>
                {/* 这里可以放置更详细的学生分析图表 */}
                <div className="h-64 flex items-center justify-center text-muted-foreground">
                  学生生命周期流程图
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>满意度分析</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { label: '非常满意', value: 65, color: 'bg-green-500' },
                    { label: '满意', value: 25, color: 'bg-blue-500' },
                    { label: '一般', value: 8, color: 'bg-yellow-500' },
                    { label: '不满意', value: 2, color: 'bg-red-500' },
                  ].map(item => (
                    <div key={item.label} className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>{item.label}</span>
                        <span className="font-medium">{item.value}%</span>
                      </div>
                      <div className={`h-2 rounded-full ${item.color}`} 
                           style={{ width: `${item.value}%` }} />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 服务质量标签页 */}
        <TabsContent value="service" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[
              { title: '平均咨询时长', value: '45min', trend: '+5min', color: 'text-blue-600' },
              { title: '问题解决率', value: '94%', trend: '+3%', color: 'text-green-600' },
              { title: '家长参与度', value: '87%', trend: '+2%', color: 'text-purple-600' },
              { title: '续费率', value: '89%', trend: '+1%', color: 'text-orange-600' },
            ].map(metric => (
              <Card key={metric.title}>
                <CardContent className="p-4 text-center">
                  <div className={`text-2xl font-bold ${metric.color}`}>
                    {metric.value}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {metric.title}
                  </div>
                  <div className="text-xs text-green-600 mt-1">
                    {metric.trend}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* 效能分析标签页 */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>工作效率分析</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">任务完成效率</span>
                    <span className="text-sm font-medium">92%</span>
                  </div>
                  <Progress value={92} />
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm">准时率</span>
                    <span className="text-sm font-medium">88%</span>
                  </div>
                  <Progress value={88} />
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm">工作负载平衡度</span>
                    <span className="text-sm font-medium">76%</span>
                  </div>
                  <Progress value={76} />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>收入分析</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">
                      ¥{data.revenue.thisMonth.toLocaleString()}
                    </div>
                    <div className="text-sm text-muted-foreground">本月收入</div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-lg font-semibold">
                        ¥{data.revenue.averagePerStudent.toLocaleString()}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        单学生平均收入
                      </div>
                    </div>
                    <div>
                      <div className="text-lg font-semibold">
                        ¥{data.revenue.yearToDate.toLocaleString()}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        年度累计收入
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AnalyticsDashboard; 