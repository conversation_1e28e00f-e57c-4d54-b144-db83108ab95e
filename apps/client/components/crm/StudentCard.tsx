"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@workspace/ui/components/avatar";
import { Badge } from "@workspace/ui/components/badge";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { <PERSON>, CardContent, CardHeader } from "@workspace/ui/components/card";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { Progress } from "@workspace/ui/components/progress";
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@workspace/ui/components/tooltip";
import { AnimatePresence, motion } from "framer-motion";
import {
    Calendar,
    CheckCircle,
    Clock,
    FileText,
    GraduationCap,
    Home,
    MessageSquare,
    MoreVertical,
    Phone,
    Star
} from "lucide-react";
import { useState } from "react";

interface Student {
  id: string;
  name: string;
  avatar?: string;
  grade: string;
  school: string;
  tags: string[];
  status: "ACTIVE" | "GRADUATED" | "INACTIVE";
  parentName?: string;
  parentPhone?: string;
  
  // 服务相关
  servicePhase: string;
  progress: number;
  nextAppointment?: string;
  satisfaction: number;
  urgency: "low" | "medium" | "high";
  
  // 统计数据
  totalSessions: number;
  completedMilestones: number;
  totalMilestones: number;
  joinDate: string;
}

interface StudentCardProps {
  student: Student;
  viewMode: "compact" | "detailed" | "kanban";
  onEdit: (student: Student) => void;
  onMessage: (student: Student) => void;
  onSchedule: (student: Student) => void;
  onGenerateReport: (student: Student) => void;
}

const getStatusConfig = (status: Student["status"]) => {
  const configs = {
    ACTIVE: { 
      color: "bg-green-100 text-green-800 border-green-300", 
      icon: CheckCircle, 
      label: "服务中" 
    },
    GRADUATED: { 
      color: "bg-blue-100 text-blue-800 border-blue-300", 
      icon: GraduationCap, 
      label: "已毕业" 
    },
    INACTIVE: { 
      color: "bg-gray-100 text-gray-800 border-gray-300", 
      icon: Clock, 
      label: "暂停中" 
    },
  };
  return configs[status];
};

const getUrgencyConfig = (urgency: Student["urgency"]) => {
  const configs = {
    low: { color: "bg-gray-500", label: "正常" },
    medium: { color: "bg-yellow-500", label: "关注" },
    high: { color: "bg-red-500", label: "紧急" },
  };
  return configs[urgency];
};

export function StudentCard({ student, viewMode, onEdit, onMessage, onSchedule, onGenerateReport }: StudentCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const statusConfig = getStatusConfig(student.status);
  const urgencyConfig = getUrgencyConfig(student.urgency);
  const StatusIcon = statusConfig.icon;

  // 紧凑视图 - 适用于列表展示
  if (viewMode === "compact") {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        whileHover={{ scale: 1.02 }}
        transition={{ duration: 0.2 }}
      >
        <Card className="hover:shadow-md transition-all duration-200 cursor-pointer">
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              {/* 头像和基本信息 */}
              <div className="flex items-center gap-3 flex-1">
                <div className="relative">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={student.avatar} />
                    <AvatarFallback className="bg-primary/10 text-primary font-medium">
                      {student.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  {/* 紧急程度指示器 */}
                  <div 
                    className={`absolute -top-1 -right-1 w-3 h-3 rounded-full ${urgencyConfig.color} border-2 border-white`}
                    title={urgencyConfig.label}
                  />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-semibold text-sm truncate">{student.name}</h4>
                    <Badge variant="outline" className={`text-xs ${statusConfig.color}`}>
                      <StatusIcon className="w-3 h-3 mr-1" />
                      {statusConfig.label}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <GraduationCap className="w-3 h-3" />
                      {student.grade}
                    </span>
                    <span className="flex items-center gap-1">
                      <Home className="w-3 h-3" />
                      {student.school}
                    </span>
                  </div>
                </div>
              </div>

              {/* 进度和统计 */}
              <div className="text-center min-w-0">
                <div className="text-xs text-muted-foreground mb-1">服务进度</div>
                <div className="flex items-center gap-2">
                  <Progress value={student.progress} className="w-16 h-2" />
                  <span className="text-xs font-medium">{student.progress}%</span>
                </div>
              </div>

              {/* 满意度 */}
              <div className="text-center min-w-0">
                <div className="text-xs text-muted-foreground mb-1">满意度</div>
                <div className="flex items-center gap-1">
                  {[...Array(5)].map((_, i) => (
                    <Star 
                      key={i} 
                      className={`w-3 h-3 ${i < student.satisfaction ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
                    />
                  ))}
                </div>
              </div>

              {/* 快速操作 */}
              <div className="flex items-center gap-1">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="sm" onClick={() => onMessage(student)}>
                        <MessageSquare className="w-4 h-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>发送消息</TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="sm" onClick={() => onSchedule(student)}>
                        <Calendar className="w-4 h-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>预约咨询</TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreVertical className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => onEdit(student)}>
                      编辑信息
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onGenerateReport(student)}>
                      生成报告
                    </DropdownMenuItem>
                    <DropdownMenuItem>查看详情</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  // 详细视图 - 适用于网格展示
  if (viewMode === "detailed") {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        whileHover={{ y: -4 }}
        transition={{ duration: 0.3 }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
      >
        <Card className="h-full hover:shadow-lg transition-all duration-300 relative overflow-hidden">
          {/* 紧急程度条带 */}
          <div className={`absolute top-0 left-0 right-0 h-1 ${urgencyConfig.color}`} />
          
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3">
                <Avatar className="h-14 w-14">
                  <AvatarImage src={student.avatar} />
                  <AvatarFallback className="bg-primary/10 text-primary font-medium text-lg">
                    {student.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                
                <div>
                  <h3 className="font-semibold text-lg">{student.name}</h3>
                  <p className="text-sm text-muted-foreground">{student.grade} • {student.school}</p>
                </div>
              </div>
              
              <Badge variant="outline" className={statusConfig.color}>
                <StatusIcon className="w-3 h-3 mr-1" />
                {statusConfig.label}
              </Badge>
            </div>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* 服务进度 */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">当前阶段：{student.servicePhase}</span>
                <span className="text-sm text-muted-foreground">{student.progress}%</span>
              </div>
              <Progress value={student.progress} className="h-2" />
            </div>

            {/* 里程碑进度 */}
            <div className="flex items-center justify-between py-2 bg-muted/50 rounded-lg px-3">
              <div className="text-sm">
                <span className="font-medium">{student.completedMilestones}</span>
                <span className="text-muted-foreground">/{student.totalMilestones} 里程碑</span>
              </div>
              <div className="text-sm">
                <span className="font-medium">{student.totalSessions}</span>
                <span className="text-muted-foreground"> 次咨询</span>
              </div>
            </div>

            {/* 标签 */}
            <div className="flex flex-wrap gap-1">
              {student.tags.slice(0, 3).map((tag) => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {student.tags.length > 3 && (
                <Badge variant="secondary" className="text-xs">
                  +{student.tags.length - 3}
                </Badge>
              )}
            </div>

            {/* 满意度和评级 */}
            <div className="flex justify-between items-center">
              <div>
                <div className="text-xs text-muted-foreground mb-1">满意度</div>
                <div className="flex items-center gap-1">
                  {[...Array(5)].map((_, i) => (
                    <Star 
                      key={i} 
                      className={`w-4 h-4 ${i < student.satisfaction ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
                    />
                  ))}
                </div>
              </div>
              
              {student.nextAppointment && (
                <div className="text-right">
                  <div className="text-xs text-muted-foreground">下次预约</div>
                  <div className="text-xs font-medium">{student.nextAppointment}</div>
                </div>
              )}
            </div>

            {/* 家长信息 */}
            {student.parentName && (
              <div className="border-t pt-3">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-xs text-muted-foreground">家长</div>
                    <div className="text-sm font-medium">{student.parentName}</div>
                  </div>
                  {student.parentPhone && (
                    <Button variant="ghost" size="sm">
                      <Phone className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              </div>
            )}

            {/* 快速操作按钮 */}
            <AnimatePresence>
              {isHovered && (
                <motion.div 
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  className="flex gap-2 pt-2"
                >
                  <Button variant="outline" size="sm" className="flex-1" onClick={() => onMessage(student)}>
                    <MessageSquare className="w-4 h-4 mr-1" />
                    沟通
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1" onClick={() => onSchedule(student)}>
                    <Calendar className="w-4 h-4 mr-1" />
                    预约
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1" onClick={() => onGenerateReport(student)}>
                    <FileText className="w-4 h-4 mr-1" />
                    报告
                  </Button>
                </motion.div>
              )}
            </AnimatePresence>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  // 看板视图 - 适用于流程管理
  if (viewMode === "kanban") {
    return (
      <motion.div
        layout
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        drag
        dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
        whileDrag={{ scale: 1.05, rotate: 2 }}
      >
        <Card className="w-72 hover:shadow-md transition-all duration-200 cursor-grab active:cursor-grabbing">
          <CardContent className="p-4">
            <div className="flex items-center gap-3 mb-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={student.avatar} />
                <AvatarFallback className="bg-primary/10 text-primary font-medium">
                  {student.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1 min-w-0">
                <h4 className="font-semibold text-sm truncate">{student.name}</h4>
                <p className="text-xs text-muted-foreground">{student.grade}</p>
              </div>
              
              <div className={`w-2 h-2 rounded-full ${urgencyConfig.color}`} />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-xs">
                <span>进度</span>
                <span>{student.progress}%</span>
              </div>
              <Progress value={student.progress} className="h-1" />
              
              <div className="flex justify-between items-center">
                <div className="flex gap-1">
                  {student.tags.slice(0, 2).map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs px-1">
                      {tag}
                    </Badge>
                  ))}
                </div>
                
                <div className="flex gap-1">
                  <Button variant="ghost" size="sm" onClick={() => onMessage(student)}>
                    <MessageSquare className="w-3 h-3" />
                  </Button>
                  <Button variant="ghost" size="sm" onClick={() => onSchedule(student)}>
                    <Calendar className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  return null;
} 