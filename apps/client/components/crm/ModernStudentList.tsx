"use client";

import { useState, useMemo } from "react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Badge } from "@workspace/ui/components/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { 
  Search, 
  Filter, 
  Plus, 
  Grid3X3, 
  List, 
  Download,
  Upload,
  MoreHorizontal,
  Users,
  TrendingUp,
  GraduationCap,
  AlertCircle
} from "lucide-react";
import { ModernStudentCard } from "./ModernStudentCard";
import { Student } from "@/hooks/useCRMStudents";

interface ModernStudentListProps {
  students: Student[];
  isLoading?: boolean;
  onCreateStudent: () => void;
  onViewStudent: (student: Student) => void;
  onEditStudent: (student: Student) => void;
  onDeleteStudent: (student: Student) => void;
  onMessageStudent: (student: Student) => void;
}

type ViewMode = 'grid' | 'list';
type FilterType = 'all' | 'ACTIVE' | 'GRADUATED' | 'INACTIVE';

export function ModernStudentList({
  students,
  isLoading = false,
  onCreateStudent,
  onViewStudent,
  onEditStudent,
  onDeleteStudent,
  onMessageStudent
}: ModernStudentListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [filterType, setFilterType] = useState<FilterType>('all');
  const [selectedStudents, setSelectedStudents] = useState<string[]>([]);

  // 筛选和搜索逻辑
  const filteredStudents = useMemo(() => {
    let result = students;

    // 状态筛选
    if (filterType !== 'all') {
      result = result.filter(student => student.status === filterType);
    }

    // 搜索筛选
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(student =>
        student.name.toLowerCase().includes(term) ||
        student.school?.toLowerCase().includes(term) ||
        student.parentName?.toLowerCase().includes(term) ||
        student.tags.some(tag => tag.toLowerCase().includes(term))
      );
    }

    return result;
  }, [students, filterType, searchTerm]);

  // 统计数据
  const stats = useMemo(() => {
    const total = students.length;
    const active = students.filter(s => s.status === 'ACTIVE').length;
    const graduated = students.filter(s => s.status === 'GRADUATED').length;
    const inactive = students.filter(s => s.status === 'INACTIVE').length;
    const avgGpa = students
      .filter(s => s.gpa)
      .reduce((sum, s) => sum + (s.gpa || 0), 0) / students.filter(s => s.gpa).length;

    return { total, active, graduated, inactive, avgGpa: avgGpa || 0 };
  }, [students]);

  // 选择处理
  const handleSelectStudent = (student: Student, selected: boolean) => {
    setSelectedStudents(prev => 
      selected 
        ? [...prev, student.id]
        : prev.filter(id => id !== student.id)
    );
  };

  const handleSelectAll = () => {
    if (selectedStudents.length === filteredStudents.length) {
      setSelectedStudents([]);
    } else {
      setSelectedStudents(filteredStudents.map(s => s.id));
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* 加载状态的骨架屏 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-muted rounded-full" />
                  <div className="space-y-2">
                    <div className="h-4 bg-muted rounded w-24" />
                    <div className="h-3 bg-muted rounded w-32" />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-3 bg-muted rounded w-full" />
                  <div className="h-3 bg-muted rounded w-2/3" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Users className="w-5 h-5 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">总学生数</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <TrendingUp className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">在读学生</p>
                <p className="text-2xl font-bold text-green-600">{stats.active}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <GraduationCap className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">已毕业</p>
                <p className="text-2xl font-bold text-blue-600">{stats.graduated}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <AlertCircle className="w-5 h-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">平均GPA</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {stats.avgGpa.toFixed(1)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 工具栏 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-1 gap-4 items-center">
              {/* 搜索框 */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="搜索学生姓名、学校、家长..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* 状态筛选 */}
              <div className="flex gap-2">
                {[
                  { key: 'all', label: '全部', count: stats.total },
                  { key: 'ACTIVE', label: '在读', count: stats.active },
                  { key: 'GRADUATED', label: '已毕业', count: stats.graduated },
                  { key: 'INACTIVE', label: '暂停', count: stats.inactive }
                ].map(filter => (
                  <Button
                    key={filter.key}
                    variant={filterType === filter.key ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setFilterType(filter.key as FilterType)}
                    className="gap-2"
                  >
                    {filter.label}
                    <Badge variant="secondary" className="text-xs">
                      {filter.count}
                    </Badge>
                  </Button>
                ))}
              </div>
            </div>

            <div className="flex gap-2 items-center">
              {/* 批量操作 */}
              {selectedStudents.length > 0 && (
                <div className="flex gap-2 items-center">
                  <span className="text-sm text-muted-foreground">
                    已选择 {selectedStudents.length} 个学生
                  </span>
                  <Button size="sm" variant="outline">
                    批量操作
                  </Button>
                </div>
              )}

              {/* 视图切换 */}
              <div className="flex border rounded-lg">
                <Button
                  size="sm"
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  onClick={() => setViewMode('grid')}
                  className="rounded-r-none"
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  size="sm"
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  onClick={() => setViewMode('list')}
                  className="rounded-l-none"
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>

              {/* 操作按钮 */}
              <Button size="sm" variant="outline">
                <Download className="w-4 h-4 mr-2" />
                导出
              </Button>
              
              <Button size="sm" onClick={onCreateStudent}>
                <Plus className="w-4 h-4 mr-2" />
                添加学生
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 学生列表 */}
      {filteredStudents.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">暂无学生数据</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm ? '没有找到匹配的学生' : '开始添加第一个学生吧'}
            </p>
            <Button onClick={onCreateStudent}>
              <Plus className="w-4 h-4 mr-2" />
              添加学生
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className={
          viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
            : 'space-y-4'
        }>
          {filteredStudents.map(student => (
            <ModernStudentCard
              key={student.id}
              student={student}
              onView={onViewStudent}
              onEdit={onEditStudent}
              onDelete={onDeleteStudent}
              onMessage={onMessageStudent}
              isSelected={selectedStudents.includes(student.id)}
              onSelect={handleSelectStudent}
            />
          ))}
        </div>
      )}
    </div>
  );
}
