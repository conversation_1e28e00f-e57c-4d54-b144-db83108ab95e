"use client";

import { Badge } from "@workspace/ui/components/badge";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Card, CardContent } from "@workspace/ui/components/card";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { Input } from "@workspace/ui/components/input";
import {
    Sheet,
    <PERSON>etContent,
    SheetHeader,
    SheetTitle
} from "@workspace/ui/components/sheet";
import {
    BarChart3,
    Calendar,
    CheckSquare,
    ChevronLeft,
    Clock,
    FileText,
    Filter,
    Loader2,
    MessageSquare,
    Mic,
    MoreVertical,
    Phone,
    Plus,
    RefreshCw,
    Search,
    Star,
    User,
    Users
} from "lucide-react";
import { useCallback, useRef, useState } from "react";

// 移动端底部导航组件
export function MobileBottomNavigation({ currentTab, onTabChange, notifications = {} }: {
  currentTab: string;
  onTabChange: (tab: string) => void;
  notifications?: Record<string, number>;
}) {
  const tabs = [
    { 
      id: 'students', 
      icon: Users, 
      label: '学生', 
      badge: notifications.students 
    },
    { 
      id: 'timeline', 
      icon: Clock, 
      label: '进度',
      badge: notifications.timeline
    },
    { 
      id: 'tasks', 
      icon: CheckSquare, 
      label: '任务', 
      badge: notifications.tasks 
    },
    { 
      id: 'analytics', 
      icon: BarChart3, 
      label: '分析' 
    },
    { 
      id: 'profile', 
      icon: User, 
      label: '我的' 
    }
  ];

  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-border z-50 safe-area-pb">
      <div className="grid grid-cols-5 h-16">
        {tabs.map(tab => {
          const Icon = tab.icon;
          const isActive = currentTab === tab.id;
          
          return (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`flex flex-col items-center justify-center relative transition-colors ${
                isActive 
                  ? 'text-primary' 
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              <div className="relative">
                <Icon className={`w-5 h-5 ${isActive ? 'text-primary' : ''}`} />
                {tab.badge && tab.badge > 0 && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-2 -right-2 h-4 w-4 text-xs p-0 flex items-center justify-center"
                  >
                    {tab.badge > 99 ? '99+' : tab.badge}
                  </Badge>
                )}
              </div>
              <span className={`text-xs mt-1 ${isActive ? 'text-primary font-medium' : ''}`}>
                {tab.label}
              </span>
            </button>
          );
        })}
      </div>
    </nav>
  );
}

// 移动端顶部导航栏
export function MobileTopBar({ title, onBack, actions = [] }: {
  title: string;
  onBack?: () => void;
  actions?: Array<{
    icon: React.ElementType;
    label: string;
    onClick: () => void;
  }>;
}) {
  return (
    <header className="sticky top-0 z-40 bg-white border-b border-border">
      <div className="flex items-center justify-between h-14 px-4">
        <div className="flex items-center gap-3">
          {onBack && (
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ChevronLeft className="w-5 h-5" />
            </Button>
          )}
          <h1 className="font-semibold text-lg truncate">{title}</h1>
        </div>
        
        <div className="flex items-center gap-1">
          {actions.map((action, index) => {
            const Icon = action.icon;
            return (
              <Button
                key={index}
                variant="ghost"
                size="sm"
                onClick={action.onClick}
                title={action.label}
              >
                <Icon className="w-5 h-5" />
              </Button>
            );
          })}
        </div>
      </div>
    </header>
  );
}

// 可滑动的学生卡片
export function SwipeableStudentCard({ 
  student, 
  onSwipeLeft,
  onSwipeRight,
  onTap,
  onLongPress 
}: {
  student: any;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onTap?: () => void;
  onLongPress?: () => void;
}) {
  const cardRef = useRef<HTMLDivElement>(null);
  const [isPressed, setIsPressed] = useState(false);
  const [startPos, setStartPos] = useState({ x: 0, y: 0 });
  const [currentPos, setCurrentPos] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);

  const longPressTimer = useRef<NodeJS.Timeout | null>(null);

  const handleStart = useCallback((clientX: number, clientY: number) => {
    setIsPressed(true);
    setStartPos({ x: clientX, y: clientY });
    setCurrentPos({ x: clientX, y: clientY });
    
    // 长按检测
    longPressTimer.current = setTimeout(() => {
      if (!isDragging && onLongPress) {
        onLongPress();
        setIsPressed(false);
      }
    }, 500);
  }, [isDragging, onLongPress]);

  const handleMove = useCallback((clientX: number, clientY: number) => {
    if (!isPressed) return;
    
    const deltaX = clientX - startPos.x;
    const deltaY = clientY - startPos.y;
    
    // 判断是否为水平滑动
    if (Math.abs(deltaX) > 10 && Math.abs(deltaX) > Math.abs(deltaY)) {
      setIsDragging(true);
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current);
        longPressTimer.current = null;
      }
    }
    
    setCurrentPos({ x: clientX, y: clientY });
  }, [isPressed, startPos]);

  const handleEnd = useCallback(() => {
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }

    if (isDragging) {
      const deltaX = currentPos.x - startPos.x;
      const threshold = 80;
      
      if (deltaX > threshold && onSwipeRight) {
        onSwipeRight();
      } else if (deltaX < -threshold && onSwipeLeft) {
        onSwipeLeft();
      }
    } else if (isPressed && !isDragging && onTap) {
      // 简单点击
      onTap();
    }
    
    setIsPressed(false);
    setIsDragging(false);
    setStartPos({ x: 0, y: 0 });
    setCurrentPos({ x: 0, y: 0 });
  }, [isDragging, currentPos, startPos, onSwipeLeft, onSwipeRight, onTap, isPressed]);

  // Touch 事件
  const handleTouchStart = (e: React.TouchEvent) => {
    const touch = e.touches[0];
    handleStart(touch.clientX, touch.clientY);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    const touch = e.touches[0];
    handleMove(touch.clientX, touch.clientY);
  };

  // Mouse 事件（用于开发测试）
  const handleMouseDown = (e: React.MouseEvent) => {
    handleStart(e.clientX, e.clientY);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    handleMove(e.clientX, e.clientY);
  };

  const swipeOffset = isDragging ? currentPos.x - startPos.x : 0;
  const showLeftAction = swipeOffset > 40;
  const showRightAction = swipeOffset < -40;

  return (
    <div className="relative overflow-hidden">
      {/* 背景操作按钮 */}
      <div className="absolute inset-0 flex items-center justify-between px-4">
        <div className={`transition-opacity ${showLeftAction ? 'opacity-100' : 'opacity-0'}`}>
          <div className="bg-green-500 text-white px-4 py-2 rounded-lg flex items-center gap-2">
            <MessageSquare className="w-4 h-4" />
            发消息
          </div>
        </div>
        <div className={`transition-opacity ${showRightAction ? 'opacity-100' : 'opacity-0'}`}>
          <div className="bg-blue-500 text-white px-4 py-2 rounded-lg flex items-center gap-2">
            <Phone className="w-4 h-4" />
            打电话
          </div>
        </div>
      </div>

      {/* 学生卡片 */}
      <Card 
        ref={cardRef}
        className={`transition-transform cursor-pointer ${
          isPressed ? 'scale-98' : 'scale-100'
        }`}
        style={{
          transform: `translateX(${Math.min(Math.max(swipeOffset, -100), 100)}px)`
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleEnd}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleEnd}
        onMouseLeave={handleEnd}
      >
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
              <span className="text-primary font-medium">
                {student.name?.charAt(0) || '?'}
              </span>
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="font-medium truncate">{student.name}</h4>
                <Badge 
                  variant={student.urgency === 'high' ? 'destructive' : 'secondary'}
                  className="text-xs"
                >
                  {student.urgency === 'high' ? '紧急' : '正常'}
                </Badge>
              </div>
              <div className="text-sm text-muted-foreground">
                {student.grade} • {student.school}
              </div>
              <div className="flex items-center gap-2 mt-1">
                <div className="text-xs text-muted-foreground">
                  进度 {student.progress}%
                </div>
                <div className="flex items-center gap-1">
                  {[...Array(5)].map((_, i) => (
                    <Star 
                      key={i}
                      className={`w-3 h-3 ${
                        i < student.satisfaction 
                          ? 'text-yellow-400 fill-current' 
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>
                  <MessageSquare className="w-4 h-4 mr-2" />
                  发送消息
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Phone className="w-4 h-4 mr-2" />
                  拨打电话
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Calendar className="w-4 h-4 mr-2" />
                  预约咨询
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <FileText className="w-4 h-4 mr-2" />
                  生成报告
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// 移动端搜索栏
export function MobileSearchBar({ 
  value, 
  onChange, 
  onVoiceSearch, 
  onFilterToggle,
  isVoiceSupported = false 
}: {
  value: string;
  onChange: (value: string) => void;
  onVoiceSearch?: () => void;
  onFilterToggle: () => void;
  isVoiceSupported?: boolean;
}) {
  return (
    <div className="p-4 space-y-3">
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder="搜索学生姓名、学校..."
            className="pl-10 pr-4"
          />
        </div>
        
        {isVoiceSupported && (
          <Button
            variant="outline"
            size="icon"
            onClick={onVoiceSearch}
          >
            <Mic className="w-4 h-4" />
          </Button>
        )}
        
        <Button
          variant="outline"
          size="icon"
          onClick={onFilterToggle}
        >
          <Filter className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}

// 快速操作浮动按钮
export function FloatingActionButton({ actions }: {
  actions: Array<{
    icon: React.ElementType;
    label: string;
    onClick: () => void;
    primary?: boolean;
  }>;
}) {
  const [isOpen, setIsOpen] = useState(false);

  const primaryAction = actions.find(a => a.primary) || actions[0];
  const secondaryActions = actions.filter(a => !a.primary);

  return (
    <div className="fixed bottom-20 right-4 z-40">
      {/* 次要操作按钮 */}
      {isOpen && secondaryActions.map((action, index) => {
        const Icon = action.icon;
        return (
          <div
            key={index}
            className="mb-3 animate-in slide-in-from-bottom duration-200"
            style={{ animationDelay: `${index * 50}ms` }}
          >
            <Button
              size="icon"
              variant="secondary"
              className="h-12 w-12 rounded-full shadow-lg"
              onClick={() => {
                action.onClick();
                setIsOpen(false);
              }}
            >
              <Icon className="w-5 h-5" />
            </Button>
            <div className="absolute right-14 top-1/2 transform -translate-y-1/2 bg-black text-white text-xs px-2 py-1 rounded whitespace-nowrap">
              {action.label}
            </div>
          </div>
        );
      })}

      {/* 主要操作按钮 */}
      <Button
        size="icon"
        className="h-14 w-14 rounded-full shadow-lg"
        onClick={() => {
          if (secondaryActions.length > 0) {
            setIsOpen(!isOpen);
          } else {
            primaryAction.onClick();
          }
        }}
      >
        {secondaryActions.length > 0 ? (
          isOpen ? (
            <Plus className="w-6 h-6 rotate-45 transition-transform" />
          ) : (
            <Plus className="w-6 h-6" />
          )
        ) : (
          <primaryAction.icon className="w-6 h-6" />
        )}
      </Button>
    </div>
  );
}

// 下拉刷新组件
export function PullToRefresh({ 
  children, 
  onRefresh, 
  isRefreshing = false 
}: {
  children: React.ReactNode;
  onRefresh: () => void;
  isRefreshing?: boolean;
}) {
  const [pullDistance, setPullDistance] = useState(0);
  const [isTriggered, setIsTriggered] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const startYRef = useRef(0);
  
  const threshold = 80;
  const maxPull = 120;

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (containerRef.current?.scrollTop === 0) {
      startYRef.current = e.touches[0].clientY;
    }
  }, []);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (containerRef.current?.scrollTop !== 0) return;
    
    const currentY = e.touches[0].clientY;
    const deltaY = currentY - startYRef.current;
    
    if (deltaY > 0) {
      e.preventDefault();
      const distance = Math.min(deltaY * 0.5, maxPull);
      setPullDistance(distance);
      setIsTriggered(distance >= threshold);
    }
  }, [threshold, maxPull]);

  const handleTouchEnd = useCallback(() => {
    if (isTriggered && !isRefreshing) {
      onRefresh();
    }
    setPullDistance(0);
    setIsTriggered(false);
  }, [isTriggered, isRefreshing, onRefresh]);

  return (
    <div 
      ref={containerRef}
      className="relative overflow-auto"
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* 刷新指示器 */}
      <div 
        className="absolute top-0 left-0 right-0 flex items-center justify-center transition-transform duration-200"
        style={{ 
          transform: `translateY(${pullDistance - 60}px)`,
          height: '60px'
        }}
      >
        {isRefreshing ? (
          <Loader2 className="w-6 h-6 animate-spin text-primary" />
        ) : (
          <RefreshCw 
            className={`w-6 h-6 text-primary transition-transform ${
              isTriggered ? 'rotate-180' : ''
            }`} 
          />
        )}
      </div>

      {/* 内容区域 */}
      <div 
        className="transition-transform duration-200"
        style={{ transform: `translateY(${pullDistance}px)` }}
      >
        {children}
      </div>
    </div>
  );
}

// 移动端筛选器
export function MobileFilterSheet({ 
  isOpen, 
  onClose, 
  filters, 
  onFiltersChange 
}: {
  isOpen: boolean;
  onClose: () => void;
  filters: any;
  onFiltersChange: (filters: any) => void;
}) {
  const [localFilters, setLocalFilters] = useState(filters);

  const handleApply = () => {
    onFiltersChange(localFilters);
    onClose();
  };

  const handleReset = () => {
    const resetFilters = {};
    setLocalFilters(resetFilters);
    onFiltersChange(resetFilters);
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="bottom" className="h-[80vh]">
        <SheetHeader>
          <SheetTitle>筛选条件</SheetTitle>
        </SheetHeader>
        
        <div className="mt-6 space-y-6 overflow-y-auto">
          {/* 筛选选项 */}
          <div>
            <h4 className="font-medium mb-3">服务状态</h4>
            <div className="grid grid-cols-2 gap-2">
              {['ACTIVE', 'GRADUATED', 'INACTIVE'].map(status => (
                <Button
                  key={status}
                  variant={localFilters.status?.includes(status) ? "default" : "outline"}
                  size="sm"
                  onClick={() => {
                    const current = localFilters.status || [];
                    const updated = current.includes(status)
                      ? current.filter((s: string) => s !== status)
                      : [...current, status];
                    setLocalFilters({ ...localFilters, status: updated });
                  }}
                >
                  {status === 'ACTIVE' ? '服务中' : 
                   status === 'GRADUATED' ? '已毕业' : '暂停中'}
                </Button>
              ))}
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-3">紧急程度</h4>
            <div className="grid grid-cols-3 gap-2">
              {['low', 'medium', 'high'].map(urgency => (
                <Button
                  key={urgency}
                  variant={localFilters.urgency?.includes(urgency) ? "default" : "outline"}
                  size="sm"
                  onClick={() => {
                    const current = localFilters.urgency || [];
                    const updated = current.includes(urgency)
                      ? current.filter((u: string) => u !== urgency)
                      : [...current, urgency];
                    setLocalFilters({ ...localFilters, urgency: updated });
                  }}
                >
                  {urgency === 'low' ? '正常' : 
                   urgency === 'medium' ? '关注' : '紧急'}
                </Button>
              ))}
            </div>
          </div>
        </div>

        <div className="flex gap-3 mt-6 pt-6 border-t">
          <Button variant="outline" className="flex-1" onClick={handleReset}>
            重置
          </Button>
          <Button className="flex-1" onClick={handleApply}>
            应用筛选
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  );
} 