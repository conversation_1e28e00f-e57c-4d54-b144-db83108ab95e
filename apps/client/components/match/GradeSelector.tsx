"use client";

import { useState } from "react";
import { ChevronDown, Check } from "lucide-react";

import { Button } from "@workspace/ui/components/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@workspace/ui/components/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import { cn } from "@workspace/ui/lib/utils";

import { 
  GradeLevel, 
  GRADE_CONFIG, 
  GRADE_GROUPS,
} from "@/constants/match-constants";

interface GradeSelectorProps {
  value?: GradeLevel;
  onValueChange?: (value: GradeLevel) => void;
  placeholder?: string;
  disabled?: boolean;
  size?: "sm" | "default" | "lg";
  className?: string;
}

export function GradeSelector({
  value,
  onValueChange,
  placeholder = "选择年级",
  disabled = false,
  size = "default",
  className,
}: GradeSelectorProps) {
  const [open, setOpen] = useState(false);

  const handleSelect = (grade: GradeLevel) => {
    onValueChange?.(grade);
    setOpen(false);
  };

  const buttonSizeClass = {
    sm: "h-8 px-2 text-sm",
    default: "h-10 px-3",
    lg: "h-12 px-4 text-lg",
  }[size];

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          className={cn("w-full justify-between", buttonSizeClass, className)}
        >
          {value ? (
            <span>{GRADE_CONFIG[value]?.label || placeholder}</span>
          ) : (
            <span className="text-muted-foreground">{placeholder}</span>
          )}
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <Command>
          <CommandInput placeholder="搜索年级..." />
          <CommandList>
            <CommandEmpty>未找到相关年级</CommandEmpty>
            {GRADE_GROUPS.map((group) => (
              <CommandGroup key={group.label} heading={group.label}>
                {group.options.map((grade) => (
                  <CommandItem
                    key={grade}
                    value={grade}
                    onSelect={() => handleSelect(grade)}
                  >
                    <span>{GRADE_CONFIG[grade].label}</span>
                    {value === grade && (
                      <Check className="ml-auto h-4 w-4 text-primary" />
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
            ))}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
