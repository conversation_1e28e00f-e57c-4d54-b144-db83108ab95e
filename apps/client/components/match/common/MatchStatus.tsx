"use client";

import { Badge } from "@workspace/ui/components/badge";
import { Progress } from "@workspace/ui/components/progress";
import { cn } from "@workspace/ui/lib/utils";
import {
  Star,
  TrendingUp,
  Users
} from "lucide-react";

import {
  MatchRequestStatus,
  type StatusConfig,
} from "@/types/match.types";
import { STATUS_CONFIG } from "@/constants/match-constants";

// 状态显示组件
interface MatchStatusBadgeProps {
  status: MatchRequestStatus;
  showIcon?: boolean;
  size?: "sm" | "default" | "lg";
  className?: string;
}

export function MatchStatusBadge({ 
  status, 
  showIcon = true, 
  size = "default",
  className 
}: MatchStatusBadgeProps) {
  const config = STATUS_CONFIG[status];
  const Icon = config.icon;
  
  const sizeClasses = {
    sm: "text-xs px-2 py-1",
    default: "text-sm px-2.5 py-1.5",
    lg: "text-base px-3 py-2",
  };

  return (
    <Badge 
      variant="outline" 
      className={cn(
        config.color,
        sizeClasses[size],
        "font-medium",
        className
      )}
    >
      {showIcon && <Icon className="h-3 w-3 mr-1" />}
      {config.label}
    </Badge>
  );
}

// 紧急程度显示组件
interface UrgencyIndicatorProps {
  urgency: number;
  showLabel?: boolean;
  size?: "sm" | "default" | "lg";
  className?: string;
}

export function UrgencyIndicator({ 
  urgency, 
  showLabel = true, 
  size = "default",
  className 
}: UrgencyIndicatorProps) {
  const getUrgencyConfig = (level: number) => {
    if (level <= 3) {
      return {
        label: "不急",
        color: "bg-green-500",
        textColor: "text-green-700",
        bgColor: "bg-green-50",
      };
    } else if (level <= 6) {
      return {
        label: "一般",
        color: "bg-yellow-500",
        textColor: "text-yellow-700",
        bgColor: "bg-yellow-50",
      };
    } else {
      return {
        label: "紧急",
        color: "bg-red-500",
        textColor: "text-red-700",
        bgColor: "bg-red-50",
      };
    }
  };

  const config = getUrgencyConfig(urgency);
  
  const sizeClasses = {
    sm: "h-2 w-2",
    default: "h-3 w-3",
    lg: "h-4 w-4",
  };

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <div className={cn("rounded-full", config.color, sizeClasses[size])} />
      {showLabel && (
        <span className={cn("text-sm font-medium", config.textColor)}>
          {config.label} ({urgency}/10)
        </span>
      )}
    </div>
  );
}

// 评分显示组件
interface RatingDisplayProps {
  rating?: number;
  maxRating?: number;
  showValue?: boolean;
  size?: "sm" | "default" | "lg";
  className?: string;
}

export function RatingDisplay({ 
  rating, 
  maxRating = 5, 
  showValue = true, 
  size = "default",
  className 
}: RatingDisplayProps) {
  if (!rating) {
    return (
      <span className={cn("text-muted-foreground text-sm", className)}>
        暂无评分
      </span>
    );
  }

  const sizeClasses = {
    sm: "h-3 w-3",
    default: "h-4 w-4",
    lg: "h-5 w-5",
  };

  return (
    <div className={cn("flex items-center gap-1", className)}>
      {Array.from({ length: maxRating }).map((_, i) => (
        <Star
          key={i}
          className={cn(
            sizeClasses[size],
            i < Math.floor(rating) 
              ? "text-yellow-400 fill-yellow-400" 
              : "text-muted-foreground"
          )}
        />
      ))}
      {showValue && (
        <span className="ml-1 text-sm font-medium">{rating.toFixed(1)}</span>
      )}
    </div>
  );
}

// 进度显示组件
interface ProgressDisplayProps {
  current: number;
  total: number;
  label?: string;
  showPercentage?: boolean;
  color?: string;
  size?: "sm" | "default" | "lg";
  className?: string;
}

export function ProgressDisplay({ 
  current, 
  total, 
  label,
  showPercentage = true,
  color,
  size = "default",
  className 
}: ProgressDisplayProps) {
  const percentage = total > 0 ? (current / total) * 100 : 0;
  
  const heightClasses = {
    sm: "h-1",
    default: "h-2",
    lg: "h-3",
  };

  return (
    <div className={cn("space-y-1", className)}>
      {label && (
        <div className="flex items-center justify-between text-sm">
          <span className="font-medium">{label}</span>
          {showPercentage && (
            <span className="text-muted-foreground">
              {percentage.toFixed(1)}%
            </span>
          )}
        </div>
      )}
      <Progress 
        value={percentage} 
        className={cn(heightClasses[size], color && `bg-${color}-200`)}
      />
      <div className="flex items-center justify-between text-xs text-muted-foreground">
        <span>{current}</span>
        <span>{total}</span>
      </div>
    </div>
  );
}

// 统计卡片组件
interface StatCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon?: any;
  trend?: {
    value: number;
    isPositive: boolean;
    label: string;
  };
  color?: string;
  className?: string;
}

export function StatCard({ 
  title, 
  value, 
  description, 
  icon: Icon,
  trend,
  color = "blue",
  className 
}: StatCardProps) {
  return (
    <div className={cn(
      "p-4 rounded-lg border bg-card",
      className
    )}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
          {description && (
            <p className="text-xs text-muted-foreground mt-1">{description}</p>
          )}
        </div>
        {Icon && (
          <div className={cn(
            "h-8 w-8 rounded-full flex items-center justify-center",
            `bg-${color}-100`
          )}>
            <Icon className={cn("h-4 w-4", `text-${color}-600`)} />
          </div>
        )}
      </div>
      
      {trend && (
        <div className="flex items-center gap-1 mt-2">
          <TrendingUp 
            className={cn(
              "h-3 w-3",
              trend.isPositive ? "text-green-600" : "text-red-600"
            )} 
          />
          <span className={cn(
            "text-xs font-medium",
            trend.isPositive ? "text-green-600" : "text-red-600"
          )}>
            {trend.isPositive ? "+" : ""}{trend.value}%
          </span>
          <span className="text-xs text-muted-foreground">{trend.label}</span>
        </div>
      )}
    </div>
  );
}
