"use client";

import { ReactNode } from "react";
import { Button } from "@workspace/ui/components/button";
import { cn } from "@workspace/ui/lib/utils";
import { 
  Search, 
  FileX, 
  Users, 
  ClipboardList, 
  AlertCircle,
  Plus,
  RefreshCw
} from "lucide-react";

interface EmptyStateProps {
  icon?: ReactNode;
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
    variant?: "default" | "outline" | "secondary";
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
    variant?: "default" | "outline" | "secondary";
  };
  className?: string;
}

export function EmptyState({
  icon,
  title,
  description,
  action,
  secondaryAction,
  className,
}: EmptyStateProps) {
  return (
    <div className={cn(
      "flex flex-col items-center justify-center text-center py-12 px-4",
      className
    )}>
      {icon && (
        <div className="mb-4 p-3 bg-muted rounded-full">
          {icon}
        </div>
      )}
      
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground mb-6 max-w-md">{description}</p>
      
      {(action || secondaryAction) && (
        <div className="flex items-center gap-3">
          {action && (
            <Button
              variant={action.variant || "default"}
              onClick={action.onClick}
            >
              {action.label}
            </Button>
          )}
          {secondaryAction && (
            <Button
              variant={secondaryAction.variant || "outline"}
              onClick={secondaryAction.onClick}
            >
              {secondaryAction.label}
            </Button>
          )}
        </div>
      )}
    </div>
  );
}

// 预定义的空状态组件

// 无搜索结果
interface NoSearchResultsProps {
  searchQuery?: string;
  onClearSearch?: () => void;
  onCreateNew?: () => void;
  createLabel?: string;
  className?: string;
}

export function NoSearchResults({
  searchQuery,
  onClearSearch,
  onCreateNew,
  createLabel = "创建新的",
  className,
}: NoSearchResultsProps) {
  return (
    <EmptyState
      icon={<Search className="h-8 w-8 text-muted-foreground" />}
      title="未找到相关结果"
      description={
        searchQuery 
          ? `没有找到与"${searchQuery}"相关的内容，试试调整搜索条件`
          : "没有找到符合条件的内容，试试调整筛选条件"
      }
      action={onClearSearch ? {
        label: "清除筛选",
        onClick: onClearSearch,
        variant: "outline" as const,
      } : undefined}
      secondaryAction={onCreateNew ? {
        label: createLabel,
        onClick: onCreateNew,
      } : undefined}
      className={className}
    />
  );
}

// 无匹配请求
interface NoMatchRequestsProps {
  onCreateRequest?: () => void;
  className?: string;
}

export function NoMatchRequests({
  onCreateRequest,
  className,
}: NoMatchRequestsProps) {
  return (
    <EmptyState
      icon={<ClipboardList className="h-8 w-8 text-muted-foreground" />}
      title="暂无匹配请求"
      description="还没有任何匹配请求，创建第一个请求开始匹配合适的规划师"
      action={onCreateRequest ? {
        label: "创建匹配请求",
        onClick: onCreateRequest,
      } : undefined}
      className={className}
    />
  );
}

// 无规划师
interface NoPlannersProps {
  onRefresh?: () => void;
  onInvitePlanners?: () => void;
  className?: string;
}

export function NoPlanners({
  onRefresh,
  onInvitePlanners,
  className,
}: NoPlannersProps) {
  return (
    <EmptyState
      icon={<Users className="h-8 w-8 text-muted-foreground" />}
      title="暂无规划师"
      description="当前没有可用的规划师，请联系管理员添加规划师或稍后再试"
      action={onRefresh ? {
        label: "刷新列表",
        onClick: onRefresh,
        variant: "outline" as const,
      } : undefined}
      secondaryAction={onInvitePlanners ? {
        label: "邀请规划师",
        onClick: onInvitePlanners,
      } : undefined}
      className={className}
    />
  );
}

// 无推荐
interface NoRecommendationsProps {
  onRefreshRecommendations?: () => void;
  onBrowsePlanners?: () => void;
  className?: string;
}

export function NoRecommendations({
  onRefreshRecommendations,
  onBrowsePlanners,
  className,
}: NoRecommendationsProps) {
  return (
    <EmptyState
      icon={<AlertCircle className="h-8 w-8 text-muted-foreground" />}
      title="暂无推荐"
      description="系统暂时无法为您推荐合适的规划师，您可以刷新推荐或浏览所有规划师"
      action={onRefreshRecommendations ? {
        label: "刷新推荐",
        onClick: onRefreshRecommendations,
        variant: "outline" as const,
      } : undefined}
      secondaryAction={onBrowsePlanners ? {
        label: "浏览规划师",
        onClick: onBrowsePlanners,
      } : undefined}
      className={className}
    />
  );
}

// 加载失败
interface LoadingErrorProps {
  error?: string;
  onRetry?: () => void;
  onGoBack?: () => void;
  className?: string;
}

export function LoadingError({
  error = "加载失败",
  onRetry,
  onGoBack,
  className,
}: LoadingErrorProps) {
  return (
    <EmptyState
      icon={<AlertCircle className="h-8 w-8 text-red-500" />}
      title="加载失败"
      description={error}
      action={onRetry ? {
        label: "重试",
        onClick: onRetry,
      } : undefined}
      secondaryAction={onGoBack ? {
        label: "返回",
        onClick: onGoBack,
        variant: "outline" as const,
      } : undefined}
      className={className}
    />
  );
}

// 通用的数据为空组件
interface DataEmptyProps {
  type: "requests" | "planners" | "recommendations" | "statistics";
  searchQuery?: string;
  hasFilters?: boolean;
  onClearFilters?: () => void;
  onCreateNew?: () => void;
  onRefresh?: () => void;
  className?: string;
}

export function DataEmpty({
  type,
  searchQuery,
  hasFilters,
  onClearFilters,
  onCreateNew,
  onRefresh,
  className,
}: DataEmptyProps) {
  const configs = {
    requests: {
      icon: <ClipboardList className="h-8 w-8 text-muted-foreground" />,
      title: "暂无匹配请求",
      description: hasFilters || searchQuery 
        ? "没有找到符合条件的匹配请求" 
        : "还没有任何匹配请求",
      createLabel: "创建匹配请求",
    },
    planners: {
      icon: <Users className="h-8 w-8 text-muted-foreground" />,
      title: "暂无规划师",
      description: hasFilters || searchQuery 
        ? "没有找到符合条件的规划师" 
        : "当前没有可用的规划师",
      createLabel: "邀请规划师",
    },
    recommendations: {
      icon: <AlertCircle className="h-8 w-8 text-muted-foreground" />,
      title: "暂无推荐",
      description: "系统暂时无法为您推荐合适的规划师",
      createLabel: "浏览所有规划师",
    },
    statistics: {
      icon: <FileX className="h-8 w-8 text-muted-foreground" />,
      title: "暂无统计数据",
      description: "当前时间范围内没有统计数据",
      createLabel: "刷新数据",
    },
  };

  const config = configs[type];

  return (
    <EmptyState
      icon={config.icon}
      title={config.title}
      description={config.description}
      action={(hasFilters || searchQuery) && onClearFilters ? {
        label: "清除筛选",
        onClick: onClearFilters,
        variant: "outline" as const,
      } : onRefresh ? {
        label: "刷新",
        onClick: onRefresh,
        variant: "outline" as const,
      } : undefined}
      secondaryAction={onCreateNew ? {
        label: config.createLabel,
        onClick: onCreateNew,
      } : undefined}
      className={className}
    />
  );
}
