"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>C<PERSON>,
  Bar,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
} from "recharts";
import { Star, TrendingUp, Target, Award, Users, Clock } from "lucide-react";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import { Progress } from "@workspace/ui/components/progress";
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import { Button } from "@workspace/ui/components/button";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Alert, AlertDescription } from "@workspace/ui/components/alert";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { cn } from "@workspace/ui/lib/utils";

import { apiClient } from "@/utils/api";

interface PlannerCategoryPerformance {
  category: string;
  totalRequests: number;
  confirmedRequests: number;
  successRate: number;
  avgRating: number;
  evaluationCount: number;
}

interface PlannerPerformanceData {
  planner: {
    id: string;
    rating: number;
    tenantUser: {
      user: {
        id: string;
        name: string;
        avatar?: string;
        email: string;
      };
    };
    matchProfile: {
      preferredSubjects: string[];
      preferredGrades: string[];
      successRate: number;
      responseTime: number;
    };
  };
  categoryPerformance: PlannerCategoryPerformance[];
  overallStats: {
    totalRequests: number;
    totalConfirmed: number;
    overallSuccessRate: number;
    avgRating: number;
    totalEvaluations: number;
  };
}

interface PlannerCategoryPerformanceDisplayProps {
  plannerId?: string;
  showComparison?: boolean;
  className?: string;
}

const CATEGORY_LABELS = {
  ACADEMIC_TUTORING: "学科辅导",
  EXAM_PREPARATION: "考试备考",
  SKILL_DEVELOPMENT: "技能发展",
  CAREER_PLANNING: "职业规划",
  STUDY_ABROAD: "留学咨询",
  COMPETITION_TRAINING: "竞赛培训",
  HOMEWORK_HELP: "作业辅导",
  PSYCHOLOGICAL_COUNSELING: "心理咨询",
  PARENT_CONSULTATION: "家长咨询",
  OTHER: "其他服务",
};

const CATEGORY_COLORS = {
  ACADEMIC_TUTORING: "#3b82f6",
  EXAM_PREPARATION: "#ef4444",
  SKILL_DEVELOPMENT: "#10b981",
  CAREER_PLANNING: "#f59e0b",
  STUDY_ABROAD: "#8b5cf6",
  COMPETITION_TRAINING: "#06b6d4",
  HOMEWORK_HELP: "#84cc16",
  PSYCHOLOGICAL_COUNSELING: "#ec4899",
  PARENT_CONSULTATION: "#6366f1",
  OTHER: "#6b7280",
};

export function PlannerCategoryPerformanceDisplay({
  plannerId,
  showComparison = false,
  className,
}: PlannerCategoryPerformanceDisplayProps) {
  const [performanceData, setPerformanceData] =
    useState<PlannerPerformanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPlannerId, setSelectedPlannerId] = useState(plannerId);

  useEffect(() => {
    if (selectedPlannerId) {
      loadPerformanceData();
    }
  }, [selectedPlannerId]);

  const loadPerformanceData = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await apiClient.request(
        `/api/match/planner/${selectedPlannerId}/performance`,
      );

      if (result.success) {
        setPerformanceData(result.data);
      } else {
        setError(result.message || "获取规划师表现数据失败");
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const getPerformanceLevel = (successRate: number) => {
    if (successRate >= 0.9)
      return { label: "优秀", color: "text-green-600", bg: "bg-green-100" };
    if (successRate >= 0.8)
      return { label: "良好", color: "text-blue-600", bg: "bg-blue-100" };
    if (successRate >= 0.7)
      return { label: "一般", color: "text-yellow-600", bg: "bg-yellow-100" };
    return { label: "待提升", color: "text-red-600", bg: "bg-red-100" };
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return "text-green-600";
    if (rating >= 4.0) return "text-yellow-600";
    return "text-red-600";
  };

  if (loading) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="flex items-center gap-4">
          <Skeleton className="h-12 w-12 rounded-full" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-3 w-24" />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-4 w-20 mb-2" />
                <Skeleton className="h-8 w-16 mb-1" />
                <Skeleton className="h-3 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-5 w-32" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !performanceData) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertDescription>
          {error}
          <Button
            variant="link"
            size="sm"
            onClick={loadPerformanceData}
            className="ml-2 p-0"
          >
            重试
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  const { planner, categoryPerformance, overallStats } = performanceData;
  const user = planner.tenantUser.user;

  // 准备图表数据
  const chartData = categoryPerformance.map((perf) => ({
    category:
      CATEGORY_LABELS[perf.category as keyof typeof CATEGORY_LABELS] ||
      perf.category,
    successRate: perf.successRate * 100,
    requests: perf.totalRequests,
    confirmed: perf.confirmedRequests,
    rating: perf.avgRating,
    evaluations: perf.evaluationCount,
  }));

  // 雷达图数据
  const radarData = categoryPerformance.slice(0, 6).map((perf) => ({
    category:
      CATEGORY_LABELS[perf.category as keyof typeof CATEGORY_LABELS] ||
      perf.category,
    successRate: perf.successRate * 100,
    rating: perf.avgRating * 20, // 转换为0-100范围
    experience: Math.min(perf.totalRequests * 2, 100), // 经验值
  }));

  return (
    <div className={cn("space-y-6", className)}>
      {/* 规划师信息头部 */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src={user.avatar} alt={user.name} />
              <AvatarFallback className="text-lg">
                {user.name.slice(0, 2)}
              </AvatarFallback>
            </Avatar>

            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <h3 className="text-xl font-semibold">{user.name}</h3>
                <Badge variant="outline">规划师</Badge>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="flex items-center gap-1">
                  <Star className="h-3 w-3 fill-current text-yellow-400" />
                  <span className={getRatingColor(planner.rating)}>
                    {planner.rating.toFixed(1)}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <Target className="h-3 w-3 text-blue-600" />
                  <span>{overallStats.totalRequests} 次服务</span>
                </div>
                <div className="flex items-center gap-1">
                  <TrendingUp className="h-3 w-3 text-green-600" />
                  <span>
                    {(overallStats.overallSuccessRate * 100).toFixed(1)}% 成功率
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3 text-purple-600" />
                  <span>{planner.matchProfile.responseTime}h 响应</span>
                </div>
              </div>

              {planner.matchProfile.preferredSubjects.length > 0 && (
                <div className="mt-3">
                  <div className="text-sm text-muted-foreground mb-1">
                    专长领域:
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {planner.matchProfile.preferredSubjects
                      .slice(0, 6)
                      .map((subject) => (
                        <Badge
                          key={subject}
                          variant="secondary"
                          className="text-xs"
                        >
                          {subject}
                        </Badge>
                      ))}
                    {planner.matchProfile.preferredSubjects.length > 6 && (
                      <Badge variant="secondary" className="text-xs">
                        +{planner.matchProfile.preferredSubjects.length - 6}
                      </Badge>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 整体统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-blue-600" />
              <span className="text-sm text-muted-foreground">总服务次数</span>
            </div>
            <div className="text-2xl font-bold mt-1">
              {overallStats.totalRequests}
            </div>
            <div className="text-xs text-muted-foreground">
              涵盖 {categoryPerformance.length} 个分类
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Award className="h-4 w-4 text-green-600" />
              <span className="text-sm text-muted-foreground">成功匹配</span>
            </div>
            <div className="text-2xl font-bold mt-1">
              {overallStats.totalConfirmed}
            </div>
            <div className="text-xs text-green-600">
              {(overallStats.overallSuccessRate * 100).toFixed(1)}% 成功率
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Star className="h-4 w-4 text-yellow-400" />
              <span className="text-sm text-muted-foreground">平均评分</span>
            </div>
            <div className="text-2xl font-bold mt-1">
              {overallStats.avgRating.toFixed(1)}
            </div>
            <div className="text-xs text-muted-foreground">
              基于 {overallStats.totalEvaluations} 条评价
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-purple-600" />
              <span className="text-sm text-muted-foreground">服务分类</span>
            </div>
            <div className="text-2xl font-bold mt-1">
              {categoryPerformance.length}
            </div>
            <div className="text-xs text-muted-foreground">不同类型服务</div>
          </CardContent>
        </Card>
      </div>

      {/* 详细分析 */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList>
          <TabsTrigger value="overview">分类概览</TabsTrigger>
          <TabsTrigger value="performance">表现分析</TabsTrigger>
          <TabsTrigger value="radar">能力雷达</TabsTrigger>
          <TabsTrigger value="details">详细数据</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>各分类服务量</CardTitle>
              <CardDescription>不同服务分类的请求量对比</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="category"
                    angle={-45}
                    textAnchor="end"
                    height={100}
                    fontSize={12}
                  />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="requests" fill="#3b82f6" name="总请求" />
                  <Bar dataKey="confirmed" fill="#10b981" name="成功匹配" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>各分类成功率</CardTitle>
              <CardDescription>不同服务分类的匹配成功率</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="category"
                    angle={-45}
                    textAnchor="end"
                    height={100}
                    fontSize={12}
                  />
                  <YAxis
                    label={{
                      value: "成功率 (%)",
                      angle: -90,
                      position: "insideLeft",
                    }}
                  />
                  <Tooltip formatter={(value) => [`${value}%`, "成功率"]} />
                  <Bar dataKey="successRate" fill="#10b981" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="radar" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>综合能力雷达图</CardTitle>
              <CardDescription>各维度综合表现评估</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <RadarChart data={radarData}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="category" fontSize={12} />
                  <PolarRadiusAxis angle={90} domain={[0, 100]} tickCount={6} />
                  <Radar
                    name="成功率"
                    dataKey="successRate"
                    stroke="#3b82f6"
                    fill="#3b82f6"
                    fillOpacity={0.3}
                  />
                  <Radar
                    name="评分"
                    dataKey="rating"
                    stroke="#f59e0b"
                    fill="#f59e0b"
                    fillOpacity={0.3}
                  />
                  <Radar
                    name="经验"
                    dataKey="experience"
                    stroke="#10b981"
                    fill="#10b981"
                    fillOpacity={0.3}
                  />
                  <Tooltip />
                </RadarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="details" className="space-y-4">
          <div className="space-y-4">
            {categoryPerformance.map((perf) => {
              const level = getPerformanceLevel(perf.successRate);

              return (
                <Card key={perf.category}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">
                        {CATEGORY_LABELS[
                          perf.category as keyof typeof CATEGORY_LABELS
                        ] || perf.category}
                      </CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant="outline"
                          style={{
                            backgroundColor:
                              CATEGORY_COLORS[
                                perf.category as keyof typeof CATEGORY_COLORS
                              ] + "20",
                            borderColor:
                              CATEGORY_COLORS[
                                perf.category as keyof typeof CATEGORY_COLORS
                              ],
                          }}
                        >
                          {perf.totalRequests} 次服务
                        </Badge>
                        <div
                          className={cn(
                            "px-2 py-1 rounded text-sm font-medium",
                            level.bg,
                            level.color,
                          )}
                        >
                          {level.label}
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground mb-1">
                          成功率
                        </div>
                        <div className="text-lg font-semibold">
                          {(perf.successRate * 100).toFixed(1)}%
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground mb-1">
                          平均评分
                        </div>
                        <div className="text-lg font-semibold text-yellow-600">
                          {perf.avgRating.toFixed(1)}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground mb-1">
                          成功匹配
                        </div>
                        <div className="text-lg font-semibold text-green-600">
                          {perf.confirmedRequests}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground mb-1">
                          评价数量
                        </div>
                        <div className="text-lg font-semibold">
                          {perf.evaluationCount}
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">成功率</span>
                        <span className="font-medium">
                          {(perf.successRate * 100).toFixed(1)}%
                        </span>
                      </div>
                      <Progress
                        value={perf.successRate * 100}
                        className="h-2"
                      />
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
