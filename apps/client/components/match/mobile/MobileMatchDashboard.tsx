"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { 
  Plus, 
  Search, 
  Filter, 
  Bell, 
  User,
  TrendingUp,
  Clock,
  CheckCircle,
  Users,
  MessageCircle,
  Star,
  ChevronRight,
  Zap
} from "lucide-react";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@workspace/ui/components/avatar";
import { Input } from "@workspace/ui/components/input";
import { cn } from "@workspace/ui/lib/utils";

import { useMatchStatistics, useMatchRequests } from "@/hooks/useMatchData";
import { MatchRequestStatus } from "@/types/match.types";
import { CATEGORY_CONFIG, STATUS_CONFIG } from "@/constants/match-constants";

export function MobileMatchDashboard() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");

  const { statistics, loading: statsLoading } = useMatchStatistics();
  const { 
    requests: recentRequests, 
    loading: requestsLoading 
  } = useMatchRequests({}, { page: 1, pageSize: 3 });

  const handleCreateRequest = () => {
    router.push("/community/match/create");
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      router.push(`/community/match/search?q=${encodeURIComponent(searchQuery)}`);
    }
  };

  const handleViewRequest = (requestId: string) => {
    router.push(`/community/match/requests/${requestId}`);
  };

  const quickActions = [
    {
      icon: Plus,
      label: "发布需求",
      color: "bg-blue-500",
      action: () => router.push("/community/match/create"),
    },
    {
      icon: Users,
      label: "找规划师",
      color: "bg-green-500", 
      action: () => router.push("/community/match/planners"),
    },
    {
      icon: MessageCircle,
      label: "我的对话",
      color: "bg-purple-500",
      action: () => router.push("/community/match/chats"),
    },
    {
      icon: Star,
      label: "我的收藏",
      color: "bg-yellow-500",
      action: () => router.push("/community/match/favorites"),
    },
  ];

  const statsCards = [
    {
      title: "待匹配",
      value: statistics?.pending || 0,
      icon: Clock,
      color: "text-yellow-600",
      bgColor: "bg-yellow-50",
    },
    {
      title: "已匹配",
      value: statistics?.matched || 0,
      icon: CheckCircle,
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      title: "成功率",
      value: `${statistics?.successRate?.toFixed(0) || 0}%`,
      icon: TrendingUp,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white border-b sticky top-0 z-10">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between mb-3">
            <div>
              <h1 className="text-xl font-bold">匹配中心</h1>
              <p className="text-sm text-muted-foreground">找到最适合的规划师</p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <Bell className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <User className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          {/* 搜索栏 */}
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索规划师或需求..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && handleSearch()}
                className="pl-10"
              />
            </div>
            <Button variant="outline" size="sm" className="px-3">
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* 快速操作 */}
        <div>
          <h2 className="text-lg font-semibold mb-3">快速操作</h2>
          <div className="grid grid-cols-2 gap-3">
            {quickActions.map((action, index) => {
              const Icon = action.icon;
              return (
                <Button
                  key={index}
                  variant="outline"
                  className="h-20 flex-col gap-2 bg-white"
                  onClick={action.action}
                >
                  <div className={cn("w-8 h-8 rounded-full flex items-center justify-center", action.color)}>
                    <Icon className="h-4 w-4 text-white" />
                  </div>
                  <span className="text-sm font-medium">{action.label}</span>
                </Button>
              );
            })}
          </div>
        </div>

        {/* 统计概览 */}
        <div>
          <h2 className="text-lg font-semibold mb-3">数据概览</h2>
          <div className="grid grid-cols-3 gap-3">
            {statsCards.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <Card key={index} className="text-center">
                  <CardContent className="p-4">
                    <div className={cn("w-10 h-10 rounded-full mx-auto mb-2 flex items-center justify-center", stat.bgColor)}>
                      <Icon className={cn("h-5 w-5", stat.color)} />
                    </div>
                    <div className="text-xl font-bold">{stat.value}</div>
                    <div className="text-xs text-muted-foreground">{stat.title}</div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* 最近请求 */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-semibold">最近请求</h2>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => router.push("/community/match/requests")}
              className="text-primary"
            >
              查看全部
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
          
          {requestsLoading ? (
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-muted rounded-full" />
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-muted rounded w-3/4" />
                        <div className="h-3 bg-muted rounded w-1/2" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : recentRequests.length > 0 ? (
            <div className="space-y-3">
              {recentRequests.map((request) => {
                const categoryConfig = CATEGORY_CONFIG[request.category];
                const statusConfig = STATUS_CONFIG[request.status];
                const CategoryIcon = categoryConfig.icon;
                
                return (
                  <Card 
                    key={request.id} 
                    className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => handleViewRequest(request.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div className={cn("w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0", categoryConfig.color)}>
                          <CategoryIcon className="h-5 w-5 text-white" />
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-medium text-sm truncate">
                              {request.requirements.title}
                            </h3>
                            <Badge 
                              variant="secondary" 
                              className={cn("text-xs", statusConfig.color)}
                            >
                              {statusConfig.label}
                            </Badge>
                          </div>
                          
                          <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                            {request.requirements.description}
                          </p>
                          
                          <div className="flex items-center gap-3 text-xs text-muted-foreground">
                            <span>{request.requirements.grade}</span>
                            <span>{request.requirements.location}</span>
                            {request._count?.responses && (
                              <span className="flex items-center gap-1">
                                <Users className="h-3 w-3" />
                                {request._count.responses}个响应
                              </span>
                            )}
                          </div>
                        </div>
                        
                        <ChevronRight className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-muted rounded-full mx-auto mb-4 flex items-center justify-center">
                  <Plus className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="font-medium mb-2">还没有匹配请求</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  发布您的第一个需求，开始寻找合适的规划师
                </p>
                <Button onClick={handleCreateRequest} className="gap-2">
                  <Plus className="h-4 w-4" />
                  发布需求
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* 推荐规划师 */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-semibold">推荐规划师</h2>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => router.push("/community/match/planners")}
              className="text-primary"
            >
              查看更多
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
          
          <div className="flex gap-3 overflow-x-auto pb-2">
            {Array.from({ length: 5 }).map((_, index) => (
              <Card key={index} className="flex-shrink-0 w-32 cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-3 text-center">
                  <Avatar className="h-12 w-12 mx-auto mb-2">
                    <AvatarFallback>师{index + 1}</AvatarFallback>
                  </Avatar>
                  <h4 className="font-medium text-sm mb-1">张规划师</h4>
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                    <span className="text-xs">4.8</span>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    心理咨询
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* 底部浮动按钮 */}
      <div className="fixed bottom-6 right-6">
        <Button
          onClick={handleCreateRequest}
          className="h-14 w-14 rounded-full shadow-lg gap-0"
        >
          <Plus className="h-6 w-6" />
        </Button>
      </div>
    </div>
  );
}
