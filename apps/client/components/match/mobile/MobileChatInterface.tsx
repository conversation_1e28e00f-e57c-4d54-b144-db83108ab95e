"use client";

import { useState, useRef, useEffect } from "react";
import { 
  ArrowLeft,
  Send, 
  Plus, 
  Mic,
  Camera,
  Image as ImageIcon,
  FileText,
  Phone,
  Video,
  MoreVertical,
  Smile
} from "lucide-react";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Avatar, AvatarFallback, AvatarImage } from "@workspace/ui/components/avatar";
import { Badge } from "@workspace/ui/components/badge";
import { cn } from "@workspace/ui/lib/utils";

interface MobileChatMessage {
  id: string;
  senderId: string;
  content: string;
  type: "text" | "image" | "file" | "voice";
  timestamp: string;
  status: "sending" | "sent" | "read";
}

interface MobileChatInterfaceProps {
  participantName: string;
  participantAvatar?: string;
  participantRole: "student" | "planner";
  isOnline: boolean;
  onBack: () => void;
  className?: string;
}

export function MobileChatInterface({
  participantName,
  participantAvatar,
  participantRole,
  isOnline,
  onBack,
  className,
}: MobileChatInterfaceProps) {
  const [messages, setMessages] = useState<MobileChatMessage[]>([
    {
      id: "1",
      senderId: "other",
      content: "您好！我是您的专属规划师，很高兴为您服务。",
      type: "text",
      timestamp: new Date(Date.now() - 300000).toISOString(),
      status: "read",
    },
    {
      id: "2",
      senderId: "me",
      content: "您好，我想了解一下关于专业选择的建议。",
      type: "text",
      timestamp: new Date(Date.now() - 240000).toISOString(),
      status: "read",
    },
    {
      id: "3",
      senderId: "other",
      content: "当然可以！首先我需要了解一下您的兴趣爱好和学习情况，这样我可以给您更精准的建议。",
      type: "text",
      timestamp: new Date(Date.now() - 180000).toISOString(),
      status: "read",
    },
  ]);
  
  const [newMessage, setNewMessage] = useState("");
  const [showAttachments, setShowAttachments] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = () => {
    if (!newMessage.trim()) return;

    const message: MobileChatMessage = {
      id: Date.now().toString(),
      senderId: "me",
      content: newMessage,
      type: "text",
      timestamp: new Date().toISOString(),
      status: "sending",
    };

    setMessages(prev => [...prev, message]);
    setNewMessage("");
    
    // 模拟发送状态更新
    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === message.id 
            ? { ...msg, status: "sent" as const }
            : msg
        )
      );
    }, 1000);
  };

  const handleVoiceRecord = () => {
    setIsRecording(!isRecording);
    // 这里实现语音录制逻辑
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString("zh-CN", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const attachmentOptions = [
    { icon: Camera, label: "拍照", action: () => console.log("拍照") },
    { icon: ImageIcon, label: "相册", action: () => console.log("相册") },
    { icon: FileText, label: "文件", action: () => console.log("文件") },
  ];

  const renderMessage = (message: MobileChatMessage) => {
    const isOwn = message.senderId === "me";
    
    return (
      <div
        key={message.id}
        className={cn(
          "flex gap-2 mb-4",
          isOwn ? "flex-row-reverse" : "flex-row"
        )}
      >
        {!isOwn && (
          <Avatar className="h-8 w-8 flex-shrink-0">
            <AvatarImage src={participantAvatar} />
            <AvatarFallback className="text-xs">
              {participantName.charAt(0)}
            </AvatarFallback>
          </Avatar>
        )}
        
        <div className={cn("flex flex-col max-w-[75%]", isOwn ? "items-end" : "items-start")}>
          <div
            className={cn(
              "px-3 py-2 rounded-2xl",
              isOwn
                ? "bg-primary text-primary-foreground rounded-br-md"
                : "bg-muted rounded-bl-md"
            )}
          >
            <p className="text-sm whitespace-pre-wrap">{message.content}</p>
          </div>
          
          <div className={cn(
            "flex items-center gap-1 mt-1 text-xs text-muted-foreground",
            isOwn ? "flex-row-reverse" : "flex-row"
          )}>
            <span>{formatTime(message.timestamp)}</span>
            {isOwn && (
              <div className="flex">
                {message.status === "sending" && "发送中"}
                {message.status === "sent" && "已发送"}
                {message.status === "read" && "已读"}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={cn("flex flex-col h-screen bg-white", className)}>
      {/* 顶部导航 */}
      <div className="flex items-center justify-between p-4 border-b bg-white sticky top-0 z-10">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="h-8 w-8 p-0"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          
          <div className="relative">
            <Avatar className="h-10 w-10">
              <AvatarImage src={participantAvatar} />
              <AvatarFallback>{participantName.charAt(0)}</AvatarFallback>
            </Avatar>
            {isOnline && (
              <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white" />
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className="font-medium truncate">{participantName}</h3>
            <div className="flex items-center gap-2">
              <p className="text-xs text-muted-foreground">
                {isOnline ? "在线" : "离线"}
              </p>
              <Badge variant="secondary" className="text-xs">
                {participantRole === "planner" ? "规划师" : "学生"}
              </Badge>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-1">
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Phone className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Video className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <MoreVertical className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* 消息列表 */}
      <div className="flex-1 overflow-y-auto p-4 pb-safe">
        {messages.map(renderMessage)}
        <div ref={messagesEndRef} />
      </div>

      {/* 附件选项 */}
      {showAttachments && (
        <div className="border-t bg-white p-4">
          <div className="grid grid-cols-3 gap-4">
            {attachmentOptions.map((option, index) => {
              const Icon = option.icon;
              return (
                <Button
                  key={index}
                  variant="outline"
                  className="h-16 flex-col gap-2"
                  onClick={() => {
                    option.action();
                    setShowAttachments(false);
                  }}
                >
                  <Icon className="h-5 w-5" />
                  <span className="text-xs">{option.label}</span>
                </Button>
              );
            })}
          </div>
        </div>
      )}

      {/* 输入区域 */}
      <div className="border-t bg-white p-4 pb-safe">
        <div className="flex items-end gap-2">
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 flex-shrink-0"
            onClick={() => setShowAttachments(!showAttachments)}
          >
            <Plus className={cn(
              "h-4 w-4 transition-transform",
              showAttachments && "rotate-45"
            )} />
          </Button>
          
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="输入消息..."
              onKeyPress={(e) => {
                if (e.key === "Enter" && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
              className="pr-10 rounded-full"
            />
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
            >
              <Smile className="h-4 w-4" />
            </Button>
          </div>
          
          {newMessage.trim() ? (
            <Button
              onClick={handleSendMessage}
              className="h-8 w-8 p-0 rounded-full flex-shrink-0"
            >
              <Send className="h-4 w-4" />
            </Button>
          ) : (
            <Button
              variant={isRecording ? "destructive" : "outline"}
              onClick={handleVoiceRecord}
              className={cn(
                "h-8 w-8 p-0 rounded-full flex-shrink-0",
                isRecording && "animate-pulse"
              )}
            >
              <Mic className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
