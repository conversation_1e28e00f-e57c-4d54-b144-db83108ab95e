"use client";

import { useState } from "react";
import {
  Check,
  ChevronDown,
  BookOpen,
  Users,
  Calendar,
  Target,
  HelpCircle,
  Briefcase,
  FileText,
  Globe,
  Languages,
  MapPin,
  PlaneTakeoff,
  Star,
} from "lucide-react";

import { Button } from "@workspace/ui/components/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@workspace/ui/components/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import { Badge } from "@workspace/ui/components/badge";
import { cn } from "@workspace/ui/lib/utils";

// 需求分类枚举映射
export enum RequirementCategory {
  LEARNING_ABILITY = "LEARNING_ABILITY",
  PSYCHOLOGICAL_SUPPORT = "PSYCHOLOGICAL_SUPPORT",
  COLLEGE_APPLICATION = "COLLEGE_APPLICATION",
  SPECIAL_ADMISSION = "SPECIAL_ADMISSION",
  HONGKONG_MACAO = "HONGKONG_MACAO",
  ENGLISH_COUNTRIES = "ENGLISH_COUNTRIES",
  NON_ENGLISH_COUNTRIES = "NON_ENGLISH_COUNTRIES",
  ACADEMIC_PLANNING = "ACADEMIC_PLANNING",
  CAREER_PLANNING = "CAREER_PLANNING",
  STUDY_ABROAD = "STUDY_ABROAD",
  EXAM_PREPARATION = "EXAM_PREPARATION",
  EXTRACURRICULAR = "EXTRACURRICULAR",
}

// 分类配置
const CATEGORY_CONFIG = {
  [RequirementCategory.LEARNING_ABILITY]: {
    label: "学习力",
    description: "学习能力提升、学习方法指导、注意力训练等",
    icon: BookOpen,
    color: "bg-blue-500",
  },
  [RequirementCategory.PSYCHOLOGICAL_SUPPORT]: {
    label: "心理辅导",
    description: "情绪管理、压力缓解、心理健康等",
    icon: HelpCircle,
    color: "bg-pink-500",
  },
  [RequirementCategory.COLLEGE_APPLICATION]: {
    label: "志愿填报",
    description: "高考志愿填报、专业选择、院校咨询等",
    icon: Target,
    color: "bg-purple-500",
  },
  [RequirementCategory.SPECIAL_ADMISSION]: {
    label: "特殊类型招生",
    description: "自主招生、综合评价、三位一体等特殊招生类型",
    icon: Star,
    color: "bg-amber-500",
  },
  [RequirementCategory.HONGKONG_MACAO]: {
    label: "港澳留学",
    description: "港澳地区的大学申请与规划",
    icon: MapPin,
    color: "bg-red-500",
  },
  [RequirementCategory.ENGLISH_COUNTRIES]: {
    label: "英语系国家留学",
    description: "美国、英国、加拿大、澳洲等英语国家留学",
    icon: Globe,
    color: "bg-indigo-500",
  },
  [RequirementCategory.NON_ENGLISH_COUNTRIES]: {
    label: "小语种国家留学",
    description: "日本、韩国、法国、德国等非英语国家留学",
    icon: Languages,
    color: "bg-emerald-500",
  },
  [RequirementCategory.ACADEMIC_PLANNING]: {
    label: "学业规划",
    description: "学习计划制定、课程选择、成绩提升等",
    icon: Calendar,
    color: "bg-blue-600",
  },
  [RequirementCategory.CAREER_PLANNING]: {
    label: "职业规划",
    description: "职业发展路径、专业选择、就业指导等",
    icon: Briefcase,
    color: "bg-green-600",
  },
  [RequirementCategory.STUDY_ABROAD]: {
    label: "出国留学",
    description: "留学整体规划、海外升学指导等",
    icon: PlaneTakeoff,
    color: "bg-sky-500",
  },
  [RequirementCategory.EXAM_PREPARATION]: {
    label: "考试备考",
    description: "标准化考试准备、考试技巧指导等",
    icon: FileText,
    color: "bg-orange-500",
  },
  [RequirementCategory.EXTRACURRICULAR]: {
    label: "课外活动规划",
    description: "社团活动、志愿服务、竞赛准备等",
    icon: Users,
    color: "bg-violet-500",
  },
};

interface RequirementCategorySelectorProps {
  value?: RequirementCategory;
  onValueChange?: (value: RequirementCategory) => void;
  placeholder?: string;
  disabled?: boolean;
  multiple?: boolean;
  values?: RequirementCategory[];
  onValuesChange?: (values: RequirementCategory[]) => void;
  showDescription?: boolean;
  size?: "sm" | "default" | "lg";
  className?: string;
}

export function RequirementCategorySelector({
  value,
  onValueChange,
  placeholder = "选择服务分类",
  disabled = false,
  multiple = false,
  values = [],
  onValuesChange,
  showDescription = true,
  size = "default",
  className,
}: RequirementCategorySelectorProps) {
  const [open, setOpen] = useState(false);

  const categories = Object.entries(CATEGORY_CONFIG) as [
    RequirementCategory,
    (typeof CATEGORY_CONFIG)[RequirementCategory],
  ][];

  const handleSelect = (category: RequirementCategory) => {
    if (multiple) {
      const newValues = values.includes(category)
        ? values.filter((v) => v !== category)
        : [...values, category];
      onValuesChange?.(newValues);
    } else {
      onValueChange?.(category);
      setOpen(false);
    }
  };

  const getButtonContent = () => {
    if (multiple) {
      if (values.length === 0) {
        return <span className="text-muted-foreground">{placeholder}</span>;
      }
      if (values.length === 1) {
        const config = CATEGORY_CONFIG[values[0]];
        const Icon = config.icon;
        return (
          <div className="flex items-center gap-2">
            <div className={cn("w-3 h-3 rounded-full", config.color)} />
            <Icon className="h-4 w-4" />
            <span>{config.label}</span>
          </div>
        );
      }
      return (
        <div className="flex items-center gap-1">
          <span>{values.length} 个分类</span>
          <div className="flex gap-1">
            {values.slice(0, 2).map((cat) => (
              <Badge key={cat} variant="secondary" className="text-xs">
                {CATEGORY_CONFIG[cat].label}
              </Badge>
            ))}
            {values.length > 2 && (
              <Badge variant="secondary" className="text-xs">
                +{values.length - 2}
              </Badge>
            )}
          </div>
        </div>
      );
    } else {
      if (!value) {
        return <span className="text-muted-foreground">{placeholder}</span>;
      }
      const config = CATEGORY_CONFIG[value];
      const Icon = config.icon;
      return (
        <div className="flex items-center gap-2">
          <div className={cn("w-3 h-3 rounded-full", config.color)} />
          <Icon className="h-4 w-4" />
          <span>{config.label}</span>
        </div>
      );
    }
  };

  const buttonSizeClass = {
    sm: "h-8 px-2 text-sm",
    default: "h-10 px-3",
    lg: "h-12 px-4 text-lg",
  }[size];

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          className={cn("w-full justify-between", buttonSizeClass, className)}
        >
          {getButtonContent()}
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <Command>
          <CommandInput placeholder="搜索分类..." />
          <CommandList>
            <CommandEmpty>未找到相关分类</CommandEmpty>
            <CommandGroup>
              {categories.map(([category, config]) => {
                const Icon = config.icon;
                const isSelected = multiple
                  ? values.includes(category)
                  : value === category;

                return (
                  <CommandItem
                    key={category}
                    value={category}
                    onSelect={() => handleSelect(category)}
                    className="flex items-start gap-3 p-3"
                  >
                    <div className="flex items-center gap-2 min-w-0 flex-1">
                      <div
                        className={cn(
                          "w-3 h-3 rounded-full flex-shrink-0",
                          config.color,
                        )}
                      />
                      <Icon className="h-4 w-4 flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <div className="font-medium">{config.label}</div>
                        {showDescription && (
                          <div className="text-sm text-muted-foreground">
                            {config.description}
                          </div>
                        )}
                      </div>
                    </div>
                    {isSelected && (
                      <Check className="h-4 w-4 text-primary flex-shrink-0" />
                    )}
                  </CommandItem>
                );
              })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

// 导出分类配置供其他组件使用
export { CATEGORY_CONFIG };
