"use client";

import { useState, useRef, useEffect } from "react";
import { 
  Send, 
  Paperclip, 
  Smile, 
  Phone, 
  Video, 
  MoreVertical,
  Calendar,
  FileText,
  Image as ImageIcon,
  Download,
  Clock,
  CheckCheck
} from "lucide-react";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Card, CardContent, CardHeader } from "@workspace/ui/components/card";
import { Avatar, AvatarFallback, AvatarImage } from "@workspace/ui/components/avatar";
import { Badge } from "@workspace/ui/components/badge";
import { ScrollArea } from "@workspace/ui/components/scroll-area";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { cn } from "@workspace/ui/lib/utils";

interface Message {
  id: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  content: string;
  type: "text" | "file" | "image" | "system";
  timestamp: string;
  status: "sending" | "sent" | "delivered" | "read";
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
}

interface ChatParticipant {
  id: string;
  name: string;
  avatar?: string;
  role: "student" | "planner";
  isOnline: boolean;
  lastSeen?: string;
}

interface MatchChatInterfaceProps {
  requestId: string;
  currentUserId: string;
  participant: ChatParticipant;
  className?: string;
}

export function MatchChatInterface({
  requestId,
  currentUserId,
  participant,
  className,
}: MatchChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      senderId: "system",
      senderName: "系统",
      content: "匹配成功！您可以开始与规划师沟通了。",
      type: "system",
      timestamp: new Date().toISOString(),
      status: "read",
    },
    {
      id: "2",
      senderId: participant.id,
      senderName: participant.name,
      senderAvatar: participant.avatar,
      content: "您好！我是您的专属规划师，很高兴为您服务。请问您希望从哪个方面开始我们的规划呢？",
      type: "text",
      timestamp: new Date(Date.now() - 300000).toISOString(),
      status: "read",
    },
  ]);
  
  const [newMessage, setNewMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = () => {
    if (!newMessage.trim()) return;

    const message: Message = {
      id: Date.now().toString(),
      senderId: currentUserId,
      senderName: "我",
      content: newMessage,
      type: "text",
      timestamp: new Date().toISOString(),
      status: "sending",
    };

    setMessages(prev => [...prev, message]);
    setNewMessage("");

    // 模拟发送状态更新
    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === message.id 
            ? { ...msg, status: "sent" as const }
            : msg
        )
      );
    }, 1000);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const message: Message = {
      id: Date.now().toString(),
      senderId: currentUserId,
      senderName: "我",
      content: `发送了文件: ${file.name}`,
      type: file.type.startsWith("image/") ? "image" : "file",
      timestamp: new Date().toISOString(),
      status: "sending",
      fileName: file.name,
      fileSize: file.size,
      fileUrl: URL.createObjectURL(file),
    };

    setMessages(prev => [...prev, message]);
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString("zh-CN", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getStatusIcon = (status: Message["status"]) => {
    switch (status) {
      case "sending":
        return <Clock className="h-3 w-3 text-gray-400" />;
      case "sent":
        return <CheckCheck className="h-3 w-3 text-gray-400" />;
      case "delivered":
        return <CheckCheck className="h-3 w-3 text-blue-500" />;
      case "read":
        return <CheckCheck className="h-3 w-3 text-green-500" />;
      default:
        return null;
    }
  };

  const renderMessage = (message: Message) => {
    const isOwn = message.senderId === currentUserId;
    const isSystem = message.type === "system";

    if (isSystem) {
      return (
        <div key={message.id} className="flex justify-center my-4">
          <div className="bg-muted px-3 py-1 rounded-full text-xs text-muted-foreground">
            {message.content}
          </div>
        </div>
      );
    }

    return (
      <div
        key={message.id}
        className={cn(
          "flex gap-3 mb-4",
          isOwn ? "flex-row-reverse" : "flex-row"
        )}
      >
        {!isOwn && (
          <Avatar className="h-8 w-8 flex-shrink-0">
            <AvatarImage src={message.senderAvatar} />
            <AvatarFallback className="text-xs">
              {message.senderName.charAt(0)}
            </AvatarFallback>
          </Avatar>
        )}
        
        <div className={cn("flex flex-col", isOwn ? "items-end" : "items-start")}>
          {!isOwn && (
            <div className="text-xs text-muted-foreground mb-1">
              {message.senderName}
            </div>
          )}
          
          <div
            className={cn(
              "max-w-xs lg:max-w-md px-3 py-2 rounded-lg",
              isOwn
                ? "bg-primary text-primary-foreground"
                : "bg-muted"
            )}
          >
            {message.type === "text" && (
              <p className="text-sm whitespace-pre-wrap">{message.content}</p>
            )}
            
            {message.type === "image" && (
              <div className="space-y-2">
                <img
                  src={message.fileUrl}
                  alt={message.fileName}
                  className="max-w-full h-auto rounded"
                />
                <p className="text-xs opacity-75">{message.fileName}</p>
              </div>
            )}
            
            {message.type === "file" && (
              <div className="flex items-center gap-2 p-2 bg-background/10 rounded">
                <FileText className="h-4 w-4" />
                <div className="flex-1 min-w-0">
                  <p className="text-xs font-medium truncate">{message.fileName}</p>
                  <p className="text-xs opacity-75">
                    {message.fileSize && formatFileSize(message.fileSize)}
                  </p>
                </div>
                <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                  <Download className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
          
          <div className={cn(
            "flex items-center gap-1 mt-1 text-xs text-muted-foreground",
            isOwn ? "flex-row-reverse" : "flex-row"
          )}>
            <span>{formatTime(message.timestamp)}</span>
            {isOwn && getStatusIcon(message.status)}
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card className={cn("flex flex-col h-[600px]", className)}>
      {/* 聊天头部 */}
      <CardHeader className="flex-row items-center justify-between space-y-0 pb-3 border-b">
        <div className="flex items-center gap-3">
          <div className="relative">
            <Avatar className="h-10 w-10">
              <AvatarImage src={participant.avatar} />
              <AvatarFallback>{participant.name.charAt(0)}</AvatarFallback>
            </Avatar>
            {participant.isOnline && (
              <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-background" />
            )}
          </div>
          <div>
            <h3 className="font-medium">{participant.name}</h3>
            <p className="text-xs text-muted-foreground">
              {participant.isOnline ? "在线" : `最后活跃: ${participant.lastSeen}`}
            </p>
          </div>
          <Badge variant="secondary" className="text-xs">
            {participant.role === "planner" ? "规划师" : "学生"}
          </Badge>
        </div>
        
        <div className="flex items-center gap-1">
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Phone className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Video className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Calendar className="h-4 w-4" />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>查看资料</DropdownMenuItem>
              <DropdownMenuItem>预约会议</DropdownMenuItem>
              <DropdownMenuItem>文件管理</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      {/* 消息列表 */}
      <CardContent className="flex-1 p-0">
        <ScrollArea className="h-full p-4">
          {messages.map(renderMessage)}
          {isTyping && (
            <div className="flex gap-3 mb-4">
              <Avatar className="h-8 w-8">
                <AvatarImage src={participant.avatar} />
                <AvatarFallback className="text-xs">
                  {participant.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className="bg-muted px-3 py-2 rounded-lg">
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce delay-100" />
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce delay-200" />
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </ScrollArea>
      </CardContent>

      {/* 输入区域 */}
      <div className="border-t p-4">
        <div className="flex items-end gap-2">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                onChange={handleFileUpload}
                accept="image/*,.pdf,.doc,.docx,.txt"
              />
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => fileInputRef.current?.click()}
              >
                <Paperclip className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <ImageIcon className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <Smile className="h-4 w-4" />
              </Button>
            </div>
            <Input
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="输入消息..."
              onKeyPress={(e) => {
                if (e.key === "Enter" && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
              className="resize-none"
            />
          </div>
          <Button
            onClick={handleSendMessage}
            disabled={!newMessage.trim()}
            className="h-10 w-10 p-0"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </Card>
  );
}
