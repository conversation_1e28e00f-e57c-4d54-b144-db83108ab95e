"use client";

import { useState } from "react";
import { 
  <PERSON>, 
  User<PERSON><PERSON><PERSON>, 
  <PERSON>lip<PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>, 
  TrendingUp, 
  <PERSON>ert<PERSON>ircle, 
  Calendar<PERSON><PERSON>,
  Handshake,
  BarChart3
} from "lucide-react";
import { useRouter } from "next/navigation";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import { Progress } from "@workspace/ui/components/progress";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from "@workspace/ui/components/tabs";

import { StatCard } from "@/components/match/common/MatchStatus";
import { MatchRequestCard } from "@/components/match/common/MatchCard";
import { DataEmpty } from "@/components/match/common/MatchEmptyState";

import { useMatchStatistics, useMatchRequests } from "@/hooks/useMatchData";
import { MatchRequestStatus } from "@/types/match.types";
import { CATEGORY_CONFIG, STATUS_CONFIG } from "@/constants/match-constants";

export function MatchDashboard() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("overview");

  // 使用重构后的hooks
  const { 
    statistics, 
    loading: statsLoading, 
    error: statsError,
    refresh: refreshStats 
  } = useMatchStatistics();

  const { 
    requests: recentRequests, 
    loading: requestsLoading,
    error: requestsError,
    refresh: refreshRequests 
  } = useMatchRequests({}, { page: 1, pageSize: 5 });

  const handleViewDetails = (requestId: string) => {
    router.push(`/community/match/requests/${requestId}`);
  };

  const handleCreateRequest = () => {
    router.push("/community/match/create");
  };

  const handleBrowsePlanners = () => {
    router.push("/community/match/planners");
  };

  const renderStatistics = () => {
    if (statsLoading) {
      return (
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
      );
    }

    if (statsError || !statistics) {
      return (
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground mb-4">
            {statsError || "无法加载统计数据"}
          </p>
          <Button onClick={refreshStats} variant="outline">
            重试
          </Button>
        </div>
      );
    }

    return (
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="总请求数"
          value={statistics.total}
          description="所有匹配请求"
          icon={ClipboardCheck}
          color="blue"
        />
        <StatCard
          title="待匹配"
          value={statistics.pending}
          description="等待匹配的请求"
          icon={Clock}
          color="yellow"
        />
        <StatCard
          title="已确认"
          value={statistics.confirmed}
          description="成功匹配并确认"
          icon={UserCheck}
          color="green"
        />
        <StatCard
          title="成功率"
          value={`${statistics.successRate?.toFixed(1) || 0}%`}
          description="匹配成功率"
          icon={TrendingUp}
          color="purple"
        />
      </div>
    );
  };

  const renderRecentRequests = () => {
    if (requestsLoading) {
      return (
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-5 w-32" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  </div>
                  <Skeleton className="h-6 w-20" />
                </div>
              </CardHeader>
              <CardContent>
                <Skeleton className="h-12 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      );
    }

    if (requestsError) {
      return (
        <div className="text-center py-8">
          <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground mb-4">{requestsError}</p>
          <Button onClick={refreshRequests} variant="outline" size="sm">
            重试
          </Button>
        </div>
      );
    }

    if (!recentRequests.length) {
      return (
        <DataEmpty
          type="requests"
          onCreateNew={handleCreateRequest}
        />
      );
    }

    return (
      <div className="space-y-4">
        {recentRequests.map((request) => {
          const categoryConfig = CATEGORY_CONFIG[request.category];
          const statusConfig = STATUS_CONFIG[request.status];
          
          return (
            <MatchRequestCard
              key={request.id}
              request={request}
              categoryConfig={categoryConfig}
              statusConfig={statusConfig}
              onViewDetails={handleViewDetails}
              actions={
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleViewDetails(request.id)}
                >
                  查看详情
                </Button>
              }
            />
          );
        })}
      </div>
    );
  };

  const renderStatusBreakdown = () => {
    if (statsLoading || !statistics) {
      return (
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-16" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-12 mb-2" />
                <Skeleton className="h-2 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      );
    }

    const statusData = [
      { status: MatchRequestStatus.PENDING, count: statistics.pending },
      { status: MatchRequestStatus.MATCHING, count: statistics.matching },
      { status: MatchRequestStatus.MATCHED, count: statistics.matched },
      { status: MatchRequestStatus.CONFIRMED, count: statistics.confirmed },
      { status: MatchRequestStatus.CANCELLED, count: statistics.cancelled },
      { status: MatchRequestStatus.EXPIRED, count: statistics.expired },
    ];

    return (
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        {statusData.map(({ status, count }) => {
          const config = STATUS_CONFIG[status];
          const percentage = statistics.total > 0 ? (count / statistics.total) * 100 : 0;
          
          return (
            <Card key={status}>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">{config.label}</CardTitle>
                <CardDescription>{config.description}</CardDescription>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="text-3xl font-bold mb-2">{count}</div>
                <Progress value={percentage} className="h-2" />
                <div className="text-xs text-muted-foreground mt-1">
                  {percentage.toFixed(1)}%
                </div>
              </CardContent>
              <CardFooter>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="w-full justify-center"
                  onClick={() => router.push(`/community/match/requests?status=${status}`)}
                >
                  查看详情
                </Button>
              </CardFooter>
            </Card>
          );
        })}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">匹配中心</h2>
          <p className="text-muted-foreground">
            查看匹配请求和规划师的匹配状态
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button 
            onClick={handleCreateRequest}
            className="gap-2"
          >
            <ClipboardCheck className="h-4 w-4" />
            创建匹配请求
          </Button>
          <Button 
            variant="outline"
            onClick={handleBrowsePlanners}
            className="gap-2"
          >
            <Users className="h-4 w-4" />
            浏览规划师
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview" className="gap-2">
            <BarChart3 className="h-4 w-4" />
            概览
          </TabsTrigger>
          <TabsTrigger value="requests" className="gap-2">
            <ClipboardCheck className="h-4 w-4" />
            最近请求
          </TabsTrigger>
          <TabsTrigger value="status" className="gap-2">
            <TrendingUp className="h-4 w-4" />
            状态分析
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {renderStatistics()}
        </TabsContent>

        <TabsContent value="requests" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">最近的匹配请求</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push("/community/match/requests")}
            >
              查看全部
            </Button>
          </div>
          {renderRecentRequests()}
        </TabsContent>

        <TabsContent value="status" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">按状态分组</h3>
          </div>
          {renderStatusBreakdown()}
        </TabsContent>
      </Tabs>
    </div>
  );
}
