"use client";

import { useState, useEffect } from "react";
import { 
  <PERSON>, 
  User<PERSON><PERSON><PERSON>, 
  <PERSON>lip<PERSON><PERSON><PERSON><PERSON>, 
  Clock, 
  TrendingUp, 
  Alert<PERSON>ircle, 
  <PERSON><PERSON><PERSON>,
  Handshake,
  Bar<PERSON>hart3
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

import { But<PERSON> } from "@workspace/ui/components/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@workspace/ui/components/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@workspace/ui/components/avatar";
import { Badge } from "@workspace/ui/components/badge";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Progress } from "@workspace/ui/components/progress";

import { apiClient } from "@/utils/api";

// 匹配请求状态
enum MatchRequestStatus {
  PENDING = "PENDING",
  MATCHING = "MATCHING",
  MATCHED = "MATCHED",
  CONFIRMED = "CONFIRMED",
  CANCELLED = "CANCELLED",
  EXPIRED = "EXPIRED",
}

// 状态配置
const STATUS_CONFIG = {
  [MatchRequestStatus.PENDING]: { label: "待匹配", color: "bg-yellow-500" },
  [MatchRequestStatus.MATCHING]: { label: "匹配中", color: "bg-blue-500" },
  [MatchRequestStatus.MATCHED]: { label: "已匹配", color: "bg-green-500" },
  [MatchRequestStatus.CONFIRMED]: { label: "已确认", color: "bg-purple-500" },
  [MatchRequestStatus.CANCELLED]: { label: "已取消", color: "bg-gray-500" },
  [MatchRequestStatus.EXPIRED]: { label: "已过期", color: "bg-red-500" },
};

export function MatchDashboard() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("overview");
  const [loading, setLoading] = useState(true);
  const [statistics, setStatistics] = useState<any>(null);
  const [recentRequests, setRecentRequests] = useState<any[]>([]);
  const [upcomingMatches, setUpcomingMatches] = useState<any[]>([]);

  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      try {
        // 获取匹配统计数据
        const statsResult = await apiClient.request("/api/match/statistics");
        
        // 获取最近的匹配请求
        const requestsResult = await apiClient.request("/api/match/requests?page=1&pageSize=5");
        
        if (statsResult.success && requestsResult.success) {
          setStatistics(statsResult.data);
          setRecentRequests(requestsResult.data.items || []);
          
          // 模拟获取即将到来的匹配（实际项目中应该有专门的API）
          setUpcomingMatches(
            requestsResult.data.items
              .filter((req: any) => req.status === MatchRequestStatus.CONFIRMED)
              .slice(0, 3)
          );
        }
      } catch (error) {
        console.error("获取数据失败:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // 渲染统计卡片
  const renderStatCard = (
    icon: React.ReactNode,
    title: string,
    value: number | string,
    description: string,
    color: string
  ) => (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between space-y-0">
          <h3 className="text-sm font-medium tracking-tight text-muted-foreground">{title}</h3>
          <div className={`p-2 rounded-full ${color}`}>{icon}</div>
        </div>
        <div className="mt-2">
          {loading ? (
            <Skeleton className="h-9 w-20" />
          ) : (
            <div className="text-3xl font-bold">{value}</div>
          )}
        </div>
        <p className="text-xs text-muted-foreground mt-1">{description}</p>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">匹配中心</h2>
          <p className="text-muted-foreground">
            查看匹配请求和规划师的匹配状态
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button 
            onClick={() => router.push("/community/match/create")}
            className="gap-2"
          >
            <ClipboardCheck className="h-4 w-4" />
            创建匹配请求
          </Button>
          <Button 
            variant="outline"
            onClick={() => router.push("/community/match/planners")}
            className="gap-2"
          >
            <Users className="h-4 w-4" />
            浏览规划师
          </Button>
        </div>
      </div>

      <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">总览</TabsTrigger>
          <TabsTrigger value="requests">请求</TabsTrigger>
          <TabsTrigger value="upcoming">即将到来</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* 统计卡片 */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {renderStatCard(
              <ClipboardCheck className="h-4 w-4 text-white" />,
              "总请求数",
              loading ? "-" : statistics?.total || 0,
              "所有匹配请求总数",
              "bg-blue-500"
            )}
            {renderStatCard(
              <UserCheck className="h-4 w-4 text-white" />,
              "已确认匹配",
              loading ? "-" : statistics?.confirmed || 0,
              "已完成的匹配数量",
              "bg-green-500"
            )}
            {renderStatCard(
              <TrendingUp className="h-4 w-4 text-white" />,
              "成功率",
              loading ? "-" : `${((statistics?.successRate || 0) * 100).toFixed(0)}%`,
              "匹配成功比率",
              "bg-purple-500"
            )}
            {renderStatCard(
              <Clock className="h-4 w-4 text-white" />,
              "平均响应时间",
              loading ? "-" : `${statistics?.avgResponseTime || 0}小时`,
              "规划师平均响应时间",
              "bg-yellow-500"
            )}
          </div>

          {/* 最近的请求 */}
          <h3 className="text-lg font-medium mt-6">最近的匹配请求</h3>
          <div className="space-y-3">
            {loading ? (
              <>
                {[1, 2, 3].map((i) => (
                  <Card key={i}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Skeleton className="h-10 w-10 rounded-full" />
                          <div>
                            <Skeleton className="h-4 w-36 mb-2" />
                            <Skeleton className="h-3 w-24" />
                          </div>
                        </div>
                        <Skeleton className="h-6 w-16 rounded-full" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </>
            ) : (
              <>
                {recentRequests.length > 0 ? (
                  recentRequests.map((request) => (
                    <Card key={request.id} className="hover:shadow-sm transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${STATUS_CONFIG[request.status]?.color || "bg-gray-500"}`}>
                              <ClipboardCheck className="h-5 w-5 text-white" />
                            </div>
                            <div>
                              <div className="font-medium">{request.requirements.title}</div>
                              <div className="text-sm text-muted-foreground">
                                创建于 {formatDate(request.createdAt)}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">
                              {STATUS_CONFIG[request.status]?.label || "未知状态"}
                            </Badge>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => router.push(`/community/match/requests/${request.id}`)}
                            >
                              查看
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <Card>
                    <CardContent className="p-6 text-center">
                      <p className="text-muted-foreground">暂无匹配请求</p>
                      <Button 
                        className="mt-2" 
                        variant="outline"
                        onClick={() => router.push("/community/match/create")}
                      >
                        创建第一个请求
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </>
            )}

            {recentRequests.length > 0 && (
              <div className="text-center mt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push("/community/match/requests")}
                >
                  查看全部请求
                </Button>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="requests" className="space-y-4">
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
            {/* 按状态分组的请求卡片 */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">待匹配</CardTitle>
                <CardDescription>尚未匹配的请求</CardDescription>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="text-3xl font-bold">
                  {loading ? <Skeleton className="h-8 w-8" /> : statistics?.pending || 0}
                </div>
                <Progress
                  value={statistics ? (statistics.pending / statistics.total) * 100 : 0}
                  className="h-2 mt-2"
                />
              </CardContent>
              <CardFooter>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="w-full justify-center"
                  onClick={() => router.push("/community/match/requests?status=PENDING")}
                >
                  查看详情
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">匹配中</CardTitle>
                <CardDescription>正在进行匹配的请求</CardDescription>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="text-3xl font-bold">
                  {loading ? <Skeleton className="h-8 w-8" /> : statistics?.matching || 0}
                </div>
                <Progress
                  value={statistics ? (statistics.matching / statistics.total) * 100 : 0}
                  className="h-2 mt-2"
                />
              </CardContent>
              <CardFooter>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="w-full justify-center"
                  onClick={() => router.push("/community/match/requests?status=MATCHING")}
                >
                  查看详情
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">已确认</CardTitle>
                <CardDescription>已成功匹配的请求</CardDescription>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="text-3xl font-bold">
                  {loading ? <Skeleton className="h-8 w-8" /> : statistics?.confirmed || 0}
                </div>
                <Progress
                  value={statistics ? (statistics.confirmed / statistics.total) * 100 : 0}
                  className="h-2 mt-2"
                />
              </CardContent>
              <CardFooter>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="w-full justify-center"
                  onClick={() => router.push("/community/match/requests?status=CONFIRMED")}
                >
                  查看详情
                </Button>
              </CardFooter>
            </Card>
          </div>

          <div className="mt-4 text-center">
            <Button onClick={() => router.push("/community/match/create")}>
              创建新匹配请求
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="upcoming" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* 即将到来的匹配 */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>即将到来的匹配</CardTitle>
                <CardDescription>最近确认的匹配</CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="space-y-4">
                    {[1, 2].map((i) => (
                      <div key={i} className="flex items-center gap-4">
                        <Skeleton className="h-12 w-12 rounded-full" />
                        <div className="space-y-2">
                          <Skeleton className="h-4 w-40" />
                          <Skeleton className="h-3 w-24" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : upcomingMatches.length > 0 ? (
                  <div className="space-y-4">
                    {upcomingMatches.map((match) => (
                      <div key={match.id} className="flex items-center gap-4 p-2 rounded-lg hover:bg-muted/50">
                        <Avatar className="h-12 w-12">
                          <AvatarImage 
                            src={match.matchedPlanner?.tenantUser?.user?.avatar} 
                            alt={match.matchedPlanner?.tenantUser?.user?.name || "规划师"} 
                          />
                          <AvatarFallback>
                            {(match.matchedPlanner?.tenantUser?.user?.name || "规划师").charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="font-medium">{match.requirements.title}</div>
                          <div className="text-sm text-muted-foreground flex items-center gap-2">
                            <CalendarClock className="h-3 w-3" />
                            匹配于 {formatDate(match.matchedAt || match.confirmedAt)}
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/community/match/requests/${match.id}`)}
                        >
                          查看详情
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Handshake className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">暂无即将到来的匹配</p>
                    <p className="text-sm text-muted-foreground mt-1">创建匹配请求以寻找合适的规划师</p>
                    <Button 
                      className="mt-4"
                      onClick={() => router.push("/community/match/create")}
                    >
                      创建匹配请求
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 匹配分析和建议 */}
            <Card>
              <CardHeader>
                <CardTitle>匹配分析</CardTitle>
                <CardDescription>基于您的历史数据</CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="space-y-4">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-4 w-5/6" />
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">平均匹配时间</span>
                      <span className="font-medium">48小时</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">匹配成功率</span>
                      <span className="font-medium">
                        {((statistics?.successRate || 0) * 100).toFixed(0)}%
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">满意度评分</span>
                      <span className="font-medium">
                        {statistics?.avgRating?.toFixed(1) || "暂无数据"} / 5
                      </span>
                    </div>
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full"
                  onClick={() => router.push("/community/match/analytics")}
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  查看详细分析
                </Button>
              </CardFooter>
            </Card>

            {/* 匹配建议 */}
            <Card>
              <CardHeader>
                <CardTitle>匹配建议</CardTitle>
                <CardDescription>提高匹配成功率的建议</CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="space-y-3">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div className="flex gap-2">
                      <AlertCircle className="h-5 w-5 text-blue-500 flex-shrink-0" />
                      <p className="text-sm">完善您的需求描述，详细的需求有助于更准确的匹配</p>
                    </div>
                    <div className="flex gap-2">
                      <AlertCircle className="h-5 w-5 text-blue-500 flex-shrink-0" />
                      <p className="text-sm">设置合理的预算范围，以匹配更多合适的规划师</p>
                    </div>
                    <div className="flex gap-2">
                      <AlertCircle className="h-5 w-5 text-blue-500 flex-shrink-0" />
                      <p className="text-sm">及时响应规划师的咨询，提高匹配效率</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
