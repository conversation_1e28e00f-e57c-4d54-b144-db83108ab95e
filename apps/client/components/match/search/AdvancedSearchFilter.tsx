"use client";

import { useState, useEffect } from "react";
import { 
  Search, 
  Filter, 
  X, 
  Save, 
  Star,
  MapPin,
  DollarSign,
  Clock,
  TrendingUp,
  <PERSON>,
  BookO<PERSON>,
  Sliders
} from "lucide-react";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import { Slider } from "@workspace/ui/components/slider";
import { Checkbox } from "@workspace/ui/components/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@workspace/ui/components/collapsible";
import { cn } from "@workspace/ui/lib/utils";

import { RequirementCategory, type MatchFilters, type PlannerFilters } from "@/types/match.types";
import { CATEGORY_CONFIG, GRADE_OPTIONS, SUBJECT_OPTIONS } from "@/constants/match-constants";

interface SavedSearch {
  id: string;
  name: string;
  filters: MatchFilters | PlannerFilters;
  type: "requests" | "planners";
  createdAt: string;
}

interface AdvancedSearchFilterProps {
  type: "requests" | "planners";
  initialFilters?: MatchFilters | PlannerFilters;
  onFiltersChange: (filters: MatchFilters | PlannerFilters) => void;
  onSaveSearch?: (name: string, filters: MatchFilters | PlannerFilters) => void;
  className?: string;
}

export function AdvancedSearchFilter({
  type,
  initialFilters = {},
  onFiltersChange,
  onSaveSearch,
  className,
}: AdvancedSearchFilterProps) {
  const [filters, setFilters] = useState<MatchFilters | PlannerFilters>(initialFilters);
  const [isExpanded, setIsExpanded] = useState(false);
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>([]);
  const [saveSearchName, setSaveSearchName] = useState("");
  const [showSaveDialog, setShowSaveDialog] = useState(false);

  // 加载保存的搜索条件
  useEffect(() => {
    const saved = localStorage.getItem(`match-saved-searches-${type}`);
    if (saved) {
      setSavedSearches(JSON.parse(saved));
    }
  }, [type]);

  const handleFilterChange = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleArrayFilterChange = (key: string, value: string, checked: boolean) => {
    const currentArray = (filters as any)[key] || [];
    const newArray = checked 
      ? [...currentArray, value]
      : currentArray.filter((item: string) => item !== value);
    
    handleFilterChange(key, newArray);
  };

  const clearFilters = () => {
    const emptyFilters = { searchQuery: "" };
    setFilters(emptyFilters);
    onFiltersChange(emptyFilters);
  };

  const saveSearch = () => {
    if (!saveSearchName.trim() || !onSaveSearch) return;
    
    const newSearch: SavedSearch = {
      id: Date.now().toString(),
      name: saveSearchName,
      filters,
      type,
      createdAt: new Date().toISOString(),
    };
    
    const updatedSearches = [...savedSearches, newSearch];
    setSavedSearches(updatedSearches);
    localStorage.setItem(`match-saved-searches-${type}`, JSON.stringify(updatedSearches));
    
    onSaveSearch(saveSearchName, filters);
    setSaveSearchName("");
    setShowSaveDialog(false);
  };

  const loadSavedSearch = (search: SavedSearch) => {
    setFilters(search.filters);
    onFiltersChange(search.filters);
  };

  const deleteSavedSearch = (searchId: string) => {
    const updatedSearches = savedSearches.filter(s => s.id !== searchId);
    setSavedSearches(updatedSearches);
    localStorage.setItem(`match-saved-searches-${type}`, JSON.stringify(updatedSearches));
  };

  const hasActiveFilters = Object.keys(filters).some(key => {
    const value = (filters as any)[key];
    return value !== undefined && value !== "" && 
           (Array.isArray(value) ? value.length > 0 : true);
  });

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            <CardTitle>高级搜索</CardTitle>
            {hasActiveFilters && (
              <Badge variant="secondary" className="ml-2">
                {Object.keys(filters).filter(key => {
                  const value = (filters as any)[key];
                  return value !== undefined && value !== "" && 
                         (Array.isArray(value) ? value.length > 0 : true);
                }).length} 个筛选条件
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            {hasActiveFilters && (
              <Button variant="ghost" size="sm" onClick={clearFilters}>
                <X className="h-4 w-4 mr-1" />
                清除
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <Sliders className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 基础搜索 */}
        <div className="space-y-2">
          <Label>关键词搜索</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={type === "requests" ? "搜索匹配请求..." : "搜索规划师..."}
              value={(filters as any).searchQuery || ""}
              onChange={(e) => handleFilterChange("searchQuery", e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* 保存的搜索 */}
        {savedSearches.length > 0 && (
          <div className="space-y-2">
            <Label>保存的搜索</Label>
            <div className="flex flex-wrap gap-2">
              {savedSearches.map((search) => (
                <div key={search.id} className="flex items-center gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => loadSavedSearch(search)}
                    className="gap-1"
                  >
                    <Star className="h-3 w-3" />
                    {search.name}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => deleteSavedSearch(search.id)}
                    className="h-6 w-6 p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleContent className="space-y-4">
            {/* 分类筛选 */}
            {type === "requests" && (
              <div className="space-y-2">
                <Label>需求分类</Label>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                  {Object.entries(CATEGORY_CONFIG).map(([category, config]) => (
                    <div key={category} className="flex items-center space-x-2">
                      <Checkbox
                        id={category}
                        checked={((filters as MatchFilters).category as any) === category}
                        onCheckedChange={(checked) => 
                          handleFilterChange("category", checked ? category : undefined)
                        }
                      />
                      <Label htmlFor={category} className="text-sm">
                        {config.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 年级筛选 */}
            <div className="space-y-2">
              <Label>年级</Label>
              <div className="grid grid-cols-3 sm:grid-cols-4 gap-2">
                {GRADE_OPTIONS.map((grade) => (
                  <div key={grade.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={grade.value}
                      checked={
                        type === "requests" 
                          ? (filters as MatchFilters).grade === grade.value
                          : ((filters as PlannerFilters).grades || []).includes(grade.value)
                      }
                      onCheckedChange={(checked) => {
                        if (type === "requests") {
                          handleFilterChange("grade", checked ? grade.value : undefined);
                        } else {
                          handleArrayFilterChange("grades", grade.value, !!checked);
                        }
                      }}
                    />
                    <Label htmlFor={grade.value} className="text-sm">
                      {grade.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* 科目筛选 */}
            <div className="space-y-2">
              <Label>科目</Label>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                {SUBJECT_OPTIONS.map((subject) => (
                  <div key={subject.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={subject.value}
                      checked={
                        type === "requests"
                          ? ((filters as MatchFilters).subjects || []).includes(subject.value)
                          : ((filters as PlannerFilters).subjects || []).includes(subject.value)
                      }
                      onCheckedChange={(checked) => 
                        handleArrayFilterChange("subjects", subject.value, !!checked)
                      }
                    />
                    <Label htmlFor={subject.value} className="text-sm">
                      {subject.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* 地区筛选 */}
            <div className="space-y-2">
              <Label>地区</Label>
              <Select
                value={(filters as any).location || ""}
                onValueChange={(value) => handleFilterChange("location", value || undefined)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择地区" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">不限</SelectItem>
                  <SelectItem value="北京">北京</SelectItem>
                  <SelectItem value="上海">上海</SelectItem>
                  <SelectItem value="广州">广州</SelectItem>
                  <SelectItem value="深圳">深圳</SelectItem>
                  <SelectItem value="杭州">杭州</SelectItem>
                  <SelectItem value="南京">南京</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 规划师特有筛选 */}
            {type === "planners" && (
              <>
                {/* 评分筛选 */}
                <div className="space-y-2">
                  <Label>最低评分</Label>
                  <div className="px-3">
                    <Slider
                      value={[(filters as PlannerFilters).ratingMin || 0]}
                      onValueChange={([value]) => handleFilterChange("ratingMin", value)}
                      max={5}
                      min={0}
                      step={0.5}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground mt-1">
                      <span>0分</span>
                      <span>{(filters as PlannerFilters).ratingMin || 0}分</span>
                      <span>5分</span>
                    </div>
                  </div>
                </div>

                {/* 经验筛选 */}
                <div className="space-y-2">
                  <Label>最少经验年限</Label>
                  <div className="px-3">
                    <Slider
                      value={[(filters as PlannerFilters).experienceMin || 0]}
                      onValueChange={([value]) => handleFilterChange("experienceMin", value)}
                      max={20}
                      min={0}
                      step={1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground mt-1">
                      <span>0年</span>
                      <span>{(filters as PlannerFilters).experienceMin || 0}年</span>
                      <span>20年+</span>
                    </div>
                  </div>
                </div>

                {/* 接单状态 */}
                <div className="space-y-2">
                  <Label>接单状态</Label>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="accepting-match"
                      checked={(filters as PlannerFilters).isAcceptingMatch === true}
                      onCheckedChange={(checked) => 
                        handleFilterChange("isAcceptingMatch", checked ? true : undefined)
                      }
                    />
                    <Label htmlFor="accepting-match" className="text-sm">
                      仅显示可接单的规划师
                    </Label>
                  </div>
                </div>
              </>
            )}

            {/* 预算筛选 (仅匹配请求) */}
            {type === "requests" && (
              <div className="space-y-2">
                <Label>预算范围</Label>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Input
                      type="number"
                      placeholder="最低预算"
                      value={(filters as MatchFilters).budgetMin || ""}
                      onChange={(e) => handleFilterChange("budgetMin", e.target.value ? Number(e.target.value) : undefined)}
                    />
                  </div>
                  <div>
                    <Input
                      type="number"
                      placeholder="最高预算"
                      value={(filters as MatchFilters).budgetMax || ""}
                      onChange={(e) => handleFilterChange("budgetMax", e.target.value ? Number(e.target.value) : undefined)}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* 排序选项 */}
            <div className="space-y-2">
              <Label>排序方式</Label>
              <div className="grid grid-cols-2 gap-2">
                <Select
                  value={(filters as any).sortBy || ""}
                  onValueChange={(value) => handleFilterChange("sortBy", value || undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="排序字段" />
                  </SelectTrigger>
                  <SelectContent>
                    {type === "requests" ? (
                      <>
                        <SelectItem value="createdAt">创建时间</SelectItem>
                        <SelectItem value="urgency">紧急程度</SelectItem>
                        <SelectItem value="budget">预算</SelectItem>
                        <SelectItem value="responses">响应数</SelectItem>
                      </>
                    ) : (
                      <>
                        <SelectItem value="rating">评分</SelectItem>
                        <SelectItem value="experience">经验</SelectItem>
                        <SelectItem value="successRate">成功率</SelectItem>
                        <SelectItem value="responseTime">响应时间</SelectItem>
                      </>
                    )}
                  </SelectContent>
                </Select>
                
                <Select
                  value={(filters as any).sortOrder || "desc"}
                  onValueChange={(value) => handleFilterChange("sortOrder", value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="desc">降序</SelectItem>
                    <SelectItem value="asc">升序</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* 保存搜索 */}
            {onSaveSearch && hasActiveFilters && (
              <div className="space-y-2">
                <Label>保存当前搜索</Label>
                {showSaveDialog ? (
                  <div className="flex gap-2">
                    <Input
                      placeholder="搜索名称"
                      value={saveSearchName}
                      onChange={(e) => setSaveSearchName(e.target.value)}
                    />
                    <Button onClick={saveSearch} disabled={!saveSearchName.trim()}>
                      保存
                    </Button>
                    <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
                      取消
                    </Button>
                  </div>
                ) : (
                  <Button
                    variant="outline"
                    onClick={() => setShowSaveDialog(true)}
                    className="gap-2"
                  >
                    <Save className="h-4 w-4" />
                    保存搜索条件
                  </Button>
                )}
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
}
