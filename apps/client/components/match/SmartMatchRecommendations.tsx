"use client";

import { useState, useEffect } from "react";
import {
  Star,
  MapPin,
  Clock,
  Users,
  TrendingUp,
  Check,
  X,
  MessageCircle,
  Phone,
} from "lucide-react";

import { Button } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { Progress } from "@workspace/ui/components/progress";
import { Alert, AlertDescription } from "@workspace/ui/components/alert";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { toast } from "sonner";
import { cn } from "@workspace/ui/lib/utils";

import { apiClient } from "@/utils/api";

interface MatchReasons {
  gradeMatch: boolean;
  subjectMatch: boolean;
  locationMatch: boolean;
  hasCapacity: boolean;
  fastResponse: boolean;
  priceMatch: boolean;
}

interface MatchDetails {
  currentLoad: number;
  responseTimeScore: number;
  capacityScore: number;
}

interface PlannerRecommendation {
  planner: {
    id: string;
    rating: number;
    tenantUser: {
      user: {
        id: string;
        name: string;
        avatar?: string;
        email: string;
      };
    };
    matchProfile: {
      isAcceptingMatch: boolean;
      maxConcurrent: number;
      preferredGrades: string[];
      preferredSubjects: string[];
      preferredLocations: string[];
      responseTime: number;
      basePrice?: number;
      successRate: number;
    };
    students: any[];
  };
  score: number;
  matchReasons: MatchReasons;
  details: MatchDetails;
}

interface SmartMatchRecommendationsProps {
  matchRequestId: string;
  onPlannerSelect?: (plannerId: string) => void;
  onContactPlanner?: (plannerId: string) => void;
  className?: string;
}

export function SmartMatchRecommendations({
  matchRequestId,
  onPlannerSelect,
  onContactPlanner,
  className,
}: SmartMatchRecommendationsProps) {
  const [recommendations, setRecommendations] = useState<
    PlannerRecommendation[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [accepting, setAccepting] = useState<string | null>(null);

  useEffect(() => {
    loadRecommendations();
  }, [matchRequestId]);

  const loadRecommendations = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await apiClient.request(
        `/api/match/recommendations/${matchRequestId}`,
      );

      if (result.success) {
        toast.success("推荐加载成功");
        setRecommendations(result.data || []);
      } else {
        setError(result.message || "获取推荐失败");
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const handleAcceptRecommendation = async (plannerId: string) => {
    try {
      setAccepting(plannerId);

      // 这里应该调用接受推荐的API
      const result = await apiClient.request(
        `/api/match/response/${matchRequestId}/accept`,
        {
          method: "POST",
          body: JSON.stringify({ plannerId }),
        },
      );

      if (result.success) {
        toast.success("已接受推荐", {
          description: "匹配请求已发送给该规划师",
        });
        onPlannerSelect?.(plannerId);
      } else {
        toast.error(result.message || "操作失败");
      }
    } catch (err) {
      toast.error("网络错误，请稍后重试");
    } finally {
      setAccepting(null);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 0.8) return "text-green-600";
    if (score >= 0.6) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 0.8) return "bg-green-100";
    if (score >= 0.6) return "bg-yellow-100";
    return "bg-red-100";
  };

  const formatMatchReasons = (reasons: MatchReasons) => {
    const activeReasons = [];
    if (reasons.gradeMatch) activeReasons.push("年级匹配");
    if (reasons.subjectMatch) activeReasons.push("科目匹配");
    if (reasons.locationMatch) activeReasons.push("地区匹配");
    if (reasons.hasCapacity) activeReasons.push("有充足时间");
    if (reasons.fastResponse) activeReasons.push("响应迅速");
    if (reasons.priceMatch) activeReasons.push("价格合适");
    return activeReasons;
  };

  if (loading) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">智能推荐</h3>
          <Skeleton className="h-4 w-20" />
        </div>
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-48" />
                  <div className="flex gap-2">
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-6 w-16" />
                  </div>
                </div>
                <Skeleton className="h-8 w-20" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertDescription>
          {error}
          <Button
            variant="link"
            size="sm"
            onClick={loadRecommendations}
            className="ml-2 p-0"
          >
            重试
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  if (recommendations.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <div className="text-muted-foreground">
            暂时没有找到合适的规划师推荐
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={loadRecommendations}
            className="mt-2"
          >
            重新匹配
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">智能推荐</h3>
        <Badge variant="secondary">
          找到 {recommendations.length} 位合适的规划师
        </Badge>
      </div>

      <div className="space-y-4">
        {recommendations.map((recommendation, index) => {
          const { planner, score, matchReasons, details } = recommendation;
          const user = planner.tenantUser.user;
          const profile = planner.matchProfile;
          const reasons = formatMatchReasons(matchReasons);

          return (
            <Card
              key={planner.id}
              className="hover:shadow-md transition-shadow"
            >
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  {/* 规划师信息 */}
                  <div className="flex items-center gap-3 flex-1">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={user.avatar} alt={user.name} />
                      <AvatarFallback>{user.name.slice(0, 2)}</AvatarFallback>
                    </Avatar>

                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium">{user.name}</h4>
                        <Badge variant="outline" className="text-xs">
                          #{index + 1}
                        </Badge>
                      </div>

                      {/* 评分和基本信息 */}
                      <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3 fill-current text-yellow-400" />
                          <span>{planner.rating.toFixed(1)}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <TrendingUp className="h-3 w-3" />
                          <span>
                            {(profile.successRate * 100).toFixed(0)}% 成功率
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>{profile.responseTime}h 响应</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          <span>
                            {planner.students.length}/{profile.maxConcurrent}{" "}
                            学生
                          </span>
                        </div>
                      </div>

                      {/* 匹配原因 */}
                      {reasons.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-2">
                          {reasons.map((reason) => (
                            <Badge
                              key={reason}
                              variant="secondary"
                              className="text-xs"
                            >
                              <Check className="h-3 w-3 mr-1" />
                              {reason}
                            </Badge>
                          ))}
                        </div>
                      )}

                      {/* 价格信息 */}
                      {profile.basePrice && (
                        <div className="text-sm text-muted-foreground">
                          <span>起步价: ¥{profile.basePrice}/小时</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 匹配度和操作 */}
                  <div className="text-right space-y-3">
                    {/* 匹配度显示 */}
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                          匹配度
                        </span>
                        <div
                          className={cn(
                            "px-2 py-1 rounded text-sm font-medium",
                            getScoreBgColor(score),
                            getScoreColor(score),
                          )}
                        >
                          {(score * 100).toFixed(0)}%
                        </div>
                      </div>
                      <Progress value={score * 100} className="w-20 h-2" />
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onContactPlanner?.(planner.id)}
                        className="gap-1"
                      >
                        <MessageCircle className="h-3 w-3" />
                        联系
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handleAcceptRecommendation(planner.id)}
                        disabled={accepting === planner.id}
                        className="gap-1"
                      >
                        {accepting === planner.id ? (
                          "处理中..."
                        ) : (
                          <>
                            <Check className="h-3 w-3" />
                            选择
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>

                {/* 详细匹配信息 */}
                <div className="mt-4 pt-4 border-t">
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div className="text-center">
                      <div className="text-muted-foreground">当前负荷</div>
                      <div
                        className={cn(
                          "font-medium",
                          details.currentLoad > 0.8
                            ? "text-red-600"
                            : details.currentLoad > 0.6
                              ? "text-yellow-600"
                              : "text-green-600",
                        )}
                      >
                        {(details.currentLoad * 100).toFixed(0)}%
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-muted-foreground">响应评分</div>
                      <div className="font-medium text-blue-600">
                        {details.responseTimeScore}分
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-muted-foreground">时间充裕度</div>
                      <div className="font-medium text-green-600">
                        {details.capacityScore}分
                      </div>
                    </div>
                  </div>
                </div>

                {/* 专长领域 */}
                {profile.preferredSubjects &&
                  profile.preferredSubjects.length > 0 && (
                    <div className="mt-3 pt-3 border-t">
                      <div className="text-sm text-muted-foreground mb-1">
                        专长科目:
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {profile.preferredSubjects
                          .slice(0, 5)
                          .map((subject) => (
                            <Badge
                              key={subject}
                              variant="outline"
                              className="text-xs"
                            >
                              {subject}
                            </Badge>
                          ))}
                        {profile.preferredSubjects.length > 5 && (
                          <Badge variant="outline" className="text-xs">
                            +{profile.preferredSubjects.length - 5}
                          </Badge>
                        )}
                      </div>
                    </div>
                  )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* 操作提示 */}
      <div className="text-center text-sm text-muted-foreground">
        <p>系统根据您的需求智能匹配，点击"选择"确认合作意向</p>
      </div>
    </div>
  );
}
