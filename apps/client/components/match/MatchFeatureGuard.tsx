"use client";

import { useEffect, useState } from "react";
import { apiClient } from "@/utils/api";
import { MatchSetupGuide } from "./MatchSetupGuide";
import { Loader2 } from "lucide-react";
import { useClientUser } from "@/hooks/useClientUser";

interface MatchFeatureGuardProps {
  children: React.ReactNode;
  onError?: (message: string) => void;
}

export function MatchFeatureGuard({
  children,
  onError,
}: MatchFeatureGuardProps) {
  const [loading, setLoading] = useState(true);
  const [canAccess, setCanAccess] = useState(false);
  const [setupRequired, setSetupRequired] = useState<string | null>(null);
  const [showSetupGuide, setShowSetupGuide] = useState(false);
  const [message, setMessage] = useState("");
  const { currentTenant } = useClientUser();

  const checkEligibility = async () => {
    setLoading(true);
    try {
      const response = await apiClient.request(
        `/api/match/check-eligibility?tenantId=${currentTenant?.tenantId}`,
      );

      if (response.success) {
        const { canCreate, setupRequired, message } = response as any;

        setCanAccess(canCreate);
        setSetupRequired(setupRequired || null);
        setMessage(message || "");

        // 如果需要设置规划师配置，显示引导
        if (setupRequired === "planner") {
          setShowSetupGuide(true);
        }
      } else {
        setMessage(response.data.message || "检查资格失败");
        onError?.(response.data.message);
      }
    } catch (error) {
      console.error("检查匹配资格失败:", error);
      setMessage("网络错误，请稍后重试");
      onError?.("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkEligibility();
  }, []);

  const handleSetupComplete = () => {
    // 设置完成后重新检查资格
    checkEligibility();
  };

  if (loading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (!canAccess && !setupRequired) {
    return (
      <div className="flex h-64 flex-col items-center justify-center gap-4">
        <p className="text-lg font-medium text-muted-foreground">
          {message || "您没有权限访问匹配功能"}
        </p>
      </div>
    );
  }

  return (
    <>
      {canAccess ? (
        children
      ) : (
        <div className="flex h-64 flex-col items-center justify-center gap-4">
          <p className="text-lg font-medium text-muted-foreground">{message}</p>
        </div>
      )}

      {/* 规划师设置引导弹窗 */}
      {setupRequired === "planner" && (
        <MatchSetupGuide
          open={showSetupGuide}
          onOpenChange={setShowSetupGuide}
          onSetupComplete={handleSetupComplete}
        />
      )}
    </>
  );
}
