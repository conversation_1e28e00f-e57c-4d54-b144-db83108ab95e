"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  <PERSON>sponsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
} from "recharts";
import { TrendingUp, Users, CheckCircle, Target } from "lucide-react";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import { Progress } from "@workspace/ui/components/progress";
import {
  Ta<PERSON>,
  <PERSON><PERSON>Content,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Button } from "@workspace/ui/components/button";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Alert, AlertDescription } from "@workspace/ui/components/alert";
import { cn } from "@workspace/ui/lib/utils";

import { apiClient } from "@/utils/api";

interface CategoryStatistics {
  category: string;
  total: number;
  avgUrgency: number;
  successRate: number;
  statusBreakdown: {
    PENDING: number;
    MATCHING: number;
    MATCHED: number;
    CONFIRMED: number;
    CANCELLED: number;
    EXPIRED: number;
  };
}

interface MatchStatisticsByCategory {
  overview: {
    totalRequests: number;
    totalCategories: number;
    avgSuccessRate: number;
    totalConfirmed: number;
  };
  categories: CategoryStatistics[];
  trends: {
    category: string;
    data: Array<{
      date: string;
      requests: number;
      successRate: number;
    }>;
  }[];
}

interface CategoryStatisticsDisplayProps {
  tenantId?: string;
  timeRange?: "7d" | "30d" | "90d" | "1y";
  className?: string;
}

const CATEGORY_LABELS = {
  ACADEMIC_TUTORING: "学科辅导",
  EXAM_PREPARATION: "考试备考",
  SKILL_DEVELOPMENT: "技能发展",
  CAREER_PLANNING: "职业规划",
  STUDY_ABROAD: "留学咨询",
  COMPETITION_TRAINING: "竞赛培训",
  HOMEWORK_HELP: "作业辅导",
  PSYCHOLOGICAL_COUNSELING: "心理咨询",
  PARENT_CONSULTATION: "家长咨询",
  OTHER: "其他服务",
};

const CATEGORY_COLORS = {
  ACADEMIC_TUTORING: "#3b82f6",
  EXAM_PREPARATION: "#ef4444",
  SKILL_DEVELOPMENT: "#10b981",
  CAREER_PLANNING: "#f59e0b",
  STUDY_ABROAD: "#8b5cf6",
  COMPETITION_TRAINING: "#06b6d4",
  HOMEWORK_HELP: "#84cc16",
  PSYCHOLOGICAL_COUNSELING: "#ec4899",
  PARENT_CONSULTATION: "#6366f1",
  OTHER: "#6b7280",
};

const STATUS_LABELS = {
  PENDING: "待匹配",
  MATCHING: "匹配中",
  MATCHED: "已匹配",
  CONFIRMED: "已确认",
  CANCELLED: "已取消",
  EXPIRED: "已过期",
};

export function CategoryStatisticsDisplay({
  tenantId,
  timeRange = "30d",
  className,
}: CategoryStatisticsDisplayProps) {
  const [statistics, setStatistics] =
    useState<MatchStatisticsByCategory | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);

  useEffect(() => {
    loadStatistics();
  }, [tenantId, selectedTimeRange]);

  const loadStatistics = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (tenantId) params.append("tenantId", tenantId);
      params.append("timeRange", selectedTimeRange);

      const result = await apiClient.request(
        `/api/match/statistics/category?${params.toString()}`,
      );

      if (result.success) {
        setStatistics(result.data);
      } else {
        setError(result.message || "获取统计数据失败");
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 0.8) return "text-green-600";
    if (rate >= 0.6) return "text-yellow-600";
    return "text-red-600";
  };

  const getSuccessRateBg = (rate: number) => {
    if (rate >= 0.8) return "bg-green-100";
    if (rate >= 0.6) return "bg-yellow-100";
    return "bg-red-100";
  };

  const formatPercentage = (value: number) => `${(value * 100).toFixed(1)}%`;

  if (loading) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-4 w-20 mb-2" />
                <Skeleton className="h-8 w-16 mb-1" />
                <Skeleton className="h-3 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-5 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-64 w-full" />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <Skeleton className="h-5 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-64 w-full" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error || !statistics) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertDescription>
          {error}
          <Button
            variant="link"
            size="sm"
            onClick={loadStatistics}
            className="ml-2 p-0"
          >
            重试
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  const { overview, categories } = statistics;

  // 准备图表数据
  const chartData = categories.map((cat) => ({
    name:
      CATEGORY_LABELS[cat.category as keyof typeof CATEGORY_LABELS] ||
      cat.category,
    total: cat.total,
    confirmed: cat.statusBreakdown.CONFIRMED,
    successRate: cat.successRate * 100,
    avgUrgency: cat.avgUrgency,
  }));

  const pieData = categories.map((cat) => ({
    name:
      CATEGORY_LABELS[cat.category as keyof typeof CATEGORY_LABELS] ||
      cat.category,
    value: cat.total,
    color:
      CATEGORY_COLORS[cat.category as keyof typeof CATEGORY_COLORS] ||
      "#6b7280",
  }));

  return (
    <div className={cn("space-y-6", className)}>
      {/* 时间范围选择器 */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">分类统计</h3>
        <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">近7天</SelectItem>
            <SelectItem value="30d">近30天</SelectItem>
            <SelectItem value="90d">近90天</SelectItem>
            <SelectItem value="1y">近1年</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* 概览指标 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-blue-600" />
              <span className="text-sm text-muted-foreground">总请求数</span>
            </div>
            <div className="text-2xl font-bold mt-1">
              {overview.totalRequests}
            </div>
            <div className="text-xs text-muted-foreground">
              涵盖 {overview.totalCategories} 个分类
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-sm text-muted-foreground">成功匹配</span>
            </div>
            <div className="text-2xl font-bold mt-1">
              {overview.totalConfirmed}
            </div>
            <div
              className={cn(
                "text-xs",
                getSuccessRateColor(overview.avgSuccessRate),
              )}
            >
              {formatPercentage(overview.avgSuccessRate)} 成功率
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="text-sm text-muted-foreground">平均成功率</span>
            </div>
            <div className="text-2xl font-bold mt-1">
              {formatPercentage(overview.avgSuccessRate)}
            </div>
            <div className="text-xs text-muted-foreground">所有分类平均</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-purple-600" />
              <span className="text-sm text-muted-foreground">活跃分类</span>
            </div>
            <div className="text-2xl font-bold mt-1">
              {overview.totalCategories}
            </div>
            <div className="text-xs text-muted-foreground">
              有匹配请求的分类
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 详细分析 */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList>
          <TabsTrigger value="overview">总览</TabsTrigger>
          <TabsTrigger value="success">成功率分析</TabsTrigger>
          <TabsTrigger value="distribution">分布统计</TabsTrigger>
          <TabsTrigger value="details">详细数据</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>各分类请求量</CardTitle>
                <CardDescription>按服务分类统计的请求数量</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="name"
                      angle={-45}
                      textAnchor="end"
                      height={100}
                      fontSize={12}
                    />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="total" fill="#3b82f6" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>分类分布</CardTitle>
                <CardDescription>各分类请求量占比</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={pieData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) =>
                        `${name} ${(percent * 100).toFixed(0)}%`
                      }
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {pieData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="success" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>各分类成功率对比</CardTitle>
              <CardDescription>成功匹配率分析</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="name"
                    angle={-45}
                    textAnchor="end"
                    height={100}
                    fontSize={12}
                  />
                  <YAxis
                    label={{
                      value: "成功率 (%)",
                      angle: -90,
                      position: "insideLeft",
                    }}
                  />
                  <Tooltip formatter={(value) => [`${value}%`, "成功率"]} />
                  <Bar dataKey="successRate" fill="#10b981" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="distribution" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>紧急程度分布</CardTitle>
              <CardDescription>各分类的平均紧急程度</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="name"
                    angle={-45}
                    textAnchor="end"
                    height={100}
                    fontSize={12}
                  />
                  <YAxis
                    domain={[0, 10]}
                    label={{
                      value: "紧急程度",
                      angle: -90,
                      position: "insideLeft",
                    }}
                  />
                  <Tooltip
                    formatter={(value) => [value.toFixed(1), "平均紧急程度"]}
                  />
                  <Bar dataKey="avgUrgency" fill="#f59e0b" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="details" className="space-y-4">
          <div className="space-y-4">
            {categories.map((category) => (
              <Card key={category.category}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">
                      {CATEGORY_LABELS[
                        category.category as keyof typeof CATEGORY_LABELS
                      ] || category.category}
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">总计: {category.total}</Badge>
                      <div
                        className={cn(
                          "px-2 py-1 rounded text-sm font-medium",
                          getSuccessRateBg(category.successRate),
                          getSuccessRateColor(category.successRate),
                        )}
                      >
                        {formatPercentage(category.successRate)}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                    {Object.entries(category.statusBreakdown).map(
                      ([status, count]) => (
                        <div key={status} className="text-center">
                          <div className="text-sm text-muted-foreground mb-1">
                            {
                              STATUS_LABELS[
                                status as keyof typeof STATUS_LABELS
                              ]
                            }
                          </div>
                          <div className="text-lg font-semibold">{count}</div>
                          <div className="text-xs text-muted-foreground">
                            {category.total > 0
                              ? ((count / category.total) * 100).toFixed(1)
                              : 0}
                            %
                          </div>
                        </div>
                      ),
                    )}
                  </div>

                  <div className="mt-4 pt-4 border-t">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">
                        平均紧急程度
                      </span>
                      <div className="flex items-center gap-2">
                        <Progress
                          value={(category.avgUrgency / 10) * 100}
                          className="w-20"
                        />
                        <span className="font-medium">
                          {category.avgUrgency.toFixed(1)}/10
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
