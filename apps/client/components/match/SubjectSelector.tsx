"use client";

import { useState } from "react";
import { ChevronDown, Check, X } from "lucide-react";

import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@workspace/ui/components/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import { Badge } from "@workspace/ui/components/badge";
import { cn } from "@workspace/ui/lib/utils";

import { 
  Subject, 
  SUBJECT_CONFIG, 
  SUBJECT_GROUPS,
} from "@/constants/match-constants";

interface SubjectSelectorProps {
  value?: Subject[];
  onValueChange?: (value: Subject[]) => void;
  placeholder?: string;
  disabled?: boolean;
  size?: "sm" | "default" | "lg";
  max?: number;
  showSelectedBadges?: boolean;
  className?: string;
}

export function SubjectSelector({
  value = [],
  onValueChange,
  placeholder = "选择科目",
  disabled = false,
  size = "default",
  max,
  showSelectedBadges = true,
  className,
}: SubjectSelectorProps) {
  const [open, setOpen] = useState(false);

  const handleSelect = (subject: Subject) => {
    if (!onValueChange) return;
    
    const isSelected = value.includes(subject);
    
    if (isSelected) {
      // 移除已选择的科目
      onValueChange(value.filter(v => v !== subject));
    } else {
      // 如果设置了最大选择数量，且已经达到，则不添加
      if (max && value.length >= max) {
        return;
      }
      // 添加科目
      onValueChange([...value, subject]);
    }
  };
  
  const handleRemove = (subject: Subject) => {
    if (!onValueChange) return;
    onValueChange(value.filter(v => v !== subject));
  };

  const buttonSizeClass = {
    sm: "h-8 px-2 text-sm",
    default: "h-10 px-3",
    lg: "h-12 px-4 text-lg",
  }[size];

  return (
    <div className="flex flex-col gap-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            disabled={disabled}
            className={cn("w-full justify-between", buttonSizeClass, className)}
          >
            {value.length > 0 ? (
              <span>{value.length} 个科目已选择</span>
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput placeholder="搜索科目..." />
            <CommandList>
              <CommandEmpty>未找到相关科目</CommandEmpty>
              {SUBJECT_GROUPS.map((group) => (
                <CommandGroup key={group.label} heading={group.label}>
                  {group.options.map((subject) => {
                    const isSelected = value.includes(subject);
                    return (
                      <CommandItem
                        key={subject}
                        value={subject}
                        onSelect={() => handleSelect(subject)}
                      >
                        <div className="flex items-center gap-2 flex-1">
                          <span>{SUBJECT_CONFIG[subject].label}</span>
                          {max && (
                            <span className="text-xs text-muted-foreground ml-auto">
                              {isSelected ? "已选择" : ""}
                            </span>
                          )}
                        </div>
                        {isSelected && (
                          <Check className="ml-auto h-4 w-4 text-primary" />
                        )}
                      </CommandItem>
                    );
                  })}
                </CommandGroup>
              ))}
              {max && (
                <div className="px-2 py-1.5 text-xs text-muted-foreground border-t">
                  最多可选择 {max} 个科目 ({value.length}/{max})
                </div>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      
      {/* 已选科目 */}
      {showSelectedBadges && value.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {value.map((subject) => (
            <Badge key={subject} variant="secondary" className="gap-1 pr-1">
              {SUBJECT_CONFIG[subject].label}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 rounded-full p-0 hover:bg-muted"
                onClick={() => handleRemove(subject)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
}
