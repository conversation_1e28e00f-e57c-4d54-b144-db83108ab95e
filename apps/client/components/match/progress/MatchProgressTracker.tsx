"use client";

import { useState, useEffect } from "react";
import { 
  CheckCircle, 
  AlertCircle, 
  Target,
  RefreshCw
} from "lucide-react";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Progress } from "@workspace/ui/components/progress";
import { Badge } from "@workspace/ui/components/badge";
import { Button } from "@workspace/ui/components/button";
import { cn } from "@workspace/ui/lib/utils";

import { MatchRequestStatus } from "@/types/match.types";
import { apiClient } from "@/utils/api";

interface MatchStep {
  id: string;
  title: string;
  description: string;
  status: "pending" | "in-progress" | "completed" | "failed";
  estimatedTime?: string;
  actualTime?: string;
  details?: string[];
}

interface MatchProgressData {
  plannerCount: number;
  matchedCount: number;
  recommendationCount: number;
  estimatedTime: string;
  currentStepDetails: string[];
}

interface MatchProgressTrackerProps {
  requestId: string;
  currentStatus: MatchRequestStatus;
  onRefresh?: () => void;
  className?: string;
}

export function MatchProgressTracker({
  requestId,
  currentStatus,
  onRefresh,
  className,
}: MatchProgressTrackerProps) {
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);
  const [estimatedCompletion, setEstimatedCompletion] = useState<string>();
  const [progressData, setProgressData] = useState<MatchProgressData | null>(null);
  const [loading, setLoading] = useState(false);

  // 获取进度数据
  const fetchProgressData = async () => {
    if (loading) return;
    
    setLoading(true);
    try {
      // 获取推荐数据以了解匹配进度
      const { data: recommendations } = await apiClient.request(`/api/match/recommendations/${requestId}`);
      
      // 模拟获取其他进度数据（这里可以根据实际API调整）
      const mockProgressData: MatchProgressData = {
        plannerCount: recommendations?.length || 0,
        matchedCount: recommendations?.filter((r: any) => r.score > 0.7)?.length || 0,
        recommendationCount: recommendations?.length || 0,
        estimatedTime: getEstimatedTime(currentStatus),
        currentStepDetails: getCurrentStepDetails(currentStatus, recommendations),
      };
      
      setProgressData(mockProgressData);
    } catch (error) {
      console.error('获取进度数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取估算时间
  const getEstimatedTime = (status: MatchRequestStatus): string => {
    switch (status) {
      case MatchRequestStatus.PENDING:
        return "预计5-8分钟后完成";
      case MatchRequestStatus.MATCHING:
        return "预计2-3分钟后完成";
      case MatchRequestStatus.MATCHED:
        return "预计1分钟后完成";
      default:
        return "匹配已完成";
    }
  };

  // 获取当前步骤详情
  const getCurrentStepDetails = (status: MatchRequestStatus, recommendations: any[]): string[] => {
    const count = recommendations?.length || 0;
    const highQualityCount = recommendations?.filter((r: any) => r.score > 0.8)?.length || 0;
    
    switch (status) {
      case MatchRequestStatus.PENDING:
        return [
          "✓ 需求类别识别完成",
          "✓ 技能要求分析完成", 
          "✓ 地理位置匹配完成",
          count > 0 ? `✓ 找到 ${count} 位候选规划师` : "⏳ 搜索候选规划师中...",
          "⏳ 综合评估中..."
        ];
      case MatchRequestStatus.MATCHING:
        return [
          "✓ 需求分析完成",
          `✓ 筛选出 ${count} 位候选规划师`,
          "✓ 技能匹配度计算完成",
          highQualityCount > 0 ? `✓ 发现 ${highQualityCount} 位高匹配度规划师` : "⏳ 分析匹配度中...",
          "⏳ 个性化偏好匹配中...",
          "⏳ 综合评分计算中..."
        ];
      case MatchRequestStatus.MATCHED:
        return [
          "✓ 需求分析完成",
          `✓ 成功匹配 ${count} 位规划师`,
          "✓ 匹配度计算完成",
          "✓ 推荐列表生成完成",
          "⏳ 推荐排序优化中..."
        ];
      default:
        return [
          "✓ 需求分析完成",
          `✓ 成功匹配 ${count} 位规划师`,
          "✓ 智能匹配完成",
          "✓ 推荐列表生成完成"
        ];
    }
  };

  // 定义匹配流程步骤
  const getMatchSteps = (status: MatchRequestStatus): MatchStep[] => {
    const baseSteps: MatchStep[] = [
      {
        id: "analysis",
        title: "需求分析",
        description: "系统正在分析您的需求特征",
        status: "completed",
        estimatedTime: "1-2分钟",
        actualTime: "1分钟",
        details: progressData?.currentStepDetails.slice(0, 4) || [
          "✓ 需求类别识别完成",
          "✓ 技能要求分析完成", 
          "✓ 地理位置匹配完成",
          "✓ 预算范围确认完成"
        ]
      },
      {
        id: "screening",
        title: "规划师筛选",
        description: "从规划师库中筛选符合条件的候选人",
        status: status === MatchRequestStatus.PENDING ? "in-progress" : "completed",
        estimatedTime: "3-5分钟",
        actualTime: status !== MatchRequestStatus.PENDING ? "4分钟" : undefined,
        details: status === MatchRequestStatus.PENDING ? 
          progressData?.currentStepDetails || [
            "⏳ 专业领域匹配中...",
            "⏳ 经验水平筛选中...",
            "⏳ 可用时间检查中...",
            "⏳ 综合评估中..."
          ] : [
            `✓ 专业领域匹配 (找到${progressData?.plannerCount || 0}位)`,
            `✓ 经验水平筛选 (筛选出${progressData?.matchedCount || 0}位)`,
            "✓ 可用时间检查完成",
            "✓ 综合评估完成"
          ]
      },
      {
        id: "matching",
        title: "智能匹配",
        description: "使用AI算法计算最佳匹配度",
        status: status === MatchRequestStatus.MATCHING ? "in-progress" : 
               status === MatchRequestStatus.PENDING ? "pending" : "completed",
        estimatedTime: "2-3分钟",
        actualTime: [MatchRequestStatus.MATCHED, MatchRequestStatus.CONFIRMED].includes(status) ? "2分钟" : undefined,
        details: status === MatchRequestStatus.MATCHING ? 
          progressData?.currentStepDetails || [
            "✓ 技能匹配度计算完成",
            "⏳ 历史成功率分析中...",
            "⏳ 个性化偏好匹配中...",
            "⏳ 综合评分计算中..."
          ] : status === MatchRequestStatus.PENDING ? [
            "等待前序步骤完成",
          ] : [
            "✓ 技能匹配度计算完成",
            "✓ 历史成功率分析完成",
            "✓ 个性化偏好匹配完成",
            "✓ 综合评分计算完成"
          ]
      },
      {
        id: "recommendation",
        title: "生成推荐",
        description: "为您生成个性化的规划师推荐列表",
        status: status === MatchRequestStatus.MATCHED ? "in-progress" :
               [MatchRequestStatus.PENDING, MatchRequestStatus.MATCHING].includes(status) ? "pending" : "completed",
        estimatedTime: "1分钟",
        actualTime: status === MatchRequestStatus.CONFIRMED ? "1分钟" : undefined,
        details: status === MatchRequestStatus.MATCHED ? [
          "⏳ 推荐列表生成中...",
          "⏳ 匹配理由分析中...",
          "⏳ 推荐排序优化中..."
        ] : [MatchRequestStatus.PENDING, MatchRequestStatus.MATCHING].includes(status) ? [
          "等待匹配完成",
        ] : [
          `✓ 推荐列表生成完成 (${progressData?.recommendationCount || 0}位)`,
          "✓ 匹配理由分析完成", 
          "✓ 推荐排序优化完成"
        ]
      }
    ];

    return baseSteps;
  };

  const steps = getMatchSteps(currentStatus);

  // 初始化时获取进度数据
  useEffect(() => {
    fetchProgressData();
  }, [requestId, currentStatus]);

  // 计算进度
  useEffect(() => {
    const completedSteps = steps.filter(step => step.status === "completed").length;
    const inProgressSteps = steps.filter(step => step.status === "in-progress").length;
    const totalSteps = steps.length;
    
    const newProgress = ((completedSteps + inProgressSteps * 0.5) / totalSteps) * 100;
    setProgress(newProgress);
    
    const currentStepIndex = steps.findIndex(step => step.status === "in-progress");
    setCurrentStep(currentStepIndex >= 0 ? currentStepIndex : completedSteps - 1);

    // 使用动态估算完成时间
    setEstimatedCompletion(progressData?.estimatedTime || getEstimatedTime(currentStatus));
  }, [currentStatus, steps, progressData]);

  // 刷新处理
  const handleRefresh = () => {
    fetchProgressData();
    onRefresh?.();
  };

  const getStatusIcon = (status: MatchStep["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "in-progress":
        return <div className="h-5 w-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />;
      case "failed":
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <div className="h-5 w-5 border-2 border-gray-300 rounded-full" />;
    }
  };

  const getStatusColor = (status: MatchStep["status"]) => {
    switch (status) {
      case "completed":
        return "border-green-200 bg-green-50";
      case "in-progress":
        return "border-blue-200 bg-blue-50";
      case "failed":
        return "border-red-200 bg-red-50";
      default:
        return "border-gray-200 bg-gray-50";
    }
  };

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5 text-primary" />
              匹配进度
            </CardTitle>
            <CardDescription>
              {estimatedCompletion}
            </CardDescription>
          </div>
          {onRefresh && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              className="gap-2"
              disabled={loading}
            >
              <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
              刷新
            </Button>
          )}
        </div>
        
        {/* 总体进度条 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium">总体进度</span>
            <span className="text-muted-foreground">{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {steps.map((step, index) => (
          <div
            key={step.id}
            className={cn(
              "relative p-4 rounded-lg border-2 transition-all duration-300",
              getStatusColor(step.status),
              index === currentStep && "ring-2 ring-primary/20"
            )}
          >
            {/* 连接线 */}
            {index < steps.length - 1 && (
              <div className="absolute left-6 top-12 w-0.5 h-8 bg-gray-300" />
            )}
            
            <div className="flex items-start gap-3">
              {/* 状态图标 */}
              <div className="flex-shrink-0 mt-0.5">
                {getStatusIcon(step.status)}
              </div>
              
              {/* 步骤内容 */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h4 className="font-medium">{step.title}</h4>
                  <div className="flex items-center gap-2">
                    {step.status === "in-progress" && (
                      <Badge variant="secondary" className="animate-pulse">
                        进行中
                      </Badge>
                    )}
                    {step.actualTime && (
                      <span className="text-xs text-muted-foreground">
                        用时: {step.actualTime}
                      </span>
                    )}
                    {!step.actualTime && step.estimatedTime && (
                      <span className="text-xs text-muted-foreground">
                        预计: {step.estimatedTime}
                      </span>
                    )}
                  </div>
                </div>
                
                <p className="text-sm text-muted-foreground mb-2">
                  {step.description}
                </p>
                
                {/* 详细信息 */}
                {step.details && step.details.length > 0 && (
                  <div className="space-y-1">
                    {step.details.map((detail, detailIndex) => (
                      <div
                        key={detailIndex}
                        className="text-xs flex items-center gap-2"
                      >
                        <span className={cn(
                          detail.startsWith("✓") ? "text-green-600" :
                          detail.startsWith("⏳") ? "text-blue-600" :
                          "text-muted-foreground"
                        )}>
                          {detail}
                        </span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
        
        {/* 完成状态提示 */}
        {currentStatus === MatchRequestStatus.MATCHED && (
          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2 text-green-800">
              <CheckCircle className="h-5 w-5" />
              <span className="font-medium">匹配完成！</span>
            </div>
            <p className="text-sm text-green-700 mt-1">
              我们为您找到了 {progressData?.recommendationCount || 0} 位高度匹配的规划师，请查看推荐列表。
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
