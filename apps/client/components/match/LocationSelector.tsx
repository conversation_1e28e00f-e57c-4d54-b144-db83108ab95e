"use client";

import { useState } from "react";
import { ChevronDown, Check, MapPin } from "lucide-react";

import { Button } from "@workspace/ui/components/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@workspace/ui/components/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import { cn } from "@workspace/ui/lib/utils";

import {
  Location,
  LOCATION_CONFIG,
  LOCATION_GROUPS,
} from "@/constants/match-constants";

interface LocationSelectorProps {
  value?: Location;
  onValueChange?: (value: Location) => void;
  placeholder?: string;
  disabled?: boolean;
  size?: "sm" | "default" | "lg";
  className?: string;
}

export function LocationSelector({
  value,
  onValueChange,
  placeholder = "选择地区",
  disabled = false,
  size = "default",
  className,
}: LocationSelectorProps) {
  const [open, setOpen] = useState(false);

  const handleSelect = (location: Location) => {
    onValueChange?.(location);
    setOpen(false);
  };

  const buttonSizeClass = {
    sm: "h-8 px-2 text-sm",
    default: "h-10 px-3",
    lg: "h-12 px-4 text-lg",
  }[size];

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          className={cn("w-full justify-between", buttonSizeClass, className)}
        >
          {value ? (
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <span>{LOCATION_CONFIG[value]?.label || placeholder}</span>
            </div>
          ) : (
            <span className="text-muted-foreground">{placeholder}</span>
          )}
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <Command>
          <CommandInput placeholder="搜索地区..." />
          <CommandList>
            <CommandEmpty>未找到相关地区</CommandEmpty>
            {LOCATION_GROUPS.map((group) => (
              <CommandGroup key={group.label} heading={group.label}>
                {group.options.map((location) => (
                  <CommandItem
                    key={location}
                    value={location}
                    onSelect={() => handleSelect(location)}
                  >
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span>{LOCATION_CONFIG[location].label}</span>
                    </div>
                    {value === location && (
                      <Check className="ml-auto h-4 w-4 text-primary" />
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
            ))}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
