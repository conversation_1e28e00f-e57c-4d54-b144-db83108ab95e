"use client";

import { useState } from "react";
import { Search, X } from "lucide-react";

import { Input } from "@workspace/ui/components/input";
import { Badge } from "@workspace/ui/components/badge";
import { Button } from "@workspace/ui/components/button";

import { LocationSelector } from "./LocationSelector";
import { GradeSelector } from "./GradeSelector";
import { SubjectSelector } from "./SubjectSelector";
import {
  LOCATION_CONFIG,
  GRADE_CONFIG,
  SUBJECT_CONFIG,
} from "@/constants/match-constants";
import { MatchFilters } from "@/types/match.types";

interface MatchFilterProps {
  onFilterChange: (filters: MatchFilters) => void;
  initialFilters?: Partial<MatchFilters>;
  showSearch?: boolean;
  showGrade?: boolean;
  showSubject?: boolean;
  showLocation?: boolean;
  searchPlaceholder?: string;
  className?: string;
}

export function MatchFilterBar({
  onFilterChange,
  initialFilters = {},
  showSearch = true,
  showGrade = true,
  showSubject = true,
  showLocation = true,
  searchPlaceholder = "搜索...",
  className,
}: MatchFilterProps) {
  const [filters, setFilters] = useState<Partial<MatchFilters>>({
    searchQuery: initialFilters.searchQuery || "",
    grade: initialFilters.grade,
    subjects: initialFilters.subjects || [],
    location: initialFilters.location,
  });

  const handleFilterChange = <T extends keyof MatchFilters>(
    key: T,
    value: MatchFilters[T],
  ) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const clearFilter = (key: keyof MatchFilters) => {
    const newFilters = { ...filters };
    if (key === "subjects") {
      newFilters[key] = [];
    } else {
      newFilters[key] = undefined as any;
    }
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const clearSearchQuery = () => {
    handleFilterChange("searchQuery", "");
  };

  // 检查是否有任何活动的过滤器
  const hasActiveFilters =
    filters.searchQuery ||
    filters.grade ||
    (Array.isArray(filters.subjects) && filters.subjects.length > 0) ||
    filters.location;

  return (
    <div className={className}>
      {/* 筛选器 */}
      <div className="flex flex-col sm:flex-row gap-4 mb-4">
        {showSearch && (
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={searchPlaceholder}
              value={filters.searchQuery}
              onChange={(e) =>
                handleFilterChange("searchQuery", e.target.value)
              }
              className="pl-10"
            />
          </div>
        )}

        {showGrade && (
          <div className="w-full sm:w-48">
            <GradeSelector
              value={filters.grade}
              onValueChange={(value) => handleFilterChange("grade", value)}
              placeholder="选择年级"
              size="sm"
            />
          </div>
        )}

        {showSubject && (
          <div className="w-full sm:w-48">
            <SubjectSelector
              value={filters.subjects}
              onValueChange={(value) => handleFilterChange("subjects", value)}
              placeholder="选择科目"
              size="sm"
              max={3}
              showSelectedBadges={false}
            />
          </div>
        )}

        {showLocation && (
          <div className="w-full sm:w-48">
            <LocationSelector
              value={filters.location}
              onValueChange={(value) => handleFilterChange("location", value)}
              placeholder="选择地区"
              size="sm"
            />
          </div>
        )}
      </div>

      {/* 活动筛选条件 */}
      {hasActiveFilters && (
        <div className="flex flex-wrap items-center gap-2 mb-4">
          <span className="text-sm text-muted-foreground">筛选条件:</span>

          {filters.searchQuery && (
            <Badge variant="secondary" className="gap-1">
              搜索: {filters.searchQuery}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 rounded-full p-0 hover:bg-muted"
                onClick={clearSearchQuery}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {filters.grade && (
            <Badge variant="secondary" className="gap-1">
              年级: {GRADE_CONFIG[filters.grade].label}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 rounded-full p-0 hover:bg-muted"
                onClick={() => clearFilter("grade")}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {Array.isArray(filters.subjects) && filters.subjects.length > 0 && (
            <Badge variant="secondary" className="gap-1">
              科目:{" "}
              {filters.subjects.length > 1
                ? `${filters.subjects.length}个科目`
                : (SUBJECT_CONFIG as any)[filters.subjects[0] as any]?.label}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 rounded-full p-0 hover:bg-muted"
                onClick={() => clearFilter("subjects")}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {filters.location && (
            <Badge variant="secondary" className="gap-1">
              地区: {LOCATION_CONFIG[filters.location].label}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 rounded-full p-0 hover:bg-muted"
                onClick={() => clearFilter("location")}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              className="text-muted-foreground h-7 px-2 text-xs"
              onClick={() => {
                const resetFilters = {
                  searchQuery: "",
                  grade: undefined,
                  subjects: [],
                  location: undefined,
                };
                setFilters(resetFilters);
                onFilterChange(resetFilters);
              }}
            >
              清除全部
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
