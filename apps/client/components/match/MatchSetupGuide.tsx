"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Loader2, Settings } from "lucide-react";
import { apiClient } from "@/utils/api";
import { useClientUser } from "@/hooks/useClientUser";
import { toast } from "sonner";

interface MatchSetupGuideProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSetupComplete?: () => void;
}

export function MatchSetupGuide({
  open,
  onOpenChange,
  onSetupComplete,
}: MatchSetupGuideProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const { currentTenant } = useClientUser();

  const handleSetupClick = () => {
    // 跳转到匹配设置页面
    router.push("/dashboard/match/settings");
    onOpenChange(false);
  };

  const handleQuickSetup = async () => {
    setLoading(true);
    try {
      const response = await apiClient.request("/api/match/planner/setup", {
        method: "POST",
        body: JSON.stringify({
          tenantUserId: currentTenant?.id,
          // 使用默认配置
          isAcceptingMatch: true,
        }),
      });

      if (response.success) {
        toast.success(response.message || "设置成功");
        onSetupComplete?.();
        onOpenChange(false);
      } else {
        toast.error(response.message || "设置失败");
        console.error("设置失败:", response.message);
      }
    } catch (error) {
      console.error("设置失败:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[545px]">
        <DialogHeader>
          <DialogTitle>完成匹配配置</DialogTitle>
          <DialogDescription>
            您还没有设置匹配配置，需要先完成配置才能使用匹配功能。
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="text-sm text-muted-foreground">匹配配置包括：</div>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li>• 设置您的服务时间和地区偏好</li>
            <li>• 选择您擅长的学科和年级</li>
            <li>• 设定您的服务价格区间</li>
            <li>• 配置响应时间和最大服务人数</li>
          </ul>
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            稍后设置
          </Button>
          <Button
            variant="secondary"
            onClick={handleQuickSetup}
            disabled={loading}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            快速设置（使用默认配置）
          </Button>
          <Button onClick={handleSetupClick}>
            <Settings className="mr-2 h-4 w-4" />
            前往设置
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
