"use client";

import { useState } from "react";
import { 
  Star, 
  Clock, 
  MapPin, 
  Award, 
  MessageCircle, 
  Heart,
  Eye,
  TrendingUp,
  CheckCircle,
  Users,
  Zap
} from "lucide-react";

import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import { Card, CardContent, CardHeader } from "@workspace/ui/components/card";
import { Avatar, AvatarFallback, AvatarImage } from "@workspace/ui/components/avatar";
import { Progress } from "@workspace/ui/components/progress";
import { cn } from "@workspace/ui/lib/utils";

import { type MatchRecommendation, type PlannerProfile } from "@/types/match.types";

interface MatchRecommendationCardProps {
  recommendation: MatchRecommendation & {
    planner: PlannerProfile;
  };
  onViewDetails: (plannerId: string) => void;
  onStartChat: (plannerId: string) => void;
  onAddToCompare: (plannerId: string) => void;
  onToggleFavorite: (plannerId: string) => void;
  isFavorited?: boolean;
  isInComparison?: boolean;
  className?: string;
}

export function MatchRecommendationCard({
  recommendation,
  onViewDetails,
  onStartChat,
  onAddToCompare,
  onToggleFavorite,
  isFavorited = false,
  isInComparison = false,
  className,
}: MatchRecommendationCardProps) {
  const { planner, score, reasons, details } = recommendation;
  const [isExpanded, setIsExpanded] = useState(false);

  // 计算匹配度百分比
  const matchPercentage = Math.round(score * 100);
  
  // 获取匹配度颜色
  const getMatchColor = (percentage: number) => {
    if (percentage >= 90) return "text-green-600 bg-green-50";
    if (percentage >= 75) return "text-blue-600 bg-blue-50";
    if (percentage >= 60) return "text-yellow-600 bg-yellow-50";
    return "text-gray-600 bg-gray-50";
  };

  // 获取推荐理由
  const getRecommendationReasons = () => {
    const reasonsList = [];
    if (reasons.gradeMatch) reasonsList.push({ icon: CheckCircle, text: "年级匹配", color: "text-green-600" });
    if (reasons.subjectMatch) reasonsList.push({ icon: CheckCircle, text: "科目对口", color: "text-green-600" });
    if (reasons.locationMatch) reasonsList.push({ icon: MapPin, text: "地区匹配", color: "text-blue-600" });
    if (reasons.hasCapacity) reasonsList.push({ icon: Users, text: "档期充足", color: "text-purple-600" });
    if (reasons.fastResponse) reasonsList.push({ icon: Zap, text: "响应迅速", color: "text-orange-600" });
    if (reasons.experienceMatch) reasonsList.push({ icon: Award, text: "经验丰富", color: "text-indigo-600" });
    if (reasons.ratingMatch) reasonsList.push({ icon: Star, text: "评分优秀", color: "text-yellow-600" });
    return reasonsList;
  };

  const recommendationReasons = getRecommendationReasons();

  return (
    <Card className={cn(
      "overflow-hidden hover:shadow-lg transition-all duration-300 border-l-4",
      matchPercentage >= 90 ? "border-l-green-500" : 
      matchPercentage >= 75 ? "border-l-blue-500" : 
      matchPercentage >= 60 ? "border-l-yellow-500" : "border-l-gray-400",
      className
    )}>
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          {/* 规划师基本信息 */}
          <div className="flex items-center gap-4 flex-1">
            <div className="relative">
              <Avatar className="h-16 w-16 border-2 border-primary/10">
                <AvatarImage src={planner.tenantUser.user.avatar} />
                <AvatarFallback className="text-lg font-semibold">
                  {planner.tenantUser.user.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              {planner.matchProfile?.isAcceptingMatch && (
                <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
              )}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="text-lg font-semibold truncate">
                  {planner.tenantUser.user.name}
                </h3>
                {planner.title && (
                  <Badge variant="secondary" className="text-xs">
                    {planner.title}
                  </Badge>
                )}
                {planner.rating && planner.rating >= 4.5 && (
                  <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                    <Award className="h-3 w-3 mr-1" />
                    金牌
                  </Badge>
                )}
              </div>
              
              <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
                {planner.experience && (
                  <span className="flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    {planner.experience}年经验
                  </span>
                )}
                {planner.rating && (
                  <span className="flex items-center gap-1">
                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                    {planner.rating.toFixed(1)}
                  </span>
                )}
                {planner._count?.students && (
                  <span className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    已服务{planner._count.students}人
                  </span>
                )}
              </div>
              
              {planner.introduction && (
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {planner.introduction}
                </p>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center gap-2 ml-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onToggleFavorite(planner.id)}
              className={cn(
                "h-8 w-8 p-0",
                isFavorited && "text-red-500 hover:text-red-600"
              )}
            >
              <Heart className={cn("h-4 w-4", isFavorited && "fill-current")} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onViewDetails(planner.id)}
              className="h-8 w-8 p-0"
            >
              <Eye className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* 匹配度显示 */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">匹配度</span>
            <span className={cn(
              "text-lg font-bold px-2 py-1 rounded-full text-xs",
              getMatchColor(matchPercentage)
            )}>
              {matchPercentage}%
            </span>
          </div>
          <Progress 
            value={matchPercentage} 
            className="h-2"
            // className={cn(
            //   "h-2",
            //   matchPercentage >= 90 ? "[&>div]:bg-green-500" :
            //   matchPercentage >= 75 ? "[&>div]:bg-blue-500" :
            //   matchPercentage >= 60 ? "[&>div]:bg-yellow-500" : "[&>div]:bg-gray-400"
            // )}
          />
        </div>

        {/* 推荐理由 */}
        <div className="mb-4">
          <h4 className="text-sm font-medium mb-2 flex items-center gap-1">
            <CheckCircle className="h-4 w-4 text-green-600" />
            推荐理由
          </h4>
          <div className="grid grid-cols-2 gap-2">
            {recommendationReasons.slice(0, isExpanded ? recommendationReasons.length : 4).map((reason, index) => {
              const Icon = reason.icon;
              return (
                <div key={index} className="flex items-center gap-2 text-xs">
                  <Icon className={cn("h-3 w-3", reason.color)} />
                  <span className="text-muted-foreground">{reason.text}</span>
                </div>
              );
            })}
          </div>
          {recommendationReasons.length > 4 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="mt-2 h-6 text-xs"
            >
              {isExpanded ? "收起" : `查看更多 (${recommendationReasons.length - 4})`}
            </Button>
          )}
        </div>

        {/* 专业领域标签 */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-1">
            {planner.specialties.slice(0, 4).map((specialty) => (
              <Badge key={specialty} variant="outline" className="text-xs">
                {specialty}
              </Badge>
            ))}
            {planner.specialties.length > 4 && (
              <Badge variant="outline" className="text-xs">
                +{planner.specialties.length - 4}
              </Badge>
            )}
          </div>
        </div>

        {/* 服务信息 */}
        <div className="flex items-center justify-between text-sm mb-4">
          <div className="flex items-center gap-4">
            <span className="font-medium text-lg">
              面议
            </span>
            {planner.matchProfile?.responseTime && (
              <span className="flex items-center gap-1 text-muted-foreground">
                <Clock className="h-3 w-3" />
                {planner.matchProfile.responseTime}小时内响应
              </span>
            )}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex gap-2">
          <Button
            onClick={() => onViewDetails(planner.id)}
            variant="outline"
            className="flex-1"
          >
            查看详情
          </Button>
          <Button
            onClick={() => onStartChat(planner.id)}
            className="flex-1"
            disabled={!planner.matchProfile?.isAcceptingMatch}
          >
            <MessageCircle className="h-4 w-4 mr-1" />
            {planner.matchProfile?.isAcceptingMatch ? "立即沟通" : "暂不接单"}
          </Button>
          <Button
            onClick={() => onAddToCompare(planner.id)}
            variant="outline"
            size="sm"
            disabled={isInComparison}
          >
            {isInComparison ? "已对比" : "对比"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
