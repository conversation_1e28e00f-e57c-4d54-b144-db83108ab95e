"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { 
  Refresh<PERSON>w, 
  Filter, 
  SlidersHorizontal,
  TrendingUp,
  Clock,
  Target,
  Users,
  Sparkles
} from "lucide-react";

import { Button } from "@workspace/ui/components/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@workspace/ui/components/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";

import { MatchRecommendationCard } from "./MatchRecommendationCard";
import { NoRecommendations } from "@/components/match/common/MatchEmptyState";
import { StatCard } from "@/components/match/common/MatchStatus";

import { useMatchRecommendations } from "@/hooks/useMatchData";
import { type MatchRecommendation } from "@/types/match.types";

interface MatchRecommendationsPageProps {
  requestId: string;
}

export function MatchRecommendationsPage({ requestId }: MatchRecommendationsPageProps) {
  const router = useRouter();
  const [sortBy, setSortBy] = useState<"score" | "rating" | "responseTime" | "experience">("score");
  const [filterBy, setFilterBy] = useState<"all" | "available" | "premium">("all");
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [comparison, setComparison] = useState<Set<string>>(new Set());

  const {
    recommendations,
    loading,
    error,
    refresh,
  } = useMatchRecommendations(requestId);

  // 处理排序和筛选
  const processedRecommendations = recommendations
    ?.filter(rec => {
      if (filterBy === "available") {
        return rec.planner.matchProfile?.isAcceptingMatch;
      }
      if (filterBy === "premium") {
        return rec.planner.rating && rec.planner.rating >= 4.5;
      }
      return true;
    })
    ?.sort((a, b) => {
      switch (sortBy) {
        case "score":
          return b.score - a.score;
        case "rating":
          return (b.planner.rating || 0) - (a.planner.rating || 0);
        case "responseTime":
          return (a.planner.matchProfile?.responseTime || 999) - (b.planner.matchProfile?.responseTime || 999);
        case "experience":
          return (b.planner.experience || 0) - (a.planner.experience || 0);
        default:
          return 0;
      }
    }) || [];

  const handleViewDetails = (plannerId: string) => {
    router.push(`/community/match/planner/${plannerId}`);
  };

  const handleStartChat = (plannerId: string) => {
    // 实现聊天功能
    router.push(`/community/match/chat/${plannerId}?requestId=${requestId}`);
  };

  const handleToggleFavorite = (plannerId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(plannerId)) {
        newFavorites.delete(plannerId);
      } else {
        newFavorites.add(plannerId);
      }
      return newFavorites;
    });
  };

  const handleAddToCompare = (plannerId: string) => {
    if (comparison.size >= 3) {
      alert("最多只能对比3位规划师");
      return;
    }
    setComparison(prev => new Set([...prev, plannerId]));
  };

  const handleViewComparison = () => {
    const plannerIds = Array.from(comparison).join(",");
    router.push(`/community/match/compare?planners=${plannerIds}`);
  };

  // 计算统计数据
  const stats = {
    total: recommendations?.length || 0,
    highMatch: recommendations?.filter(r => r.score >= 0.8).length || 0,
    available: recommendations?.filter(r => r.planner.matchProfile?.isAcceptingMatch).length || 0,
    avgScore: recommendations?.length ? 
      Math.round((recommendations.reduce((sum, r) => sum + r.score, 0) / recommendations.length) * 100) : 0,
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {/* 加载状态的统计卡片 */}
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* 加载状态的推荐卡片 */}
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <Card key={i} className="p-6">
              <div className="flex items-start gap-4">
                <Skeleton className="h-16 w-16 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-6 w-32" />
                  <Skeleton className="h-4 w-48" />
                  <Skeleton className="h-16 w-full" />
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={refresh} variant="outline">
          重试
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Sparkles className="h-6 w-6 text-primary" />
            智能推荐
          </h1>
          <p className="text-muted-foreground">
            基于您的需求，为您推荐最合适的规划师
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {comparison.size > 0 && (
            <Button
              onClick={handleViewComparison}
              variant="outline"
              className="gap-2"
            >
              <SlidersHorizontal className="h-4 w-4" />
              对比 ({comparison.size})
            </Button>
          )}
          <Button
            onClick={refresh}
            variant="outline"
            size="sm"
            className="gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            刷新推荐
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="推荐总数"
          value={stats.total}
          description="为您找到的规划师"
          icon={Users}
          color="blue"
        />
        <StatCard
          title="高匹配度"
          value={stats.highMatch}
          description="匹配度80%以上"
          icon={Target}
          color="green"
        />
        <StatCard
          title="可接单"
          value={stats.available}
          description="当前可接受服务"
          icon={Clock}
          color="orange"
        />
        <StatCard
          title="平均匹配度"
          value={`${stats.avgScore}%`}
          description="整体匹配质量"
          icon={TrendingUp}
          color="purple"
        />
      </div>

      {/* 筛选和排序 */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">筛选:</span>
            <Select value={filterBy} onValueChange={setFilterBy}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="available">可接单</SelectItem>
                <SelectItem value="premium">金牌规划师</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">排序:</span>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="score">匹配度</SelectItem>
                <SelectItem value="rating">评分</SelectItem>
                <SelectItem value="responseTime">响应速度</SelectItem>
                <SelectItem value="experience">经验年限</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="text-sm text-muted-foreground">
          显示 {processedRecommendations.length} 个推荐结果
        </div>
      </div>

      {/* 推荐列表 */}
      {processedRecommendations.length > 0 ? (
        <div className="space-y-4">
          {processedRecommendations.map((recommendation, index) => (
            <div key={recommendation.id} className="relative">
              {index === 0 && recommendation.score >= 0.9 && (
                <Badge className="absolute -top-2 left-4 z-10 bg-gradient-to-r from-yellow-400 to-orange-500 text-white">
                  <Sparkles className="h-3 w-3 mr-1" />
                  最佳推荐
                </Badge>
              )}
              <MatchRecommendationCard
                recommendation={recommendation}
                onViewDetails={handleViewDetails}
                onStartChat={handleStartChat}
                onAddToCompare={handleAddToCompare}
                onToggleFavorite={handleToggleFavorite}
                isFavorited={favorites.has(recommendation.planner.id)}
                isInComparison={comparison.has(recommendation.planner.id)}
              />
            </div>
          ))}
        </div>
      ) : (
        <NoRecommendations
          onRefreshRecommendations={refresh}
          onBrowsePlanners={() => router.push("/community/match/planners")}
        />
      )}
    </div>
  );
}
