"use client";

import { useState } from "react";
import { 
  CheckCircle, 
  Clock, 
  AlertCircle, 
  FileText, 
  Calendar, 
  MessageCircle,
  TrendingUp,
  Target,
  Users,
  Star,
  Download,
  Upload,
  Edit
} from "lucide-react";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Progress } from "@workspace/ui/components/progress";
import { Badge } from "@workspace/ui/components/badge";
import { Button } from "@workspace/ui/components/button";
import { Avatar, AvatarFallback, AvatarImage } from "@workspace/ui/components/avatar";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@workspace/ui/components/tabs";
import { cn } from "@workspace/ui/lib/utils";

interface ServiceTask {
  id: string;
  title: string;
  description: string;
  status: "pending" | "in-progress" | "completed" | "overdue";
  dueDate?: string;
  completedDate?: string;
  estimatedHours: number;
  actualHours?: number;
  deliverables: string[];
  notes?: string;
}

interface ServiceMilestone {
  id: string;
  title: string;
  description: string;
  status: "pending" | "in-progress" | "completed";
  targetDate: string;
  completedDate?: string;
  tasks: ServiceTask[];
  progress: number;
}

interface ServiceProgressTrackerProps {
  serviceId: string;
  plannerName: string;
  plannerAvatar?: string;
  serviceTitle: string;
  startDate: string;
  endDate: string;
  totalHours: number;
  usedHours: number;
  className?: string;
}

export function ServiceProgressTracker({
  serviceId,
  plannerName,
  plannerAvatar,
  serviceTitle,
  startDate,
  endDate,
  totalHours,
  usedHours,
  className,
}: ServiceProgressTrackerProps) {
  const [activeTab, setActiveTab] = useState("overview");

  // 模拟数据
  const milestones: ServiceMilestone[] = [
    {
      id: "1",
      title: "需求分析与评估",
      description: "深入了解学生情况，制定个性化方案",
      status: "completed",
      targetDate: "2024-01-20",
      completedDate: "2024-01-18",
      progress: 100,
      tasks: [
        {
          id: "1-1",
          title: "个人兴趣测评",
          description: "通过专业测评工具了解学生兴趣倾向",
          status: "completed",
          completedDate: "2024-01-15",
          estimatedHours: 2,
          actualHours: 1.5,
          deliverables: ["兴趣测评报告", "分析总结"],
        },
        {
          id: "1-2", 
          title: "能力评估分析",
          description: "评估学生各项能力水平",
          status: "completed",
          completedDate: "2024-01-17",
          estimatedHours: 3,
          actualHours: 2.5,
          deliverables: ["能力评估报告", "优势分析"],
        },
      ],
    },
    {
      id: "2",
      title: "专业选择分析",
      description: "基于评估结果推荐合适的专业方向",
      status: "in-progress",
      targetDate: "2024-02-10",
      progress: 60,
      tasks: [
        {
          id: "2-1",
          title: "专业匹配分析",
          description: "根据兴趣和能力匹配专业",
          status: "completed",
          completedDate: "2024-01-25",
          estimatedHours: 4,
          actualHours: 3.5,
          deliverables: ["专业匹配报告"],
        },
        {
          id: "2-2",
          title: "就业前景分析",
          description: "分析各专业的就业前景和发展趋势",
          status: "in-progress",
          estimatedHours: 3,
          deliverables: ["就业前景报告", "行业分析"],
        },
      ],
    },
    {
      id: "3",
      title: "院校推荐",
      description: "推荐合适的院校和申请策略",
      status: "pending",
      targetDate: "2024-02-25",
      progress: 0,
      tasks: [
        {
          id: "3-1",
          title: "院校筛选",
          description: "根据专业和个人条件筛选院校",
          status: "pending",
          estimatedHours: 5,
          deliverables: ["院校推荐清单", "申请难度分析"],
        },
        {
          id: "3-2",
          title: "申请策略制定",
          description: "制定详细的申请时间表和策略",
          status: "pending",
          estimatedHours: 4,
          deliverables: ["申请策略方案", "时间规划表"],
        },
      ],
    },
  ];

  const overallProgress = Math.round((usedHours / totalHours) * 100);
  const completedMilestones = milestones.filter(m => m.status === "completed").length;
  const totalMilestones = milestones.length;

  const getStatusColor = (status: ServiceTask["status"]) => {
    switch (status) {
      case "completed":
        return "text-green-600 bg-green-50 border-green-200";
      case "in-progress":
        return "text-blue-600 bg-blue-50 border-blue-200";
      case "overdue":
        return "text-red-600 bg-red-50 border-red-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const getStatusIcon = (status: ServiceTask["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "in-progress":
        return <Clock className="h-4 w-4 text-blue-600" />;
      case "overdue":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN", {
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* 服务概览卡片 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Avatar className="h-12 w-12">
                <AvatarImage src={plannerAvatar} />
                <AvatarFallback>{plannerName.charAt(0)}</AvatarFallback>
              </Avatar>
              <div>
                <CardTitle className="text-lg">{serviceTitle}</CardTitle>
                <CardDescription>规划师: {plannerName}</CardDescription>
              </div>
            </div>
            <Badge variant="secondary" className="gap-1">
              <TrendingUp className="h-3 w-3" />
              进行中
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{overallProgress}%</div>
              <div className="text-sm text-muted-foreground">总体进度</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{completedMilestones}</div>
              <div className="text-sm text-muted-foreground">已完成里程碑</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{usedHours}h</div>
              <div className="text-sm text-muted-foreground">已用时间</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{totalHours - usedHours}h</div>
              <div className="text-sm text-muted-foreground">剩余时间</div>
            </div>
          </div>
          
          <div className="mt-4">
            <div className="flex items-center justify-between text-sm mb-2">
              <span>服务周期</span>
              <span className="text-muted-foreground">
                {formatDate(startDate)} - {formatDate(endDate)}
              </span>
            </div>
            <Progress value={overallProgress} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* 详细进度 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">里程碑</TabsTrigger>
          <TabsTrigger value="tasks">任务详情</TabsTrigger>
          <TabsTrigger value="files">文件资料</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {milestones.map((milestone, index) => (
            <Card key={milestone.id} className="overflow-hidden">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={cn(
                      "w-8 h-8 rounded-full flex items-center justify-center border-2",
                      milestone.status === "completed" ? "bg-green-100 border-green-300" :
                      milestone.status === "in-progress" ? "bg-blue-100 border-blue-300" :
                      "bg-gray-100 border-gray-300"
                    )}>
                      <span className="text-sm font-medium">{index + 1}</span>
                    </div>
                    <div>
                      <CardTitle className="text-base">{milestone.title}</CardTitle>
                      <CardDescription>{milestone.description}</CardDescription>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{milestone.progress}%</div>
                    <div className="text-xs text-muted-foreground">
                      目标: {formatDate(milestone.targetDate)}
                    </div>
                  </div>
                </div>
                <Progress value={milestone.progress} className="h-1" />
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="space-y-2">
                  {milestone.tasks.map((task) => (
                    <div
                      key={task.id}
                      className={cn(
                        "flex items-center justify-between p-2 rounded border",
                        getStatusColor(task.status)
                      )}
                    >
                      <div className="flex items-center gap-2">
                        {getStatusIcon(task.status)}
                        <span className="text-sm font-medium">{task.title}</span>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {task.actualHours ? `${task.actualHours}h` : `预计${task.estimatedHours}h`}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="tasks" className="space-y-4">
          {milestones.flatMap(milestone => milestone.tasks).map((task) => (
            <Card key={task.id}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(task.status)}
                    <CardTitle className="text-base">{task.title}</CardTitle>
                    <Badge variant="outline" className={getStatusColor(task.status)}>
                      {task.status === "completed" ? "已完成" :
                       task.status === "in-progress" ? "进行中" :
                       task.status === "overdue" ? "已逾期" : "待开始"}
                    </Badge>
                  </div>
                  <Button variant="ghost" size="sm">
                    <Edit className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-3">{task.description}</p>
                
                <div className="grid gap-3 sm:grid-cols-2">
                  <div>
                    <div className="text-xs font-medium text-muted-foreground mb-1">交付物</div>
                    <div className="space-y-1">
                      {task.deliverables.map((deliverable, index) => (
                        <div key={index} className="flex items-center gap-2 text-sm">
                          <FileText className="h-3 w-3 text-muted-foreground" />
                          <span>{deliverable}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-xs font-medium text-muted-foreground mb-1">时间统计</div>
                    <div className="space-y-1 text-sm">
                      <div>预计: {task.estimatedHours}小时</div>
                      {task.actualHours && <div>实际: {task.actualHours}小时</div>}
                      {task.completedDate && (
                        <div>完成: {formatDate(task.completedDate)}</div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="files" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>项目文件</span>
                <Button size="sm" className="gap-2">
                  <Upload className="h-4 w-4" />
                  上传文件
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[
                  { name: "兴趣测评报告.pdf", size: "2.3 MB", date: "2024-01-15" },
                  { name: "能力评估分析.docx", size: "1.8 MB", date: "2024-01-17" },
                  { name: "专业匹配报告.pdf", size: "3.1 MB", date: "2024-01-25" },
                ].map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <FileText className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <div className="font-medium text-sm">{file.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {file.size} • {file.date}
                        </div>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
