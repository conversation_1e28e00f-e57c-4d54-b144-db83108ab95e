"use client";

import { useState, useEffect } from "react";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@workspace/ui/components/alert";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { AlertTriangle, Building2, X } from "lucide-react";
import { useClientUser } from "@/contexts/UserContext";
import { TenantSelectionDialog } from "./tenant-selection-dialog";

interface TenantReminderProps {
  /**
   * 是否强制要求选择租户（阻塞式）
   * true: 显示模态框，必须选择租户才能继续
   * false: 显示提醒横幅，可以关闭
   */
  required?: boolean;
  /**
   * 提醒消息
   */
  message?: string;
  /**
   * 是否显示在页面顶部
   */
  banner?: boolean;
  /**
   * 自定义样式类名
   */
  className?: string;
  /**
   * 租户选择完成后的回调
   */
  onTenantSelected?: () => void;
}

export function TenantReminder({
  required = false,
  message = "当前未选择工作空间，部分功能可能无法正常使用",
  banner = true,
  className,
  onTenantSelected,
}: TenantReminderProps) {
  const { user, currentTenant, tenants } = useClientUser();
  const [showSelectionDialog, setShowSelectionDialog] = useState(false);
  const [dismissed, setDismissed] = useState(false);

  // 检查是否需要显示提醒
  const shouldShowReminder = user && !currentTenant && tenants.length > 0 && !dismissed;

  // 强制模式下，自动打开选择对话框
  useEffect(() => {
    if (required && shouldShowReminder) {
      setShowSelectionDialog(true);
    }
  }, [required, shouldShowReminder]);

  const handleSelectTenant = () => {
    setShowSelectionDialog(true);
  };

  const handleDismiss = () => {
    if (!required) {
      setDismissed(true);
    }
  };

  const handleTenantSelectionComplete = () => {
    setShowSelectionDialog(false);
    setDismissed(true);
    onTenantSelected?.();
  };

  // 如果不需要显示提醒，返回 null
  if (!shouldShowReminder) {
    return null;
  }

  // 强制模式：只显示对话框
  if (required) {
    return (
      <TenantSelectionDialog
        open={showSelectionDialog}
        onOpenChange={(open) => {
          // 强制模式下不允许关闭对话框
          if (!open && required) {
            return;
          }
          setShowSelectionDialog(open);
          if (!open) {
            handleTenantSelectionComplete();
          }
        }}
        tenants={tenants}
      />
    );
  }

  // 横幅模式
  if (banner) {
    return (
      <div className={className}>
        <Alert className="border-yellow-200 bg-yellow-50 dark:bg-yellow-950 dark:border-yellow-800">
          <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
          <AlertTitle className="text-yellow-800 dark:text-yellow-200">
            未选择工作空间
          </AlertTitle>
          <AlertDescription className="text-yellow-700 dark:text-yellow-300">
            <div className="flex items-center justify-between">
              <span>{message}</span>
              <div className="flex items-center gap-2 ml-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectTenant}
                  className="bg-white dark:bg-yellow-900 border-yellow-300 dark:border-yellow-700 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-100 dark:hover:bg-yellow-800"
                >
                  <Building2 className="h-4 w-4 mr-1" />
                  选择工作空间
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDismiss}
                  className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-200"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </AlertDescription>
        </Alert>

        <TenantSelectionDialog
          open={showSelectionDialog}
          onOpenChange={(open) => {
            setShowSelectionDialog(open);
            if (!open) {
              handleTenantSelectionComplete();
            }
          }}
          tenants={tenants}
        />
      </div>
    );
  }

  // 内联模式
  return (
    <div className={className}>
      <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4 dark:bg-yellow-950 dark:border-yellow-800">
        <div className="flex items-start">
          <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
          <div className="ml-3 flex-1">
            <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
              未选择工作空间
            </h3>
            <p className="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
              {message}
            </p>
            <div className="mt-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleSelectTenant}
                className="bg-white dark:bg-yellow-900 border-yellow-300 dark:border-yellow-700 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-100 dark:hover:bg-yellow-800"
              >
                <Building2 className="h-4 w-4 mr-1" />
                选择工作空间
              </Button>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDismiss}
            className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-200 ml-2"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <TenantSelectionDialog
        open={showSelectionDialog}
        onOpenChange={(open) => {
          setShowSelectionDialog(open);
          if (!open) {
            handleTenantSelectionComplete();
          }
        }}
        tenants={tenants}
      />
    </div>
  );
}

/**
 * 租户检查 Hook
 * 用于在组件中检查租户状态，并提供便捷的处理方法
 */
export function useTenantCheck() {
  const { user, currentTenant, tenants } = useClientUser();

  const needsTenant = user && !currentTenant && tenants.length > 0;
  const hasTenant = !!currentTenant;
  const hasMultipleTenants = tenants.length > 1;

  return {
    needsTenant,
    hasTenant,
    hasMultipleTenants,
    currentTenant,
    tenants,
  };
}

