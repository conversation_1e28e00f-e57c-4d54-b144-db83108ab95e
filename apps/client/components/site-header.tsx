"use client";
import { useState } from "react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Separator } from "@workspace/ui/components/separator";
import { SidebarTrigger } from "@workspace/ui/components/sidebar";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { unstable_ViewTransition as ViewTransition } from "react";
import { systems } from "@/config/menu";
import { useClientUser } from "@/contexts/UserContext";
import { TenantSelectionDialog } from "./tenant-selection-dialog";

export function SiteHeader() {
  const pathname = usePathname();
  // tenants.length < 1 说明不是租户，也就没有业务系统
  const { tenants } = useClientUser();
  const [showTenantDialog, setShowTenantDialog] = useState(false);

  return (
    <ViewTransition name="site-header-transition">
      <header className="flex h-(--header-height) shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-(--header-height)">
        <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mx-2 data-[orientation=vertical]:h-4"
          />
          {/* <h1 className="text-base font-medium">Documents</h1> */}
          <div className="ml-auto flex items-center gap-2">
            <div className="hidden sm:flex items-center gap-2">
              {tenants.length > 0 &&
                systems.map((sys) => {
                  // 资源系统直接跳转，业务系统需要检查租户
                  const handleClick = (e: React.MouseEvent) => {
                    if (sys.url === "/workspace" && tenants.length > 1) {
                      // 业务系统且有多个租户，弹出选择框
                      e.preventDefault();
                      setShowTenantDialog(true);
                    }
                    // 其他情况使用默认的 Link 行为
                  };

                  const IconComponent = sys.icon;

                  return (
                    <Button
                      key={sys.url}
                      variant={pathname.includes(sys.url) ? "default" : "ghost"}
                      asChild={sys.url !== "/workspace" || tenants.length <= 1}
                      size="sm"
                      onClick={
                        sys.url === "/workspace" && tenants.length > 1
                          ? handleClick
                          : undefined
                      }
                    >
                      {sys.url === "/workspace" && tenants.length > 1 ? (
                        // 多租户业务系统使用 button
                        <span className="dark:text-foreground cursor-pointer flex items-center gap-2">
                          <IconComponent className="h-4 w-4" />
                          {sys.label}
                        </span>
                      ) : (
                        // 其他情况使用 Link
                        <Link
                          href={sys.url}
                          className="dark:text-foreground flex items-center gap-2"
                        >
                          <IconComponent className="h-4 w-4" />
                          {sys.label}
                        </Link>
                      )}
                    </Button>
                  );
                })}
            </div>
          </div>
        </div>
      </header>

      {/* 租户选择弹窗 */}
      <TenantSelectionDialog
        open={showTenantDialog}
        onOpenChange={setShowTenantDialog}
        tenants={tenants}
      />
    </ViewTransition>
  );
}
