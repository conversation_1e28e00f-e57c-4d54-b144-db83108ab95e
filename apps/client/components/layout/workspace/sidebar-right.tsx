"use client";
import * as React from "react";
import { Plus } from "lucide-react";

import { NavUser } from "@/components/nav-user";
import {
  Sidebar,
  SidebarContent,
  <PERSON>barFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@workspace/ui/components/sidebar";
import EventCalendar from "@/components/event-calendar";

// TODO: This is sample data.
const data = {
  calendars: [
    {
      title: "Team Sync Meeting",
      from: "2025-06-12T09:00:00",
      to: "2025-06-12T10:00:00",
    },
    {
      title: "Design Review",
      from: "2025-06-12T11:30:00",
      to: "2025-06-12T12:30:00",
    },
    {
      title: "Client Presentation",
      from: "2025-06-12T14:00:00",
      to: "2025-06-12T15:00:00",
    },
  ],
};

export function SidebarRight({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  const [calendarsDate, setCalendarDate] = React.useState<Date | undefined>(
    new Date(2025, 5, 12),
  );

  return (
    <Sidebar
      collapsible="none"
      className="sticky top-0 hidden h-svh border-l lg:flex"
      {...props}
    >
      <SidebarHeader className="border-sidebar-border h-16 border-b">
        <NavUser />
      </SidebarHeader>
      <SidebarContent className="flex flex-col items-center py-4">
        <EventCalendar
          events={data.calendars}
          value={calendarsDate}
          onChange={setCalendarDate}
        />
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton>
              <Plus />
              <span>New Calendar</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
