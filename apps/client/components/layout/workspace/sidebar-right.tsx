"use client";
import { Plus } from "lucide-react";
import * as React from "react";

import EventCalendar from "@/components/event-calendar";
import { NavUser } from "@/components/nav-user";
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
} from "@workspace/ui/components/sidebar";

export function SidebarRight({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  const { selectedDate, setSelectedDate, getAppointmentsForDate } = useAppointments();

  // 获取选中日期的预约，转换为 EventCalendar 需要的格式
  const selectedDateAppointments = React.useMemo(() => {
    const appointments = getAppointmentsForDate(selectedDate);
    return appointments.map(appointment => ({
      title: `${appointment.studentName} - ${appointment.title}`,
      from: appointment.startTime,
      to: appointment.endTime
    }));
  }, [selectedDate, getAppointmentsForDate]);

  return (
    <Sidebar
      collapsible="none"
      className="sticky top-0 hidden h-svh border-l lg:flex"
      {...props}
    >
      <SidebarHeader className="border-sidebar-border h-16 border-b">
        <NavUser />
      </SidebarHeader>
      <SidebarContent className="flex flex-col items-center py-4">
        <EventCalendar
          events={selectedDateAppointments}
          value={selectedDate}
          onChange={(date) => date && setSelectedDate(date)}
        />
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton>
              <Plus />
              <span>New Calendar</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
