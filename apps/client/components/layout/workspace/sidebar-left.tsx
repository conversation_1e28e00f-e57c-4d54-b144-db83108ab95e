"use client";

import * as React from "react";
import { NavFavorites } from "@/components/layout/workspace/nav-favorites";
import { NavMain } from "@/components/layout/workspace/nav-main";
import { NavSecondary } from "@/components/nav-secondary";
import { NavWorkspaces } from "@/components/layout/workspace/nav-workspaces";
import { TeamSwitcher } from "@/components/team-switcher";
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarRail,
} from "@workspace/ui/components/sidebar";
import { WorkspaceMenus } from "@/config/menu";

export function SidebarLeft({
  menus,
  ...props
}: React.ComponentProps<typeof Sidebar> & { menus: typeof WorkspaceMenus }) {
  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <TeamSwitcher />
        <NavMain items={menus.navMain} />
      </SidebarHeader>
      <SidebarContent>
        <NavFavorites favorites={menus.favorites} />
        <NavWorkspaces workspaces={menus.workspaces} />
        <NavSecondary items={menus.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}
