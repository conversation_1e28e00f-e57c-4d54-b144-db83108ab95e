"use client";

import * as React from "react";

import { NavMain } from "@/components/layout/community/nav-main";
import { NavProjects } from "@/components/layout/community/nav-projects";
import { NavUser } from "@/components/nav-user";
import { TeamSwitcher } from "@/components/team-switcher";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@workspace/ui/components/sidebar";
import { CommunityMenus } from "@/config/menu";

type MenusProps = { menus: typeof CommunityMenus };
export function AppSidebar({
  menus,
  ...props
}: React.ComponentProps<typeof Sidebar> & MenusProps) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={menus.navMain} />
        <NavProjects projects={menus.projects} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
