"use client";

import { Badge } from "@workspace/ui/components/badge";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Card, CardContent } from "@workspace/ui/components/card";
import {
  addDays,
  addMonths,
  addWeeks,
  endOfMonth,
  endOfWeek,
  format,
  isSameDay,
  isSameMonth,
  isToday,
  startOfMonth,
  startOfWeek,
} from "date-fns";
import { zhCN } from "date-fns/locale";
import {
  Calendar,
  ChevronLeft,
  ChevronRight,
  Clock,
  Filter,
  MapPin,
  Phone,
  Plus,
  Video,
} from "lucide-react";
import { useMemo, useState } from "react";

export type ViewMode = "day" | "week" | "month";

export interface Appointment {
  id: string;
  title: string;
  description?: string;
  studentName: string;
  studentId: string;
  startTime: Date;
  endTime: Date;
  type: "ONLINE" | "OFFLINE" | "PHONE";
  location?: string;
  status: "CONFIRMED" | "PENDING" | "COMPLETED" | "CANCELLED";
  color?: string;
}

interface AppointmentSchedulerProps {
  appointments: Appointment[];
  onAppointmentClick: (appointment: Appointment) => void;
  onCreateAppointment: (date: Date, time?: string) => void;
  onEditAppointment: (appointment: Appointment) => void;
  onDeleteAppointment: (appointment: Appointment) => void;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "CONFIRMED":
      return "bg-green-100 text-green-800 border-green-200";
    case "PENDING":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "COMPLETED":
      return "bg-blue-100 text-blue-800 border-blue-200";
    case "CANCELLED":
      return "bg-red-100 text-red-800 border-red-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

const getTypeIcon = (type: string) => {
  switch (type) {
    case "ONLINE":
      return <Video className="w-3 h-3" />;
    case "OFFLINE":
      return <MapPin className="w-3 h-3" />;
    case "PHONE":
      return <Phone className="w-3 h-3" />;
    default:
      return <Calendar className="w-3 h-3" />;
  }
};

export function AppointmentScheduler({
  appointments,
  onAppointmentClick,
  onCreateAppointment,
  onEditAppointment,
  onDeleteAppointment,
}: AppointmentSchedulerProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<ViewMode>("week");

  // 导航函数
  const navigatePrevious = () => {
    switch (viewMode) {
      case "day":
        setCurrentDate((prev) => addDays(prev, -1));
        break;
      case "week":
        setCurrentDate((prev) => addWeeks(prev, -1));
        break;
      case "month":
        setCurrentDate((prev) => addMonths(prev, -1));
        break;
    }
  };

  const navigateNext = () => {
    switch (viewMode) {
      case "day":
        setCurrentDate((prev) => addDays(prev, 1));
        break;
      case "week":
        setCurrentDate((prev) => addWeeks(prev, 1));
        break;
      case "month":
        setCurrentDate((prev) => addMonths(prev, 1));
        break;
    }
  };

  const navigateToday = () => {
    setCurrentDate(new Date());
  };

  // 获取当前视图的日期范围
  const getDateRange = () => {
    switch (viewMode) {
      case "day":
        return { start: currentDate, end: currentDate };
      case "week":
        return {
          start: startOfWeek(currentDate, { weekStartsOn: 1 }),
          end: endOfWeek(currentDate, { weekStartsOn: 1 }),
        };
      case "month":
        return {
          start: startOfMonth(currentDate),
          end: endOfMonth(currentDate),
        };
    }
  };

  // 获取标题
  const getTitle = () => {
    switch (viewMode) {
      case "day":
        return format(currentDate, "yyyy年M月d日 EEEE", { locale: zhCN });
      case "week":
        const weekStart = startOfWeek(currentDate, { weekStartsOn: 1 });
        const weekEnd = endOfWeek(currentDate, { weekStartsOn: 1 });
        return `${format(weekStart, "M月d日", { locale: zhCN })} - ${format(weekEnd, "M月d日", { locale: zhCN })}`;
      case "month":
        return format(currentDate, "yyyy年M月", { locale: zhCN });
    }
  };

  // 筛选当前视图的预约
  const filteredAppointments = useMemo(() => {
    const { start, end } = getDateRange();
    const filtered = appointments.filter((appointment) => {
      const appointmentDate = appointment.startTime;
      // 对于日视图，只比较日期部分
      if (viewMode === "day") {
        return isSameDay(appointmentDate, currentDate);
      }
      // 对于周视图和月视图，使用日期范围比较
      return appointmentDate >= start && appointmentDate <= end;
    });

    return filtered;
  }, [appointments, currentDate, viewMode]);

  return (
    <div className="space-y-6">
      {/* 工具栏 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex items-center gap-4">
              {/* 导航按钮 */}
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={navigatePrevious}>
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="sm" onClick={navigateToday}>
                  今天
                </Button>
                <Button variant="outline" size="sm" onClick={navigateNext}>
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>

              {/* 标题 */}
              <h2 className="text-lg font-semibold">{getTitle()}</h2>
            </div>

            <div className="flex items-center gap-2">
              {/* 视图切换 */}
              <div className="flex border rounded-lg">
                <Button
                  size="sm"
                  variant={viewMode === "day" ? "default" : "ghost"}
                  onClick={() => setViewMode("day")}
                  className="rounded-r-none"
                >
                  日
                </Button>
                <Button
                  size="sm"
                  variant={viewMode === "week" ? "default" : "ghost"}
                  onClick={() => setViewMode("week")}
                  className="rounded-none"
                >
                  周
                </Button>
                <Button
                  size="sm"
                  variant={viewMode === "month" ? "default" : "ghost"}
                  onClick={() => setViewMode("month")}
                  className="rounded-l-none"
                >
                  月
                </Button>
              </div>

              {/* 操作按钮 */}
              <Button size="sm" variant="outline">
                <Filter className="w-4 h-4 mr-2" />
                筛选
              </Button>
              <Button size="sm" onClick={() => onCreateAppointment(new Date())}>
                <Plus className="w-4 h-4 mr-2" />
                新建预约
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 调度器视图 */}
      <Card>
        <CardContent className="p-0">
          {viewMode === "day" && (
            <DayView
              date={currentDate}
              appointments={filteredAppointments}
              onAppointmentClick={onAppointmentClick}
              onCreateAppointment={onCreateAppointment}
            />
          )}
          {viewMode === "week" && (
            <WeekView
              date={currentDate}
              appointments={filteredAppointments}
              onAppointmentClick={onAppointmentClick}
              onCreateAppointment={onCreateAppointment}
            />
          )}
          {viewMode === "month" && (
            <MonthView
              date={currentDate}
              appointments={filteredAppointments}
              onAppointmentClick={onAppointmentClick}
              onCreateAppointment={onCreateAppointment}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// 日视图组件
function DayView({
  date,
  appointments,
  onAppointmentClick,
  onCreateAppointment,
}: {
  date: Date;
  appointments: Appointment[];
  onAppointmentClick: (appointment: Appointment) => void;
  onCreateAppointment: (date: Date, time?: string) => void;
}) {
  const hours = Array.from({ length: 24 }, (_, i) => i);
  const dayAppointments = appointments.filter((apt) =>
    isSameDay(apt.startTime, date),
  );

  return (
    <div className="p-4">
      <div className="grid grid-cols-1 gap-2">
        {hours.map((hour) => (
          <div
            key={hour}
            className="flex items-center gap-4 min-h-[60px] border-b border-gray-100"
          >
            <div className="w-16 text-sm text-muted-foreground text-right">
              {hour.toString().padStart(2, "0")}:00
            </div>
            <div className="flex-1 relative">
              {dayAppointments
                .filter((apt) => apt.startTime.getHours() === hour)
                .map((appointment) => (
                  <div
                    key={appointment.id}
                    className="p-3 mb-1 rounded-lg border cursor-pointer hover:shadow-md transition-all duration-200 hover:scale-[1.02]"
                    style={{
                      backgroundColor: appointment.color
                        ? `${appointment.color}40`
                        : "rgba(243, 244, 246, 0.6)",
                      borderColor: appointment.color || "#e5e7eb",
                    }}
                    onClick={() => onAppointmentClick(appointment)}
                  >
                    <div className="flex items-center gap-2 mb-2">
                      {getTypeIcon(appointment.type)}
                      <span className="font-semibold text-sm">
                        {appointment.studentName}
                      </span>
                      <Badge
                        variant="outline"
                        className={`${getStatusColor(appointment.status)} text-xs`}
                      >
                        {appointment.status}
                      </Badge>
                    </div>
                    <div className="text-xs text-gray-600">
                      <div className="font-medium mb-1">
                        {appointment.title}
                      </div>
                      <div className="flex items-center gap-1 text-gray-500">
                        <Clock className="w-3 h-3" />
                        {format(appointment.startTime, "HH:mm")} -{" "}
                        {format(appointment.endTime, "HH:mm")}
                      </div>
                    </div>
                  </div>
                ))}
              {dayAppointments.filter(
                (apt) => apt.startTime.getHours() === hour,
              ).length === 0 && (
                <button
                  className="w-full h-full text-left text-xs text-muted-foreground hover:bg-muted/50 rounded transition-colors"
                  onClick={() =>
                    onCreateAppointment(
                      date,
                      `${hour.toString().padStart(2, "0")}:00`,
                    )
                  }
                >
                  点击添加预约
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// 周视图组件
function WeekView({
  date,
  appointments,
  onAppointmentClick,
  onCreateAppointment,
}: {
  date: Date;
  appointments: Appointment[];
  onAppointmentClick: (appointment: Appointment) => void;
  onCreateAppointment: (date: Date, time?: string) => void;
}) {
  const weekStart = startOfWeek(date, { weekStartsOn: 1 });
  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i));
  const hours = Array.from({ length: 12 }, (_, i) => i + 8); // 8:00 - 19:00

  return (
    <div className="p-4">
      {/* 星期标题 */}
      <div className="grid grid-cols-8 gap-2 mb-4">
        <div className="w-16"></div>
        {weekDays.map((day) => (
          <div key={day.toISOString()} className="text-center">
            <div className="text-sm font-medium">
              {format(day, "EEE", { locale: zhCN })}
            </div>
            <div
              className={`text-lg ${isToday(day) ? "bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center mx-auto" : ""}`}
            >
              {format(day, "d")}
            </div>
          </div>
        ))}
      </div>

      {/* 时间网格 */}
      <div className="space-y-1">
        {hours.map((hour) => (
          <div key={hour} className="grid grid-cols-8 gap-2 min-h-[50px]">
            <div className="w-16 text-sm text-muted-foreground text-right py-2">
              {hour.toString().padStart(2, "0")}:00
            </div>
            {weekDays.map((day) => {
              const dayAppointments = appointments.filter(
                (apt) =>
                  isSameDay(apt.startTime, day) &&
                  apt.startTime.getHours() === hour,
              );

              return (
                <div
                  key={day.toISOString()}
                  className="border border-gray-100 rounded p-1 min-h-[50px] relative"
                >
                  {dayAppointments.map((appointment) => (
                    <div
                      key={appointment.id}
                      className="text-xs p-2 rounded-md cursor-pointer hover:shadow-md transition-all duration-200 hover:scale-[1.02] mb-1 border"
                      style={{
                        backgroundColor: appointment.color
                          ? `${appointment.color}30`
                          : "rgba(243, 244, 246, 0.5)",
                        borderColor: appointment.color || "#e5e7eb",
                      }}
                      onClick={() => onAppointmentClick(appointment)}
                    >
                      <div className="font-semibold truncate">
                        {appointment.studentName}
                      </div>
                      <div className="text-gray-600 truncate text-[10px] mt-0.5">
                        {appointment.title}
                      </div>
                      <div className="text-gray-500 text-[10px] mt-0.5">
                        {format(appointment.startTime, "HH:mm")}
                      </div>
                    </div>
                  ))}
                  {dayAppointments.length === 0 && (
                    <button
                      className="w-full h-full hover:bg-muted/30 rounded transition-colors"
                      onClick={() =>
                        onCreateAppointment(
                          day,
                          `${hour.toString().padStart(2, "0")}:00`,
                        )
                      }
                    />
                  )}
                </div>
              );
            })}
          </div>
        ))}
      </div>
    </div>
  );
}

// 月视图组件
function MonthView({
  date,
  appointments,
  onAppointmentClick,
  onCreateAppointment,
}: {
  date: Date;
  appointments: Appointment[];
  onAppointmentClick: (appointment: Appointment) => void;
  onCreateAppointment: (date: Date, time?: string) => void;
}) {
  const monthStart = startOfMonth(date);
  const monthEnd = endOfMonth(date);
  const calendarStart = startOfWeek(monthStart, { weekStartsOn: 1 });
  const calendarEnd = endOfWeek(monthEnd, { weekStartsOn: 1 });

  const days = [];
  let currentDay = calendarStart;
  while (currentDay <= calendarEnd) {
    days.push(currentDay);
    currentDay = addDays(currentDay, 1);
  }

  const weeks = [];
  for (let i = 0; i < days.length; i += 7) {
    weeks.push(days.slice(i, i + 7));
  }

  return (
    <div className="p-4">
      {/* 星期标题 */}
      <div className="grid grid-cols-7 gap-2 mb-4">
        {["周一", "周二", "周三", "周四", "周五", "周六", "周日"].map((day) => (
          <div
            key={day}
            className="text-center text-sm font-medium text-muted-foreground py-2"
          >
            {day}
          </div>
        ))}
      </div>

      {/* 日期网格 */}
      <div className="space-y-2">
        {weeks.map((week, weekIndex) => (
          <div key={weekIndex} className="grid grid-cols-7 gap-2">
            {week.map((day) => {
              const dayAppointments = appointments.filter((apt) =>
                isSameDay(apt.startTime, day),
              );
              const isCurrentMonth = isSameMonth(day, date);
              const isDayToday = isToday(day);

              return (
                <div
                  key={day.toISOString()}
                  className={`min-h-[100px] border rounded-lg p-2 cursor-pointer hover:bg-muted/50 transition-colors ${
                    !isCurrentMonth ? "bg-muted/20 text-muted-foreground" : ""
                  } ${isDayToday ? "ring-2 ring-primary" : ""}`}
                  onClick={() => onCreateAppointment(day)}
                >
                  <div
                    className={`text-sm font-medium mb-1 ${isDayToday ? "text-primary" : ""}`}
                  >
                    {format(day, "d")}
                  </div>
                  <div className="space-y-1">
                    {dayAppointments.slice(0, 3).map((appointment) => (
                      <div
                        key={appointment.id}
                        className="text-xs p-1.5 rounded-md cursor-pointer hover:shadow-md transition-all duration-200 hover:scale-[1.02] border"
                        style={{
                          backgroundColor: appointment.color
                            ? `${appointment.color}25`
                            : "rgba(243, 244, 246, 0.4)",
                          borderColor: appointment.color || "#e5e7eb",
                        }}
                        onClick={(e) => {
                          e.stopPropagation();
                          onAppointmentClick(appointment);
                        }}
                      >
                        <div className="font-semibold truncate text-[11px]">
                          {appointment.studentName}
                        </div>
                        <div className="text-gray-600 truncate text-[10px] mt-0.5">
                          {format(appointment.startTime, "HH:mm")}{" "}
                          {appointment.title}
                        </div>
                      </div>
                    ))}
                    {dayAppointments.length > 3 && (
                      <div className="text-xs text-muted-foreground">
                        +{dayAppointments.length - 3} 更多
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        ))}
      </div>
    </div>
  );
}
