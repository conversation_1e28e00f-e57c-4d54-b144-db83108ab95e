"use client";

import { useState } from "react";
import {
  Heart,
  MessageCircle,
  Bookmark,
  Eye,
  Calendar,
  Share2,
  Flag,
  Edit,
  Trash2,
  ArrowLeft,
  Clock,
  Star,
  ArrowUp,
} from "lucide-react";
import Link from "next/link";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { Separator } from "@workspace/ui/components/separator";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { Post, Comment, PostType } from "@/lib/post/types";

interface PostDetailProps {
  post: Post;
  comments?: Comment[];
  isAuthor?: boolean;
  onLike?: (postId: string) => void;
  onFavorite?: (postId: string) => void;
  onShare?: (postId: string) => void;
  onEdit?: (postId: string) => void;
  onDelete?: (postId: string) => void;
  onReport?: (postId: string) => void;
  onBack?: () => void;
  className?: string;
}

// Post type badge mapping
const POST_TYPE_LABELS: Record<PostType, string> = {
  [PostType.ARTICLE]: "文章",
  [PostType.SHARE]: "分享",
  [PostType.QUESTION]: "问题",
  [PostType.ANNOUNCEMENT]: "公告",
  [PostType.EXPERIENCE]: "经验",
};

const POST_TYPE_COLORS: Record<
  PostType,
  "default" | "secondary" | "destructive" | "outline"
> = {
  [PostType.ARTICLE]: "default",
  [PostType.SHARE]: "secondary",
  [PostType.QUESTION]: "outline",
  [PostType.ANNOUNCEMENT]: "destructive",
  [PostType.EXPERIENCE]: "default",
};

export function PostDetail({
  post,
  comments = [],
  isAuthor = false,
  onLike,
  onFavorite,
  onShare,
  onEdit,
  onDelete,
  onReport,
  onBack,
  className = "",
}: PostDetailProps) {
  const [isLiked, setIsLiked] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  const [likeCount, setLikeCount] = useState(post._count?.likes || 0);
  const [favoriteCount, setFavoriteCount] = useState(
    post._count?.favorites || 0,
  );

  const handleLike = () => {
    setIsLiked(!isLiked);
    setLikeCount((prev: any) => (isLiked ? prev - 1 : prev + 1));
    onLike?.(post.id);
  };

  const handleFavorite = () => {
    setIsFavorited(!isFavorited);
    setFavoriteCount((prev: any) => (isFavorited ? prev - 1 : prev + 1));
    onFavorite?.(post.id);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("zh-CN", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(new Date(date));
  };

  const getAuthorName = () =>
    post.authorName || post.author?.name || post.author?.username;
  const getAuthorAvatar = () => post.authorAvatar || post.author?.avatar;
  const readingTime = Math.ceil(post.content.length / 500);

  return (
    <div className={`max-w-4xl mx-auto ${className}`}>
      {/* Back Button */}
      {onBack && (
        <Button variant="ghost" onClick={onBack} className="mb-4 gap-2">
          <ArrowLeft className="h-4 w-4" />
          返回列表
        </Button>
      )}

      <Card>
        {/* Header */}
        <CardHeader className="space-y-4">
          {/* Post Meta */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge variant={POST_TYPE_COLORS[post.type]}>
                {POST_TYPE_LABELS[post.type]}
              </Badge>
              {post.category && (
                <Badge variant="outline">{post.category.name}</Badge>
              )}
              {post.isTop && (
                <Badge variant="destructive" className="text-xs">
                  <ArrowUp className="h-3 w-3 mr-1" />
                  置顶
                </Badge>
              )}
              {post.isRecommended && (
                <Badge variant="default" className="text-xs">
                  <Star className="h-3 w-3 mr-1" />
                  推荐
                </Badge>
              )}
              {!post.isOriginal && (
                <Badge variant="secondary" className="text-xs">
                  转载
                </Badge>
              )}
            </div>

            {/* Actions Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  •••
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onShare?.(post.id)}>
                  <Share2 className="h-4 w-4 mr-2" />
                  分享
                </DropdownMenuItem>
                {isAuthor ? (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => onEdit?.(post.id)}>
                      <Edit className="h-4 w-4 mr-2" />
                      编辑
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => onDelete?.(post.id)}
                      className="text-red-600"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      删除
                    </DropdownMenuItem>
                  </>
                ) : (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => onReport?.(post.id)}>
                      <Flag className="h-4 w-4 mr-2" />
                      举报
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Title */}
          <CardTitle className="text-2xl md:text-3xl leading-tight">
            {post.title}
          </CardTitle>

          {/* Author Info */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={getAuthorAvatar()} alt={getAuthorName()} />
                <AvatarFallback>
                  {getAuthorName()?.charAt(0)?.toUpperCase() || "U"}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">{getAuthorName()}</p>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {formatDate(new Date(post.publishedAt || post.createdAt))}
                  </span>
                  <span className="flex items-center gap-1">
                    <Eye className="h-3 w-3" />
                    {post.viewCount} 次阅读
                  </span>
                  <span className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />约 {readingTime} 分钟阅读
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Summary */}
          {post.summary && (
            <div className="bg-muted/50 p-4 rounded-lg">
              <p className="text-muted-foreground italic">{post.summary}</p>
            </div>
          )}
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Cover Image */}
          {post.cover && (
            <div className="relative w-full h-64 md:h-96 overflow-hidden rounded-lg">
              <img
                src={post.cover}
                alt={post.title}
                className="w-full h-full object-cover"
              />
            </div>
          )}

          {/* Content */}
          <div className="prose prose-gray max-w-none dark:prose-invert">
            <div style={{ whiteSpace: "pre-wrap" }}>{post.content}</div>
          </div>

          {/* Tags */}
          {post.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 pt-4">
              {post.tags.map((postTag: any) => (
                <Link
                  key={postTag.tagId}
                  href={`/community/posts?tag=${postTag.tag.slug}`}
                >
                  <Badge
                    variant="outline"
                    className="hover:bg-muted cursor-pointer"
                  >
                    #{postTag.tag.name}
                  </Badge>
                </Link>
              ))}
            </div>
          )}

          <Separator />

          {/* Action Buttons */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {/* Like Button */}
              <Button
                variant={isLiked ? "default" : "outline"}
                size="sm"
                onClick={handleLike}
                className="gap-2"
              >
                <Heart className={`h-4 w-4 ${isLiked ? "fill-current" : ""}`} />
                点赞 {likeCount > 0 && `(${likeCount})`}
              </Button>

              {/* Favorite Button */}
              <Button
                variant={isFavorited ? "default" : "outline"}
                size="sm"
                onClick={handleFavorite}
                className="gap-2"
              >
                <Bookmark
                  className={`h-4 w-4 ${isFavorited ? "fill-current" : ""}`}
                />
                收藏 {favoriteCount > 0 && `(${favoriteCount})`}
              </Button>

              {/* Comment Count */}
              <div className="flex items-center gap-2 text-muted-foreground">
                <MessageCircle className="h-4 w-4" />
                <span>{post._count?.comments || 0} 条评论</span>
              </div>
            </div>

            {/* Share Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onShare?.(post.id)}
              className="gap-2"
            >
              <Share2 className="h-4 w-4" />
              分享
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Comments Section */}
      {comments.length > 0 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5" />
              评论 ({comments.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {comments.map((comment) => (
              <div key={comment.id} className="space-y-3">
                <div className="flex items-start gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage
                      src={comment.userAvatar}
                      alt={comment.userName}
                    />
                    <AvatarFallback>
                      {comment.userName?.charAt(0)?.toUpperCase() || "U"}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-sm">
                        {comment.userName}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {formatDate(new Date(comment.createdAt))}
                      </span>
                    </div>
                    <p className="text-sm">{comment.content}</p>
                  </div>
                </div>
                {Array.isArray(comment.replies) &&
                  comment.replies.length > 0 && (
                    <div className="ml-11 space-y-3">
                      {comment.replies.map((reply: any) => (
                        <div key={reply.id} className="flex items-start gap-3">
                          <Avatar className="h-6 w-6">
                            <AvatarImage
                              src={reply.userAvatar}
                              alt={reply.userName}
                            />
                            <AvatarFallback className="text-xs">
                              {reply.userName?.charAt(0)?.toUpperCase() || "U"}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium text-xs">
                                {reply.userName}
                              </span>
                              <span className="text-xs text-muted-foreground">
                                {formatDate(new Date(reply.createdAt))}
                              </span>
                            </div>
                            <p className="text-xs">{reply.content}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
              </div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
