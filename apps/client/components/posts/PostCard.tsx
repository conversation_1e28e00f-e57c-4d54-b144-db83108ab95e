"use client";

import {
  Heart,
  MessageCircle,
  Bookmark,
  Eye,
  Clock,
  User,
  Calendar,
  ArrowUp,
  Star,
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

import { Badge } from "@workspace/ui/components/badge";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { Post, PostStatus, PostType } from "@/lib/post/types";

interface PostCardProps {
  post: Post;
  compact?: boolean;
  showActions?: boolean;
  onLike?: (postId: string) => void;
  onFavorite?: (postId: string) => void;
  className?: string;
}

// Post type badge mapping
const POST_TYPE_LABELS: Record<PostType, string> = {
  [PostType.ARTICLE]: "文章",
  [PostType.SHARE]: "分享",
  [PostType.QUESTION]: "问题",
  [PostType.ANNOUNCEMENT]: "公告",
  [PostType.EXPERIENCE]: "经验",
};

// Post type colors
const POST_TYPE_COLORS: Record<
  PostType,
  "default" | "secondary" | "destructive" | "outline"
> = {
  [PostType.ARTICLE]: "default",
  [PostType.SHARE]: "secondary",
  [PostType.QUESTION]: "outline",
  [PostType.ANNOUNCEMENT]: "destructive",
  [PostType.EXPERIENCE]: "default",
};

// Post status labels
const POST_STATUS_LABELS: Record<PostStatus, string> = {
  [PostStatus.DRAFT]: "草稿",
  [PostStatus.PENDING_REVIEW]: "待审核",
  [PostStatus.PUBLISHED]: "已发布",
  [PostStatus.HIDDEN]: "已隐藏",
  [PostStatus.DELETED]: "已删除",
};

export function PostCard({
  post,
  compact = false,
  showActions = true,
  onLike,
  onFavorite,
  className = "",
}: PostCardProps) {
  const [isLiked, setIsLiked] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  const [likeCount, setLikeCount] = useState(post._count?.likes || 0);
  const [favoriteCount, setFavoriteCount] = useState(
    post._count?.favorites || 0,
  );

  const handleLike = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setIsLiked(!isLiked);
    setLikeCount((prev) => (isLiked ? prev - 1 : prev + 1));
    onLike?.(post.id);
  };

  const handleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setIsFavorited(!isFavorited);
    setFavoriteCount((prev) => (isFavorited ? prev - 1 : prev + 1));
    onFavorite?.(post.id);
  };

  const formatDate = (date: Date) => {
    return new Intl.RelativeTimeFormat("zh-CN", { numeric: "auto" }).format(
      Math.floor((date.getTime() - Date.now()) / (1000 * 60 * 60 * 24)),
      "day",
    );
  };

  const getAuthorName = () =>
    post.authorName || post.author?.name || post.author?.username;
  const getAuthorAvatar = () => post.authorAvatar || post.author?.avatar;

  if (compact) {
    return (
      <Link
        href={`/community/posts/${post.id}`}
        className={`block ${className}`}
      >
        <Card className="hover:shadow-sm transition-all cursor-pointer">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              {/* Author Avatar */}
              <Avatar className="h-8 w-8 flex-shrink-0">
                <AvatarImage src={getAuthorAvatar()} alt={getAuthorName()} />
                <AvatarFallback>
                  {getAuthorName()?.charAt(0)?.toUpperCase() || "U"}
                </AvatarFallback>
              </Avatar>

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <Badge
                    variant={POST_TYPE_COLORS[post.type]}
                    className="text-xs"
                  >
                    {POST_TYPE_LABELS[post.type]}
                  </Badge>
                  {post.isTop && (
                    <Badge variant="destructive" className="text-xs">
                      <ArrowUp className="h-3 w-3 mr-1" />
                      置顶
                    </Badge>
                  )}
                  {post.isRecommended && (
                    <Badge variant="default" className="text-xs">
                      <Star className="h-3 w-3 mr-1" />
                      推荐
                    </Badge>
                  )}
                </div>

                <h3 className="font-medium text-sm line-clamp-2 mb-1">
                  {post.title}
                </h3>

                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <User className="h-3 w-3" />
                    {getAuthorName()}
                  </span>
                  <span className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {formatDate(new Date(post.createdAt))}
                  </span>
                  <span className="flex items-center gap-1">
                    <Eye className="h-3 w-3" />
                    {post.viewCount}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </Link>
    );
  }

  return (
    <Link href={`/community/posts/${post.id}`} className={`block ${className}`}>
      <Card className="hover:shadow-lg transition-all cursor-pointer group">
        <CardHeader className="pb-3">
          {/* Post Cover Image */}
          {post.cover && (
            <div className="relative w-full h-48 mb-4 overflow-hidden rounded-lg">
              <img
                src={post.cover}
                alt={post.title}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              {/* Overlay badges */}
              <div className="absolute top-3 right-3 flex gap-2">
                {post.isTop && (
                  <Badge variant="destructive" className="text-xs">
                    <ArrowUp className="h-3 w-3 mr-1" />
                    置顶
                  </Badge>
                )}
                {post.isRecommended && (
                  <Badge variant="default" className="text-xs">
                    <Star className="h-3 w-3 mr-1" />
                    推荐
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* Post Type and Category */}
          <div className="flex items-center gap-2 mb-2">
            <Badge variant={POST_TYPE_COLORS[post.type]}>
              {POST_TYPE_LABELS[post.type]}
            </Badge>
            {post.category && (
              <Badge variant="outline">{post.category.name}</Badge>
            )}
            {!post.isOriginal && (
              <Badge variant="secondary" className="text-xs">
                转载
              </Badge>
            )}
          </div>

          {/* Post Title */}
          <CardTitle className="line-clamp-2 group-hover:text-primary transition-colors">
            {post.title}
          </CardTitle>

          {/* Post Summary */}
          {post.summary && (
            <CardDescription className="line-clamp-3 mt-2">
              {post.summary}
            </CardDescription>
          )}
        </CardHeader>

        <CardContent className="pt-0">
          {/* Tags */}
          {post.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-4">
              {post.tags.slice(0, 3).map((postTag) => (
                <Badge
                  key={postTag.tagId}
                  variant="outline"
                  className="text-xs"
                >
                  #{postTag.tag.name}
                </Badge>
              ))}
              {post.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{post.tags.length - 3}
                </Badge>
              )}
            </div>
          )}

          {/* Author Info */}
          <div className="flex items-center gap-3 mb-4">
            <Avatar className="h-8 w-8">
              <AvatarImage src={getAuthorAvatar()} alt={getAuthorName()} />
              <AvatarFallback>
                {getAuthorName()?.charAt(0)?.toUpperCase() || "U"}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <p className="text-sm font-medium">{getAuthorName()}</p>
              <div className="flex items-center gap-3 text-xs text-muted-foreground">
                <span className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {post.publishedAt
                    ? formatDate(new Date(post.publishedAt))
                    : formatDate(new Date(post.createdAt))}
                </span>
                <span className="flex items-center gap-1">
                  <Eye className="h-3 w-3" />
                  {post.viewCount} 次阅读
                </span>
              </div>
            </div>
          </div>
        </CardContent>

        {showActions && (
          <CardFooter className="pt-0">
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-4">
                {/* Like Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  className={`gap-1 ${isLiked ? "text-red-500" : ""}`}
                  onClick={handleLike}
                >
                  <Heart
                    className={`h-4 w-4 ${isLiked ? "fill-current" : ""}`}
                  />
                  {likeCount > 0 && likeCount}
                </Button>

                {/* Comment Count */}
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <MessageCircle className="h-4 w-4" />
                  {post._count?.comments || 0}
                </div>

                {/* Favorite Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  className={`gap-1 ${isFavorited ? "text-yellow-500" : ""}`}
                  onClick={handleFavorite}
                >
                  <Bookmark
                    className={`h-4 w-4 ${isFavorited ? "fill-current" : ""}`}
                  />
                  {favoriteCount > 0 && favoriteCount}
                </Button>
              </div>

              {/* Reading Time Estimate */}
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <Clock className="h-3 w-3" />约{" "}
                {Math.ceil(post.content.length / 500)} 分钟
              </div>
            </div>
          </CardFooter>
        )}
      </Card>
    </Link>
  );
}
