"use client";

import { useState, useEffect } from "react";
import { Search, Plus, Grid3X3, List } from "lucide-react";

import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Badge } from "@workspace/ui/components/badge";
import { Skeleton } from "@workspace/ui/components/skeleton";

import { Post, PostStatus, PostType } from "@/lib/post/types";
import { PostCard } from "./PostCard";

interface PostListProps {
  posts?: Post[];
  loading?: boolean;
  error?: string | null;
  showCreateButton?: boolean;
  showFilters?: boolean;
  showSearch?: boolean;
  compact?: boolean;
  onCreatePost?: () => void;
  onLike?: (postId: string) => void;
  onFavorite?: (postId: string) => void;
  className?: string;
}

// Post type filter options
const POST_TYPE_OPTIONS = [
  { value: "all", label: "全部类型" },
  { value: PostType.ARTICLE, label: "文章" },
  { value: PostType.SHARE, label: "分享" },
  { value: PostType.QUESTION, label: "问题" },
  { value: PostType.ANNOUNCEMENT, label: "公告" },
  { value: PostType.EXPERIENCE, label: "经验" },
];

// Sort options
const SORT_OPTIONS = [
  { value: "latest", label: "最新发布" },
  { value: "popular", label: "最受欢迎" },
  { value: "trending", label: "热门讨论" },
  { value: "most_viewed", label: "最多浏览" },
];

export function PostList({
  posts = [],
  loading = false,
  error,
  showCreateButton = true,
  showFilters = true,
  showSearch = true,
  compact = false,
  onCreatePost,
  onLike,
  onFavorite,
  className = "",
}: PostListProps) {
  const [filteredPosts, setFilteredPosts] = useState<Post[]>(posts);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedType, setSelectedType] = useState("all");
  const [sortBy, setSortBy] = useState("latest");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // Filter and sort posts
  useEffect(() => {
    let filtered = [...posts];

    // Search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(
        (post) =>
          post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          post.summary?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          post.content.toLowerCase().includes(searchQuery.toLowerCase()),
      );
    }

    // Type filter
    if (selectedType !== "all") {
      filtered = filtered.filter((post) => post.type === selectedType);
    }

    // Only show published posts
    filtered = filtered.filter((post) => post.status === PostStatus.PUBLISHED);

    // Sort posts
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "popular":
          return (b._count?.likes || 0) - (a._count?.likes || 0);
        case "trending":
          return (b._count?.comments || 0) - (a._count?.comments || 0);
        case "most_viewed":
          return b.viewCount - a.viewCount;
        case "latest":
        default:
          return (
            new Date(b.publishedAt || b.createdAt).getTime() -
            new Date(a.publishedAt || a.createdAt).getTime()
          );
      }
    });

    // Prioritize pinned and recommended posts
    filtered.sort((a, b) => {
      if (a.isTop && !b.isTop) return -1;
      if (!a.isTop && b.isTop) return 1;
      if (a.isRecommended && !b.isRecommended) return -1;
      if (!a.isRecommended && b.isRecommended) return 1;
      return 0;
    });

    setFilteredPosts(filtered);
  }, [posts, searchQuery, selectedType, sortBy]);

  if (error) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <p className="text-red-500 mb-4">{error}</p>
        <Button onClick={() => window.location.reload()}>重试</Button>
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`}>
      {/* Header */}
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">社区帖子</h2>
            <p className="text-muted-foreground">发现和分享有价值的内容</p>
          </div>
          {showCreateButton && (
            <Button onClick={onCreatePost} className="gap-2">
              <Plus className="h-4 w-4" />
              发布帖子
            </Button>
          )}
        </div>

        {/* Filters and Search */}
        {(showSearch || showFilters) && (
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            {showSearch && (
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索帖子..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            )}

            {showFilters && (
              <div className="flex gap-2">
                {/* Post Type Filter */}
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {POST_TYPE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Sort Filter */}
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {SORT_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* View Mode Toggle */}
                <div className="flex rounded-md border">
                  <Button
                    variant={viewMode === "grid" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                    className="rounded-r-none"
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                    className="rounded-l-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Active Filters */}
        {(searchQuery || selectedType !== "all") && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">筛选条件:</span>
            {searchQuery && (
              <Badge variant="secondary" className="gap-1">
                搜索: {searchQuery}
                <button
                  onClick={() => setSearchQuery("")}
                  className="ml-1 hover:bg-muted rounded-full p-0.5"
                >
                  ×
                </button>
              </Badge>
            )}
            {selectedType !== "all" && (
              <Badge variant="secondary" className="gap-1">
                类型:{" "}
                {
                  POST_TYPE_OPTIONS.find((opt) => opt.value === selectedType)
                    ?.label
                }
                <button
                  onClick={() => setSelectedType("all")}
                  className="ml-1 hover:bg-muted rounded-full p-0.5"
                >
                  ×
                </button>
              </Badge>
            )}
          </div>
        )}
      </div>

      {/* Loading State */}
      {loading && (
        <div
          className={`grid gap-6 ${
            viewMode === "grid"
              ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
              : "grid-cols-1"
          }`}
        >
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="space-y-3">
              <Skeleton className="h-48 w-full" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Posts Grid/List */}
      {!loading && (
        <>
          {filteredPosts.length > 0 ? (
            <div
              className={`grid gap-6 ${
                viewMode === "grid"
                  ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
                  : "grid-cols-1"
              }`}
            >
              {filteredPosts.map((post) => (
                <PostCard
                  key={post.id}
                  post={post}
                  compact={viewMode === "list" || compact}
                  onLike={onLike}
                  onFavorite={onFavorite}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
                <Search className="h-12 w-12 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">暂无帖子</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery || selectedType !== "all"
                  ? "没有找到符合条件的帖子，试试调整筛选条件"
                  : "还没有任何帖子，成为第一个发布内容的人吧！"}
              </p>
              {showCreateButton && (
                <Button onClick={onCreatePost} className="gap-2">
                  <Plus className="h-4 w-4" />
                  发布帖子
                </Button>
              )}
            </div>
          )}
        </>
      )}

      {/* Results Count */}
      {!loading && filteredPosts.length > 0 && (
        <div className="mt-6 text-center text-sm text-muted-foreground">
          共找到 {filteredPosts.length} 个帖子
          {posts.length !== filteredPosts.length && ` (共 ${posts.length} 个)`}
        </div>
      )}
    </div>
  );
}
