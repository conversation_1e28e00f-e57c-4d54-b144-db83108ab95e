"use client";

import { useState, useRef } from "react";
import { Save, Eye, Send, Image, X, Tag, FileText } from "lucide-react";

import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Textarea } from "@workspace/ui/components/textarea";
import { Label } from "@workspace/ui/components/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Badge } from "@workspace/ui/components/badge";
import { Switch } from "@workspace/ui/components/switch";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Separator } from "@workspace/ui/components/separator";
import { Alert, AlertDescription } from "@workspace/ui/components/alert";
import { Post, PostStatus, PostType } from "@/lib/post/types";

interface CreatePostProps {
  post?: Partial<Post & { tags: any }>;
  onSave?: (postData: CreatePostData) => Promise<void>;
  onCancel?: () => void;
  onPreview?: (postData: CreatePostData) => void;
  loading?: boolean;
  className?: string;
}

export interface CreatePostData {
  title: string;
  content: string;
  summary?: string;
  cover?: string;
  type: PostType;
  status: PostStatus;
  categoryId?: string;
  tags: string[];
  isOriginal: boolean;
  isTop?: boolean;
  isRecommended?: boolean;
}

// Post type options
const POST_TYPE_OPTIONS = [
  {
    value: PostType.ARTICLE,
    label: "📄 文章",
    description: "深度分析和见解分享",
  },
  {
    value: PostType.SHARE,
    label: "📢 分享",
    description: "有趣内容和资源分享",
  },
  { value: PostType.QUESTION, label: "❓ 问题", description: "寻求帮助和解答" },
  {
    value: PostType.EXPERIENCE,
    label: "💡 经验",
    description: "实践经验和心得",
  },
  {
    value: PostType.ANNOUNCEMENT,
    label: "📣 公告",
    description: "重要通知和公告",
  },
];

// Mock categories (in real app, fetch from API)
const CATEGORIES = [
  { id: "1", name: "规划指导" },
  { id: "2", name: "升学经验" },
  { id: "3", name: "职业发展" },
  { id: "4", name: "学习方法" },
  { id: "5", name: "生活分享" },
];

export function CreatePost({
  post,
  onSave,
  onCancel,
  onPreview,
  loading = false,
  className = "",
}: CreatePostProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Form state
  const [formData, setFormData] = useState<CreatePostData>({
    title: post?.title || "",
    content: post?.content || "",
    summary: post?.summary || "",
    cover: post?.cover || "",
    type: post?.type || PostType.ARTICLE,
    status: post?.status || PostStatus.DRAFT,
    categoryId: post?.categoryId || "",
    tags:
      post?.tags?.map((t: any) =>
        typeof t === "string" ? t : t.tag?.name || "",
      ) || [],
    isOriginal: post?.isOriginal ?? true,
    isTop: post?.isTop || false,
    isRecommended: post?.isRecommended || false,
  });

  const [tagInput, setTagInput] = useState("");
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Handle form field changes
  const handleChange = (field: keyof CreatePostData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  // Handle tag input
  const handleTagAdd = () => {
    const trimmedTag = tagInput.trim();
    if (trimmedTag && !formData.tags.includes(trimmedTag)) {
      handleChange("tags", [...formData.tags, trimmedTag]);
      setTagInput("");
    }
  };

  const handleTagRemove = (tagToRemove: string) => {
    handleChange(
      "tags",
      formData.tags.filter((tag) => tag !== tagToRemove),
    );
  };

  const handleTagInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" || e.key === ",") {
      e.preventDefault();
      handleTagAdd();
    }
  };

  // Handle cover image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // In real app, upload to cloud storage
      const reader = new FileReader();
      reader.onload = (e) => {
        handleChange("cover", e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Validation
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = "请输入帖子标题";
    } else if (formData.title.length > 100) {
      newErrors.title = "标题不能超过100个字符";
    }

    if (!formData.content.trim()) {
      newErrors.content = "请输入帖子内容";
    } else if (formData.content.length < 10) {
      newErrors.content = "内容至少需要10个字符";
    }

    if (formData.summary && formData.summary.length > 200) {
      newErrors.summary = "摘要不能超过200个字符";
    }

    if (formData.tags.length > 10) {
      newErrors.tags = "标签数量不能超过10个";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle submit
  const handleSubmit = async (status: PostStatus) => {
    if (!validateForm()) return;

    const submitData = {
      ...formData,
      status,
      summary: formData.summary || formData.content.slice(0, 100) + "...",
    };

    try {
      await onSave?.(submitData);
    } catch (error) {
      console.error("Failed to save post:", error);
    }
  };

  const handleSaveDraft = () => handleSubmit(PostStatus.DRAFT);
  const handlePublish = () => handleSubmit(PostStatus.PUBLISHED);
  const handlePreview = () => onPreview?.(formData);

  return (
    <div className={`max-w-4xl mx-auto ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {post?.id ? "编辑帖子" : "创建新帖子"}
          </CardTitle>
          <CardDescription>
            分享你的想法和经验，与社区成员互动交流
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Post Type Selection */}
          <div className="space-y-2">
            <Label>帖子类型 *</Label>
            <Select
              value={formData.type}
              onValueChange={(value: PostType) => handleChange("type", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择帖子类型" />
              </SelectTrigger>
              <SelectContent>
                {POST_TYPE_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div>
                      <div className="font-medium">{option.label}</div>
                      <div className="text-xs text-muted-foreground">
                        {option.description}
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">标题 *</Label>
            <Input
              id="title"
              placeholder="请输入吸引人的标题..."
              value={formData.title}
              onChange={(e) => handleChange("title", e.target.value)}
              className={errors.title ? "border-red-500" : ""}
            />
            {errors.title && (
              <p className="text-sm text-red-500">{errors.title}</p>
            )}
            <p className="text-xs text-muted-foreground">
              {formData.title.length}/100 字符
            </p>
          </div>

          {/* Cover Image */}
          <div className="space-y-2">
            <Label>封面图片</Label>
            {formData.cover ? (
              <div className="relative">
                <img
                  src={formData.cover}
                  alt="Cover"
                  className="w-full h-48 object-cover rounded-lg"
                />
                <Button
                  variant="destructive"
                  size="sm"
                  className="absolute top-2 right-2"
                  onClick={() => handleChange("cover", "")}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ) : (
              <div
                className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center cursor-pointer hover:border-muted-foreground/50 transition-colors"
                onClick={() => fileInputRef.current?.click()}
              >
                <Image className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-sm text-muted-foreground mb-2">
                  点击上传封面图片
                </p>
                <p className="text-xs text-muted-foreground">
                  支持 JPG、PNG 格式，建议尺寸 16:9
                </p>
              </div>
            )}
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="image/*"
              onChange={handleImageUpload}
            />
          </div>

          {/* Summary */}
          <div className="space-y-2">
            <Label htmlFor="summary">摘要</Label>
            <Textarea
              id="summary"
              placeholder="简要描述帖子内容，留空将自动生成..."
              value={formData.summary}
              onChange={(e) => handleChange("summary", e.target.value)}
              className={errors.summary ? "border-red-500" : ""}
              rows={3}
            />
            {errors.summary && (
              <p className="text-sm text-red-500">{errors.summary}</p>
            )}
            <p className="text-xs text-muted-foreground">
              {formData.summary?.length}/200 字符
            </p>
          </div>

          {/* Content */}
          <div className="space-y-2">
            <Label htmlFor="content">内容 *</Label>
            <Textarea
              id="content"
              placeholder="在这里写下你的内容..."
              value={formData.content}
              onChange={(e) => handleChange("content", e.target.value)}
              className={`min-h-64 ${errors.content ? "border-red-500" : ""}`}
            />
            {errors.content && (
              <p className="text-sm text-red-500">{errors.content}</p>
            )}
            <p className="text-xs text-muted-foreground">
              {formData.content.length} 字符
            </p>
          </div>

          {/* Category */}
          <div className="space-y-2">
            <Label>分类</Label>
            <Select
              value={formData.categoryId || ""}
              onValueChange={(value) =>
                handleChange("categoryId", value || undefined)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="选择分类（可选）" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">无分类</SelectItem>
                {CATEGORIES.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Tags */}
          <div className="space-y-2">
            <Label>标签</Label>
            <div className="flex flex-wrap gap-2 mb-2">
              {formData.tags.map((tag) => (
                <Badge key={tag} variant="secondary" className="gap-1">
                  #{tag}
                  <button
                    onClick={() => handleTagRemove(tag)}
                    className="ml-1 hover:bg-muted rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                placeholder="输入标签后按回车或逗号添加..."
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyPress={handleTagInputKeyPress}
                onBlur={handleTagAdd}
              />
              <Button
                type="button"
                variant="outline"
                onClick={handleTagAdd}
                disabled={!tagInput.trim()}
              >
                <Tag className="h-4 w-4" />
              </Button>
            </div>
            {errors.tags && (
              <p className="text-sm text-red-500">{errors.tags}</p>
            )}
            <p className="text-xs text-muted-foreground">
              {formData.tags.length}/10 个标签
            </p>
          </div>

          <Separator />

          {/* Options */}
          <div className="space-y-4">
            <h4 className="font-medium">发布选项</h4>

            <div className="flex items-center justify-between">
              <div>
                <Label>原创内容</Label>
                <p className="text-sm text-muted-foreground">
                  标记这是原创内容
                </p>
              </div>
              <Switch
                checked={formData.isOriginal}
                onCheckedChange={(checked) =>
                  handleChange("isOriginal", checked)
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label>置顶帖子</Label>
                <p className="text-sm text-muted-foreground">
                  将帖子显示在列表顶部
                </p>
              </div>
              <Switch
                checked={formData.isTop}
                onCheckedChange={(checked) => handleChange("isTop", checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label>推荐帖子</Label>
                <p className="text-sm text-muted-foreground">标记为推荐内容</p>
              </div>
              <Switch
                checked={formData.isRecommended}
                onCheckedChange={(checked) =>
                  handleChange("isRecommended", checked)
                }
              />
            </div>
          </div>

          {/* Preview Alert */}
          {Object.keys(errors).length > 0 && (
            <Alert>
              <AlertDescription>请修正以上错误后再发布</AlertDescription>
            </Alert>
          )}
        </CardContent>

        {/* Actions */}
        <div className="flex items-center justify-between p-6 pt-0">
          <Button variant="outline" onClick={onCancel}>
            取消
          </Button>

          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handlePreview} className="gap-2">
              <Eye className="h-4 w-4" />
              预览
            </Button>

            <Button
              variant="outline"
              onClick={handleSaveDraft}
              disabled={loading}
              className="gap-2"
            >
              <Save className="h-4 w-4" />
              保存草稿
            </Button>

            <Button
              onClick={handlePublish}
              disabled={loading}
              className="gap-2"
            >
              <Send className="h-4 w-4" />
              发布
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}
