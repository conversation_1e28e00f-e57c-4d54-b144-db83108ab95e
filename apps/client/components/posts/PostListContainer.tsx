"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { PostList } from "./PostList";
import { Post, PostStatus } from "@/lib/post/types";

interface PostListContainerProps {
  filter?: "latest" | "hot" | "recommended" | "following";
  categoryId?: string;
  authorId?: string;
  tags?: string[];
  className?: string;
}

export function PostListContainer({
  filter = "latest",
  categoryId,
  authorId,
  tags,
  className,
}: PostListContainerProps) {
  const router = useRouter();
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取帖子数据
  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setLoading(true);
        setError(null);

        // 构建查询参数
        const params = new URLSearchParams({
          page: "1",
          limit: "20",
          status: PostStatus.PUBLISHED,
        });

        // 添加过滤条件
        if (categoryId) params.append("categoryId", categoryId);
        if (authorId) params.append("authorId", authorId);
        if (tags) tags.forEach((tag) => params.append("tags", tag));

        // 根据过滤类型设置排序
        switch (filter) {
          case "hot":
            params.append("sortBy", "likes");
            break;
          case "recommended":
            params.append("isRecommended", "true");
            break;
          case "following":
            // TODO: 实现关注功能时补充
            params.append("sortBy", "createdAt");
            break;
          default:
            params.append("sortBy", "createdAt");
        }

        const response = await fetch(`/api/posts?${params}`);
        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.message || "获取帖子列表失败");
        }

        if (result.success) {
          setPosts(result.data.posts || []);
        } else {
          throw new Error(result.message || "获取帖子列表失败");
        }
      } catch (err) {
        console.error("获取帖子列表失败:", err);
        setError(err instanceof Error ? err.message : "获取帖子列表失败");
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, [filter, categoryId, authorId, tags]);

  // 处理创建帖子
  const handleCreatePost = () => {
    router.push("/community/posts/create");
  };

  // 处理点赞
  const handleLike = async (postId: string) => {
    try {
      // TODO: 实现用户认证后补充
      console.log("点赞帖子:", postId);

      // 临时更新UI
      setPosts((prevPosts: any) =>
        prevPosts.map((post: any) =>
          post.id === postId
            ? {
                ...post,
                _count: {
                  ...post._count,
                  likes: (post._count?.likes || 0) + 1,
                },
              }
            : post,
        ),
      );
    } catch (error) {
      console.error("点赞失败:", error);
    }
  };

  // 处理收藏
  const handleFavorite = async (postId: string) => {
    try {
      // TODO: 实现用户认证后补充
      console.log("收藏帖子:", postId);

      // 临时更新UI
      setPosts((prevPosts: any) =>
        prevPosts.map((post: any) =>
          post.id === postId
            ? {
                ...post,
                _count: {
                  ...post._count,
                  favorites: (post._count?.favorites || 0) + 1,
                },
              }
            : post,
        ),
      );
    } catch (error) {
      console.error("收藏失败:", error);
    }
  };

  return (
    <PostList
      posts={posts}
      loading={loading}
      error={error}
      onCreatePost={handleCreatePost}
      onLike={handleLike}
      onFavorite={handleFavorite}
      className={className}
    />
  );
}
