"use client";

import * as React from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { UserProvider } from "../contexts/UserContext";
import { PermissionProvider } from "../contexts/PermissionContext";
import { Toaster } from "sonner";

// 创建 QueryClient 实例
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // 数据被认为是新鲜的时间（毫秒）
      staleTime: 5 * 60 * 1000, // 5分钟
      // 缓存时间（毫秒）
      gcTime: 10 * 60 * 1000, // 10分钟 (在 v5 中 cacheTime 改名为 gcTime)
      // 失败重试次数
      retry: 3,
      // 重试延迟
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      // 窗口重新获得焦点时重新获取数据
      refetchOnWindowFocus: false,
      // 网络重新连接时重新获取数据
      refetchOnReconnect: true,
    },
    mutations: {
      // 失败重试次数
      retry: 1,
    },
  },
});

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <QueryClientProvider client={queryClient}>
      <NextThemesProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
        enableColorScheme
      >
        <UserProvider>
          <PermissionProvider>
            {children}
            <Toaster richColors />
          </PermissionProvider>
        </UserProvider>
      </NextThemesProvider>
      {/* 开发环境下显示 React Query DevTools */}
      {process.env.NODE_ENV === "development" && (
        <ReactQueryDevtools initialIsOpen={false} />
      )}
    </QueryClientProvider>
  );
}
