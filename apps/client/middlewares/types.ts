// 中间件系统类型定义
import type { NextRequest, NextResponse } from "next/server";

// 中间件上下文类型
export interface MiddlewareContext {
  request: NextRequest;
  response?: NextResponse;
  // 用户相关
  user?: {
    id: string;
    role: string;
  };
  // 性能监控
  startTime?: number;
  // 自定义数据
  [key: string]: any;
}

// 中间件函数类型
export type MiddlewareFunction = (
  context: MiddlewareContext,
) => Promise<NextResponse | void> | NextResponse | void;
