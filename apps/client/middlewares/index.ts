// 中间件组合和统一导出
import { NextResponse } from "next/server";
import type { MiddlewareFunction, MiddlewareContext } from "./types";

// 中间件组合函数
export function composeMiddleware(
  ...middlewares: MiddlewareFunction[]
): (request: Request) => Promise<NextResponse> {
  return async (request: Request): Promise<NextResponse> => {
    // 创建中间件上下文
    const context: MiddlewareContext = {
      request: request as any, // Next.js 类型兼容
      startTime: Date.now(),
    };

    try {
      // 依次执行中间件
      for (const middleware of middlewares) {
        const result = await middleware(context);

        // 如果中间件返回响应，直接返回
        if (result instanceof NextResponse) {
          return result;
        }

        // 如果上下文中有响应，使用它
        if (context.response instanceof NextResponse) {
          return context.response;
        }
      }

      // 所有中间件都通过，继续请求
      const response = context.response || NextResponse.next();

      // 添加性能指标
      const duration = Date.now() - context.startTime!;
      response.headers.set("X-Response-Time", `${duration}ms`);
      response.headers.set("X-Request-ID", `req_${context.startTime}`);

      // console.log(
      //   `[中间件完成] 耗时: ${duration}ms, 路径: ${context.request.nextUrl?.pathname}`,
      // );

      return response;
    } catch (error) {
      console.error(`[中间件错误] ${error}`);

      // 返回错误响应
      return NextResponse.redirect(
        new URL("/error", context.request.url || "http://localhost:3000"),
      );
    }
  };
}

// 条件中间件 - 根据条件决定是否执行中间件
export function conditionalMiddleware(
  condition: (context: MiddlewareContext) => boolean,
  middleware: MiddlewareFunction,
): MiddlewareFunction {
  return async (context) => {
    if (condition(context)) {
      // console.log(
      //   `[条件中间件] 条件满足，执行中间件: ${context.request.nextUrl?.pathname}`,
      // );
      return await middleware(context);
    } else {
      console.log(
        `[条件中间件] 条件不满足，跳过中间件: ${context.request.nextUrl?.pathname}`,
      );
    }
  };
}

// 错误处理中间件 - 捕获中间件执行错误
export function errorHandlerMiddleware(
  middleware: MiddlewareFunction,
): MiddlewareFunction {
  return async (context) => {
    try {
      return await middleware(context);
    } catch (error) {
      console.error(
        `[错误处理] 中间件执行失败: ${error}, 路径: ${context.request.nextUrl?.pathname}`,
      );

      // 返回错误页面
      return NextResponse.redirect(new URL("/error", context.request.url));
    }
  };
}

// 路径匹配中间件 - 只在特定路径下执行
export function pathMatcherMiddleware(
  paths: string[],
  middleware: MiddlewareFunction,
): MiddlewareFunction {
  return conditionalMiddleware((context) => {
    const pathname = context.request.nextUrl?.pathname || "";
    return paths.some((path) => {
      if (path.endsWith("*")) {
        return pathname.startsWith(path.slice(0, -1));
      }
      return pathname === path;
    });
  }, middleware);
}

// 排除路径中间件 - 排除特定路径
export function excludePathsMiddleware(
  excludePaths: string[],
  middleware: MiddlewareFunction,
): MiddlewareFunction {
  return conditionalMiddleware((context) => {
    const pathname = context.request.nextUrl?.pathname || "";
    return !excludePaths.some((path) => {
      if (path.endsWith("*")) {
        return pathname.startsWith(path.slice(0, -1));
      }
      return pathname === path;
    });
  }, middleware);
}

// 统一导出所有中间件
export * from "./logging";
export * from "./auth";
export * from "./types";
