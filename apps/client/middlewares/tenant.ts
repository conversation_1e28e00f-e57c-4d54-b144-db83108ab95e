import { NextRequest, NextResponse } from "next/server";

// 处理租户子域名请求
export function handleTenantSubdomain(
  request: NextRequest,
  subdomain: string,
  pathname: string,
) {
  const url = request.nextUrl.clone();

  // 重写到 /workspace 路径
  url.pathname = `/workspace${pathname === "/" ? "" : pathname}`;

  // console.log(
  //   `[子域名重写] ${subdomain}.domain.com${pathname} -> /workspace${pathname}`,
  // );

  // 创建重写响应并添加租户信息到请求头
  const response = NextResponse.rewrite(url);
  response.headers.set("x-tenant-subdomain", subdomain);
  response.headers.set("x-original-pathname", pathname);
  response.headers.set("x-tenant-mode", "true");

  return response;
}
