// 使用 jose 库的替代方案
import { NextResponse } from "next/server";
import type { MiddlewareFunction } from "./types";
import { jwtVerify } from "jose";
import { AUTH_CONFIG } from "@/config/auth.config";

// JWT配置
const JWT_SECRET = AUTH_CONFIG.JWT.SECRET;

// JWT载荷接口 - 与认证服务保持一致
interface JWTPayload {
  userId: string;
  email: string;
  username: string;
  tenantId?: string; // 当前租户ID（可选）
  role?: string; // 当前租户下的角色（可选）
  sessionId: string;
  type: "access" | "refresh"; // Token类型
  iat?: number;
  exp?: number;
}

/**
 * 使用 jose 库验证 JWT
 */
async function verifyJWT(token: string): Promise<{
  success: boolean;
  user?: JWTPayload;
  message?: string;
  expired?: boolean;
}> {
  try {
    const secret = new TextEncoder().encode(JWT_SECRET);
    const { payload } = await jwtVerify(token, secret);

    return {
      success: true,
      user: payload as unknown as JWTPayload,
    };
  } catch (error: any) {
    console.error("JWT验证失败:", error);

    // 检查是否是过期错误
    if (error?.code === "ERR_JWT_EXPIRED" || error?.message?.includes("exp")) {
      return {
        success: false,
        message: "Token已过期",
        expired: true,
      };
    }

    return {
      success: false,
      message: "无效的Token",
    };
  }
}

// 认证中间件 - 检查用户是否已登录
export const authMiddleware: MiddlewareFunction = async (context) => {
  const { request } = context;
  const { pathname } = request.nextUrl;

  try {
    // 1. 获取 Access Token (从 Cookie 或 Header)
    let token = request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;
    console.log(token, "0000000000000");

    // 如果 Cookie 中没有，尝试从 Authorization Header 获取
    if (!token) {
      const authHeader = request.headers.get("authorization");
      if (authHeader?.startsWith("Bearer ")) {
        token = authHeader.substring(7);
      }
    }

    if (!token) {
      // console.log(`[认证失败] 未找到 access token: ${pathname}`);
      const redirectUrl = new URL("/login", request.url);
      redirectUrl.searchParams.set("redirect", pathname);
      return NextResponse.redirect(redirectUrl);
    }

    // 2. 验证Token并获取用户信息
    const result = await verifyJWT(token);
    if (!result.success || !result.user) {
      // console.log(`[认证失败] ${result.message}: ${pathname}`);
      const redirectUrl = new URL("/login", request.url);
      redirectUrl.searchParams.set("redirect", pathname);
      return NextResponse.redirect(redirectUrl);
    }

    const user = result.user;

    // 3. 验证 Token 类型（必须是 access token）
    if (user.type !== "access") {
      // console.log(
      //   `[认证失败] 无效的 Token 类型: ${user.type}, 路径: ${pathname}`,
      // );
      const redirectUrl = new URL("/login", request.url);
      redirectUrl.searchParams.set("redirect", pathname);
      return NextResponse.redirect(redirectUrl);
    }

    // 4. 继续处理请求，添加用户信息到请求头
    // console.log(
    //   `[认证成功] 用户: ${user.userId}, 租户: ${user.tenantId || "none"}, 路径: ${pathname}`,
    // );

    const response = NextResponse.next();
    response.headers.set("x-user-id", user.userId);
    response.headers.set("x-user-email", user.email);
    response.headers.set("x-user-username", user.username);

    if (user.role) {
      response.headers.set("x-user-role", user.role);
    }

    if (user.tenantId) {
      response.headers.set("x-tenant-id", user.tenantId);
    }

    return response;
  } catch (error) {
    console.error(`[认证错误] ${error}, 路径: ${pathname}`);
    return NextResponse.redirect(new URL("/login", request.url));
  }
};
