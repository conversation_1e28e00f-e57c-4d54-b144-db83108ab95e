"use client";

import React, { createContext, useContext, useState, useCallback } from 'react';

export interface Appointment {
  id: string;
  title: string;
  description?: string;
  studentName: string;
  studentId: string;
  startTime: Date;
  endTime: Date;
  type: 'ONLINE' | 'OFFLINE' | 'PHONE';
  location?: string;
  status: 'CONFIRMED' | 'PENDING' | 'COMPLETED' | 'CANCELLED';
  color?: string;
}

interface AppointmentContextType {
  appointments: Appointment[];
  selectedDate: Date;
  setSelectedDate: (date: Date) => void;
  getAppointmentsForDate: (date: Date) => Appointment[];
  addAppointment: (appointment: Omit<Appointment, 'id'>) => void;
  updateAppointment: (id: string, appointment: Partial<Appointment>) => void;
  deleteAppointment: (id: string) => void;
}

const AppointmentContext = createContext<AppointmentContextType | undefined>(undefined);

// 生成当前日期附近的模拟数据
const generateMockAppointments = (): Appointment[] => {
  const today = new Date();
  const appointments: Appointment[] = [];
  
  // 今天的预约
  appointments.push({
    id: '1',
    title: '升学规划咨询',
    description: '讨论高考志愿填报和专业选择',
    studentName: '张小明',
    studentId: '1',
    startTime: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 14, 0),
    endTime: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 15, 30),
    type: 'ONLINE',
    location: 'https://meet.example.com/abc123',
    status: 'CONFIRMED',
    color: '#3b82f6'
  });

  // 明天的预约
  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() + 1);
  appointments.push({
    id: '2',
    title: '语言能力评估',
    description: '英语水平测试和学习计划制定',
    studentName: '李晓红',
    studentId: '2',
    startTime: new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate(), 10, 0),
    endTime: new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate(), 11, 0),
    type: 'OFFLINE',
    location: '北京市朝阳区咨询室A',
    status: 'PENDING',
    color: '#f59e0b'
  });

  // 后天的预约
  const dayAfterTomorrow = new Date(today);
  dayAfterTomorrow.setDate(today.getDate() + 2);
  appointments.push({
    id: '3',
    title: '编程学习指导',
    description: '计算机编程学习路径规划',
    studentName: '陈子豪',
    studentId: '3',
    startTime: new Date(dayAfterTomorrow.getFullYear(), dayAfterTomorrow.getMonth(), dayAfterTomorrow.getDate(), 16, 0),
    endTime: new Date(dayAfterTomorrow.getFullYear(), dayAfterTomorrow.getMonth(), dayAfterTomorrow.getDate(), 17, 0),
    type: 'PHONE',
    location: '电话咨询',
    status: 'CONFIRMED',
    color: '#10b981'
  });

  // 本周的其他预约
  const thisWeekStart = new Date(today);
  thisWeekStart.setDate(today.getDate() - today.getDay() + 1); // 本周一
  
  for (let i = 0; i < 7; i++) {
    const date = new Date(thisWeekStart);
    date.setDate(thisWeekStart.getDate() + i);
    
    // 跳过已经添加的日期
    if (i === today.getDay() - 1 || i === tomorrow.getDay() - 1 || i === dayAfterTomorrow.getDay() - 1) {
      continue;
    }
    
    // 随机添加一些预约
    if (Math.random() > 0.5) {
      appointments.push({
        id: `week-${i}`,
        title: `学习指导`,
        studentName: `学生${String.fromCharCode(65 + i)}`,
        studentId: `student-${i}`,
        startTime: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 9 + i, 0),
        endTime: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 10 + i, 0),
        type: i % 3 === 0 ? 'ONLINE' : i % 3 === 1 ? 'OFFLINE' : 'PHONE',
        location: i % 3 === 0 ? 'https://meet.example.com/room' : i % 3 === 1 ? '咨询室' : '电话',
        status: i % 2 === 0 ? 'CONFIRMED' : 'PENDING',
        color: ['#3b82f6', '#f59e0b', '#10b981', '#8b5cf6', '#ef4444', '#06b6d4', '#f97316'][i % 7]
      });
    }
  }

  // 下周的一些预约
  const nextWeek = new Date(today);
  nextWeek.setDate(today.getDate() + 7);
  appointments.push({
    id: '4',
    title: '数学辅导',
    studentName: '王小华',
    studentId: '4',
    startTime: new Date(nextWeek.getFullYear(), nextWeek.getMonth(), nextWeek.getDate(), 9, 0),
    endTime: new Date(nextWeek.getFullYear(), nextWeek.getMonth(), nextWeek.getDate(), 10, 30),
    type: 'ONLINE',
    status: 'CONFIRMED',
    color: '#8b5cf6'
  });

  return appointments;
};

export function AppointmentProvider({ children }: { children: React.ReactNode }) {
  const [appointments, setAppointments] = useState<Appointment[]>(generateMockAppointments());
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());

  const getAppointmentsForDate = useCallback((date: Date) => {
    return appointments.filter(appointment => {
      const appointmentDate = appointment.startTime;
      return appointmentDate.getFullYear() === date.getFullYear() &&
             appointmentDate.getMonth() === date.getMonth() &&
             appointmentDate.getDate() === date.getDate();
    });
  }, [appointments]);

  const addAppointment = useCallback((appointmentData: Omit<Appointment, 'id'>) => {
    const newAppointment: Appointment = {
      ...appointmentData,
      id: `appointment-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    };
    setAppointments(prev => [...prev, newAppointment]);
  }, []);

  const updateAppointment = useCallback((id: string, updates: Partial<Appointment>) => {
    setAppointments(prev => prev.map(appointment => 
      appointment.id === id ? { ...appointment, ...updates } : appointment
    ));
  }, []);

  const deleteAppointment = useCallback((id: string) => {
    setAppointments(prev => prev.filter(appointment => appointment.id !== id));
  }, []);

  const value: AppointmentContextType = {
    appointments,
    selectedDate,
    setSelectedDate,
    getAppointmentsForDate,
    addAppointment,
    updateAppointment,
    deleteAppointment
  };

  return (
    <AppointmentContext.Provider value={value}>
      {children}
    </AppointmentContext.Provider>
  );
}

export function useAppointments() {
  const context = useContext(AppointmentContext);
  if (context === undefined) {
    throw new Error('useAppointments must be used within an AppointmentProvider');
  }
  return context;
}
