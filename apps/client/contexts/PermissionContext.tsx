"use client";

import React, { createContext, useContext, useState, ReactNode } from "react";

export interface MenuItem {
  id: string;
  title: string;
  path: string;
  icon?: string;
  children?: MenuItem[];
  permission?: string; // 权限码
}

interface PermissionContextType {
  permissions: string[];
  setPermissions: (permissions: string[]) => void;
  hasPermission: (permission: string) => boolean;
  filterMenus: (menus: MenuItem[]) => MenuItem[];
}

const PermissionContext = createContext<PermissionContextType | undefined>(
  undefined
);

export function PermissionProvider({ children }: { children: ReactNode }) {
  const [permissions, setPermissions] = useState<string[]>([]);

  const hasPermission = (permission: string) => {
    return permissions.includes(permission);
  };

  const filterMenus = (menus: MenuItem[]): MenuItem[] => {
    return menus
      .filter((menu) => {
        // 如果菜单有权限要求且用户没有该权限，则过滤掉
        if (menu.permission && !hasPermission(menu.permission)) {
          return false;
        }
        return true;
      })
      .map((menu) => {
        // 递归过滤子菜单
        if (menu.children && menu.children.length > 0) {
          return {
            ...menu,
            children: filterMenus(menu.children),
          };
        }
        return menu;
      })
      .filter((menu) => {
        // 过滤掉没有子菜单的空父菜单
        if (menu.children && menu.children.length === 0) {
          return false;
        }
        return true;
      });
  };

  return (
    <PermissionContext.Provider
      value={{
        permissions,
        setPermissions,
        hasPermission,
        filterMenus,
      }}
    >
      {children}
    </PermissionContext.Provider>
  );
}

export function useClientPermission() {
  const context = useContext(PermissionContext);
  if (context === undefined) {
    throw new Error(
      "useClientPermission must be used within a PermissionProvider"
    );
  }
  return context;
}

