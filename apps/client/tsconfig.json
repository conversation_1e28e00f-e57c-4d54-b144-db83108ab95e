{"extends": "@workspace/typescript-config/nextjs.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./*"], "@workspace/ui/*": ["../../packages/ui/src/*"], "@workspace/database": ["../../packages/database/dist"], "@workspace/database/*": ["../../packages/database/dist/*"], "@workspace/ioredis": ["../../packages/ioredis/dist"], "@workspace/ioredis/*": ["../../packages/ioredis/dist/*"]}, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "next.config.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}