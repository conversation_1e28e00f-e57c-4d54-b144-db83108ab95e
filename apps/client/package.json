{"name": "client", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@tabler/icons-react": "^3.34.0", "@tanstack/react-query": "^5.83.0", "@tanstack/react-table": "^8.21.3", "@workspace/database": "workspace:*", "@workspace/ioredis": "workspace:*", "@workspace/ui": "workspace:*", "babel-plugin-react-compiler": "19.1.0-rc.2", "date-fns": "^4.1.0", "framer-motion": "^12.16.0", "jose": "^6.0.11", "little-date": "^1.0.0", "lucide-react": "^0.475.0", "next": "^15.3.3", "next-themes": "^0.4.4", "openai": "^5.5.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "sonner": "^2.0.5", "zod": "^3.24.2"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.8", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "tailwindcss": "^4.1.8", "typescript": "^5.7.3"}}