// Next.js 主中间件文件 - 使用模块化架构
import type { NextRequest } from "next/server";
import {
  composeMiddleware,
  requestLoggingMiddleware,
  authMiddleware,
  // conditionalMiddleware,
  // excludePathsMiddleware,
} from "./middlewares/index";
import { handleTenantSubdomain } from "./middlewares/tenant";
import { extractTenantFromHostname } from "./utils/common";

// 公开路径配置（不需要认证和权限检查）
const PUBLIC_PATHS = [
  "/auth",
  "/login",
  "/register",
  "/forgot-password",
  "/reset-password",
  "/api/auth", // 所有认证相关的API
  "/error",
  "/403",
  "/404",
  "/500",
];

// 静态资源路径（只需要日志记录）
const STATIC_PATHS = [
  "/_next",
  "/favicon.ico",
  "/public",
  "/icon-512x512.png", // 修复：添加前缀斜杠
  "/icon-192x192.png",
  "/apple-touch-icon.png",
  "/manifest.json",
  "/robots.txt",
];

// 检查是否为静态资源路径
function isStaticPath(pathname: string): boolean {
  // 检查特定路径
  const staticPaths = STATIC_PATHS.some((path) => pathname.startsWith(path));

  // 检查文件扩展名
  const staticExtensions = [
    ".png",
    ".jpg",
    ".jpeg",
    ".gif",
    ".svg",
    ".ico",
    ".css",
    ".js",
    ".woff",
    ".woff2",
    ".ttf",
    ".eot",
  ];
  const hasStaticExtension = staticExtensions.some((ext) =>
    pathname.toLowerCase().endsWith(ext),
  );

  return staticPaths || hasStaticExtension;
}

// 检查是否为公开路径
function isPublicPath(pathname: string): boolean {
  return PUBLIC_PATHS.some((path) => pathname.startsWith(path));
}

// 主中间件函数
export default function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const hostname = request.headers.get("host") || "";

  console.log(`\n=== 中间件开始 ===`);
  console.log(`请求路径: ${pathname}`);
  console.log(`请求方法: ${request.method}`);

  // ==================== 子域名处理 ====================

  // 检查是否为租户子域名
  const tenantSubdomain = extractTenantFromHostname(hostname);
  if (tenantSubdomain) {
    console.log(`[子域名检测] 租户: ${tenantSubdomain}`);

    // 为子域名请求重写到 /workspace
    return handleTenantSubdomain(request, tenantSubdomain, pathname);
  }

  // ==================== 主域名处理 ====================

  // 1. 静态资源路径 - 只记录日志
  if (isStaticPath(pathname)) {
    console.log(`[路由匹配] 静态资源路径: ${pathname}`);
    return composeMiddleware(requestLoggingMiddleware)(request);
  }

  // 2. 公开路径 - 包括认证API，不需要token验证
  if (isPublicPath(pathname) || pathname === "/") {
    console.log(`[路由匹配] 公开路径: ${pathname}`);
    return composeMiddleware(
      requestLoggingMiddleware,
      // 可以在这里添加其他公开路径需要的中间件
      // 比如：rateLimitMiddleware, corsMiddleware 等
    )(request);
  }

  // 3. 其他 API 路径 - API 路由自己处理认证
  if (pathname.startsWith("/api")) {
    console.log(`[路由匹配] API 路径: ${pathname}`);
    return composeMiddleware(requestLoggingMiddleware)(request);
  }

  // 4. 受保护的页面路径 - 完整的中间件链
  console.log(`[路由匹配] 受保护路径: ${pathname}`);
  return composeMiddleware(
    // 请求日志记录
    requestLoggingMiddleware,
    // 用户认证检查
    authMiddleware,
  )(request);
}

// 中间件配置 - 定义哪些路径需要经过中间件处理
export const config = {
  matcher: [
    /*
     * 匹配所有路径除了:
     * - api routes
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    // "/((?!api|_next/static|_next/image|favicon.ico|public).*)",
    "/((?!_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$|.*\\.ico$|.*\\.css$|.*\\.js$|.*\\.woff$|.*\\.woff2$|.*\\.ttf$|.*\\.eot$|manifest.json|robots.txt).*)",
  ],
};
