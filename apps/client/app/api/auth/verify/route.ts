"use server";
import { NextRequest, NextResponse } from "next/server";
import { ClientAuthService, prisma } from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

const authService = new ClientAuthService(prisma);

export async function POST(request: NextRequest) {
  try {
    // 从Cookie中获取access token (优先)，如果没有则从Authorization header获取
    let token = request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
      token = extractTokenFromHeader(authHeader)!;
    }

    if (!token) {
      return NextResponse.json(
        {
          success: false,
          message: "未找到有效的访问令牌",
        },
        { status: 401 },
      );
    }

    // 验证token
    const result = await authService.verifySession(token);

    if (result.success) {
      return NextResponse.json({
        success: true,
        user: result.user,
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          message: result.message || "令牌验证失败",
        },
        { status: 401 },
      );
    }
  } catch (error) {
    console.error("验证令牌API错误:", error);
    return NextResponse.json(
      {
        success: false,
        message: "服务器内部错误",
      },
      { status: 500 },
    );
  }
}
