import { NextRequest, NextResponse } from "next/server";
import { ClientAuthService, prisma, AUTH_CONSTANTS } from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

const authService = new ClientAuthService(prisma);

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // 从Cookie中获取access token (优先)，如果没有则从Authorization header获取
    let token = request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
      token = extractTokenFromHeader(authHeader)!;
    }

    if (!token) {
      return NextResponse.json(
        {
          success: false,
          message: "未找到有效的访问令牌",
        },
        { status: 401 },
      );
    }

    // 调用登出服务
    const result = await authService.logout(token);

    if (result.success) {
      // 登出成功，清除所有认证相关的cookies
      const response = NextResponse.json(result);
      response.cookies.delete(AUTH_CONSTANTS.COOKIES.ACCESS_TOKEN);
      response.cookies.delete(AUTH_CONSTANTS.COOKIES.REFRESH_TOKEN);
      return response;
    } else {
      return NextResponse.json(result, { status: 400 });
    }
  } catch (error) {
    console.error("登出API错误:", error);
    return NextResponse.json(
      {
        success: false,
        message: "服务器内部错误，请稍后重试",
      },
      { status: 500 },
    );
  }
}
