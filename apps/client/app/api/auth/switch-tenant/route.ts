import { NextRequest, NextResponse } from "next/server";
import { ClientAuthService, prisma, AUTH_CONSTANTS } from "@workspace/database";
import {
  AUTH_CONFIG,
  extractTokenFromHeader,
  getCookieOptions,
} from "@/config/auth.config";

const authService = new ClientAuthService(prisma);

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const { tenantId } = body;

    // 基础验证
    if (!tenantId) {
      return NextResponse.json(
        {
          success: false,
          message: "租户ID不能为空",
          errors: {
            tenantId: "请选择要切换的工作区",
          },
        },
        { status: 400 },
      );
    }

    // 从Cookie中获取access token (优先)，如果没有则从Authorization header获取
    let token = request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;
    const host = request.headers.get("host") || "";

    if (!token) {
      const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
      token = extractTokenFromHeader(authHeader)!;
    }

    if (!token) {
      return NextResponse.json(
        {
          success: false,
          message: "未找到有效的访问令牌",
        },
        { status: 401 },
      );
    }

    // 调用切换租户服务
    const result = await authService.switchTenant(token, tenantId);

    if (result.success) {
      // 切换成功，更新 access_token 和 refresh_token cookies
      const response = NextResponse.json(result);

      if (result.data?.token) {
        // 设置新的 access_token
        const accessTokenMaxAge = AUTH_CONSTANTS.TOKEN.ACCESS_EXPIRES_IN / 1000;
        response.cookies.set(
          AUTH_CONSTANTS.COOKIES.ACCESS_TOKEN,
          result.data.token,
          getCookieOptions(accessTokenMaxAge, host),
        );
      }

      if (result.data?.refreshToken) {
        // 设置新的 refresh_token
        const refreshTokenMaxAge =
          AUTH_CONSTANTS.TOKEN.REFRESH_EXPIRES_IN / 1000;
        response.cookies.set(
          AUTH_CONSTANTS.COOKIES.REFRESH_TOKEN,
          result.data.refreshToken,
          getCookieOptions(refreshTokenMaxAge, host),
        );
      }

      // 如果有自定义域名，设置重定向URL
      const tenant = result.data?.currentTenant;
      if (tenant?.customDomain) {
        // 获取当前请求的主域名
        const host = request.headers.get("host") || "";
        const protocol = request.headers.get("x-forwarded-proto") || "https";

        // 构建新的子域名URL
        const baseDomain = host.split(".").slice(-2).join("."); // 获取主域名部分
        const newUrl = `${protocol}://${tenant.customDomain}.${baseDomain}/`;

        // 在响应中添加重定向URL
        const responseData = {
          ...result,
          redirectUrl: newUrl,
        };

        return NextResponse.json(responseData);
      }

      return response;
    } else {
      return NextResponse.json(result, { status: 400 });
    }
  } catch (error) {
    console.error("切换租户API错误:", error);
    return NextResponse.json(
      {
        success: false,
        message: "服务器内部错误，请稍后重试",
      },
      { status: 500 },
    );
  }
}
