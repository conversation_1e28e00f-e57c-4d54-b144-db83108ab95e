import { NextRequest, NextResponse } from "next/server";
import { ClientAuthService, prisma, AUTH_CONSTANTS } from "@workspace/database";
import { getCookieOptions } from "@/config/auth.config";

const authService = new ClientAuthService(prisma);

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const { email, password, remember } = body;

    // 基础验证
    if (!email || !password) {
      return NextResponse.json(
        {
          success: false,
          message: "邮箱和密码不能为空",
          errors: {
            email: !email ? "请输入邮箱" : "",
            password: !password ? "请输入密码" : "",
          },
        },
        { status: 400 },
      );
    }

    // 邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        {
          success: false,
          message: "请输入有效的邮箱地址",
          errors: {
            email: "邮箱格式不正确",
          },
        },
        { status: 400 },
      );
    }

    // 获取客户端信息
    const ip =
      request.headers.get("x-forwarded-for") ||
      request.headers.get("x-real-ip") ||
      "unknown";
    const userAgent = request.headers.get("user-agent") || "unknown";
    const host = request.headers.get("host") || "";

    // 调用认证服务
    const result = await authService.login(
      { email, password, remember },
      ip,
      userAgent,
    );

    if (result.success) {
      // 登录成功，设置httpOnly cookies for both access and refresh tokens
      const response = NextResponse.json(result);

      if (result.data?.token) {
        // 设置 access_token Cookie
        const accessTokenMaxAge = AUTH_CONSTANTS.TOKEN.ACCESS_EXPIRES_IN / 1000; // 2小时
        response.cookies.set(
          AUTH_CONSTANTS.COOKIES.ACCESS_TOKEN,
          result.data.token,
          getCookieOptions(accessTokenMaxAge, host),
        );
      }

      if (result.data?.refreshToken) {
        // 设置 refresh_token Cookie
        const refreshTokenMaxAge = remember
          ? AUTH_CONSTANTS.TOKEN.REMEMBER_EXPIRES_IN / 1000 // 转换为秒
          : AUTH_CONSTANTS.TOKEN.REFRESH_EXPIRES_IN / 1000;

        response.cookies.set(
          AUTH_CONSTANTS.COOKIES.REFRESH_TOKEN,
          result.data.refreshToken,
          getCookieOptions(refreshTokenMaxAge, host),
        );
      }

      return response;
    } else {
      return NextResponse.json(result, { status: 401 });
    }
  } catch (error) {
    console.error("登录API错误:", error);
    return NextResponse.json(
      {
        success: false,
        message: "服务器内部错误，请稍后重试",
      },
      { status: 500 },
    );
  }
}
