import { NextRequest, NextResponse } from "next/server";
import { ClientAuthService, prisma, AUTH_CONSTANTS } from "@workspace/database";
import { getCookieOptions } from "@/config/auth.config";

const authService = new ClientAuthService(prisma);

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // 从cookies中获取refresh token
    const refreshToken = request.cookies.get(
      AUTH_CONSTANTS.COOKIES.REFRESH_TOKEN,
    )?.value;
    const host = request.headers.get("host") || "";

    if (!refreshToken) {
      return NextResponse.json(
        {
          success: false,
          message: "未找到刷新令牌",
        },
        { status: 401 },
      );
    }

    // 调用刷新服务
    const result = await authService.refreshToken(refreshToken);

    if (result.success) {
      // 设置新的 access_token 和 refresh_token cookies
      const response = NextResponse.json(result);

      if (result.data?.token) {
        // 设置新的 access_token
        const accessTokenMaxAge = AUTH_CONSTANTS.TOKEN.ACCESS_EXPIRES_IN / 1000;
        response.cookies.set(
          AUTH_CONSTANTS.COOKIES.ACCESS_TOKEN,
          result.data.token,
          getCookieOptions(accessTokenMaxAge, host),
        );
      }

      if (result.data?.refreshToken) {
        // 设置新的 refresh_token
        const refreshTokenMaxAge =
          AUTH_CONSTANTS.TOKEN.REFRESH_EXPIRES_IN / 1000;
        response.cookies.set(
          AUTH_CONSTANTS.COOKIES.REFRESH_TOKEN,
          result.data.refreshToken,
          getCookieOptions(refreshTokenMaxAge, host),
        );
      }

      return response;
    } else {
      // 刷新失败，清除所有认证相关的cookies
      const response = NextResponse.json(result, { status: 401 });
      response.cookies.delete(AUTH_CONSTANTS.COOKIES.ACCESS_TOKEN);
      response.cookies.delete(AUTH_CONSTANTS.COOKIES.REFRESH_TOKEN);
      return response;
    }
  } catch (error) {
    console.error("刷新令牌API错误:", error);
    return NextResponse.json(
      {
        success: false,
        message: "服务器内部错误",
      },
      { status: 500 },
    );
  }
}
