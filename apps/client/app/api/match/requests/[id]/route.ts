import { NextRequest, NextResponse } from "next/server";
import { MatchService, ClientAuthService, prisma } from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

/**
 * GET /api/match/requests/[id]
 * 获取单个匹配请求详情
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const authService = new ClientAuthService(prisma);
    const userResult = await authService.verifySession(token);

    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const matchRequest = await prisma.matchRequest.findUnique({
      where: { id },
      include: {
        tenant: true,
        matchedPlanner: {
          include: {
            tenantUser: {
              include: {
                user: true,
              },
            },
          },
        },
        responses: {
          include: {
            planner: {
              include: {
                tenantUser: {
                  include: {
                    user: true,
                  },
                },
              },
            },
          },
        },
        evaluations: true,
      },
    });

    if (!matchRequest) {
      return NextResponse.json({ error: "匹配请求不存在" }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: matchRequest,
    });
  } catch (error: any) {
    console.error("获取匹配请求详情失败:", error);
    return NextResponse.json(
      { error: error.message || "获取匹配请求详情失败" },
      { status: 500 },
    );
  }
}

/**
 * PUT /api/match/requests/[id]
 * 更新匹配请求
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const authService = new ClientAuthService(prisma);
    const userResult = await authService.verifySession(token);

    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    const { id } = await params;
    // 检查请求是否存在且用户有权限
    const existingRequest = await prisma.matchRequest.findUnique({
      where: { id },
    });

    if (!existingRequest) {
      return NextResponse.json({ error: "匹配请求不存在" }, { status: 404 });
    }

    if (existingRequest.requesterId !== userResult.user.id) {
      return NextResponse.json({ error: "无权限操作此请求" }, { status: 403 });
    }

    // 更新请求
    const updatedRequest = await prisma.matchRequest.update({
      where: { id },
      data: {
        requirements: body.requirements,
        category: body.category,
        urgency: body.urgency,
        mode: body.mode,
        expiredAt: body.expiredAt ? new Date(body.expiredAt) : undefined,
        metadata: body.metadata,
      },
      include: {
        tenant: true,
        matchedPlanner: {
          include: {
            tenantUser: {
              include: {
                user: true,
              },
            },
          },
        },
        responses: {
          include: {
            planner: {
              include: {
                tenantUser: {
                  include: {
                    user: true,
                  },
                },
              },
            },
          },
        },
        evaluations: true,
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedRequest,
    });
  } catch (error: any) {
    console.error("更新匹配请求失败:", error);
    return NextResponse.json(
      { error: error.message || "更新匹配请求失败" },
      { status: 500 },
    );
  }
}

/**
 * DELETE /api/match/requests/[id]
 * 删除匹配请求
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const authService = new ClientAuthService(prisma);
    const userResult = await authService.verifySession(token);

    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    // 检查请求是否存在且用户有权限
    const existingRequest = await prisma.matchRequest.findUnique({
      where: { id },
    });

    if (!existingRequest) {
      return NextResponse.json({ error: "匹配请求不存在" }, { status: 404 });
    }

    if (existingRequest.requesterId !== userResult.user.id) {
      return NextResponse.json({ error: "无权限操作此请求" }, { status: 403 });
    }

    // 取消匹配请求
    const matchService = new MatchService(prisma);
    const result = await matchService.cancelMatchRequest(
      id,
      userResult.user.id,
    );

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    console.error("删除匹配请求失败:", error);
    return NextResponse.json(
      { error: error.message || "删除匹配请求失败" },
      { status: 500 },
    );
  }
}
