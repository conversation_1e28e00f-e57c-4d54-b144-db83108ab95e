import { NextRequest, NextResponse } from "next/server";
import { MatchService, ClientAuthService, prisma } from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

/**
 * GET /api/match/planners/[id]
 * 获取单个规划师详情
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const authService = new ClientAuthService(prisma);
    const userResult = await authService.verifySession(token);

    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: plannerId } = await params;

    if (!plannerId) {
      return NextResponse.json(
        {
          success: false,
          error: "规划师ID不能为空",
        },
        { status: 400 },
      );
    }

    const matchService = new MatchService(prisma);
    const planner = await matchService.getPlannerDetail(plannerId);

    if (!planner) {
      return NextResponse.json(
        {
          success: false,
          error: "规划师不存在",
        },
        { status: 404 },
      );
    }

    return NextResponse.json({
      success: true,
      data: planner,
    });
  } catch (error: any) {
    console.error("获取规划师详情失败:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "获取规划师详情失败",
      },
      { status: 500 },
    );
  }
}
