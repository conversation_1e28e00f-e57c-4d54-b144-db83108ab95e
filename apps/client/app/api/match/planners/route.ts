import { NextRequest, NextResponse } from "next/server";
import { MatchService, ClientAuthService, prisma } from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

/**
 * GET /api/match/planners
 * 获取规划师列表
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const authService = new ClientAuthService(prisma);
    const userResult = await authService.verifySession(token);
    
    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    
    // 分页参数
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    
    // 筛选参数
    const searchQuery = searchParams.get("searchQuery") || "";
    const subjects = searchParams.getAll("subjects");
    const location = searchParams.get("location") || "";
    const gradeLevel = searchParams.get("gradeLevel") || "";
    const minRating = searchParams.get("minRating") 
      ? parseFloat(searchParams.get("minRating")!) 
      : undefined;
    const maxPrice = searchParams.get("maxPrice") 
      ? parseFloat(searchParams.get("maxPrice")!) 
      : undefined;
    const availability = searchParams.get("availability") || "";
    
    // 排序参数
    const sortBy = searchParams.get("sortBy") || "rating";
    const sortOrder = searchParams.get("sortOrder") === "asc" ? "asc" : "desc";

    const matchService = new MatchService(prisma);
    const result = await matchService.getPlannersList({
      page,
      pageSize,
      searchQuery,
      subjects,
      location,
      gradeLevel,
      minRating,
      maxPrice,
      availability,
      sortBy,
      sortOrder,
    });

    return NextResponse.json({
      success: true,
      data: {
        items: result.items,
        totalCount: result.totalCount,
        totalPages: result.totalPages,
        currentPage: page,
        pageSize: pageSize,
      },
    });
  } catch (error: any) {
    console.error("获取规划师列表失败:", error);
    return NextResponse.json(
      { 
        success: false,
        error: error.message || "获取规划师列表失败" 
      },
      { status: 500 }
    );
  }
}

