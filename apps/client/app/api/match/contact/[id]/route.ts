import { NextRequest, NextResponse } from "next/server";
import { ClientAuthService, prisma } from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

/**
 * POST /api/match/contact/[id]
 * 联系指定规划师
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const authService = new ClientAuthService(prisma);
    const userResult = await authService.verifySession(token);
    
    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const plannerId = params.id;
    const { message, contactMethod } = await request.json();
    
    if (!plannerId) {
      return NextResponse.json(
        { 
          success: false,
          error: "规划师ID不能为空" 
        },
        { status: 400 }
      );
    }

    if (!message || !message.trim()) {
      return NextResponse.json(
        { 
          success: false,
          error: "消息内容不能为空" 
        },
        { status: 400 }
      );
    }

    // 检查规划师是否存在
    const planner = await prisma.planner.findUnique({
      where: { id: plannerId },
      include: {
        tenantUser: {
          include: {
            user: true,
          },
        },
      },
    });

    if (!planner) {
      return NextResponse.json(
        { 
          success: false,
          error: "规划师不存在" 
        },
        { status: 404 }
      );
    }

    // 获取发送者的租户信息
    const senderTenantUser = await prisma.tenantUser.findFirst({
      where: { userId: userResult.user.id },
      include: {
        tenant: true,
        user: true,
      },
    });

    // 创建通知给规划师（使用现有的通知系统）
    const notification = await prisma.notification.create({
      data: {
        userId: planner.tenantUser.userId,
        type: "COMMENT", // 使用现有的通知类型
        title: `收到来自 ${userResult.user.name} 的消息`,
        content: `${message.trim()}${contactMethod ? `\n\n联系方式：${contactMethod.trim()}` : ''}`,
        relatedId: plannerId,
        relatedType: "PLANNER_CONTACT",
        isRead: false,
      },
    });

    // TODO: 这里可以集成消息推送服务，通知规划师有新消息
    // 例如：微信消息、邮件通知、站内信等

    return NextResponse.json({
      success: true,
      message: "消息已发送，规划师将会尽快回复",
      data: {
        notificationId: notification.id,
        plannerName: planner.tenantUser?.user.name,
        senderName: userResult.user.name,
        sentAt: notification.createdAt,
      },
    });
  } catch (error: any) {
    console.error("发送消息失败:", error);
    return NextResponse.json(
      { 
        success: false,
        error: error.message || "发送消息失败" 
      },
      { status: 500 }
    );
  }
}

