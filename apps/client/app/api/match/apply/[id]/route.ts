import { NextRequest, NextResponse } from "next/server";
import { MatchService, ClientAuthService, prisma } from "@workspace/database";
import { RequirementCategory, MatchMode } from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

const MatchRequestType = {
  STUDENT: "STUDENT_TO_PLANNER", // 学生找规划师
  PARENT: "PARENT_TO_PLANNER", // 家长找规划师
  PLANNER: "PLANNER_TO_PLANNER", // 规划师找规划师（转介）
  COMMUNITY_USER: "PLANNER_TO_PLANNER", // 机构批量匹配
  TENANT_ADMIN: "INSTITUTION_BATCH", // 机构批量匹配
  GUEST: "STUDENT_TO_PLANNER", // 游客找规划师
};
/**
 * POST /api/match/apply/[id]
 * 向指定规划师申请匹配
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const authService = new ClientAuthService(prisma);
    const userResult = await authService.verifySession(token);

    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: plannerId } = await params;
    const { message, contactInfo, category } = await request.json();

    if (!plannerId) {
      return NextResponse.json(
        {
          success: false,
          error: "规划师ID不能为空",
        },
        { status: 400 },
      );
    }

    if (!message || !message.trim()) {
      return NextResponse.json(
        {
          success: false,
          error: "申请说明不能为空",
        },
        { status: 400 },
      );
    }

    // 检查规划师是否存在且接受匹配
    const planner = await prisma.planner.findUnique({
      where: { id: plannerId },
      include: {
        matchProfile: true,
        tenantUser: {
          include: {
            user: true,
          },
        },
      },
    });

    if (!planner) {
      return NextResponse.json(
        {
          success: false,
          error: "规划师不存在",
        },
        { status: 404 },
      );
    }

    if (!planner.matchProfile?.isAcceptingMatch) {
      return NextResponse.json(
        {
          success: false,
          error: "该规划师暂不接受新的匹配申请",
        },
        { status: 400 },
      );
    }

    // 获取用户的租户信息
    const userTenantUser = await prisma.tenantUser.findFirst({
      where: { userId: userResult.user.id },
      include: {
        tenant: true,
      },
    });

    if (!userTenantUser) {
      return NextResponse.json(
        {
          success: false,
          error: "用户未绑定租户",
        },
        { status: 400 },
      );
    }

    const requesterType =
      MatchRequestType[userTenantUser?.role as keyof typeof MatchRequestType] ??
      MatchRequestType.STUDENT;
    // 创建匹配申请记录（这里可以扩展为专门的申请表）
    // 暂时使用 matchRequest 作为申请记录
    const matchService = new MatchService(prisma);
    const matchRequest = await matchService.createMatchRequest({
      requesterId: userResult.user.id,
      requesterType: requesterType as any,
      tenantId: userTenantUser.tenantId,
      requirements: {
        title: `向 ${planner.tenantUser?.user.name} 申请匹配`,
        description: message,
        preferredPlanner: plannerId,
        contactInfo: contactInfo,
        appliedAt: new Date(),
      },
      category: category ?? RequirementCategory.CAREER_PLANNING,
      urgency: 3, // 中等紧急程度
      mode: MatchMode.DIRECT_ASSIGN,
    });

    // 发送通知给规划师（这里可以集成消息推送服务）
    // TODO: 实现消息推送

    return NextResponse.json({
      success: true,
      message: "申请已提交，规划师将会尽快回复",
      data: {
        requestId: matchRequest.id,
        plannerName: planner.tenantUser?.user.name,
        estimatedResponseTime: planner.matchProfile?.responseTime || 24,
      },
    });
  } catch (error: any) {
    console.error("申请匹配失败:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "申请匹配失败",
      },
      { status: 500 },
    );
  }
}
