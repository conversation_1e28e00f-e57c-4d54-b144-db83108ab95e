import { NextRequest, NextResponse } from "next/server";
import { MatchService, ClientAuthService, prisma } from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

/**
 * POST /api/match/pool/[id]/respond
 * 响应公开抢单
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const authService = new ClientAuthService(prisma);
    const userResult = await authService.verifySession(token);
    
    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const requestId = params.id;
    const body = await request.json();
    const { message, plannerId } = body;

    if (!message || !plannerId) {
      return NextResponse.json(
        { error: "缺少必要参数" },
        { status: 400 }
      );
    }

    // 验证当前用户是否是规划师
    if (userResult.user.id !== plannerId) {
      return NextResponse.json(
        { error: "无权限操作" },
        { status: 403 }
      );
    }

    const matchService = new MatchService(prisma);
    const result = await matchService.respondToPoolRequest({
      requestId,
      plannerId,
      message,
      responderId: userResult.user.id
    });

    if (!result.success) {
      return NextResponse.json(
        { error: result.message },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      message: "响应成功"
    });
  } catch (error: any) {
    console.error("响应抢单失败:", error);
    return NextResponse.json(
      { error: error.message || "响应抢单失败" },
      { status: 500 }
    );
  }
}

