import { NextRequest, NextResponse } from "next/server";
import { MatchService, prisma } from "@qyqm/database";
import { authWithPermission } from "@/lib/auth";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await authWithPermission(['MATCH_VIEW']);
    
    if (!user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const plannerId = params.id;

    if (!plannerId) {
      return NextResponse.json(
        { message: "规划师ID是必需的" },
        { status: 400 }
      );
    }

    const matchService = new MatchService(prisma);
    const performance = await matchService.getPlannerPerformanceByCategory(plannerId);

    return NextResponse.json({
      success: true,
      data: performance,
    });
  } catch (error) {
    console.error("获取规划师分类表现失败:", error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : "获取规划师分类表现失败" 
      },
      { status: 500 }
    );
  }
}

