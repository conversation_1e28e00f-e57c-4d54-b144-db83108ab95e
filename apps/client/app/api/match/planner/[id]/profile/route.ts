import { NextRequest, NextResponse } from "next/server";
import { MatchService, ClientAuthService, prisma } from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

/**
 * GET /api/match/planner/[id]/profile
 * 获取规划师匹配配置
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const authService = new ClientAuthService(prisma);
    const userResult = await authService.verifySession(token);

    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const matchService = new MatchService(prisma);
    const { id } = await params;
    const profile = await matchService.getPlannerMatchProfile(id);

    return NextResponse.json({
      success: true,
      data: profile,
    });
  } catch (error: any) {
    console.error("获取规划师配置失败:", error);

    // 如果是规划师不存在的错误，返回404
    if (error.message && error.message.includes("规划师不存在")) {
      return NextResponse.json({ error: error.message }, { status: 404 });
    }

    return NextResponse.json(
      { error: error.message || "获取规划师配置失败" },
      { status: 500 },
    );
  }
}

/**
 * PUT /api/match/planner/[id]/profile
 * 更新规划师匹配配置
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const authService = new ClientAuthService(prisma);
    const userResult = await authService.verifySession(token);

    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // TODO: 验证当前用户是否有权限修改该规划师的配置

    const matchService = new MatchService(prisma);
    const { id } = await params;
    const profile = await matchService.updatePlannerMatchProfile(id, {
      isAcceptingMatch: body.isAcceptingMatch,
      maxConcurrent: body.maxConcurrent,
      preferredGrades: body.preferredGrades,
      preferredSubjects: body.preferredSubjects,
      preferredLocations: body.preferredLocations,
      expertise: body.expertise,
      availability: body.availability,
      responseTime: body.responseTime,
      basePrice: body.basePrice,
      priceRange: body.priceRange,
    });

    return NextResponse.json({
      success: true,
      data: profile,
    });
  } catch (error: any) {
    console.error("更新规划师配置失败:", error);

    // 如果是规划师不存在的错误，返回404
    if (error.message && error.message.includes("规划师不存在")) {
      return NextResponse.json({ error: error.message }, { status: 404 });
    }

    return NextResponse.json(
      { error: error.message || "更新规划师配置失败" },
      { status: 500 },
    );
  }
}
