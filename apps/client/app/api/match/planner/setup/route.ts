import { NextRequest, NextResponse } from "next/server";
import { ClientAuthService, MatchService, prisma } from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

const authService = new ClientAuthService(prisma);
const matchService = new MatchService(prisma);

export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, message: "未登录" },
        { status: 401 },
      );
    }

    const userResult = await authService.verifySession(token);

    if (!userResult.success || !userResult.user) {
      return NextResponse.json(
        { success: false, message: "未登录" },
        { status: 401 },
      );
    }

    // 从body或查询参数获取tenantId
    const body = await request.json();
    const { tenantUserId, ...restBody } = body;

    if (!tenantUserId) {
      return NextResponse.json(
        { success: false, message: "只有规划师才能设置匹配配置" },
        { status: 403 },
      );
    }

    // 获取规划师信息
    const planner = await prisma.planner.findUnique({
      where: { tenantUserId },
    });

    if (!planner) {
      return NextResponse.json(
        { success: false, message: "未找到规划师信息" },
        { status: 404 },
      );
    }

    const result = await matchService.createDefaultPlannerMatchProfile(
      planner.id,
      restBody,
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error("创建规划师匹配配置失败:", error);
    return NextResponse.json(
      { success: false, message: "创建配置失败" },
      { status: 500 },
    );
  }
}
