import { NextRequest, NextResponse } from "next/server";
import { MatchService, ClientAuthService, prisma } from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

/**
 * GET /api/match/algorithm-config
 * 获取算法配置列表
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const authService = new ClientAuthService(prisma);
    const userResult = await authService.verifySession(token);
    
    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const category = searchParams.get("category") as any;
    const isActive = searchParams.get("isActive");
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");

    const matchService = new MatchService(prisma);
    const result = await matchService.getAlgorithmConfigs({
      category,
      isActive: isActive ? isActive === "true" : undefined,
      page,
      pageSize,
    });

    return NextResponse.json(result);
  } catch (error: any) {
    console.error("获取算法配置失败:", error);
    return NextResponse.json(
      { error: error.message || "获取算法配置失败" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/match/algorithm-config
 * 创建算法配置
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const authService = new ClientAuthService(prisma);
    const userResult = await authService.verifySession(token);
    
    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    
    // 验证必填字段
    if (!body.name || 
        typeof body.gradeMatchWeight !== 'number' ||
        typeof body.subjectMatchWeight !== 'number' ||
        typeof body.locationMatchWeight !== 'number' ||
        typeof body.ratingWeight !== 'number' ||
        typeof body.successRateWeight !== 'number' ||
        typeof body.capacityWeight !== 'number' ||
        typeof body.responseTimeWeight !== 'number') {
      return NextResponse.json(
        { error: "缺少必填字段或字段类型错误" },
        { status: 400 }
      );
    }

    // 验证权重总和
    const totalWeight = body.gradeMatchWeight + body.subjectMatchWeight + 
                       body.locationMatchWeight + body.ratingWeight + 
                       body.successRateWeight + body.capacityWeight + 
                       body.responseTimeWeight + (body.priceMatchWeight || 0);
    
    if (Math.abs(totalWeight - 1.0) > 0.01) {
      return NextResponse.json(
        { error: "权重总和必须等于1.0" },
        { status: 400 }
      );
    }

    const matchService = new MatchService(prisma);
    const config = await matchService.createAlgorithmConfig({
      name: body.name,
      description: body.description,
      category: body.category,
      gradeMatchWeight: body.gradeMatchWeight,
      subjectMatchWeight: body.subjectMatchWeight,
      locationMatchWeight: body.locationMatchWeight,
      ratingWeight: body.ratingWeight,
      successRateWeight: body.successRateWeight,
      capacityWeight: body.capacityWeight,
      responseTimeWeight: body.responseTimeWeight,
      priceMatchWeight: body.priceMatchWeight,
      urgencyMultiplier: body.urgencyMultiplier,
      newPlannerBoost: body.newPlannerBoost,
      repeatCustomerBoost: body.repeatCustomerBoost,
      minRating: body.minRating,
      minSuccessRate: body.minSuccessRate,
      maxResponseTime: body.maxResponseTime,
      isDefault: body.isDefault,
    });

    return NextResponse.json(config);
  } catch (error: any) {
    console.error("创建算法配置失败:", error);
    return NextResponse.json(
      { error: error.message || "创建算法配置失败" },
      { status: 500 }
    );
  }
}

