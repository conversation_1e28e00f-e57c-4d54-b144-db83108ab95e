import { NextRequest, NextResponse } from "next/server";
import { MatchService, ClientAuthService, prisma } from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

/**
 * PUT /api/match/algorithm-config/[id]
 * 更新算法配置
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const authService = new ClientAuthService(prisma);
    const userResult = await authService.verifySession(token);
    
    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const configId = params.id;

    // 如果提供了权重，验证权重总和
    if (body.gradeMatchWeight !== undefined || 
        body.subjectMatchWeight !== undefined ||
        body.locationMatchWeight !== undefined ||
        body.ratingWeight !== undefined ||
        body.successRateWeight !== undefined ||
        body.capacityWeight !== undefined ||
        body.responseTimeWeight !== undefined ||
        body.priceMatchWeight !== undefined) {
      
      // 获取当前配置以计算新的权重总和
      const matchService = new MatchService(prisma);
      const configs = await matchService.getAlgorithmConfigs({});
      const currentConfig = configs.items.find((config: any) => config.id === configId);

      if (!currentConfig) {
        return NextResponse.json(
          { error: "算法配置不存在" },
          { status: 404 }
        );
      }

      const newWeights = {
        gradeMatchWeight: body.gradeMatchWeight ?? currentConfig.gradeMatchWeight,
        subjectMatchWeight: body.subjectMatchWeight ?? currentConfig.subjectMatchWeight,
        locationMatchWeight: body.locationMatchWeight ?? currentConfig.locationMatchWeight,
        ratingWeight: body.ratingWeight ?? currentConfig.ratingWeight,
        successRateWeight: body.successRateWeight ?? currentConfig.successRateWeight,
        capacityWeight: body.capacityWeight ?? currentConfig.capacityWeight,
        responseTimeWeight: body.responseTimeWeight ?? currentConfig.responseTimeWeight,
        priceMatchWeight: body.priceMatchWeight ?? currentConfig.priceMatchWeight,
      };

      const totalWeight = Object.values(newWeights).reduce((sum, weight) => sum + weight, 0);
      
      if (Math.abs(totalWeight - 1.0) > 0.01) {
        return NextResponse.json(
          { error: "权重总和必须等于1.0" },
          { status: 400 }
        );
      }
    }

    const config = await matchService.updateAlgorithmConfig(
      configId,
      body,
      userResult.user.id,
      body.reason
    );

    return NextResponse.json(config);
  } catch (error: any) {
    console.error("更新算法配置失败:", error);
    return NextResponse.json(
      { error: error.message || "更新算法配置失败" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/match/algorithm-config/[id]
 * 删除算法配置
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const authService = new ClientAuthService(prisma);
    const userResult = await authService.verifySession(token);
    
    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const configId = params.id;
    
    const matchService = new MatchService(prisma);
    await matchService.deleteAlgorithmConfig(configId);

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error("删除算法配置失败:", error);
    return NextResponse.json(
      { error: error.message || "删除算法配置失败" },
      { status: 500 }
    );
  }
}

