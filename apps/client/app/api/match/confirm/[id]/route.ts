import { NextRequest, NextResponse } from "next/server";
import { MatchService, ClientAuthService, prisma } from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

/**
 * PUT /api/match/confirm/[id]
 * 确认匹配
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const authService = new ClientAuthService(prisma);
    const userResult = await authService.verifySession(token);
    
    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const requesterId = body.requesterId || userResult.user.id;

    const matchService = new MatchService(prisma);
    const matchRequest = await matchService.confirmMatch(
      params.id,
      requesterId
    );

    return NextResponse.json(matchRequest);
  } catch (error: any) {
    console.error("确认匹配失败:", error);
    return NextResponse.json(
      { error: error.message || "确认匹配失败" },
      { status: 500 }
    );
  }
}

