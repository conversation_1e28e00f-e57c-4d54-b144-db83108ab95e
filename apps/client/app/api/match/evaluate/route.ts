import { NextRequest, NextResponse } from "next/server";
import { MatchService, ClientAuthService, prisma } from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

/**
 * POST /api/match/evaluate
 * 评价匹配
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const authService = new ClientAuthService(prisma);
    const userResult = await authService.verifySession(token);
    
    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    
    // 验证必填字段
    if (!body.matchRequestId || !body.evaluatorType || !body.rating) {
      return NextResponse.json(
        { error: "缺少必填字段" },
        { status: 400 }
      );
    }

    // 验证评分范围
    if (body.rating < 1 || body.rating > 5) {
      return NextResponse.json(
        { error: "评分必须在1-5之间" },
        { status: 400 }
      );
    }

    const matchService = new MatchService(prisma);
    const evaluation = await matchService.evaluateMatch({
      matchRequestId: body.matchRequestId,
      evaluatorId: body.evaluatorId || userResult.user.id,
      evaluatorType: body.evaluatorType,
      rating: body.rating,
      comment: body.comment,
      tags: body.tags,
    });

    return NextResponse.json(evaluation);
  } catch (error: any) {
    console.error("评价匹配失败:", error);
    return NextResponse.json(
      { error: error.message || "评价匹配失败" },
      { status: 500 }
    );
  }
}

