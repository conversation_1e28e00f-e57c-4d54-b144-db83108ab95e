import { NextRequest, NextResponse } from "next/server";
import { MatchService, ClientAuthService, prisma } from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

/**
 * PUT /api/match/response/[id]
 * 接受或拒绝匹配响应
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const authService = new ClientAuthService(prisma);
    const userResult = await authService.verifySession(token);
    
    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    
    if (!body.action || !["accept", "reject"].includes(body.action)) {
      return NextResponse.json(
        { error: "无效的操作类型" },
        { status: 400 }
      );
    }

    const matchService = new MatchService(prisma);
    const response = await matchService.handleMatchResponse({
      responseId: params.id,
      requesterId: body.requesterId || userResult.user.id,
      action: body.action,
    });

    return NextResponse.json(response);
  } catch (error: any) {
    console.error("处理匹配响应失败:", error);
    return NextResponse.json(
      { error: error.message || "处理匹配响应失败" },
      { status: 500 }
    );
  }
}

