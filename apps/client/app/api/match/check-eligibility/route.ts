import { NextRequest, NextResponse } from "next/server";
import { ClientAuthService, MatchService, prisma } from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

const authService = new ClientAuthService(prisma);
const matchService = new MatchService(prisma);

export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, message: "未登录" },
        { status: 401 },
      );
    }

    const userResult = await authService.verifySession(token);

    if (!userResult.success || !userResult.user) {
      return NextResponse.json(
        { success: false, message: "未登录" },
        { status: 401 },
      );
    }

    // 从查询参数获取tenantId
    const tenantId = request.nextUrl.searchParams.get("tenantId");

    if (!tenantId) {
      return NextResponse.json(
        { success: false, message: "未找到租户信息" },
        { status: 400 },
      );
    }

    // 检查用户是否可以创建匹配请求
    const eligibility = await matchService.checkMatchRequestEligibility(
      userResult.user.id,
      tenantId,
    );

    return NextResponse.json(eligibility);
  } catch (error) {
    console.error("检查匹配资格失败:", error);
    return NextResponse.json(
      { success: false, message: "检查失败" },
      { status: 500 },
    );
  }
}
