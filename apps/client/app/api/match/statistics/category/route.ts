import { NextRequest, NextResponse } from "next/server";
import { MatchService, ClientAuthService, prisma } from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

/**
 * GET /api/match/statistics/category
 * 获取按服务分类的匹配统计
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const authService = new ClientAuthService(prisma);
    const userResult = await authService.verifySession(token);

    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const tenantId = searchParams.get("tenantId")!;
    const timeRange = searchParams.get("timeRange") || "30d";

    // 计算时间范围
    const endDate = new Date();
    const startDate = new Date();

    switch (timeRange) {
      case "7d":
        startDate.setDate(endDate.getDate() - 7);
        break;
      case "30d":
        startDate.setDate(endDate.getDate() - 30);
        break;
      case "90d":
        startDate.setDate(endDate.getDate() - 90);
        break;
      case "1y":
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    // 获取分类统计
    const matchService = new MatchService(prisma);
    const categoryStats = await matchService.getMatchStatisticsByCategory({
      tenantId,
      startDate,
      endDate,
    });

    // 计算总体概览
    const totalRequests = categoryStats.reduce(
      (sum, cat) => sum + cat.total,
      0,
    );
    const totalConfirmed = categoryStats.reduce(
      (sum, cat) => sum + cat.statusBreakdown.CONFIRMED,
      0,
    );
    const avgSuccessRate =
      totalRequests > 0
        ? categoryStats.reduce(
            (sum, cat) => sum + cat.successRate * cat.total,
            0,
          ) / totalRequests
        : 0;

    const overview = {
      totalRequests,
      totalCategories: categoryStats.length,
      avgSuccessRate,
      totalConfirmed,
    };

    // 获取趋势数据（简化版本，实际应该按时间分组）
    const trends = categoryStats.map((cat) => ({
      category: cat.category,
      data: [
        // 这里应该是实际的时间序列数据
        // 现在返回模拟数据
        {
          date: startDate.toISOString().split("T")[0],
          requests: Math.floor(cat.total * 0.3),
          successRate: cat.successRate * 0.9,
        },
        {
          date: new Date(
            startDate.getTime() + (endDate.getTime() - startDate.getTime()) / 2,
          )
            .toISOString()
            .split("T")[0],
          requests: Math.floor(cat.total * 0.4),
          successRate: cat.successRate * 0.95,
        },
        {
          date: endDate.toISOString().split("T")[0],
          requests: Math.floor(cat.total * 0.3),
          successRate: cat.successRate,
        },
      ],
    }));

    return NextResponse.json({
      success: true,
      data: {
        overview,
        categories: categoryStats,
        trends,
      },
    });
  } catch (error: any) {
    console.error("获取分类统计失败:", error);
    return NextResponse.json(
      { error: error.message || "获取分类统计失败" },
      { status: 500 },
    );
  }
}
