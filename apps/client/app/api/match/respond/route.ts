import { NextRequest, NextResponse } from "next/server";
import { MatchService, ClientAuthService, prisma } from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

/**
 * POST /api/match/respond
 * 规划师响应匹配请求
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const authService = new ClientAuthService(prisma);
    const userResult = await authService.verifySession(token);
    
    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    
    // 验证必填字段
    if (!body.matchRequestId || !body.plannerId) {
      return NextResponse.json(
        { error: "缺少必填字段" },
        { status: 400 }
      );
    }

    const matchService = new MatchService(prisma);
    const response = await matchService.respondToMatch({
      matchRequestId: body.matchRequestId,
      plannerId: body.plannerId,
      message: body.message,
      availability: body.availability,
      quote: body.quote,
    });

    return NextResponse.json(response);
  } catch (error: any) {
    console.error("响应匹配请求失败:", error);
    return NextResponse.json(
      { error: error.message || "响应匹配请求失败" },
      { status: 500 }
    );
  }
}

