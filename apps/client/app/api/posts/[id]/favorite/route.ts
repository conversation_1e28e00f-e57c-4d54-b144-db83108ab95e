import { NextRequest, NextResponse } from "next/server";
import { prisma, PostsService } from "@workspace/database";

// 创建帖子服务实例
const postsService = new PostsService(prisma);

// POST /api/posts/[id]/favorite - 收藏/取消收藏
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { userId } = body;

    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          message: "缺少用户ID",
        },
        { status: 400 }
      );
    }

    // 切换收藏状态
    const isFavorited = await postsService.toggleFavorite(id, userId);

    return NextResponse.json({
      success: true,
      data: { isFavorited },
      message: isFavorited ? "收藏成功" : "取消收藏成功",
    });
  } catch (error) {
    console.error("收藏操作失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "收藏操作失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 }
    );
  }
}

