import { NextRequest, NextResponse } from "next/server";
import { prisma, PostsService } from "@workspace/database";

// 创建帖子服务实例
const postsService = new PostsService(prisma);

// POST /api/posts/[id]/like - 点赞/取消点赞
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { userId } = body;

    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          message: "缺少用户ID",
        },
        { status: 400 }
      );
    }

    // 切换点赞状态
    const isLiked = await postsService.toggleLike(id, userId);

    return NextResponse.json({
      success: true,
      data: { isLiked },
      message: isLiked ? "点赞成功" : "取消点赞成功",
    });
  } catch (error) {
    console.error("点赞操作失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "点赞操作失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 }
    );
  }
}

