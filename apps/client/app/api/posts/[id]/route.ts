import { NextRequest, NextResponse } from "next/server";
import {
  prisma,
  PostsService,
  PostStatus,
  UpdatePostInput,
} from "@workspace/database";

// 创建帖子服务实例
const postsService = new PostsService(prisma);

// GET /api/posts/[id] - 获取单个帖子详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;
    const searchParams = request.nextUrl.searchParams;
    const includeComments = searchParams.get("includeComments") === "true";

    // 获取帖子详情
    const post = await postsService.getPostById(id, includeComments);

    if (!post) {
      return NextResponse.json(
        {
          success: false,
          message: "帖子不存在",
        },
        { status: 404 },
      );
    }

    // 增加浏览量
    if (post.status === PostStatus.PUBLISHED) {
      await postsService.incrementViewCount(id);
    }

    return NextResponse.json({
      success: true,
      data: post,
      message: "帖子详情获取成功",
    });
  } catch (error) {
    console.error("获取帖子详情失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "获取帖子详情失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 },
    );
  }
}

// PUT /api/posts/[id] - 更新帖子
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;
    const body = await request.json();

    // 构建更新输入
    const updateInput: UpdatePostInput = {};

    if (body.title !== undefined) updateInput.title = body.title;
    if (body.content !== undefined) updateInput.content = body.content;
    if (body.summary !== undefined) updateInput.summary = body.summary;
    if (body.cover !== undefined) updateInput.cover = body.cover;
    if (body.type !== undefined) updateInput.type = body.type;
    if (body.status !== undefined) updateInput.status = body.status;
    if (body.categoryId !== undefined) updateInput.categoryId = body.categoryId;
    if (body.tags !== undefined) updateInput.tags = body.tags;
    if (body.isOriginal !== undefined) updateInput.isOriginal = body.isOriginal;
    if (body.isTop !== undefined) updateInput.isTop = body.isTop;
    if (body.isRecommended !== undefined)
      updateInput.isRecommended = body.isRecommended;

    // 更新帖子
    const post = await postsService.updatePost(id, updateInput);

    if (!post) {
      return NextResponse.json(
        {
          success: false,
          message: "帖子不存在",
        },
        { status: 404 },
      );
    }

    return NextResponse.json({
      success: true,
      data: post,
      message: "帖子更新成功",
    });
  } catch (error) {
    console.error("更新帖子失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "更新帖子失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 },
    );
  }
}

// DELETE /api/posts/[id] - 删除帖子
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;

    // 删除帖子
    const success = await postsService.deletePost(id);

    if (!success) {
      return NextResponse.json(
        {
          success: false,
          message: "删除帖子失败",
        },
        { status: 500 },
      );
    }

    return NextResponse.json({
      success: true,
      message: "帖子删除成功",
    });
  } catch (error) {
    console.error("删除帖子失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "删除帖子失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 },
    );
  }
}
