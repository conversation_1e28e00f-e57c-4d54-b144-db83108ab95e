"use server";
import { NextRequest, NextResponse } from "next/server";
import {
  prisma,
  PostsService,
  PostType,
  PostStatus,
  CreatePostInput,
  GetPostsOptions,
} from "@workspace/database";

// 创建帖子服务实例
const postsService = new PostsService(prisma);

// GET /api/posts - 获取帖子列表
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;

    // 构建查询选项
    const options: GetPostsOptions = {
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "10"),
      categoryId: searchParams.get("categoryId") || undefined,
      type: (searchParams.get("type") as PostType) || undefined,
      status: (searchParams.get("status") as PostStatus) || undefined,
      authorId: searchParams.get("authorId") || undefined,
      search: searchParams.get("search") || undefined,
      tags: searchParams.getAll("tags"),
      isTop: searchParams.get("isTop")
        ? searchParams.get("isTop") === "true"
        : undefined,
      isRecommended: searchParams.get("isRecommended")
        ? searchParams.get("isRecommended") === "true"
        : undefined,
      sortBy: (searchParams.get("sortBy") as any) || "createdAt",
      sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "desc",
      includeAuthor: searchParams.get("includeAuthor") !== "false",
      includeCategory: searchParams.get("includeCategory") !== "false",
      includeTags: searchParams.get("includeTags") !== "false",
      includeStats: searchParams.get("includeStats") !== "false",
    };

    // 获取帖子列表
    const result = await postsService.getPosts(options);

    return NextResponse.json({
      success: true,
      data: result,
      message: "帖子列表获取成功",
    });
  } catch (error) {
    console.error("获取帖子列表失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "获取帖子列表失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 },
    );
  }
}

// POST /api/posts - 创建帖子
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // 验证必需字段
    const requiredFields = ["title", "content", "type", "authorId"];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          {
            success: false,
            message: `缺少必需字段: ${field}`,
          },
          { status: 400 },
        );
      }
    }

    // 验证帖子类型
    if (!Object.values(PostType).includes(body.type)) {
      return NextResponse.json(
        {
          success: false,
          message: "无效的帖子类型",
        },
        { status: 400 },
      );
    }

    // 构建创建输入
    const createInput: CreatePostInput = {
      title: body.title,
      content: body.content,
      summary: body.summary,
      cover: body.cover,
      type: body.type,
      status: body.status || PostStatus.DRAFT,
      categoryId: body.categoryId,
      tags: body.tags || [],
      isOriginal: body.isOriginal ?? true,
      isTop: body.isTop ?? false,
      isRecommended: body.isRecommended ?? false,
      authorId: body.authorId,
    };

    // 创建帖子
    const post = await postsService.createPost(createInput);

    return NextResponse.json({
      success: true,
      data: post,
      message: "帖子创建成功",
    });
  } catch (error) {
    console.error("创建帖子失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "创建帖子失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 },
    );
  }
}
