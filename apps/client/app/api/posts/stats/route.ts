import { NextRequest, NextResponse } from "next/server";
import { prisma, PostsService } from "@workspace/database";

// 创建帖子服务实例
const postsService = new PostsService(prisma);

// GET /api/posts/stats - 获取帖子统计数据
export async function GET(request: NextRequest) {
  try {
    // 获取统计数据
    const stats = await postsService.getPostStats();

    return NextResponse.json({
      success: true,
      data: stats,
      message: "统计数据获取成功",
    });
  } catch (error) {
    console.error("获取统计数据失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "获取统计数据失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 }
    );
  }
}

