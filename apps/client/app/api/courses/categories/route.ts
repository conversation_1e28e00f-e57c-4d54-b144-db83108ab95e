import { NextRequest, NextResponse } from "next/server";
import { 
  prisma, 
  CategoryService
} from "@workspace/database";

const categoryService = new CategoryService(prisma);

// 获取课程分类列表
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const includeChildren = searchParams.get('includeChildren') === 'true';
    const isActive = searchParams.get('isActive') !== 'false'; // 默认只返回激活的分类
    const parentId = searchParams.get('parentId') || null;

    // 如果请求树形结构
    if (includeChildren) {
      const result = await categoryService.getCategoryTree();
      
      if (!result.success) {
        return NextResponse.json(
          {
            success: false,
            message: result.message || "获取分类树失败",
          },
          { status: 500 }
        );
      }

      // 转换数据格式以匹配前端需求
      const transformCategory = (category: any) => ({
        id: category.id,
        name: category.name,
        slug: category.slug,
        description: category.description,
        icon: category.icon,
        order: category.order,
        isActive: category.isActive,
        courseCount: category._count?.courses || 0,
        children: category.children ? category.children.map(transformCategory) : [],
      });

      const categories = result.data?.map(transformCategory) || [];

      return NextResponse.json({
        success: true,
        data: categories,
        message: "获取课程分类树成功"
      });
    }

    // 获取平铺列表
    const result = await categoryService.getCategories({
      parentId: parentId === 'null' ? null : parentId,
      isActive,
      includeChildren: false,
      includeParent: false,
    });

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          message: result.message || "获取分类列表失败",
        },
        { status: 500 }
      );
    }

    // 转换数据格式以匹配前端需求
    const categories = result.data?.map(category => ({
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      icon: category.icon,
      order: category.order,
      isActive: category.isActive,
      courseCount: category._count?.courses || 0,
    })) || [];

    return NextResponse.json({
      success: true,
      data: categories,
      message: "获取课程分类成功"
    });

  } catch (error) {
    console.error("获取课程分类错误:", error);
    return NextResponse.json(
      {
        success: false,
        message: "获取课程分类失败，请稍后重试",
      },
      { status: 500 }
    );
  }
}

