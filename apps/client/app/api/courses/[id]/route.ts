import { NextRequest, NextResponse } from "next/server";
import {
  prisma,
  CourseContentService,
  CoursesService,
  ChaptersService,
  LessonsService,
  ClientAuthService,
  CourseEnrollment,
  EnrollmentsService,
} from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

const coursesService = new CoursesService(prisma);
const chaptersService = new ChaptersService(prisma);
const lessonsService = new LessonsService(prisma);
const courseContentService = new CourseContentService(
  prisma,
  coursesService,
  chaptersService,
  lessonsService,
);
const enrollmentsService = new EnrollmentsService(prisma);
const authService = new ClientAuthService(prisma);

// 获取课程详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
): Promise<NextResponse> {
  try {
    const { id: courseId } = await params;

    // 获取课程完整内容
    const course =
      await courseContentService.getCourseWithFullContent(courseId);

    if (!course) {
      return NextResponse.json(
        {
          success: false,
          message: "课程不存在",
        },
        { status: 404 },
      );
    }

    // 检查课程状态
    if (course.status !== "PUBLISHED") {
      return NextResponse.json(
        {
          success: false,
          message: "课程暂未发布",
        },
        { status: 403 },
      );
    }

    // 获取用户信息（如果有token）
    let userId: string | null = null;
    let enrollment = null;
    let lessonProgress: any[] = [];

    try {
      const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
      const token =
        extractTokenFromHeader(authHeader) ||
        request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

      if (token) {
        const userResult = await authService.verifySession(token);
        if (userResult.success && userResult.user) {
          userId = userResult.user.id;

          // 获取用户报名信息
          try {
            const enrollmentResult = await prisma.courseEnrollment.findUnique({
              where: {
                courseId_userId: {
                  courseId,
                  userId,
                },
              },
            });
            enrollment = enrollmentResult;
          } catch (error) {
            console.error("获取报名信息失败:", error);
          }

          // 获取用户学习进度
          if (enrollment) {
            try {
              lessonProgress = await prisma.lessonProgress.findMany({
                where: {
                  userId,
                  lesson: {
                    chapter: {
                      courseId,
                    },
                  },
                },
                include: {
                  lesson: true,
                },
              });
            } catch (error) {
              console.error("获取学习进度失败:", error);
            }
          }
        }
      }
    } catch (error) {
      console.error("验证用户信息失败:", error);
      // 继续执行，不影响课程内容获取
    }

    // 转换数据格式以匹配前端需求
    const courseData = {
      id: course.id,
      title: course.title,
      subtitle: course.subtitle,
      description: course.description,
      cover: course.cover,
      previewVideo: course.previewVideo,
      categoryId: course.categoryId,
      category: {
        id: course.category.id,
        name: course.category.name,
      },
      tags: course.tags,
      level: course.level,
      duration: course.duration,
      lessonsCount: course.lessonsCount,
      instructorName: course.instructorName,
      instructorTitle: course.instructorTitle,
      instructorAvatar: course.instructorAvatar,
      instructorBio: course.instructorBio,
      price: Number(course.price),
      originalPrice: course.originalPrice ? Number(course.originalPrice) : null,
      isFree: course.isFree,
      status: course.status,
      viewCount: course.viewCount,
      enrollCount: course.enrollCount,
      rating: course.rating,
      publishedAt: course.publishedAt,
      createdAt: course.createdAt,
      updatedAt: course.updatedAt,
      chapters: course.chapters.map((chapter) => ({
        id: chapter.id,
        title: chapter.title,
        description: chapter.description,
        order: chapter.order,
        lessons: chapter.lessons.map((lesson) => {
          // 查找该课时的学习进度
          const progress = lessonProgress.find(
            (p) => p.lesson.id === lesson.id,
          );

          return {
            id: lesson.id,
            title: lesson.title,
            description: lesson.description,
            videoUrl: lesson.videoUrl,
            videoDuration: lesson.videoDuration,
            videoSize: lesson.videoSize,
            order: lesson.order,
            isFree: lesson.isFree,
            // 添加学习进度信息
            progress: progress
              ? {
                  watchedDuration: progress.watchedDuration,
                  lastPosition: progress.lastPosition,
                  isCompleted: progress.isCompleted,
                  completedAt: progress.completedAt,
                }
              : null,
          };
        }),
      })),
      // 用户相关信息
      enrollment: enrollment
        ? {
            id: enrollment.id,
            enrolledAt: enrollment.enrolledAt,
            completedAt: enrollment.completedAt,
            progress: enrollment.progress,
            lastAccessAt: enrollment.lastAccessAt,
            isPaid: enrollment.isPaid,
            paidAmount: enrollment.paidAmount
              ? Number(enrollment.paidAmount)
              : null,
          }
        : null,
      // 统计信息
      stats: course.stats,
    };

    // 增加浏览次数
    try {
      await prisma.course.update({
        where: { id: courseId },
        data: {
          viewCount: {
            increment: 1,
          },
        },
      });
    } catch (error) {
      console.error("更新浏览次数失败:", error);
      // 不影响主要功能
    }

    return NextResponse.json({
      success: true,
      data: courseData,
      message: "获取课程详情成功",
    });
  } catch (error) {
    console.error("获取课程详情错误:", error);
    return NextResponse.json(
      {
        success: false,
        message: "获取课程详情失败，请稍后重试",
      },
      { status: 500 },
    );
  }
}
