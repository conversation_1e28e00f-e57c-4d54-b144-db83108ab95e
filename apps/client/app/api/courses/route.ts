import { NextRequest, NextResponse } from "next/server";
import { 
  prisma, 
  CoursesService,
  CourseStatus,
  CourseLevel
} from "@workspace/database";

const coursesService = new CoursesService(prisma);

// 获取课程列表
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const categoryId = searchParams.get('categoryId') || undefined;
    const level = searchParams.get('level') as CourseLevel || undefined;
    const isFree = searchParams.get('isFree') === 'true' ? true : 
                   searchParams.get('isFree') === 'false' ? false : undefined;
    const search = searchParams.get('search') || undefined;
    const sortBy = (searchParams.get('sortBy') || 'createdAt') as 'createdAt' | 'updatedAt' | 'title' | 'price';
    const sortOrder = (searchParams.get('sortOrder') || 'desc') as 'asc' | 'desc';

    // 获取课程列表
    const result = await coursesService.getCourses({
      page,
      limit,
      categoryId,
      level,
      status: CourseStatus.PUBLISHED, // 只返回已发布的课程
      isFree,
      search,
      sortBy,
      sortOrder,
    });

    // 转换数据格式以匹配前端需求
    const courses = result.data.map(course => ({
      id: course.id,
      title: course.title,
      subtitle: course.subtitle,
      description: course.description,
      cover: course.cover,
      previewVideo: course.previewVideo,
      categoryId: course.categoryId,
      category: {
        id: course.category.id,
        name: course.category.name,
      },
      tags: course.tags,
      level: course.level,
      duration: course.duration,
      lessonsCount: course.lessonsCount,
      instructorName: course.instructorName,
      instructorTitle: course.instructorTitle,
      instructorAvatar: course.instructorAvatar,
      instructorBio: course.instructorBio,
      price: Number(course.price),
      originalPrice: course.originalPrice ? Number(course.originalPrice) : null,
      isFree: course.isFree,
      status: course.status,
      viewCount: course.viewCount,
      enrollCount: course.enrollCount,
      rating: course.rating,
      publishedAt: course.publishedAt,
      createdAt: course.createdAt,
      updatedAt: course.updatedAt,
    }));

    return NextResponse.json({
      success: true,
      data: {
        courses,
        pagination: {
          total: result.total,
          page: result.page,
          limit: result.limit,
          totalPages: result.totalPages,
        }
      },
      message: "获取课程列表成功"
    });

  } catch (error) {
    console.error("获取课程列表错误:", error);
    return NextResponse.json(
      {
        success: false,
        message: "获取课程列表失败，请稍后重试",
      },
      { status: 500 }
    );
  }
}

