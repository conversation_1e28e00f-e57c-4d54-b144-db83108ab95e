import { NextRequest, NextResponse } from "next/server";
import { 
  prisma, 
  ClientAuthService,
  EnrollmentsService,
  DashboardService 
} from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

const authService = new ClientAuthService(prisma);
const enrollmentsService = new EnrollmentsService(prisma);
const dashboardService = new DashboardService(prisma);

// 获取用户学习统计
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token = extractTokenFromHeader(authHeader) || 
                 request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json(
        {
          success: false,
          message: "需要登录",
        },
        { status: 401 }
      );
    }

    const userResult = await authService.verifySession(token);
    if (!userResult.success || !userResult.user) {
      return NextResponse.json(
        {
          success: false,
          message: "用户验证失败",
        },
        { status: 401 }
      );
    }

    const userId = userResult.user.id;

    // 获取用户的课程报名信息
    const enrollments = await prisma.courseEnrollment.findMany({
      where: { userId },
      include: {
        course: {
          select: {
            id: true,
            title: true,
            cover: true,
            duration: true,
            lessonsCount: true,
            instructorName: true,
            category: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: { enrolledAt: 'desc' }
    });

    // 获取用户的课时学习进度
    const lessonProgress = await prisma.lessonProgress.findMany({
      where: { userId },
      include: {
        lesson: {
          include: {
            chapter: {
              include: {
                course: {
                  select: {
                    id: true,
                    title: true
                  }
                }
              }
            }
          }
        }
      }
    });

    // 计算统计数据
    const totalEnrollments = enrollments.length;
    const completedCourses = enrollments.filter(e => e.completedAt).length;
    const inProgressCourses = totalEnrollments - completedCourses;
    
    // 计算总学习时长（已观看的时长）
    const totalWatchedDuration = lessonProgress.reduce((sum, p) => sum + p.watchedDuration, 0);
    const totalLearningHours = Math.floor(totalWatchedDuration / 3600); // 转换为小时
    
    // 计算本月新增学习时长
    const thisMonthStart = new Date();
    thisMonthStart.setDate(1);
    thisMonthStart.setHours(0, 0, 0, 0);
    
    const thisMonthProgress = lessonProgress.filter(p => 
      p.updatedAt >= thisMonthStart
    );
    const thisMonthWatchedDuration = thisMonthProgress.reduce((sum, p) => sum + p.watchedDuration, 0);
    const thisMonthLearningHours = Math.floor(thisMonthWatchedDuration / 3600);
    
    // 获得的证书数量（完成的课程数量）
    const certificates = completedCourses;
    
    // 本月完成的课程数量
    const thisMonthCompletedCourses = enrollments.filter(e => 
      e.completedAt && e.completedAt >= thisMonthStart
    ).length;
    
    // 学习排名（模拟数据，实际可以基于学习时长排名）
    const allUsersLearningTime = await prisma.lessonProgress.groupBy({
      by: ['userId'],
      _sum: {
        watchedDuration: true
      }
    });
    
    const userRank = allUsersLearningTime
      .sort((a, b) => (b._sum.watchedDuration || 0) - (a._sum.watchedDuration || 0))
      .findIndex(item => item.userId === userId) + 1;
    
    const totalUsers = allUsersLearningTime.length;
    const rankPercentage = totalUsers > 0 ? Math.floor((1 - userRank / totalUsers) * 100) : 0;
    
    // 最近学习的课程
    const recentCourses = enrollments.slice(0, 5).map(enrollment => ({
      id: enrollment.course.id,
      title: enrollment.course.title,
      cover: enrollment.course.cover,
      category: enrollment.course.category.name,
      progress: enrollment.progress,
      lastAccessAt: enrollment.lastAccessAt,
      isCompleted: !!enrollment.completedAt,
      instructor: enrollment.course.instructorName,
    }));
    
    // 学习进度概览
    const progressOverview = enrollments.map(enrollment => {
      const courseProgress = lessonProgress.filter(p => 
        p.lesson.chapter.course.id === enrollment.course.id
      );
      const completedLessons = courseProgress.filter(p => p.isCompleted).length;
      
      return {
        courseId: enrollment.course.id,
        courseTitle: enrollment.course.title,
        totalLessons: enrollment.course.lessonsCount,
        completedLessons,
        progress: enrollment.progress,
        totalDuration: enrollment.course.duration,
        watchedDuration: courseProgress.reduce((sum, p) => sum + p.watchedDuration, 0),
      };
    });

    const stats = {
      // 总体统计
      totalLearningHours,
      thisMonthLearningHours,
      completedCourses,
      thisMonthCompletedCourses,
      certificates,
      rankPercentage,
      
      // 详细信息
      totalEnrollments,
      inProgressCourses,
      recentCourses,
      progressOverview,
      
      // 学习排名信息
      rank: {
        position: userRank,
        total: totalUsers,
        percentage: rankPercentage,
      }
    };

    return NextResponse.json({
      success: true,
      data: stats,
      message: "获取学习统计成功"
    });

  } catch (error) {
    console.error("获取学习统计错误:", error);
    return NextResponse.json(
      {
        success: false,
        message: "获取学习统计失败，请稍后重试",
      },
      { status: 500 }
    );
  }
}

