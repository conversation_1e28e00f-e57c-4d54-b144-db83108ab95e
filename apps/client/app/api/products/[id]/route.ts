import { NextRequest, NextResponse } from "next/server";
import {
  prisma,
  ProductsService,
  ProductStatus,
  ProductType,
} from "@workspace/database";
import { UpdateProductInput } from "@workspace/database/types/product.types";

// 创建产品服务实例
const productsService = new ProductsService(prisma);

// GET /api/products/[id] - 获取单个产品详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          message: "产品ID不能为空",
        },
        { status: 400 },
      );
    }

    // 检查是否需要完整信息
    const searchParams = request.nextUrl.searchParams;
    const includeFullInfo = searchParams.get("full") === "true";

    let product;
    if (includeFullInfo) {
      product = await productsService.getProductWithFullInfo(id);
    } else {
      product = await productsService.getProductWithBasicInfo(id);
    }

    if (!product) {
      return NextResponse.json(
        {
          success: false,
          message: "产品不存在",
        },
        { status: 404 },
      );
    }

    return NextResponse.json({
      success: true,
      data: product,
      message: "产品详情获取成功",
    });
  } catch (error) {
    console.error("获取产品详情失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "获取产品详情失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 },
    );
  }
}

// PUT /api/products/[id] - 更新产品
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const body = await request.json();

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          message: "产品ID不能为空",
        },
        { status: 400 },
      );
    }

    // 构建更新输入（只包含提供的字段）
    const updateInput: UpdateProductInput = {};

    // 基本信息
    if (body.name !== undefined) updateInput.name = body.name;
    if (body.code !== undefined) updateInput.code = body.code;
    if (body.type !== undefined) updateInput.type = body.type as ProductType;
    if (body.categoryId !== undefined) updateInput.categoryId = body.categoryId;
    if (body.description !== undefined)
      updateInput.description = body.description;
    if (body.features !== undefined) updateInput.features = body.features;
    if (body.highlights !== undefined) updateInput.highlights = body.highlights;
    if (body.images !== undefined) updateInput.images = body.images;
    if (body.brochureUrl !== undefined)
      updateInput.brochureUrl = body.brochureUrl;

    // 价格信息
    if (body.price !== undefined)
      updateInput.price = body.price ? parseFloat(body.price) : null;
    if (body.priceUnit !== undefined) updateInput.priceUnit = body.priceUnit;
    if (body.priceNote !== undefined) updateInput.priceNote = body.priceNote;

    // 产品详情
    if (body.duration !== undefined) updateInput.duration = body.duration;
    if (body.location !== undefined) updateInput.location = body.location;
    if (body.startDate !== undefined)
      updateInput.startDate = body.startDate ? new Date(body.startDate) : null;
    if (body.endDate !== undefined)
      updateInput.endDate = body.endDate ? new Date(body.endDate) : null;
    if (body.capacity !== undefined)
      updateInput.capacity = body.capacity ? parseInt(body.capacity) : null;
    if (body.minParticipants !== undefined)
      updateInput.minParticipants = body.minParticipants
        ? parseInt(body.minParticipants)
        : null;

    // 适用对象
    if (body.targetAudience !== undefined)
      updateInput.targetAudience = body.targetAudience;
    if (body.ageRange !== undefined) updateInput.ageRange = body.ageRange;
    if (body.gradeRange !== undefined) updateInput.gradeRange = body.gradeRange;

    // 关联信息
    if (body.partnerId !== undefined) updateInput.partnerId = body.partnerId;

    // 状态和优先级
    if (body.status !== undefined)
      updateInput.status = body.status as ProductStatus;
    if (body.priority !== undefined)
      updateInput.priority = parseInt(body.priority);

    // SEO信息
    if (body.metaTitle !== undefined) updateInput.metaTitle = body.metaTitle;
    if (body.metaDescription !== undefined)
      updateInput.metaDescription = body.metaDescription;
    if (body.metaKeywords !== undefined)
      updateInput.metaKeywords = body.metaKeywords;

    // 更新产品
    const product = await productsService.updateProduct(id, updateInput);

    return NextResponse.json({
      success: true,
      data: product,
      message: "产品更新成功",
    });
  } catch (error) {
    console.error("更新产品失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "更新产品失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 },
    );
  }
}

// DELETE /api/products/[id] - 删除产品
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          message: "产品ID不能为空",
        },
        { status: 400 },
      );
    }

    // 删除产品
    await productsService.deleteProduct(id);

    return NextResponse.json({
      success: true,
      message: "产品删除成功",
    });
  } catch (error) {
    console.error("删除产品失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "删除产品失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 },
    );
  }
}
