import { NextRequest, NextResponse } from "next/server";
import {
  prisma,
  ProductsService,
  ProductStatus,
  ProductType,
} from "@workspace/database";
import {
  CreateProductInput,
  GetProductsOptions,
} from "@workspace/database/types/product.types";

// 创建产品服务实例
const productsService = new ProductsService(prisma);

// GET /api/products - 获取产品列表
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;

    // 构建查询选项
    const options: GetProductsOptions = {
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "10"),
      categoryId: searchParams.get("categoryId") || undefined,
      type: (searchParams.get("type") as ProductType) || undefined,
      status: (searchParams.get("status") as ProductStatus) || undefined,
      partnerId: searchParams.get("partnerId") || undefined,
      search: searchParams.get("search") || undefined,
      sortBy: (searchParams.get("sortBy") as any) || "createdAt",
      sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "desc",
    };

    // 获取产品列表
    const result = await productsService.getProducts(options);

    return NextResponse.json({
      success: true,
      data: result,
      message: "产品列表获取成功",
    });
  } catch (error) {
    console.error("获取产品列表失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "获取产品列表失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 },
    );
  }
}

// POST /api/products - 创建产品
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // 验证必需字段
    const requiredFields = [
      "name",
      "code",
      "type",
      "categoryId",
      "description",
      "createdById",
    ];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          {
            success: false,
            message: `缺少必需字段: ${field}`,
          },
          { status: 400 },
        );
      }
    }

    // 构建创建输入
    const createInput: CreateProductInput = {
      name: body.name,
      code: body.code,
      type: body.type,
      categoryId: body.categoryId,
      description: body.description,
      features: body.features || [],
      highlights: body.highlights,
      images: body.images || [],
      brochureUrl: body.brochureUrl,
      price: body.price ? parseFloat(body.price) : undefined,
      priceUnit: body.priceUnit,
      priceNote: body.priceNote,
      duration: body.duration,
      location: body.location,
      startDate: body.startDate ? new Date(body.startDate) : undefined,
      endDate: body.endDate ? new Date(body.endDate) : undefined,
      capacity: body.capacity ? parseInt(body.capacity) : undefined,
      minParticipants: body.minParticipants
        ? parseInt(body.minParticipants)
        : undefined,
      targetAudience: body.targetAudience || [],
      ageRange: body.ageRange,
      gradeRange: body.gradeRange,
      partnerId: body.partnerId,
      status: body.status || ProductStatus.DRAFT,
      priority: body.priority ? parseInt(body.priority) : 0,
      metaTitle: body.metaTitle,
      metaDescription: body.metaDescription,
      metaKeywords: body.metaKeywords || [],
      createdById: body.createdById,
    };

    // 创建产品
    const product = await productsService.createProduct(createInput);

    return NextResponse.json({
      success: true,
      data: product,
      message: "产品创建成功",
    });
  } catch (error) {
    console.error("创建产品失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "创建产品失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 },
    );
  }
}
