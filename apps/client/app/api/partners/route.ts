import { NextRequest, NextResponse } from "next/server";
import { prisma, ProductsService } from "@workspace/database";

// 创建产品服务实例
const productsService = new ProductsService(prisma);

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    
    // 获取查询参数
    const includeInactive = searchParams.get('includeInactive') === 'true';
    
    // 获取合作伙伴列表
    const partners = await productsService.getPartners();
    
    // 如果需要包含非活跃的合作伙伴，需要调用prisma直接查询
    const finalPartners = includeInactive 
      ? await prisma.partner.findMany({
          include: {
            _count: {
              select: {
                products: true,
              },
            },
          },
          orderBy: {
            name: "asc",
          },
        })
      : partners;

    return NextResponse.json({
      success: true,
      data: finalPartners,
      message: "获取合作伙伴列表成功",
    });
  } catch (error) {
    console.error("获取合作伙伴列表失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "获取合作伙伴列表失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 验证必需字段
    const requiredFields = ['name', 'code', 'contactName', 'contactPhone', 'contactEmail'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          {
            success: false,
            message: `缺少必需字段: ${field}`,
          },
          { status: 400 }
        );
      }
    }

    // 创建合作伙伴
    const partner = await productsService.createPartner({
      name: body.name,
      code: body.code,
      logo: body.logo,
      contactName: body.contactName,
      contactPhone: body.contactPhone,
      contactEmail: body.contactEmail,
      address: body.address,
      cooperationType: body.cooperationType || [],
      description: body.description,
      isActive: body.isActive !== undefined ? body.isActive : true,
    });

    return NextResponse.json({
      success: true,
      data: partner,
      message: "创建合作伙伴成功",
    });
  } catch (error) {
    console.error("创建合作伙伴失败:", error);
    
    // 处理唯一性约束错误
    if (error instanceof Error && error.message.includes('Unique constraint')) {
      return NextResponse.json(
        {
          success: false,
          message: "合作伙伴代码已存在",
        },
        { status: 409 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "创建合作伙伴失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 }
    );
  }
}

