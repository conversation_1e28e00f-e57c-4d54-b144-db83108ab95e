import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// 验证schema - 基于 Prisma Student 模型
const StudentSchema = z.object({
  name: z.string().min(1, '学生姓名不能为空'),
  gender: z.string().optional().nullable(),
  birthday: z.string().optional().nullable(),
  phone: z.string().optional().nullable(),
  email: z.string().email().optional().nullable().or(z.literal('')),
  avatar: z.string().optional().nullable(),

  // 学业信息
  school: z.string().optional().nullable(),
  grade: z.string().optional().nullable(),
  major: z.string().optional().nullable(),
  gpa: z.number().min(0).max(4).optional().nullable(),
  rank: z.number().min(1).optional().nullable(),

  // 家庭信息
  parentName: z.string().optional().nullable(),
  parentPhone: z.string().min(1, '家长电话不能为空'),
  parentEmail: z.string().email().optional().nullable().or(z.literal('')),
  familyBackground: z.string().optional().nullable(),

  // 标签和备注
  tags: z.array(z.string()).default([]),
  notes: z.string().optional().nullable(),

  // 状态
  status: z.enum(['ACTIVE', 'GRADUATED', 'INACTIVE']).default('ACTIVE')
});

const StudentUpdateSchema = StudentSchema.partial();

// 筛选参数验证
const FilterSchema = z.object({
  search: z.string().optional(),
  status: z.string().optional(),
  tags: z.string().optional(),
  grade: z.string().optional(),
  school: z.string().optional()
});

// 模拟数据库 - 实际项目中会使用 Prisma
let studentsDb = [
  {
    id: '1',
    tenantUserId: null,
    tenantId: 'tenant-1',
    name: '张小明',
    gender: '男',
    birthday: '2006-05-15',
    phone: '13600136001',
    email: '<EMAIL>',
    avatar: null,
    school: '北京四中',
    grade: '高二',
    major: null,
    gpa: 3.8,
    rank: 15,
    parentName: '张父',
    parentPhone: '13600136000',
    parentEmail: '<EMAIL>',
    familyBackground: '双职工家庭，父母都是教师',
    tags: ['理科强', '数学竞赛', '重点跟进'],
    notes: '学习成绩优秀，数学特别突出，有参加数学竞赛的经历',
    status: 'ACTIVE',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-12-01T10:00:00Z'
  },
  {
    id: '2',
    tenantUserId: null,
    tenantId: 'tenant-1',
    name: '李晓红',
    gender: '女',
    birthday: '2007-08-22',
    phone: '13922223334',
    email: '<EMAIL>',
    avatar: null,
    school: '清华附中',
    grade: '初三',
    major: null,
    gpa: 3.6,
    rank: 8,
    parentName: '李女士',
    parentPhone: '13922223333',
    parentEmail: '<EMAIL>',
    familyBackground: '单亲家庭，母亲是医生',
    tags: ['文科', '语言天赋'],
    notes: '语文和英语成绩突出，有出国留学意向',
    status: 'ACTIVE',
    createdAt: '2024-02-01T10:00:00Z',
    updatedAt: '2024-12-01T10:00:00Z'
  },
  {
    id: '3',
    tenantUserId: null,
    tenantId: 'tenant-1',
    name: '陈子豪',
    gender: '男',
    birthday: '2005-12-03',
    phone: '13733334445',
    email: '<EMAIL>',
    avatar: null,
    school: '人大附中',
    grade: '高二',
    major: null,
    gpa: 3.9,
    rank: 5,
    parentName: '陈先生',
    parentPhone: '13733334444',
    parentEmail: '<EMAIL>',
    familyBackground: '企业家家庭，经济条件优越',
    tags: ['理科', '编程', '创新能力强'],
    notes: '对计算机编程很有兴趣，参加过多次编程比赛',
    status: 'ACTIVE',
    createdAt: '2024-01-20T10:00:00Z',
    updatedAt: '2024-12-01T10:00:00Z'
  }
];

// 获取学生列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const filters = FilterSchema.parse(Object.fromEntries(searchParams));

    let filteredStudents = [...studentsDb];

    // 搜索过滤
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filteredStudents = filteredStudents.filter(student =>
        student.name.toLowerCase().includes(searchTerm) ||
        student.school?.toLowerCase().includes(searchTerm) ||
        student.parentName?.toLowerCase().includes(searchTerm) ||
        student.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    // 状态过滤
    if (filters.status) {
      const statusList = filters.status.split(',');
      filteredStudents = filteredStudents.filter(student =>
        statusList.includes(student.status)
      );
    }

    // 标签过滤
    if (filters.tags) {
      const tagList = filters.tags.split(',');
      filteredStudents = filteredStudents.filter(student =>
        tagList.some(tag => student.tags.includes(tag))
      );
    }

    // 年级过滤
    if (filters.grade) {
      filteredStudents = filteredStudents.filter(student =>
        student.grade === filters.grade
      );
    }

    // 学校过滤
    if (filters.school) {
      filteredStudents = filteredStudents.filter(student =>
        student.school === filters.school
      );
    }

    return NextResponse.json(filteredStudents);

  } catch (error) {
    console.error('获取学生列表失败:', error);
    return NextResponse.json(
      { error: '获取学生列表失败' },
      { status: 500 }
    );
  }
}

// 创建新学生
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = StudentSchema.parse(body);

    const newStudent = {
      id: Date.now().toString(),
      tenantUserId: null,
      tenantId: 'tenant-1', // 实际项目中从认证信息获取
      ...validatedData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    studentsDb.push(newStudent);

    return NextResponse.json(newStudent, { status: 201 });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '数据验证失败', details: error.errors },
        { status: 400 }
      );
    }

    console.error('创建学生失败:', error);
    return NextResponse.json(
      { error: '创建学生失败' },
      { status: 500 }
    );
  }
}

// 批量操作
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { operation } = body;

    if (operation === 'batch-update') {
      const { ids, data } = body;
      const updateData = StudentUpdateSchema.parse(data);

      studentsDb = studentsDb.map(student => {
        if (ids.includes(student.id)) {
          return {
            ...student,
            ...updateData,
            updatedAt: new Date().toISOString()
          };
        }
        return student;
      });

      const updatedStudents = studentsDb.filter(student => ids.includes(student.id));
      return NextResponse.json({
        message: `成功更新 ${updatedStudents.length} 个学生`,
        students: updatedStudents
      });
    }

    if (operation === 'batch-delete') {
      const { ids } = body;
      const beforeCount = studentsDb.length;
      studentsDb = studentsDb.filter(student => !ids.includes(student.id));
      const deletedCount = beforeCount - studentsDb.length;

      return NextResponse.json({
        message: `成功删除 ${deletedCount} 个学生`
      });
    }

    return NextResponse.json(
      { error: '不支持的批量操作' },
      { status: 400 }
    );

  } catch (error) {
    console.error('批量操作失败:', error);
    return NextResponse.json(
      { error: '批量操作失败' },
      { status: 500 }
    );
  }
}

// 删除所有学生（仅开发环境）
export async function DELETE() {
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: '仅开发环境可用' },
      { status: 403 }
    );
  }

  studentsDb = [];
  return NextResponse.json({ message: '已清空所有学生数据' });
}