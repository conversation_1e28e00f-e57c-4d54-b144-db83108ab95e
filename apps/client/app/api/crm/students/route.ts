import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// 验证schema
const StudentSchema = z.object({
  name: z.string().min(1, '学生姓名不能为空'),
  grade: z.string().min(1, '年级不能为空'),
  school: z.string().optional(),
  parentName: z.string().optional(),
  parentPhone: z.string().min(1, '家长电话不能为空'),
  parentEmail: z.string().email().optional().or(z.literal('')),
  tags: z.array(z.string()).default([]),
  notes: z.string().optional(),
  servicePhase: z.string().default('初次咨询'),
  progress: z.number().min(0).max(100).default(0),
  satisfaction: z.number().min(1).max(5).default(5),
  urgency: z.enum(['low', 'medium', 'high']).default('low'),
  status: z.enum(['ACTIVE', 'GRADUATED', 'INACTIVE']).default('ACTIVE')
});

const StudentUpdateSchema = StudentSchema.partial();

// 搜索和过滤参数schema
const FilterSchema = z.object({
  search: z.string().optional(),
  status: z.string().optional(),
  tags: z.string().optional(),
  grades: z.string().optional(),
  urgency: z.string().optional(),
  servicePhase: z.string().optional(),
  page: z.string().default('1').transform(Number),
  limit: z.string().default('20').transform(Number),
  sortBy: z.string().default('updatedAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
});

// 模拟数据库 - 实际项目中会使用 Prisma
let studentsDb = [
  {
    id: '1',
    name: '张小明',
    grade: '高二',
    school: '北京四中',
    parentName: '张父',
    parentPhone: '13600136000',
    parentEmail: '<EMAIL>',
    tags: ['理科强', '数学竞赛'],
    notes: '学习成绩优秀，数学特别突出',
    servicePhase: '方案制定',
    progress: 65,
    satisfaction: 5,
    urgency: 'medium' as const,
    status: 'ACTIVE' as const,
    totalSessions: 8,
    completedMilestones: 3,
    totalMilestones: 5,
    joinDate: '2024-01-15',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-12-01T10:00:00Z'
  },
  {
    id: '2',
    name: '李小红',
    grade: '高三',
    school: '清华附中',
    parentName: '李母',
    parentPhone: '13700137000',
    parentEmail: '<EMAIL>',
    tags: ['文科强', '英语优秀'],
    notes: '性格开朗，语言天赋很好',
    servicePhase: '执行跟踪',
    progress: 80,
    satisfaction: 4,
    urgency: 'low' as const,
    status: 'ACTIVE' as const,
    totalSessions: 12,
    completedMilestones: 4,
    totalMilestones: 5,
    joinDate: '2024-02-20',
    createdAt: '2024-02-20T10:00:00Z',
    updatedAt: '2024-12-01T11:00:00Z'
  },
  {
    id: '3',
    name: '王小华',
    grade: '高一',
    school: '人大附中',
    parentName: '王父',
    parentPhone: '13800138000',
    tags: ['艺术特长', '钢琴'],
    notes: '有音乐天赋，正在考虑艺术类专业',
    servicePhase: '初次咨询',
    progress: 20,
    satisfaction: 5,
    urgency: 'high' as const,
    status: 'ACTIVE' as const,
    totalSessions: 2,
    completedMilestones: 0,
    totalMilestones: 6,
    joinDate: '2024-11-10',
    createdAt: '2024-11-10T10:00:00Z',
    updatedAt: '2024-11-25T10:00:00Z'
  }
];

// 获取学生列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const filters = FilterSchema.parse(Object.fromEntries(searchParams));

    let filteredStudents = [...studentsDb];

    // 搜索过滤
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filteredStudents = filteredStudents.filter(student =>
        student.name.toLowerCase().includes(searchTerm) ||
        student.school?.toLowerCase().includes(searchTerm) ||
        student.parentName?.toLowerCase().includes(searchTerm) ||
        student.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    // 状态过滤
    if (filters.status) {
      const statusList = filters.status.split(',');
      filteredStudents = filteredStudents.filter(student =>
        statusList.includes(student.status)
      );
    }

    // 标签过滤
    if (filters.tags) {
      const tagList = filters.tags.split(',');
      filteredStudents = filteredStudents.filter(student =>
        tagList.some(tag => student.tags.includes(tag))
      );
    }

    // 年级过滤
    if (filters.grades) {
      const gradeList = filters.grades.split(',');
      filteredStudents = filteredStudents.filter(student =>
        gradeList.includes(student.grade)
      );
    }

    // 紧急程度过滤
    if (filters.urgency) {
      const urgencyList = filters.urgency.split(',');
      filteredStudents = filteredStudents.filter(student =>
        urgencyList.includes(student.urgency)
      );
    }

    // 服务阶段过滤
    if (filters.servicePhase) {
      const phaseList = filters.servicePhase.split(',');
      filteredStudents = filteredStudents.filter(student =>
        phaseList.includes(student.servicePhase)
      );
    }

    // 排序
    filteredStudents.sort((a, b) => {
      const aVal = a[filters.sortBy as keyof typeof a];
      const bVal = b[filters.sortBy as keyof typeof b];
      
      if (typeof aVal === 'string' && typeof bVal === 'string') {
        const comparison = aVal.localeCompare(bVal);
        return filters.sortOrder === 'asc' ? comparison : -comparison;
      }
      
      if (typeof aVal === 'number' && typeof bVal === 'number') {
        return filters.sortOrder === 'asc' ? aVal - bVal : bVal - aVal;
      }
      
      return 0;
    });

    // 分页
    const startIndex = (filters.page - 1) * filters.limit;
    const endIndex = startIndex + filters.limit;
    const paginatedStudents = filteredStudents.slice(startIndex, endIndex);

    return NextResponse.json({
      students: paginatedStudents,
      pagination: {
        total: filteredStudents.length,
        page: filters.page,
        limit: filters.limit,
        totalPages: Math.ceil(filteredStudents.length / filters.limit)
      }
    });

  } catch (error) {
    console.error('获取学生列表失败:', error);
    return NextResponse.json(
      { error: '获取学生列表失败' },
      { status: 500 }
    );
  }
}

// 创建新学生
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = StudentSchema.parse(body);

    const newStudent = {
      id: Date.now().toString(),
      ...validatedData,
      totalSessions: 0,
      completedMilestones: 0,
      totalMilestones: 5,
      joinDate: new Date().toISOString().split('T')[0],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    studentsDb.push(newStudent);

    return NextResponse.json(newStudent, { status: 201 });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '数据验证失败', details: error.errors },
        { status: 400 }
      );
    }

    console.error('创建学生失败:', error);
    return NextResponse.json(
      { error: '创建学生失败' },
      { status: 500 }
    );
  }
}

// 批量操作
export async function PATCH(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const operation = searchParams.get('operation');
    const body = await request.json();

    if (operation === 'batch-update') {
      const { ids, data } = body;
      const updateData = StudentUpdateSchema.parse(data);

      studentsDb = studentsDb.map(student => {
        if (ids.includes(student.id)) {
          return {
            ...student,
            ...updateData,
            updatedAt: new Date().toISOString()
          };
        }
        return student;
      });

      const updatedStudents = studentsDb.filter(s => ids.includes(s.id));
      return NextResponse.json({ 
        message: `成功更新 ${updatedStudents.length} 个学生`,
        students: updatedStudents 
      });
    }

    if (operation === 'batch-delete') {
      const { ids } = body;
      const beforeCount = studentsDb.length;
      studentsDb = studentsDb.filter(student => !ids.includes(student.id));
      const deletedCount = beforeCount - studentsDb.length;

      return NextResponse.json({ 
        message: `成功删除 ${deletedCount} 个学生` 
      });
    }

    return NextResponse.json(
      { error: '不支持的批量操作' },
      { status: 400 }
    );

  } catch (error) {
    console.error('批量操作失败:', error);
    return NextResponse.json(
      { error: '批量操作失败' },
      { status: 500 }
    );
  }
}

// 删除所有学生（仅开发环境）
export async function DELETE() {
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: '仅开发环境可用' },
      { status: 403 }
    );
  }

  studentsDb = [];
  return NextResponse.json({ message: '已清空所有学生数据' });
} 