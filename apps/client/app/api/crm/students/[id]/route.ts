import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// 验证schema - 基于 Prisma Student 模型
const StudentUpdateSchema = z.object({
  name: z.string().min(1, '学生姓名不能为空').optional(),
  gender: z.string().optional().nullable(),
  birthday: z.string().optional().nullable(),
  phone: z.string().optional().nullable(),
  email: z.string().email().optional().nullable().or(z.literal('')),
  avatar: z.string().optional().nullable(),
  
  // 学业信息
  school: z.string().optional().nullable(),
  grade: z.string().optional().nullable(),
  major: z.string().optional().nullable(),
  gpa: z.number().min(0).max(4).optional().nullable(),
  rank: z.number().min(1).optional().nullable(),
  
  // 家庭信息
  parentName: z.string().optional().nullable(),
  parentPhone: z.string().optional().nullable(),
  parentEmail: z.string().email().optional().nullable().or(z.literal('')),
  familyBackground: z.string().optional().nullable(),
  
  // 标签和备注
  tags: z.array(z.string()).optional(),
  notes: z.string().optional().nullable(),
  
  // 状态
  status: z.enum(['ACTIVE', 'GRADUATED', 'INACTIVE']).optional()
});

// 模拟数据库 - 实际项目中会使用 Prisma
let studentsDb = [
  {
    id: '1',
    tenantUserId: null,
    tenantId: 'tenant-1',
    name: '张小明',
    gender: '男',
    birthday: '2006-05-15',
    phone: '13600136001',
    email: '<EMAIL>',
    avatar: null,
    school: '北京四中',
    grade: '高二',
    major: null,
    gpa: 3.8,
    rank: 15,
    parentName: '张父',
    parentPhone: '13600136000',
    parentEmail: '<EMAIL>',
    familyBackground: '双职工家庭，父母都是教师',
    tags: ['理科强', '数学竞赛', '重点跟进'],
    notes: '学习成绩优秀，数学特别突出，有参加数学竞赛的经历',
    status: 'ACTIVE',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-12-01T10:00:00Z'
  },
  {
    id: '2',
    tenantUserId: null,
    tenantId: 'tenant-1',
    name: '李晓红',
    gender: '女',
    birthday: '2007-08-22',
    phone: '13922223334',
    email: '<EMAIL>',
    avatar: null,
    school: '清华附中',
    grade: '初三',
    major: null,
    gpa: 3.6,
    rank: 8,
    parentName: '李女士',
    parentPhone: '13922223333',
    parentEmail: '<EMAIL>',
    familyBackground: '单亲家庭，母亲是医生',
    tags: ['文科', '语言天赋'],
    notes: '语文和英语成绩突出，有出国留学意向',
    status: 'ACTIVE',
    createdAt: '2024-02-01T10:00:00Z',
    updatedAt: '2024-12-01T10:00:00Z'
  },
  {
    id: '3',
    tenantUserId: null,
    tenantId: 'tenant-1',
    name: '陈子豪',
    gender: '男',
    birthday: '2005-12-03',
    phone: '13733334445',
    email: '<EMAIL>',
    avatar: null,
    school: '人大附中',
    grade: '高二',
    major: null,
    gpa: 3.9,
    rank: 5,
    parentName: '陈先生',
    parentPhone: '13733334444',
    parentEmail: '<EMAIL>',
    familyBackground: '企业家家庭，经济条件优越',
    tags: ['理科', '编程', '创新能力强'],
    notes: '对计算机编程很有兴趣，参加过多次编程比赛',
    status: 'ACTIVE',
    createdAt: '2024-01-20T10:00:00Z',
    updatedAt: '2024-12-01T10:00:00Z'
  }
];

// GET /api/crm/students/[id] - 获取单个学生详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: '学生ID不能为空' },
        { status: 400 }
      );
    }

    const student = studentsDb.find(s => s.id === id);

    if (!student) {
      return NextResponse.json(
        { error: '学生不存在' },
        { status: 404 }
      );
    }

    // 在实际项目中，这里会包含关联数据
    const studentWithRelations = {
      ...student,
      tenantUser: null, // 如果学生有关联的用户账号
      planners: [], // 关联的规划师
      plans: [], // 学习计划
      appointments: [], // 预约记录
      documents: [] // 相关文档
    };

    return NextResponse.json(studentWithRelations);

  } catch (error) {
    console.error('获取学生详情失败:', error);
    return NextResponse.json(
      { error: '获取学生详情失败' },
      { status: 500 }
    );
  }
}

// PUT /api/crm/students/[id] - 更新学生信息
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    
    if (!id) {
      return NextResponse.json(
        { error: '学生ID不能为空' },
        { status: 400 }
      );
    }

    const validatedData = StudentUpdateSchema.parse(body);
    
    const studentIndex = studentsDb.findIndex(s => s.id === id);
    
    if (studentIndex === -1) {
      return NextResponse.json(
        { error: '学生不存在' },
        { status: 404 }
      );
    }

    // 更新学生信息
    studentsDb[studentIndex] = {
      ...studentsDb[studentIndex],
      ...validatedData,
      updatedAt: new Date().toISOString()
    };

    return NextResponse.json(studentsDb[studentIndex]);

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '数据验证失败', details: error.errors },
        { status: 400 }
      );
    }

    console.error('更新学生失败:', error);
    return NextResponse.json(
      { error: '更新学生失败' },
      { status: 500 }
    );
  }
}

// DELETE /api/crm/students/[id] - 删除学生
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: '学生ID不能为空' },
        { status: 400 }
      );
    }

    const studentIndex = studentsDb.findIndex(s => s.id === id);
    
    if (studentIndex === -1) {
      return NextResponse.json(
        { error: '学生不存在' },
        { status: 404 }
      );
    }

    // 删除学生
    const deletedStudent = studentsDb.splice(studentIndex, 1)[0];

    return NextResponse.json({ 
      message: '学生删除成功',
      deletedStudent 
    });

  } catch (error) {
    console.error('删除学生失败:', error);
    return NextResponse.json(
      { error: '删除学生失败' },
      { status: 500 }
    );
  }
}
