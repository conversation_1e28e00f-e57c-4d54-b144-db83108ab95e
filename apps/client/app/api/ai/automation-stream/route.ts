import { NextRequest, NextResponse } from "next/server";
import { OpenAI } from "openai";

const openai = new OpenAI({
  baseURL: process.env.DEEPSEEK_API_BASE_URL || "https://api.deepseek.com",
  apiKey: process.env.DEEPSEEK_API_KEY,
});

// 存储待处理的消息队列
const messageQueue = new Map<string, any>();

// GET 请求处理 SSE 连接
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const messageId = searchParams.get("messageId");

  if (!messageId || !messageQueue.has(messageId)) {
    return new NextResponse("Message not found", { status: 404 });
  }

  const messageData = messageQueue.get(messageId);
  messageQueue.delete(messageId); // 使用后删除

  const encoder = new TextEncoder();
  const stream = new TransformStream();
  const writer = stream.writable.getWriter();

  // 异步处理流
  (async () => {
    try {
      const { message, currentUrl, pageContent } = messageData;

      // 分析页面内容
      const pageAnalysis = analyzePageContent(pageContent);

      const systemPrompt = `
你是一个专业的网页自动化助手，当前用户是 newObjectccc。你需要根据用户的自然语言指令，生成具体的网页自动化操作步骤。

当前页面信息：
- URL: ${currentUrl}
- 可用元素: ${JSON.stringify(pageAnalysis.elements.slice(0, 10), null, 2)}

请根据用户的请求，生成一系列自动化步骤。每个步骤应该包含：
- id: 唯一标识符（格式：step-1, step-2...）
- type: 操作类型 (click, fill, scroll, wait, highlight)
- selector: CSS选择器 (如果适用)
- value: 值 (如果适用)
- duration: 持续时间毫秒 (如果适用)
- description: 人类可读的中文描述
- status: 必须是 'pending'

请先提供一个友好的回复说明你要执行的操作，然后提供JSON格式的步骤列表。

响应格式示例：
我将帮你执行以下操作：点击登录按钮。

{
  "steps": [
    {
      "id": "step-1",
      "type": "highlight",
      "selector": "#login-button",
      "duration": 1000,
      "description": "高亮显示登录按钮",
      "status": "pending"
    }
  ]
}
`;

      // 使用OpenAI流式API
      const completion = await openai.chat.completions.create({
        model: "deepseek-chat",
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: message },
        ],
        temperature: 0.3,
        stream: true,
      });

      let fullResponse = "";
      let jsonPart = "";
      let isInJsonBlock = false;

      for await (const chunk of completion) {
        const content = chunk.choices[0]?.delta?.content || "";
        fullResponse += content;

        // 检查是否进入JSON块
        if (content.includes("{") && !isInJsonBlock) {
          isInJsonBlock = true;
          jsonPart = content.substring(content.indexOf("{"));
        } else if (isInJsonBlock) {
          jsonPart += content;
        }

        // 发送流式消息内容
        if (content && !isInJsonBlock) {
          await writer.write(
            encoder.encode(
              `data: ${JSON.stringify({
                type: "message",
                content: content,
              })}\n\n`,
            ),
          );
        }

        // 尝试解析JSON
        if (isInJsonBlock && jsonPart.includes("}")) {
          try {
            const jsonMatch = jsonPart.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
              const parsed = JSON.parse(jsonMatch[0]);
              if (parsed.steps) {
                const steps = parsed.steps.map((step: any, index: number) => ({
                  ...step,
                  id: step.id || `step-${index + 1}`,
                  status: "pending",
                }));

                await writer.write(
                  encoder.encode(
                    `data: ${JSON.stringify({
                      type: "steps",
                      steps: steps,
                    })}\n\n`,
                  ),
                );
              }
            }
          } catch (e) {
            // 继续收集
          }
        }
      }

      // 发送结束信号
      await writer.write(
        encoder.encode(`data: ${JSON.stringify({ type: "end" })}\n\n`),
      );
    } catch (error) {
      console.error("AI automation stream error:", error);
      await writer.write(
        encoder.encode(
          `data: ${JSON.stringify({
            type: "error",
            message: "抱歉，处理你的请求时出现了错误。",
          })}\n\n`,
        ),
      );
    } finally {
      await writer.close();
    }
  })();

  return new NextResponse(stream.readable, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
      "X-Accel-Buffering": "no",
    },
  });
}

// POST 请求创建消息并返回 messageId
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const messageId = `msg-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    // 存储消息数据
    messageQueue.set(messageId, body);

    // 5分钟后自动清理
    setTimeout(
      () => {
        messageQueue.delete(messageId);
      },
      5 * 60 * 1000,
    );

    return NextResponse.json({ messageId });
  } catch (error) {
    return NextResponse.json(
      { error: "Invalid request body" },
      { status: 400 },
    );
  }
}

function analyzePageContent(htmlContent: string) {
  const elements = [];

  // 提取按钮
  const buttonRegex =
    /<button[^>]*(?:id="([^"]*)")?[^>]*(?:class="([^"]*)")?[^>]*>([^<]*)<\/button>/gi;
  let match;

  while ((match = buttonRegex.exec(htmlContent)) !== null) {
    const [, id, className, text] = match;
    if (text && text.trim()) {
      elements.push({
        type: "button",
        id: id || null,
        className: className || null,
        text: text.trim(),
        selector: id ? `#${id}` : `button:contains("${text.trim()}")`,
      });
    }
  }

  // 提取输入框
  const inputRegex =
    /<input[^>]*(?:id="([^"]*)")?[^>]*(?:placeholder="([^"]*)")?[^>]*(?:type="([^"]*)")?[^>]*>/gi;
  while ((match = inputRegex.exec(htmlContent)) !== null) {
    const [, id, placeholder, type] = match;
    if (type !== "hidden") {
      elements.push({
        type: "input",
        id: id || null,
        placeholder: placeholder || null,
        inputType: type || "text",
        selector: id
          ? `#${id}`
          : placeholder
            ? `input[placeholder="${placeholder}"]`
            : "input",
      });
    }
  }

  // 提取文本域
  const textareaRegex =
    /<textarea[^>]*(?:id="([^"]*)")?[^>]*(?:placeholder="([^"]*)")?[^>]*>/gi;
  while ((match = textareaRegex.exec(htmlContent)) !== null) {
    const [, id, placeholder] = match;
    elements.push({
      type: "textarea",
      id: id || null,
      placeholder: placeholder || null,
      selector: id
        ? `#${id}`
        : placeholder
          ? `textarea[placeholder="${placeholder}"]`
          : "textarea",
    });
  }

  return {
    elements: elements.slice(0, 20),
    totalElements: elements.length,
  };
}
