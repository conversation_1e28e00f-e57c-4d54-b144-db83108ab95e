import { NextRequest, NextResponse } from "next/server";
import { prisma, ProductsService, ProductType } from "@workspace/database";
import { CreateProductCategoryInput } from "@workspace/database/types/product.types";

// 创建产品服务实例
const productsService = new ProductsService(prisma);

// GET /api/product-categories - 获取产品分类列表
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const type = searchParams.get("type") as ProductType;

    // 根据类型获取分类
    let categories;
    if (type) {
      categories = await productsService.getProductCategoriesByType(type);
    } else {
      categories = await productsService.getProductCategories();
    }

    return NextResponse.json({
      success: true,
      data: categories,
      message: "产品分类列表获取成功",
    });
  } catch (error) {
    console.error("获取产品分类列表失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "获取产品分类列表失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 },
    );
  }
}

// POST /api/product-categories - 创建产品分类
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // 验证必需字段
    const requiredFields = ["name", "type"];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          {
            success: false,
            message: `缺少必需字段: ${field}`,
          },
          { status: 400 },
        );
      }
    }

    // 验证产品类型
    const validTypes = Object.values(ProductType);
    if (!validTypes.includes(body.type)) {
      return NextResponse.json(
        {
          success: false,
          message: `无效的产品类型: ${body.type}`,
        },
        { status: 400 },
      );
    }

    // 构建创建输入
    const createInput: CreateProductCategoryInput = {
      name: body.name,
      slug: body.slug,
      description: body.description,
      type: body.type,
      icon: body.icon,
      order: body.order ? parseInt(body.order) : 0,
      isActive: body.isActive !== undefined ? body.isActive : true,
    };

    // 创建产品分类
    const category = await productsService.createProductCategory(createInput);

    return NextResponse.json({
      success: true,
      data: category,
      message: "产品分类创建成功",
    });
  } catch (error) {
    console.error("创建产品分类失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "创建产品分类失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 },
    );
  }
}
