import { Suspense } from "react";
import { PostListContainer } from "@/components/posts/PostListContainer";
import {
  Users,
  MessageCircle,
  Heart,
  Bookmark,
  Plus,
  TrendingUp,
} from "lucide-react";
import Link from "next/link";
import { Button } from "@workspace/ui/components/button";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
} from "@workspace/ui/components/card";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@workspace/ui/components/tabs";
import { Badge } from "@workspace/ui/components/badge";

// 社区统计数据
const communityStats = {
  totalPosts: 1234,
  totalUsers: 567,
  totalLikes: 8901,
  totalComments: 2345,
};

// 热门标签
const popularTags = [
  { name: "升学规划", count: 156 },
  { name: "留学申请", count: 134 },
  { name: "职业发展", count: 98 },
  { name: "院校选择", count: 87 },
  { name: "专业选择", count: 76 },
  { name: "考试准备", count: 65 },
];

// 社区公告
const announcements = [
  {
    id: 1,
    title: "社区规则更新通知",
    content: "为了维护良好的社区环境，我们更新了社区发帖规则...",
    date: "2024-01-15",
    isTop: true,
  },
  {
    id: 2,
    title: "新功能上线：专家问答",
    content: "现在可以直接向专业规划师提问了！",
    date: "2024-01-10",
    isTop: false,
  },
];

export default function CommunityPage() {
  return (
    <div className="flex flex-1 flex-col gap-6 p-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">规划师社区</h1>
          <p className="text-muted-foreground">
            与其他规划师分享经验，交流心得，共同成长
          </p>
        </div>
        <Link href="/community/posts/create">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            发布帖子
          </Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 主内容区 */}
        <div className="lg:col-span-3 space-y-6">
          {/* 社区公告 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                社区公告
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {announcements.map((announcement) => (
                <div
                  key={announcement.id}
                  className="flex items-start gap-3 p-3 rounded-lg border"
                >
                  {announcement.isTop && (
                    <Badge variant="destructive" className="mt-1">
                      置顶
                    </Badge>
                  )}
                  <div className="flex-1">
                    <h4 className="font-medium">{announcement.title}</h4>
                    <p className="text-sm text-muted-foreground mt-1">
                      {announcement.content}
                    </p>
                    <p className="text-xs text-muted-foreground mt-2">
                      {announcement.date}
                    </p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* 帖子列表 */}
          <Card>
            <CardHeader>
              <CardTitle>社区动态</CardTitle>
              <CardDescription>最新的社区帖子和讨论</CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="latest" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="latest">最新</TabsTrigger>
                  <TabsTrigger value="hot">热门</TabsTrigger>
                  <TabsTrigger value="recommended">推荐</TabsTrigger>
                  <TabsTrigger value="following">关注</TabsTrigger>
                </TabsList>

                <TabsContent value="latest" className="mt-6">
                  <Suspense
                    fallback={
                      <div className="space-y-4">
                        {[1, 2, 3].map((i) => (
                          <div key={i} className="animate-pulse">
                            <div className="h-32 bg-muted rounded-lg"></div>
                          </div>
                        ))}
                      </div>
                    }
                  >
                    <PostListContainer />
                  </Suspense>
                </TabsContent>

                <TabsContent value="hot" className="mt-6">
                  <Suspense
                    fallback={
                      <div className="space-y-4">
                        {[1, 2, 3].map((i) => (
                          <div key={i} className="animate-pulse">
                            <div className="h-32 bg-muted rounded-lg"></div>
                          </div>
                        ))}
                      </div>
                    }
                  >
                    <PostListContainer filter="hot" />
                  </Suspense>
                </TabsContent>

                <TabsContent value="recommended" className="mt-6">
                  <Suspense
                    fallback={
                      <div className="space-y-4">
                        {[1, 2, 3].map((i) => (
                          <div key={i} className="animate-pulse">
                            <div className="h-32 bg-muted rounded-lg"></div>
                          </div>
                        ))}
                      </div>
                    }
                  >
                    <PostListContainer filter="recommended" />
                  </Suspense>
                </TabsContent>

                <TabsContent value="following" className="mt-6">
                  <Suspense
                    fallback={
                      <div className="space-y-4">
                        {[1, 2, 3].map((i) => (
                          <div key={i} className="animate-pulse">
                            <div className="h-32 bg-muted rounded-lg"></div>
                          </div>
                        ))}
                      </div>
                    }
                  >
                    <PostListContainer filter="following" />
                  </Suspense>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* 侧边栏 */}
        <div className="space-y-6">
          {/* 社区统计 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">社区数据</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <MessageCircle className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">总帖子数</p>
                  <p className="font-semibold">
                    {communityStats.totalPosts.toLocaleString()}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Users className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">社区成员</p>
                  <p className="font-semibold">
                    {communityStats.totalUsers.toLocaleString()}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="p-2 bg-red-100 rounded-lg">
                  <Heart className="h-4 w-4 text-red-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">总点赞数</p>
                  <p className="font-semibold">
                    {communityStats.totalLikes.toLocaleString()}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Bookmark className="h-4 w-4 text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">总评论数</p>
                  <p className="font-semibold">
                    {communityStats.totalComments.toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 热门标签 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">热门标签</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {popularTags.map((tag) => (
                  <Badge
                    key={tag.name}
                    variant="secondary"
                    className="cursor-pointer hover:bg-secondary/80"
                  >
                    {tag.name}
                    <span className="ml-1 text-xs">({tag.count})</span>
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 快速导航 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">快速导航</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Link
                href="/community/courses"
                className="flex items-center gap-2 p-2 rounded-lg hover:bg-secondary/50 transition-colors"
              >
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm">规划师课程</span>
              </Link>
              <Link
                href="/community/products"
                className="flex items-center gap-2 p-2 rounded-lg hover:bg-secondary/50 transition-colors"
              >
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">特色研学营</span>
              </Link>
              <Link
                href="/community/edu-news"
                className="flex items-center gap-2 p-2 rounded-lg hover:bg-secondary/50 transition-colors"
              >
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span className="text-sm">教育咨询</span>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
