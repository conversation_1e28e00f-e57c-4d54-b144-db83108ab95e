"use client";

import { useState, useEffect } from "react";
import { PostDetail } from "@/components/posts";
import { Button } from "@workspace/ui/components/button";
import { Card, CardContent } from "@workspace/ui/components/card";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { ArrowLeft, AlertCircle } from "lucide-react";
import Link from "next/link";
import { notFound, useParams } from "next/navigation";
import { Post, Comment } from "@/lib/post/types";

export default function PostPage() {
  const { id } = useParams<{ id: string }>();
  const [post, setPost] = useState<Post | null>(null);
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 验证 ID 格式
  if (!id || id.length < 1) {
    notFound();
  }

  // 获取帖子数据
  useEffect(() => {
    const fetchPost = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/posts/${id}?includeComments=true`);
        const result = await response.json();

        if (!response.ok) {
          if (response.status === 404) {
            notFound();
          }
          throw new Error(result.message || "获取帖子失败");
        }

        if (result.success) {
          setPost(result.data);
          setComments(result.data.comments || []);
        } else {
          throw new Error(result.message || "获取帖子失败");
        }
      } catch (err) {
        console.error("获取帖子失败:", err);
        setError(err instanceof Error ? err.message : "获取帖子失败");
      } finally {
        setLoading(false);
      }
    };

    fetchPost();
  }, [id]);

  // 处理点赞
  const handleLike = async (postId: string) => {
    try {
      // TODO: 实现点赞功能，需要用户认证
      console.log("点赞帖子:", postId);
    } catch (error) {
      console.error("点赞失败:", error);
    }
  };

  // 处理收藏
  const handleFavorite = async (postId: string) => {
    try {
      // TODO: 实现收藏功能，需要用户认证
      console.log("收藏帖子:", postId);
    } catch (error) {
      console.error("收藏失败:", error);
    }
  };

  // 处理分享
  const handleShare = async (postId: string) => {
    try {
      const url = `${window.location.origin}/community/posts/${postId}`;
      if (navigator.share) {
        await navigator.share({
          title: post?.title,
          text: post?.summary || post?.title,
          url: url,
        });
      } else {
        // 复制到剪贴板
        await navigator.clipboard.writeText(url);
        // TODO: 显示成功提示
        console.log("链接已复制到剪贴板");
      }
    } catch (error) {
      console.error("分享失败:", error);
    }
  };

  // 加载状态
  if (loading) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6">
        {/* 返回按钮 */}
        <div className="flex items-center gap-4">
          <Link href="/community">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回社区
            </Button>
          </Link>
        </div>

        {/* 加载骨架 */}
        <div className="max-w-4xl mx-auto w-full">
          <Card>
            <CardContent className="p-6 space-y-4">
              <div className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-8 w-3/4" />
                <div className="flex items-center gap-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-3 w-40" />
                  </div>
                </div>
              </div>
              <Skeleton className="h-64 w-full" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6">
        {/* 返回按钮 */}
        <div className="flex items-center gap-4">
          <Link href="/community">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回社区
            </Button>
          </Link>
        </div>

        {/* 错误信息 */}
        <div className="max-w-4xl mx-auto w-full">
          <Card>
            <CardContent className="p-6">
              <div className="text-center space-y-4">
                <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                  <AlertCircle className="h-8 w-8 text-red-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold">加载失败</h3>
                  <p className="text-muted-foreground mt-2">{error}</p>
                </div>
                <Button onClick={() => window.location.reload()}>
                  重新加载
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // 帖子不存在
  if (!post) {
    notFound();
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6">
      {/* 返回按钮 */}
      <div className="flex items-center gap-4">
        <Link href="/community">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回社区
          </Button>
        </Link>
      </div>

      {/* 帖子详情 */}
      <div className="max-w-4xl mx-auto w-full">
        <PostDetail
          post={post}
          comments={comments}
          onLike={handleLike}
          onFavorite={handleFavorite}
          onShare={handleShare}
        />
      </div>
    </div>
  );
}
