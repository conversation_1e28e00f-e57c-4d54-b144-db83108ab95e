import { CreatePost } from "@/components/posts";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from "@workspace/ui/components/card";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

export default async function CreatePostPage() {
  return (
    <div className="flex flex-1 flex-col gap-6 p-6">
      {/* 页面标题 */}
      <div className="flex items-center gap-4">
        <Link href="/community">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回社区
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">发布帖子</h1>
          <p className="text-muted-foreground">
            分享您的经验和见解，与社区成员一起成长
          </p>
        </div>
      </div>

      {/* 创建帖子表单 */}
      <div className="max-w-4xl mx-auto w-full">
        <Card>
          <CardHeader>
            <CardTitle>创建新帖子</CardTitle>
          </CardHeader>
          <CardContent>
            <CreatePost />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
