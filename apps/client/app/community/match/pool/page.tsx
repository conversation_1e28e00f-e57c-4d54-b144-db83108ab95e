"use client";

import {
  <PERSON><PERSON><PERSON>,
  Clock,
  DollarSign,
  Filter,
  MapPin,
  Plus,
  Search,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { Alert, AlertDescription } from "@workspace/ui/components/alert";
import { Badge } from "@workspace/ui/components/badge";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Input } from "@workspace/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Skeleton } from "@workspace/ui/components/skeleton";

import { MatchCard } from "@/components/match/common/MatchCard";
import { TenantReminder } from "@/components/tenant-reminder";
import { cn } from "@workspace/ui/lib/utils";

import {
  PlannerMatchProfileForm,
  ProfileFormData,
} from "@/components/match/PlannerMatchProfileForm";
import {
  CATEGORY_CONFIG,
  GRADE_CONFIG,
  LOCATION_CONFIG,
  SUBJECT_CONFIG,
  formatDate,
  getUrgencyColor,
} from "@/constants/match-constants";
import { useClientUser } from "@/hooks/useClientUser";
import { MatchMode, RequirementCategory } from "@/types/match.types";
import { apiClient } from "@/utils/api";
import { toast } from "sonner";

interface PoolRequest {
  id: string;
  title: string;
  description: string;
  category: RequirementCategory;
  requirements: {
    grade: string;
    subjects: string[];
    location: string;
    budget?: number;
  };
  urgency: number;
  createdAt: string;
  expiredAt?: string;
  requester: {
    id: string;
    name: string;
    avatar?: string;
  };
}

interface PoolFilters {
  category?: RequirementCategory;
  grade?: string;
  subjects?: string[];
  location?: string;
  minBudget?: number;
  maxBudget?: number;
  urgency?: number;
}

export default function MatchPoolPage() {
  const [requests, setRequests] = useState<PoolRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState<PoolFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [showProfileDialog, setShowProfileDialog] = useState(false);

  const { user, currentTenant } = useClientUser();
  const router = useRouter();

  useEffect(() => {
    fetchPoolRequests();
  }, []);

  const handleCreateRequest = () => {
    router.push(`/community/match/create?matchType=${MatchMode.PUBLIC_POOL}`);
  };

  const fetchPoolRequests = async () => {
    if (!user?.id || !currentTenant?.id) {
      return toast.error("请先登录或选择租户");
    }

    try {
      setLoading(true);
      const queryParams = new URLSearchParams({
        tenantUserId: currentTenant!.id,
        page: "1",
        pageSize: "20",
      });

      const result = await apiClient.request(
        `/api/match/pool?${queryParams.toString()}`,
      );

      if (result.success) {
        setRequests(result.data.items || []);
      } else {
        // 检查是否是配置问题
        if (
          result.message?.includes("配置") ||
          result.message?.includes("profile")
        ) {
          setShowProfileDialog(true);
        } else {
          setError(result.message || "获取抢单池失败");
        }
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const handleRequestClick = (requestId: string) => {
    router.push(`/community/match/pool/${requestId}`);
  };

  // 前端筛选逻辑
  const filteredRequests = requests.filter((request) => {
    // 搜索过滤
    const matchesSearch =
      searchTerm === "" ||
      request.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.description?.toLowerCase().includes(searchTerm.toLowerCase());

    // 分类筛选
    const matchesCategory =
      !filters.category || request.category === filters.category;

    // 年级筛选
    const matchesGrade =
      !filters.grade || request.requirements.grade === filters.grade;

    // 地区筛选
    const matchesLocation =
      !filters.location || request.requirements.location === filters.location;

    // 预算筛选
    const matchesBudget =
      (!filters.minBudget ||
        !request.requirements.budget ||
        request.requirements.budget >= filters.minBudget) &&
      (!filters.maxBudget ||
        !request.requirements.budget ||
        request.requirements.budget <= filters.maxBudget);

    // 紧急度筛选
    const matchesUrgency =
      !filters.urgency || request.urgency >= filters.urgency;

    // 科目筛选
    const matchesSubjects =
      !filters.subjects?.length ||
      filters.subjects.some((subject) =>
        request.requirements.subjects.includes(subject),
      );

    return (
      matchesSearch &&
      matchesCategory &&
      matchesGrade &&
      matchesLocation &&
      matchesBudget &&
      matchesUrgency &&
      matchesSubjects
    );
  });

  const handleFilterChange = (key: keyof PoolFilters, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({});
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid gap-4">
          {[1, 2, 3, 4, 5].map((i) => (
            <Skeleton key={i} className="h-48 w-full" />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 租户检查提醒 */}
      <TenantReminder 
        required={false}
        message="请选择一个工作空间以查看对应的抢单池内容"
        banner={true}
      />

      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">公开抢单池</h1>
          <p className="text-muted-foreground">查看并响应公开的需求匹配请求</p>
        </div>
        <div>
          <Button onClick={handleCreateRequest} className="mr-2">
            <Plus className="h-4 w-4" />
            创建请求
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="gap-2"
          >
            <Filter className="h-4 w-4" />
            筛选
          </Button>
        </div>
      </div>

      {/* 搜索栏和筛选状态 */}
      <div className="space-y-4">
        <div className="flex gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索需求标题或描述..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* 活跃筛选条件指示器 */}
        {(searchTerm ||
          Object.keys(filters).some(
            (key) => filters[key as keyof PoolFilters],
          )) && (
          <div className="flex items-center gap-2 flex-wrap">
            <span className="text-sm text-muted-foreground">活跃筛选:</span>
            {searchTerm && (
              <Badge variant="secondary" className="gap-1">
                搜索: "{searchTerm}"
                <button
                  onClick={() => setSearchTerm("")}
                  className="ml-1 hover:bg-gray-300 rounded-full w-4 h-4 flex items-center justify-center text-xs"
                >
                  ×
                </button>
              </Badge>
            )}
            {filters.category && (
              <Badge variant="secondary" className="gap-1">
                {CATEGORY_CONFIG[filters.category]?.label}
                <button
                  onClick={() => handleFilterChange("category", undefined)}
                  className="ml-1 hover:bg-gray-300 rounded-full w-4 h-4 flex items-center justify-center text-xs"
                >
                  ×
                </button>
              </Badge>
            )}
            {filters.grade && (
              <Badge variant="secondary" className="gap-1">
                {
                  GRADE_CONFIG[filters.grade as keyof typeof GRADE_CONFIG]
                    ?.label
                }
                <button
                  onClick={() => handleFilterChange("grade", undefined)}
                  className="ml-1 hover:bg-gray-300 rounded-full w-4 h-4 flex items-center justify-center text-xs"
                >
                  ×
                </button>
              </Badge>
            )}
            {filters.location && (
              <Badge variant="secondary" className="gap-1">
                {
                  LOCATION_CONFIG[
                    filters.location as keyof typeof LOCATION_CONFIG
                  ]?.label
                }
                <button
                  onClick={() => handleFilterChange("location", undefined)}
                  className="ml-1 hover:bg-gray-300 rounded-full w-4 h-4 flex items-center justify-center text-xs"
                >
                  ×
                </button>
              </Badge>
            )}
            {filters.subjects?.length && (
              <Badge variant="secondary" className="gap-1">
                {
                  SUBJECT_CONFIG[
                    filters.subjects[0] as keyof typeof SUBJECT_CONFIG
                  ]?.label
                }
                <button
                  onClick={() => handleFilterChange("subjects", undefined)}
                  className="ml-1 hover:bg-gray-300 rounded-full w-4 h-4 flex items-center justify-center text-xs"
                >
                  ×
                </button>
              </Badge>
            )}
            {filters.urgency && (
              <Badge variant="secondary" className="gap-1">
                紧急度 {filters.urgency}+
                <button
                  onClick={() => handleFilterChange("urgency", undefined)}
                  className="ml-1 hover:bg-gray-300 rounded-full w-4 h-4 flex items-center justify-center text-xs"
                >
                  ×
                </button>
              </Badge>
            )}
            {(filters.minBudget || filters.maxBudget) && (
              <Badge variant="secondary" className="gap-1">
                预算: {filters.minBudget || 0}-{filters.maxBudget || "∞"}
                <button
                  onClick={() => {
                    handleFilterChange("minBudget", undefined);
                    handleFilterChange("maxBudget", undefined);
                  }}
                  className="ml-1 hover:bg-gray-300 rounded-full w-4 h-4 flex items-center justify-center text-xs"
                >
                  ×
                </button>
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSearchTerm("");
                clearFilters();
              }}
              className="text-xs"
            >
              清除全部
            </Button>
          </div>
        )}

        {/* 筛选结果统计 */}
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>
            共找到{" "}
            <span className="font-medium text-foreground">
              {filteredRequests.length}
            </span>{" "}
            个需求
            {filteredRequests.length !== requests.length && (
              <span> (共 {requests.length} 个)</span>
            )}
          </span>
        </div>
      </div>

      {/* 筛选面板 */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle>筛选条件</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* 服务分类 */}
              <div>
                <label className="text-sm font-medium mb-2 block">
                  服务分类
                </label>
                <Select
                  value={filters.category || ""}
                  onValueChange={(value) =>
                    handleFilterChange("category", value || undefined)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择分类" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(CATEGORY_CONFIG).map(([key, config]) => (
                      <SelectItem key={key} value={key}>
                        {config.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* 年级 */}
              <div>
                <label className="text-sm font-medium mb-2 block">年级</label>
                <Select
                  value={filters.grade || ""}
                  onValueChange={(value) =>
                    handleFilterChange("grade", value || undefined)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择年级" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(GRADE_CONFIG).map(([key, config]) => (
                      <SelectItem key={key} value={key}>
                        {config.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* 地区 */}
              <div>
                <label className="text-sm font-medium mb-2 block">地区</label>
                <Select
                  value={filters.location || ""}
                  onValueChange={(value) =>
                    handleFilterChange("location", value || undefined)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择地区" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(LOCATION_CONFIG).map(([key, config]) => (
                      <SelectItem key={key} value={key}>
                        {config.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* 科目筛选 */}
              <div>
                <label className="text-sm font-medium mb-2 block">科目</label>
                <Select
                  value={filters.subjects?.[0] || ""}
                  onValueChange={(value) =>
                    handleFilterChange("subjects", value ? [value] : undefined)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择科目" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(SUBJECT_CONFIG).map(([key, config]) => (
                      <SelectItem key={key} value={key}>
                        {config.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* 紧急度筛选 */}
              <div>
                <label className="text-sm font-medium mb-2 block">
                  最低紧急度
                </label>
                <Select
                  value={filters.urgency?.toString() || ""}
                  onValueChange={(value) =>
                    handleFilterChange(
                      "urgency",
                      value ? parseInt(value) : undefined,
                    )
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择紧急度" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">紧急度 1+</SelectItem>
                    <SelectItem value="2">紧急度 2+</SelectItem>
                    <SelectItem value="3">紧急度 3+</SelectItem>
                    <SelectItem value="4">紧急度 4+</SelectItem>
                    <SelectItem value="5">紧急度 5</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 预算范围 */}
              <div>
                <label className="text-sm font-medium mb-2 block">
                  预算范围
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    type="number"
                    placeholder="最低"
                    value={filters.minBudget || ""}
                    onChange={(e) =>
                      handleFilterChange(
                        "minBudget",
                        e.target.value ? parseInt(e.target.value) : undefined,
                      )
                    }
                  />
                  <Input
                    type="number"
                    placeholder="最高"
                    value={filters.maxBudget || ""}
                    onChange={(e) =>
                      handleFilterChange(
                        "maxBudget",
                        e.target.value ? parseInt(e.target.value) : undefined,
                      )
                    }
                  />
                </div>
              </div>
            </div>

            <div className="flex gap-2 mt-4">
              <Button variant="outline" onClick={clearFilters}>
                清除筛选
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 请求列表 */}
      <div className="space-y-4">
        {filteredRequests.length === 0 ? (
          <Card>
            <CardContent className="py-8 text-center">
              <p className="text-muted-foreground">暂无匹配的需求</p>
            </CardContent>
          </Card>
        ) : (
          filteredRequests.map((request) => {
            const categoryConfig = CATEGORY_CONFIG[request.category];
            const CategoryIcon = categoryConfig?.icon;

            return (
              <MatchCard
                key={request.id}
                title={request.title}
                description={request.description?.length > 200
                  ? `${request.description?.substring(0, 200)}...`
                  : request.description}
                avatar={{
                  src: request.requester?.avatar,
                  fallback: request.requester?.name?.charAt(0) || "?",
                }}
                badges={[
                  {
                    text: `紧急度 ${request.urgency}`,
                    className: getUrgencyColor(request.urgency),
                  },
                  {
                    text: categoryConfig?.label || request.category,
                    variant: "outline",
                  },
                ]}
                metadata={[
                  {
                    label: "年级",
                    value: GRADE_CONFIG[
                      request.requirements?.grade as keyof typeof GRADE_CONFIG
                    ]?.label || request.requirements?.grade,
                    icon: <BookOpen className="h-4 w-4" />,
                  },
                  {
                    label: "地区",
                    value: LOCATION_CONFIG[
                      request.requirements?.location as keyof typeof LOCATION_CONFIG
                    ]?.label || request.requirements?.location,
                    icon: <MapPin className="h-4 w-4" />,
                  },
                  ...(request.requirements?.budget
                    ? [
                        {
                          label: "预算",
                          value: `¥${request.requirements.budget}`,
                          icon: <DollarSign className="h-4 w-4" />,
                        },
                      ]
                    : []),
                  {
                    label: "发布时间",
                    value: formatDate(request.createdAt, {
                      month: "short",
                      day: "numeric",
                    }),
                    icon: <Clock className="h-4 w-4" />,
                  },
                ]}
                onClick={() => handleRequestClick(request.id)}
              >
                {CategoryIcon && (
                  <div className="flex items-center gap-2 mb-2">
                    <div
                      className={cn(
                        "w-6 h-6 rounded-lg flex items-center justify-center",
                        categoryConfig.color,
                      )}
                    >
                      <CategoryIcon className="h-3 w-3 text-white" />
                    </div>
                    <span className="text-sm font-medium">{categoryConfig.label}</span>
                  </div>
                )}

                {request.requirements?.subjects?.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {request.requirements.subjects.map((subject) => (
                      <Badge
                        key={subject}
                        variant="outline"
                        className="text-xs"
                      >
                        {SUBJECT_CONFIG[subject as keyof typeof SUBJECT_CONFIG]
                          ?.label || subject}
                      </Badge>
                    ))}
                  </div>
                )}
              </MatchCard>
            );
          })
        )}
      </div>

      {/* 配置确认弹窗 */}
      <Dialog open={showProfileDialog} onOpenChange={setShowProfileDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>完善匹配配置</DialogTitle>
            <DialogDescription>
              您需要完善匹配配置才能查看和响应抢单池中的请求。请完成以下配置：
            </DialogDescription>
          </DialogHeader>
          <div className="mt-4">
            <PlannerMatchProfileForm
              plannerId={user?.id}
              mode="confirm"
              onSave={(data: ProfileFormData) => {
                setShowProfileDialog(false);
                fetchPoolRequests();
              }}
              onCancel={() => setShowProfileDialog(false)}
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
