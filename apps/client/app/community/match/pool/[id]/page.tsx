"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useParams } from "next/navigation";
import { ArrowLeft, Clock, MapPin, BookOpen, DollarSign, User, Send } from "lucide-react";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Alert, AlertDescription } from "@workspace/ui/components/alert";
import { Avatar, AvatarFallback, AvatarImage } from "@workspace/ui/components/avatar";
import { Separator } from "@workspace/ui/components/separator";

import { apiClient } from "@/utils/api";
import { useClientUser } from "@/hooks/useClientUser";
import { 
  CATEGORY_CONFIG, 
  GRADE_CONFIG, 
  SUBJECT_CONFIG, 
  LOCATION_CONFIG,
  formatDate,
  getUrgencyColor 
} from "@/constants/match-constants";
import { RequirementCategory } from "@/types/match.types";

interface PoolRequestDetail {
  id: string;
  title: string;
  description: string;
  category: RequirementCategory;
  requirements: {
    grade: string;
    subjects: string[];
    location: string;
    budget?: number;
    additionalInfo?: string;
  };
  urgency: number;
  createdAt: string;
  expiredAt?: string;
  requester: {
    id: string;
    name: string;
    avatar?: string;
  };
}

export default function PoolRequestDetailPage() {
  const [request, setRequest] = useState<PoolRequestDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [responseMessage, setResponseMessage] = useState("");
  const [responding, setResponding] = useState(false);
  const [responseError, setResponseError] = useState<string | null>(null);
  const [hasResponded, setHasResponded] = useState(false);
  
  const { user } = useClientUser();
  const router = useRouter();
  const params = useParams();
  const requestId = params?.id as string;

  useEffect(() => {
    if (requestId) {
      fetchRequestDetail();
    }
  }, [requestId]);

  const fetchRequestDetail = async () => {
    try {
      setLoading(true);
      const result = await apiClient.request(`/api/match/pool/${requestId}`);

      if (result.success) {
        setRequest(result.data);
        setHasResponded(result.data.hasResponded || false);
      } else {
        setError(result.message || "获取需求详情失败");
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const handleResponse = async () => {
    if (!responseMessage.trim()) {
      setResponseError("请填写响应消息");
      return;
    }

    try {
      setResponding(true);
      setResponseError(null);

      const result = await apiClient.request(`/api/match/pool/${requestId}/respond`, {
        method: "POST",
        body: JSON.stringify({
          message: responseMessage,
          plannerId: user?.id,
        }),
      });

      if (result.success) {
        setHasResponded(true);
        setResponseMessage("");
        // 可以显示成功提示
      } else {
        setResponseError(result.message || "响应失败");
      }
    } catch (err) {
      setResponseError("网络错误，请稍后重试");
    } finally {
      setResponding(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-64 w-full" />
        <Skeleton className="h-48 w-full" />
      </div>
    );
  }

  if (error || !request) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertDescription>{error || "需求不存在"}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 导航 */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.back()}
          className="gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          返回
        </Button>
        <div className="text-sm text-muted-foreground">
          公开抢单池 / 需求详情
        </div>
      </div>

      {/* 需求详情 */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-xl">{request.title}</CardTitle>
              <CardDescription className="mt-2">
                发布于 {formatDate(request.createdAt)}
                {request.expiredAt && ` · 过期时间: ${formatDate(request.expiredAt)}`}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge
                className={getUrgencyColor(request.urgency)}
                variant="secondary"
              >
                紧急度 {request.urgency}
              </Badge>
              <Badge variant="outline">
                {CATEGORY_CONFIG[request.category]?.label}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 需求描述 */}
          <div>
            <h3 className="font-medium mb-2">需求描述</h3>
            <p className="text-sm text-muted-foreground leading-relaxed">
              {request.description}
            </p>
          </div>

          <Separator />

          {/* 基本信息 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-3">
              <BookOpen className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">年级</p>
                <p className="text-sm text-muted-foreground">
                  {GRADE_CONFIG[request.requirements.grade as keyof typeof GRADE_CONFIG]?.label}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <MapPin className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">地区</p>
                <p className="text-sm text-muted-foreground">
                  {LOCATION_CONFIG[request.requirements.location as keyof typeof LOCATION_CONFIG]?.label}
                </p>
              </div>
            </div>

            {request.requirements.budget && (
              <div className="flex items-center gap-3">
                <DollarSign className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">预算</p>
                  <p className="text-sm text-muted-foreground">
                    ¥{request.requirements.budget}
                  </p>
                </div>
              </div>
            )}

            <div className="flex items-center gap-3">
              <Clock className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">发布时间</p>
                <p className="text-sm text-muted-foreground">
                  {formatDate(request.createdAt)}
                </p>
              </div>
            </div>
          </div>

          {/* 相关科目 */}
          {request.requirements.subjects.length > 0 && (
            <div>
              <h3 className="font-medium mb-2">相关科目</h3>
              <div className="flex flex-wrap gap-2">
                {request.requirements.subjects.map((subject) => (
                  <Badge key={subject} variant="secondary">
                    {SUBJECT_CONFIG[subject as keyof typeof SUBJECT_CONFIG]?.label || subject}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* 补充信息 */}
          {request.requirements.additionalInfo && (
            <div>
              <h3 className="font-medium mb-2">补充信息</h3>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {request.requirements.additionalInfo}
              </p>
            </div>
          )}

          <Separator />

          {/* 需求方信息 */}
          <div>
            <h3 className="font-medium mb-3">需求方信息</h3>
            <div className="flex items-center gap-3">
              <Avatar>
                <AvatarImage src={request.requester.avatar} />
                <AvatarFallback>
                  <User className="h-4 w-4" />
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">{request.requester.name}</p>
                <p className="text-sm text-muted-foreground">需求发布者</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 响应区域 */}
      {!hasResponded ? (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">响应需求</CardTitle>
            <CardDescription>
              向需求方发送您的响应消息，介绍您的能力和服务
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {responseError && (
              <Alert variant="destructive">
                <AlertDescription>{responseError}</AlertDescription>
              </Alert>
            )}
            
            <div>
              <label className="text-sm font-medium mb-2 block">
                响应消息 *
              </label>
              <Textarea
                placeholder="请介绍您的相关经验、能力和服务方案..."
                value={responseMessage}
                onChange={(e) => setResponseMessage(e.target.value)}
                className="min-h-[120px]"
              />
            </div>

            <div className="flex gap-2">
              <Button
                onClick={handleResponse}
                disabled={responding || !responseMessage.trim()}
                className="gap-2"
              >
                <Send className="h-4 w-4" />
                {responding ? "发送中..." : "发送响应"}
              </Button>
              <Button
                variant="outline"
                onClick={() => router.back()}
                disabled={responding}
              >
                取消
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="py-8 text-center">
            <div className="text-green-600 mb-2">
              <Send className="h-8 w-8 mx-auto" />
            </div>
            <p className="font-medium">已响应此需求</p>
            <p className="text-sm text-muted-foreground">
              您的响应已发送给需求方，请耐心等待回复
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

