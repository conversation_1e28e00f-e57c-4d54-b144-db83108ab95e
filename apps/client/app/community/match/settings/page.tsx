"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, User, Bell, Shield } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  <PERSON><PERSON>,
  <PERSON>bs<PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import { Badge } from "@workspace/ui/components/badge";

import {
  PlannerMatchProfileForm,
  ProfileFormData,
} from "@/components/match/PlannerMatchProfileForm";
import { apiClient } from "@/utils/api";
import { useClientUser } from "@/contexts/UserContext";

export default function PlannerSettingsPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("profile");
  const { currentTenant, user } = useClientUser();

  const handleProfileSave = (data: ProfileFormData) => {
    toast.success("匹配配置已保存");
    // 可以在这里添加额外的逻辑
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <div className="container mx-auto px-4 py-6">
      {/* 页面头部 */}
      <div className="mb-6">
        <Button variant="ghost" onClick={handleBack} className="mb-4 gap-2">
          <ArrowLeft className="h-4 w-4" />
          返回
        </Button>

        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <Settings className="h-8 w-8" />
              规划师设置
            </h1>
            <p className="text-muted-foreground mt-2">
              管理您的个人信息和匹配偏好设置
            </p>
          </div>

          {currentTenant && user && (
            <Card className="p-4">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                  <User className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="font-medium">
                    {(currentTenant as any).tenant.contactName || "规划师"}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {(currentTenant as any).tenant.contactEmail}
                  </p>
                </div>
                <Badge variant="secondary">
                  {currentTenant.role === "PLANNER"
                    ? "规划师"
                    : currentTenant.role === "TENANT_ADMIN"
                      ? "机构管理员"
                      : "用户"}
                </Badge>
              </div>
            </Card>
          )}
        </div>
      </div>

      {/* 设置内容 */}
      <div className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="profile" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              <span className="hidden sm:inline">个人资料</span>
            </TabsTrigger>
            <TabsTrigger value="match" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              <span className="hidden sm:inline">匹配设置</span>
            </TabsTrigger>
            <TabsTrigger
              value="notifications"
              className="flex items-center gap-2"
            >
              <Bell className="h-4 w-4" />
              <span className="hidden sm:inline">通知设置</span>
            </TabsTrigger>
            <TabsTrigger value="security" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              <span className="hidden sm:inline">安全设置</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>个人资料</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center text-muted-foreground py-8">
                    <User className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
                    <p>个人资料编辑功能即将开放</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="match" className="space-y-6">
            <PlannerMatchProfileForm
              plannerId={(currentTenant as any)?.planner?.id}
              mode="full"
              onSave={handleProfileSave}
            />
          </TabsContent>

          <TabsContent value="notifications" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>通知设置</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center text-muted-foreground py-8">
                    <Bell className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
                    <p>通知设置功能即将开放</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>安全设置</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center text-muted-foreground py-8">
                    <Shield className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
                    <p>安全设置功能即将开放</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
