"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Plus } from "lucide-react";

import { But<PERSON> } from "@workspace/ui/components/button";
import { Skeleton } from "@workspace/ui/components/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";

import { RequirementCategorySelector } from "@/components/match/RequirementCategorySelector";
import { MatchFilterBar } from "@/components/match/MatchFilterBar";
import { MatchRequestCard } from "@/components/match/common/MatchCard";
import { TenantReminder } from "@/components/tenant-reminder";
import { MatchPagination } from "@/components/match/common/MatchPagination";
import { DataEmpty } from "@/components/match/common/MatchEmptyState";

import { useMatchRequests } from "@/hooks/useMatchData";
import {
  RequirementCategory,
  MatchRequestStatus,
  type MatchFilters,
  MatchMode,
} from "@/types/match.types";
import { CATEGORY_CONFIG, STATUS_CONFIG } from "@/constants/match-constants";

export default function MatchRequestsPage() {
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] =
    useState<RequirementCategory>();
  const [selectedStatus, setSelectedStatus] = useState<MatchRequestStatus>();

  // 使用重构后的hook
  const {
    requests,
    loading,
    error,
    pagination,
    filters,
    totalPages,
    totalCount,
    updateFilters,
    updatePagination,
    refresh,
  } = useMatchRequests({
    category: selectedCategory,
    status: selectedStatus,
  });

  const handleFilterChange = (newFilters: Partial<MatchFilters>) => {
    updateFilters(newFilters);
  };

  const handleCategoryChange = (category?: RequirementCategory) => {
    setSelectedCategory(category);
    updateFilters({ category });
  };

  const handleStatusChange = (status?: MatchRequestStatus) => {
    setSelectedStatus(status);
    updateFilters({ status });
  };

  const handleViewDetails = (requestId: string) => {
    router.push(`/community/match/requests/${requestId}`);
  };

  const handleCreateRequest = () => {
    router.push(`/community/match/create?matchType=${MatchMode.SMART_MATCH}`);
  };

  const handleClearFilters = () => {
    setSelectedCategory(undefined);
    setSelectedStatus(undefined);
    updateFilters({
      category: undefined,
      status: undefined,
      searchQuery: "",
      subjects: [],
      location: undefined,
    });
  };

  const hasFilters = !!(
    filters.searchQuery ||
    filters.category ||
    filters.status ||
    filters.grade ||
    filters.subjects?.length ||
    filters.location
  );

  return (
    <div className="container mx-auto px-4 py-6">
      {/* 租户检查提醒 */}
      <TenantReminder 
        required={false}
        message="请选择一个工作空间以查看对应的匹配请求"
        banner={true}
      />

      {/* 页面头部 */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold">匹配请求</h1>
          <p className="text-muted-foreground">查看和管理所有匹配请求</p>
        </div>
        <Button onClick={handleCreateRequest} className="gap-2">
          <Plus className="h-4 w-4" />
          创建请求
        </Button>
      </div>

      {/* 筛选器 */}
      <div className="space-y-4 mb-6">
        <MatchFilterBar
          onFilterChange={handleFilterChange}
          initialFilters={filters}
          showGrade={true}
          searchPlaceholder="搜索匹配请求..."
        />

        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">分类:</span>
            <RequirementCategorySelector
              value={selectedCategory}
              onValueChange={handleCategoryChange}
              placeholder="所有分类"
              size="sm"
              className="w-48"
            />
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">状态:</span>
            <Select
              value={selectedStatus || ""}
              onValueChange={(value) =>
                handleStatusChange((value as MatchRequestStatus) || undefined)
              }
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="所有状态" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(STATUS_CONFIG).map(([status, config]) => (
                  <SelectItem key={status} value={status}>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${config.color}`} />
                      <span>{config.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {hasFilters && (
            <Button variant="outline" size="sm" onClick={handleClearFilters}>
              清除筛选
            </Button>
          )}
        </div>
      </div>

      {/* 统计信息 */}
      {!loading && (
        <div className="mb-4 text-sm text-muted-foreground">
          共找到 {totalCount} 个匹配请求
        </div>
      )}

      {/* 请求列表 */}
      {loading ? (
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="p-4 border rounded-lg">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-3">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-5 w-48" />
                    <Skeleton className="h-4 w-32" />
                  </div>
                </div>
                <Skeleton className="h-6 w-20" />
              </div>
              <Skeleton className="h-16 w-full" />
            </div>
          ))}
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={refresh} variant="outline">
            重试
          </Button>
        </div>
      ) : requests.length > 0 ? (
        <div className="space-y-4">
          {requests.map((request) => {
            const categoryConfig =
              CATEGORY_CONFIG[request.category as RequirementCategory];
            const statusConfig =
              STATUS_CONFIG[request.status as MatchRequestStatus];

            return (
              <MatchRequestCard
                key={request.id}
                request={request}
                categoryConfig={categoryConfig}
                statusConfig={statusConfig}
                onViewDetails={handleViewDetails}
                actions={
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewDetails(request.id)}
                  >
                    查看详情
                  </Button>
                }
              />
            );
          })}
        </div>
      ) : (
        <DataEmpty
          type="requests"
          searchQuery={filters.searchQuery}
          hasFilters={hasFilters}
          onClearFilters={handleClearFilters}
          onCreateNew={handleCreateRequest}
        />
      )}

      {/* 分页 */}
      {totalPages > 1 && (
        <div className="mt-6">
          <MatchPagination
            currentPage={pagination.page}
            totalPages={totalPages}
            totalCount={totalCount}
            pageSize={pagination.pageSize}
            onPageChange={(page) => updatePagination({ page })}
            onPageSizeChange={(pageSize) =>
              updatePagination({ pageSize, page: 1 })
            }
          />
        </div>
      )}
    </div>
  );
}
