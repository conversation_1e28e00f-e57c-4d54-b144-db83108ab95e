"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import {
  <PERSON>Left,
  Share2,
  MoreHorizontal,
  MapPin,
  DollarSign,
  AlertCircle,
  CheckCircle,
  Users,
  MessageCircle,
  TrendingUp,
} from "lucide-react";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { Skeleton } from "@workspace/ui/components/skeleton";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";

import { MatchProgressTracker } from "@/components/match/progress/MatchProgressTracker";
import { SmartMatchRecommendations } from "@/components/match/SmartMatchRecommendations";
import {
  MatchStatusBadge,
  UrgencyIndicator,
} from "@/components/match/common/MatchStatus";

import { useMatchRequest } from "@/hooks/useMatchData";
import { MatchRequestStatus } from "@/types/match.types";
import {
  CATEGORY_CONFIG,
  getGradeLabel,
  getLocationLabel,
  getSubjectLabels,
} from "@/constants/match-constants";
import { formatDate, formatRelativeTime } from "@/utils/match.utils";
import { toast } from "sonner";
import { apiClient } from "@/utils/api";

export default function MatchRequestDetailPage() {
  const { id } = useParams<{ id: string }>();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("overview");

  const { request, loading, error, refresh } = useMatchRequest(id);

  const handleBack = () => {
    router.back();
  };

  const handleShare = () => {
    // 实现分享功能
    navigator.share?.({
      title: request?.requirements?.title,
      text: request?.requirements?.description,
      url: window.location.href,
    });
  };

  const handleCancel = async () => {
    const { success, error, message } = await apiClient.request(
      `/api/match/cancel/${id}`,
      {
        method: "PUT",
        body: JSON.stringify({ requesterId: request?.requesterId }),
      },
    );
    success && toast.success(message || "取消请求成功");
    success && router.push("/community/match/requests");
    error && toast.error(message || "取消请求失败");
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* 头部骨架屏 */}
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-10" />
          <div className="flex-1">
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-32" />
          </div>
        </div>

        {/* 内容骨架屏 */}
        <div className="grid gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent className="space-y-4">
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </CardContent>
            </Card>
          </div>
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-32 w-full" />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (error || !request) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">加载失败</h2>
          <p className="text-muted-foreground mb-4">
            {error || "无法找到该匹配请求"}
          </p>
          <div className="flex items-center justify-center gap-2">
            <Button onClick={handleBack} variant="outline">
              返回
            </Button>
            <Button onClick={refresh}>重试</Button>
          </div>
        </div>
      </div>
    );
  }

  const categoryConfig =
    CATEGORY_CONFIG[request.category as keyof typeof CATEGORY_CONFIG];
  const CategoryIcon = categoryConfig.icon;

  return (
    <div className="container mx-auto px-4 py-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleBack}
            className="gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            返回
          </Button>

          <div>
            <h1 className="text-2xl font-bold">
              {request.requirements?.title}
            </h1>
            <div className="flex items-center gap-2 mt-1">
              <span className="text-sm text-muted-foreground">
                创建于 {formatRelativeTime(request.createdAt)}
              </span>
              <MatchStatusBadge status={request.status} />
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleShare}
            className="gap-2"
          >
            <Share2 className="h-4 w-4" />
            分享
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {/* <DropdownMenuItem onClick={handleEdit}> */}
              {/*   <Edit className="h-4 w-4 mr-2" /> */}
              {/*   编辑请求 */}
              {/* </DropdownMenuItem> */}
              <DropdownMenuItem
                onClick={handleCancel}
                className="text-red-600"
                disabled={[
                  MatchRequestStatus.CANCELLED,
                  MatchRequestStatus.CONFIRMED,
                ].includes(request.status)}
              >
                <AlertCircle className="h-4 w-4 mr-2" />
                取消请求
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* 左侧主要内容 */}
        <div className="lg:col-span-2">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">概览</TabsTrigger>
              <TabsTrigger value="recommendations">推荐</TabsTrigger>
              <TabsTrigger value="history">历史</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* 需求详情 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <div
                      className={`w-8 h-8 rounded-lg flex items-center justify-center ${categoryConfig.color}`}
                    >
                      <CategoryIcon className="h-4 w-4 text-white" />
                    </div>
                    需求详情
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="font-medium mb-2">需求描述</h3>
                    <p className="text-muted-foreground leading-relaxed">
                      {request.requirements?.description}
                    </p>
                  </div>

                  <div className="grid gap-4 sm:grid-cols-2">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        年级: {getGradeLabel(request.requirements?.grade)}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        地区: {getLocationLabel(request.requirements?.location)}
                      </span>
                    </div>
                    {request.requirements?.budget && (
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">
                          预算: ¥{request.requirements?.budget}
                        </span>
                      </div>
                    )}
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-muted-foreground" />
                      <UrgencyIndicator urgency={request.urgency} />
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">相关科目</h4>
                    <div className="flex flex-wrap gap-2">
                      {getSubjectLabels(request.requirements?.subjects).map(
                        (subjectLabel, index) => (
                          <Badge
                            key={request.requirements?.subjects[index]}
                            variant="secondary"
                          >
                            {subjectLabel}
                          </Badge>
                        ),
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 匹配统计 */}
              {request._count && (
                <Card>
                  <CardHeader>
                    <CardTitle>匹配统计</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 sm:grid-cols-3">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">
                          {request._count.responses || 0}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          规划师响应
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {request._count.views || 0}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          查看次数
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">
                          {Math.floor(Math.random() * 50) + 10}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          匹配度评分
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="recommendations">
              <SmartMatchRecommendations
                matchRequestId={id}
                onPlannerSelect={(plannerId) => {
                  toast.success("已选择规划师", {
                    description: "正在为您安排匹配，请稍候...",
                  });
                  refresh();
                }}
                onContactPlanner={(plannerId) => {
                  toast.info("联系功能", {
                    description: "即将开放规划师直接联系功能",
                  });
                }}
              />
            </TabsContent>

            <TabsContent value="history">
              <Card>
                <CardHeader>
                  <CardTitle>状态历史</CardTitle>
                  <CardDescription>查看请求的状态变更历史</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <div className="flex-1">
                        <div className="font-medium">请求已创建</div>
                        <div className="text-sm text-muted-foreground">
                          {formatDate(request.createdAt)}
                        </div>
                      </div>
                    </div>
                    {/* 这里可以添加更多历史记录 */}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* 右侧边栏 */}
        <div className="space-y-6">
          {/* 匹配进度 */}
          {[
            MatchRequestStatus.PENDING,
            MatchRequestStatus.MATCHING,
            MatchRequestStatus.MATCHED,
          ].includes(request.status) && (
            <MatchProgressTracker
              requestId={id}
              currentStatus={request.status}
              onRefresh={refresh}
            />
          )}

          {/* 已匹配规划师 */}
          {request.matchedPlanner && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  已匹配规划师
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-3 mb-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage
                      src={request.matchedPlanner.tenantUser.user.avatar}
                    />
                    <AvatarFallback>
                      {request.matchedPlanner.tenantUser.user.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="font-medium">
                      {request.matchedPlanner.tenantUser.user.name}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {request.matchedPlanner.title || "专业规划师"}
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <Button className="w-full gap-2">
                    <MessageCircle className="h-4 w-4" />
                    开始沟通
                  </Button>
                  <Button variant="outline" className="w-full">
                    查看详情
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 快速操作 */}
          <Card>
            <CardHeader>
              <CardTitle>快速操作</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button
                variant="outline"
                className="w-full justify-start gap-2"
                onClick={() => router.push("/community/match/planners")}
              >
                <Users className="h-4 w-4" />
                浏览所有规划师
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start gap-2"
                onClick={() => router.push("/community/match/create")}
              >
                <TrendingUp className="h-4 w-4" />
                创建新请求
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
