"use client";

import { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  TrendingUp,
  Download,
} from "lucide-react";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import { Badge } from "@workspace/ui/components/badge";

import { CategoryStatisticsDisplay } from "@/components/match/CategoryStatisticsDisplay";

export default function MatchAnalyticsPage() {
  const [selectedTenant, setSelectedTenant] = useState<string | undefined>();

  const handleExportData = () => {
    // 实现数据导出功能
    console.log("导出统计数据");
  };

  return (
    <div className="container mx-auto px-4 py-6">
      {/* 页面头部 */}
      <div className="mb-6">
        <div className="flex items-start justify-between">
          <div>
            <h1 className="text-3xl font-bold">匹配分析</h1>
            <p className="text-muted-foreground mt-2">
              查看匹配请求的分类统计和趋势分析
            </p>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={handleExportData}>
              <Download className="h-4 w-4 mr-2" />
              导出数据
            </Button>
          </div>
        </div>
      </div>

      {/* 分析面板 */}
      <Tabs defaultValue="category" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="category" className="gap-2">
            <PieChartIcon className="h-4 w-4" />
            分类统计
          </TabsTrigger>
          <TabsTrigger value="trends" className="gap-2">
            <TrendingUp className="h-4 w-4" />
            趋势分析
          </TabsTrigger>
          <TabsTrigger value="performance" className="gap-2">
            <BarChart3 className="h-4 w-4" />
            性能指标
          </TabsTrigger>
          <TabsTrigger value="insights" className="gap-2">
            <TrendingUp className="h-4 w-4" />
            智能洞察
          </TabsTrigger>
        </TabsList>

        <TabsContent value="category" className="space-y-4">
          <CategoryStatisticsDisplay
            tenantId={selectedTenant}
            timeRange="30d"
          />
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>请求量趋势</CardTitle>
                <CardDescription>各分类请求量的时间趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center text-muted-foreground">
                  趋势图表开发中...
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>成功率趋势</CardTitle>
                <CardDescription>匹配成功率的变化趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center text-muted-foreground">
                  成功率趋势图开发中...
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">平均响应时间</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">12.5 小时</div>
                <div className="text-sm text-muted-foreground">
                  比上月提升 15%
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">匹配精准度</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">87.3%</div>
                <div className="text-sm text-muted-foreground">
                  高于行业平均水平
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">用户满意度</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4.6/5.0</div>
                <div className="text-sm text-muted-foreground">
                  基于 234 条评价
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>各分类性能对比</CardTitle>
              <CardDescription>
                响应时间、成功率、满意度综合对比
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center text-muted-foreground">
                性能对比图表开发中...
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>热门需求分析</CardTitle>
                <CardDescription>当前最受欢迎的服务分类</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">1</Badge>
                    <span>学科辅导</span>
                  </div>
                  <span className="text-sm text-muted-foreground">45.2%</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">2</Badge>
                    <span>考试备考</span>
                  </div>
                  <span className="text-sm text-muted-foreground">23.1%</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">3</Badge>
                    <span>作业辅导</span>
                  </div>
                  <span className="text-sm text-muted-foreground">12.8%</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>优化建议</CardTitle>
                <CardDescription>基于数据分析的改进建议</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="font-medium text-blue-900">
                    提升心理咨询匹配率
                  </div>
                  <div className="text-sm text-blue-700">
                    该分类成功率较低，建议增加专业规划师数量
                  </div>
                </div>
                <div className="p-3 bg-green-50 rounded-lg">
                  <div className="font-medium text-green-900">
                    优化技能发展服务
                  </div>
                  <div className="text-sm text-green-700">
                    需求增长快速，可考虑扩展相关服务类型
                  </div>
                </div>
                <div className="p-3 bg-yellow-50 rounded-lg">
                  <div className="font-medium text-yellow-900">
                    关注留学咨询质量
                  </div>
                  <div className="text-sm text-yellow-700">
                    用户反馈显示需要提升服务深度和专业性
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>预测分析</CardTitle>
              <CardDescription>基于历史数据的需求预测</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center text-muted-foreground">
                预测分析图表开发中...
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
