"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { ArrowLeft } from "lucide-react";

import { Button } from "@workspace/ui/components/button";
import { toast } from "sonner";

import { MatchRequestForm } from "@/components/match/MatchRequestForm";
import { MatchFeatureGuard } from "@/components/match/MatchFeatureGuard";
import { MatchMode } from "@/types/match.types";

export default function CreateMatchRequestPage() {
  const searchParams = useSearchParams();
  const matchType = searchParams.get("matchType") as MatchMode;
  const router = useRouter();

  const handleSuccess = (data: any) => {
    toast.success("匹配请求创建成功！", {
      description: "我们将尽快为您匹配合适的规划师",
    });

    // 跳转到请求详情页面或列表页面
    router.push(`/community/match/requests/${data.id}`);
  };

  const handleCancel = () => {
    router.back();
  };

  const handleError = (message: string) => {
    toast.error("无法访问匹配功能", {
      description: message,
    });
  };

  return (
    <div className="container mx-auto px-4 py-6">
      {/* 页面头部 */}
      <div className="mb-6">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4 gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          返回
        </Button>

        <div>
          <h1 className="text-3xl font-bold">创建匹配请求</h1>
          <p className="text-muted-foreground mt-2">
            填写您的需求信息，我们将为您匹配最合适的规划师
          </p>
        </div>
      </div>

      {/* 使用守卫组件包裹匹配请求表单 */}
      <MatchFeatureGuard onError={handleError}>
        <MatchRequestForm
          matchType={matchType}
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </MatchFeatureGuard>
    </div>
  );
}
