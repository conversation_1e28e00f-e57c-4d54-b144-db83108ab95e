"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { User<PERSON>he<PERSON>, ArrowUpDown, ChevronDown } from "lucide-react";

import { Button } from "@workspace/ui/components/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Card, CardContent } from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";

import { MatchFilterBar } from "@/components/match/MatchFilterBar";
import { PlannerCard } from "@/components/match/common/MatchCard";
import { MatchPagination } from "@/components/match/common/MatchPagination";
import { DataEmpty } from "@/components/match/common/MatchEmptyState";

import { useMatchPlanners } from "@/hooks/useMatchData";
import { type MatchFilters } from "@/types/match.types";

export default function PlannersListPage() {
  const router = useRouter();
  const [sortBy, setSortBy] = useState("rating");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // 使用重构后的hook
  const {
    planners,
    loading,
    error,
    pagination,
    filters,
    totalPages,
    totalCount,
    updateFilters,
    updatePagination,
    refresh,
  } = useMatchPlanners({
    sortBy,
    sortOrder,
  });

  const handleFilterChange = (newFilters: Partial<MatchFilters>) => {
    updateFilters(newFilters);
  };

  const handleSortChange = (value: string) => {
    if (value === sortBy) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(value);
      setSortOrder("desc");
    }
    updateFilters({ sortBy: value, sortOrder: sortOrder === "asc" ? "desc" : "asc" });
  };

  const handleViewDetails = (plannerId: string) => {
    router.push(`/community/match/planner/${plannerId}`);
  };

  const handleApplyMatch = (plannerId: string) => {
    // 这里可以打开匹配申请对话框或跳转到申请页面
    console.log("申请匹配规划师:", plannerId);
  };

  const handleClearFilters = () => {
    updateFilters({
      searchQuery: "",
      subjects: [],
      location: undefined,
    } as Partial<MatchFilters>);
  };

  const hasFilters = !!(
    filters.searchQuery || 
    filters.subjects?.length || 
    filters.location
  );

  return (
    <div className="container mx-auto px-4 py-6">
      {/* 页面头部 */}
      <div className="flex flex-col gap-6 mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">规划师列表</h1>
            <p className="text-muted-foreground mt-1">浏览所有可匹配的规划师</p>
          </div>
          
          {/* 统计信息 */}
          {!loading && (
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="text-sm">
                {totalCount} 位规划师
              </Badge>
              {hasFilters && (
                <Badge variant="outline" className="text-sm">
                  已筛选
                </Badge>
              )}
            </div>
          )}
        </div>

        {/* 筛选器 */}
        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <MatchFilterBar
              onFilterChange={handleFilterChange}
              initialFilters={filters}
              showGrade={false}
              searchPlaceholder="搜索规划师..."
            />
          </CardContent>
        </Card>

        {/* 排序和操作栏 */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-2">
            {hasFilters && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearFilters}
                className="gap-2"
              >
                <ChevronDown className="h-4 w-4" />
                清除筛选
              </Button>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">排序:</span>
            <Select value={sortBy} onValueChange={handleSortChange}>
              <SelectTrigger className="w-40">
                <div className="flex items-center gap-2">
                  <ArrowUpDown className="h-4 w-4" />
                  <SelectValue placeholder="排序方式" />
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="rating">
                  <div className="flex items-center gap-2">
                    <span>⭐</span>
                    <span>评分{sortBy === "rating" && (sortOrder === "desc" ? " ↓" : " ↑")}</span>
                  </div>
                </SelectItem>
                <SelectItem value="successRate">
                  <div className="flex items-center gap-2">
                    <span>📈</span>
                    <span>成功率{sortBy === "successRate" && (sortOrder === "desc" ? " ↓" : " ↑")}</span>
                  </div>
                </SelectItem>
                <SelectItem value="experience">
                  <div className="flex items-center gap-2">
                    <span>🎓</span>
                    <span>经验年限{sortBy === "experience" && (sortOrder === "desc" ? " ↓" : " ↑")}</span>
                  </div>
                </SelectItem>
                <SelectItem value="responseTime">
                  <div className="flex items-center gap-2">
                    <span>⚡</span>
                    <span>响应时间{sortBy === "responseTime" && (sortOrder === "desc" ? " ↓" : " ↑")}</span>
                  </div>
                </SelectItem>
                <SelectItem value="students">
                  <div className="flex items-center gap-2">
                    <span>👥</span>
                    <span>服务人数{sortBy === "students" && (sortOrder === "desc" ? " ↓" : " ↑")}</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* 规划师列表 */}
      {loading ? (
        <div className="space-y-6">
          {Array.from({ length: 5 }).map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-4">
                    <Skeleton className="h-12 w-12 rounded-full border-2" />
                    <div className="space-y-2">
                      <Skeleton className="h-5 w-28" />
                      <div className="flex gap-2">
                        <Skeleton className="h-4 w-16" />
                        <Skeleton className="h-4 w-20" />
                      </div>
                      <Skeleton className="h-4 w-36" />
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-8 w-20" />
                  </div>
                </div>
                
                <div className="flex flex-wrap items-center gap-4 mb-4 text-sm">
                  <div className="flex items-center gap-1">
                    <Skeleton className="h-4 w-4" />
                    <Skeleton className="h-4 w-12" />
                    <Skeleton className="h-4 w-8" />
                  </div>
                  <div className="flex items-center gap-1">
                    <Skeleton className="h-4 w-4" />
                    <Skeleton className="h-4 w-12" />
                    <Skeleton className="h-4 w-8" />
                  </div>
                  <div className="flex items-center gap-1">
                    <Skeleton className="h-4 w-4" />
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-4 w-8" />
                  </div>
                </div>
                
                <Skeleton className="h-12 w-full mb-3" />
                
                <div className="flex flex-wrap gap-2">
                  <Skeleton className="h-6 w-16" />
                  <Skeleton className="h-6 w-20" />
                  <Skeleton className="h-6 w-14" />
                  <Skeleton className="h-6 w-18" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={refresh} variant="outline">
            重试
          </Button>
        </div>
      ) : planners.length > 0 ? (
        <div className="space-y-6">
          {planners.map((planner) => (
            <PlannerCard
              key={planner.id}
              planner={planner}
              onViewDetails={handleViewDetails}
              onApplyMatch={handleApplyMatch}
              actions={
                <Button
                  variant="default"
                  size="sm"
                  className="gap-1"
                  disabled={!planner.matchProfile?.isAcceptingMatch}
                  onClick={() => handleApplyMatch(planner.id)}
                >
                  <UserCheck className="h-4 w-4" />
                  {planner.matchProfile?.isAcceptingMatch
                    ? "申请匹配"
                    : "暂不接单"}
                </Button>
              }
            />
          ))}
        </div>
      ) : (
        <DataEmpty
          type="planners"
          searchQuery={filters.searchQuery}
          hasFilters={hasFilters}
          onClearFilters={handleClearFilters}
          onRefresh={refresh}
        />
      )}

      {/* 分页 */}
      {totalPages > 1 && (
        <div className="mt-6">
          <MatchPagination
            currentPage={pagination.page}
            totalPages={totalPages}
            totalCount={totalCount}
            pageSize={pagination.pageSize}
            onPageChange={(page) => updatePagination({ page })}
            onPageSizeChange={(pageSize) => updatePagination({ pageSize, page: 1 })}
          />
        </div>
      )}
    </div>
  );
}
