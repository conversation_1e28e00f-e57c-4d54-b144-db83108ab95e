"use client";

import { useState, useEffect } from "react";
import {
  Bar<PERSON>hart3,
  Calendar,
  Download,
  Filter,
  Pie<PERSON><PERSON> as Pie<PERSON><PERSON><PERSON><PERSON>,
  TrendingUp,
  Arrow<PERSON><PERSON>,
  ArrowDown,
  HelpCircle,
} from "lucide-react";

import { Button } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  <PERSON><PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import { Badge } from "@workspace/ui/components/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";

import { apiClient } from "@/utils/api";

// 模拟图表组件
const ChartPlaceholder = ({ height = 300, text = "图表加载中..." }) => (
  <div
    className={`w-full bg-muted/30 rounded-lg flex items-center justify-center`}
    style={{ height: `${height}px` }}
  >
    <span className="text-muted-foreground">{text}</span>
  </div>
);

// 统计卡片组件
const StatCard = ({
  title,
  value,
  change,
  changeType,
  tooltip,
  icon: Icon,
}: any) => (
  <Card>
    <CardHeader className="pb-2">
      <div className="flex items-start justify-between">
        <CardTitle className="text-sm font-medium">
          <div className="flex items-center gap-1">
            {title}
            {tooltip && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className="h-3 w-3 text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="w-60">{tooltip}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        </CardTitle>
        {Icon && <Icon className="h-4 w-4 text-muted-foreground" />}
      </div>
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      {change !== undefined && (
        <div
          className={`text-xs flex items-center ${
            changeType === "positive"
              ? "text-green-600"
              : changeType === "negative"
                ? "text-red-600"
                : "text-muted-foreground"
          }`}
        >
          {changeType === "positive" ? (
            <ArrowUp className="h-3 w-3 mr-1" />
          ) : changeType === "negative" ? (
            <ArrowDown className="h-3 w-3 mr-1" />
          ) : null}
          {change}
        </div>
      )}
    </CardContent>
  </Card>
);

export default function MatchStatisticsPage() {
  const [timeRange, setTimeRange] = useState("30d");
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalRequests: 0,
    matchedRequests: 0,
    averageResponseTime: 0,
    successRate: 0,
  });

  // 获取统计数据
  const fetchStatistics = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      // const result = await apiClient.request(`/api/match/statistics?timeRange=${timeRange}`);

      // 模拟数据
      const mockStats = {
        totalRequests: 256,
        matchedRequests: 219,
        averageResponseTime: 3.4,
        successRate: 85.5,
        categoryStats: [
          { name: "学业规划", count: 85, percentage: 33.2 },
          { name: "升学咨询", count: 64, percentage: 25.0 },
          { name: "学科辅导", count: 43, percentage: 16.8 },
          { name: "考试备考", count: 35, percentage: 13.7 },
          { name: "其他", count: 29, percentage: 11.3 },
        ],
        dailyStats: [
          // 模拟30天的数据
          ...Array.from({ length: 30 }).map((_, i) => ({
            date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000)
              .toISOString()
              .split("T")[0],
            requests: Math.floor(Math.random() * 15) + 5,
            matches: Math.floor(Math.random() * 12) + 3,
          })),
        ],
        performanceStats: {
          avgMatchTime: 2.3,
          avgResponseRate: 92.4,
          avgSatisfactionRate: 4.7,
        },
      };

      setStats(mockStats);
      setLoading(false);
    } catch (error) {
      console.error("获取统计数据失败:", error);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatistics();
  }, [timeRange]);

  const handleExportData = () => {
    // 实现数据导出功能
    console.log("导出统计数据");
  };

  return (
    <div className="container mx-auto px-4 py-6">
      {/* 页面头部 */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">匹配统计</h1>
            <p className="text-muted-foreground mt-2">
              查看匹配系统的统计指标和表现
            </p>
          </div>

          <div className="flex items-center gap-3">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="选择时间范围" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">最近7天</SelectItem>
                <SelectItem value="30d">最近30天</SelectItem>
                <SelectItem value="90d">最近90天</SelectItem>
                <SelectItem value="365d">最近一年</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              onClick={handleExportData}
              className="gap-1"
            >
              <Download className="h-4 w-4" />
              导出
            </Button>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <StatCard
          title="匹配请求总数"
          value={stats.totalRequests}
          change="+12% 较上期"
          changeType="positive"
          tooltip="所选时间范围内的匹配请求总数"
          icon={Filter}
        />
        <StatCard
          title="已匹配数量"
          value={stats.matchedRequests}
          change="+8% 较上期"
          changeType="positive"
          tooltip="成功匹配规划师的请求数量"
          icon={Calendar}
        />
        <StatCard
          title="平均响应时间"
          value={`${stats.averageResponseTime}小时`}
          change="-15% 较上期"
          changeType="positive"
          tooltip="规划师响应匹配请求的平均时间"
          icon={TrendingUp}
        />
        <StatCard
          title="匹配成功率"
          value={`${stats.successRate}%`}
          change="+5% 较上期"
          changeType="positive"
          tooltip="已确认匹配的请求占比"
          icon={BarChart3}
        />
      </div>

      {/* 分析面板 */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="gap-2">
            <BarChart3 className="h-4 w-4" />
            概览
          </TabsTrigger>
          <TabsTrigger value="categories" className="gap-2">
            <PieChartIcon className="h-4 w-4" />
            分类分布
          </TabsTrigger>
          <TabsTrigger value="trends" className="gap-2">
            <TrendingUp className="h-4 w-4" />
            时间趋势
          </TabsTrigger>
          <TabsTrigger value="performance" className="gap-2">
            <BarChart3 className="h-4 w-4" />
            系统表现
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4 mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>匹配请求状态分布</CardTitle>
                <CardDescription>各状态请求数量占比</CardDescription>
              </CardHeader>
              <CardContent>
                <ChartPlaceholder height={300} text="饼图加载中..." />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>匹配请求来源</CardTitle>
                <CardDescription>请求发起方分布</CardDescription>
              </CardHeader>
              <CardContent>
                <ChartPlaceholder height={300} text="柱状图加载中..." />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>每日匹配请求数量</CardTitle>
              <CardDescription>所选时间范围内的每日请求量变化</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartPlaceholder height={300} text="折线图加载中..." />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories" className="space-y-4 mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>需求分类分布</CardTitle>
                <CardDescription>不同分类的请求数量占比</CardDescription>
              </CardHeader>
              <CardContent>
                <ChartPlaceholder height={300} text="饼图加载中..." />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>分类匹配成功率</CardTitle>
                <CardDescription>各分类的匹配成功比例</CardDescription>
              </CardHeader>
              <CardContent>
                <ChartPlaceholder height={300} text="条形图加载中..." />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>热门需求详情</CardTitle>
              <CardDescription>排名靠前的具体需求</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* 模拟热门需求数据 */}
                {[
                  {
                    rank: 1,
                    name: "高考志愿填报咨询",
                    category: "升学咨询",
                    count: 48,
                  },
                  {
                    rank: 2,
                    name: "高中数学辅导",
                    category: "学科辅导",
                    count: 37,
                  },
                  {
                    rank: 3,
                    name: "留学申请规划",
                    category: "学业规划",
                    count: 32,
                  },
                  {
                    rank: 4,
                    name: "中考备考指导",
                    category: "考试备考",
                    count: 29,
                  },
                  {
                    rank: 5,
                    name: "学习能力提升",
                    category: "能力培养",
                    count: 23,
                  },
                ].map((item) => (
                  <div
                    key={item.rank}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center gap-3">
                      <Badge variant="outline">{item.rank}</Badge>
                      <div>
                        <div className="font-medium">{item.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {item.category}
                        </div>
                      </div>
                    </div>
                    <div className="text-sm font-medium">{item.count}次</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4 mt-6">
          <div className="grid grid-cols-1 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>匹配请求趋势</CardTitle>
                <CardDescription>请求数量和匹配数量的变化趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <ChartPlaceholder height={350} text="趋势图加载中..." />
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>响应时间趋势</CardTitle>
                  <CardDescription>规划师平均响应时间变化</CardDescription>
                </CardHeader>
                <CardContent>
                  <ChartPlaceholder height={300} text="折线图加载中..." />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>成功率趋势</CardTitle>
                  <CardDescription>匹配成功率的变化</CardDescription>
                </CardHeader>
                <CardContent>
                  <ChartPlaceholder height={300} text="折线图加载中..." />
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">平均匹配时间</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2.3小时</div>
                <div className="text-xs text-green-600 flex items-center">
                  <ArrowDown className="h-3 w-3 mr-1" />
                  较上期减少18%
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">响应率</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">92.4%</div>
                <div className="text-xs text-green-600 flex items-center">
                  <ArrowUp className="h-3 w-3 mr-1" />
                  较上期提升5%
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">用户满意度</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4.7/5.0</div>
                <div className="text-xs text-green-600 flex items-center">
                  <ArrowUp className="h-3 w-3 mr-1" />
                  较上期提升0.3分
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>各分类性能对比</CardTitle>
                <CardDescription>
                  响应时间、成功率、满意度综合对比
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ChartPlaceholder height={300} text="雷达图加载中..." />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>规划师匹配效率</CardTitle>
                <CardDescription>规划师接单和完成率统计</CardDescription>
              </CardHeader>
              <CardContent>
                <ChartPlaceholder height={300} text="条形图加载中..." />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>算法权重变更影响</CardTitle>
              <CardDescription>权重调整前后匹配质量变化</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartPlaceholder height={350} text="对比图表加载中..." />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
