"use client";

import React from "react";
import { useParams } from "next/navigation";
import { PlannerCategoryPerformanceDisplay } from "@/components/match/PlannerCategoryPerformanceDisplay";
import { But<PERSON> } from "@workspace/ui/components/button";
import { ArrowLeft, BarChart3 } from "lucide-react";
import Link from "next/link";

export default function PlannerPerformancePage() {
  const params = useParams();
  const plannerId = params.id as string;

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/workspace/match/planners">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回规划师列表
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <BarChart3 className="h-6 w-6" />
              规划师分类表现
            </h1>
            <p className="text-muted-foreground">
              查看规划师在各服务分类下的详细表现数据
            </p>
          </div>
        </div>
      </div>

      {/* 表现数据展示 */}
      <PlannerCategoryPerformanceDisplay plannerId={plannerId} />
    </div>
  );
}
