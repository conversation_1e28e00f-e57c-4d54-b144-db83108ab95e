"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import {
  ArrowLeft,
  Share2,
  MoreHorizontal,
  MapPin,
  DollarSign,
  Clock,
  Star,
  Users,
  MessageCircle,
  TrendingUp,
  Award,
  Calendar,
  CheckCircle,
  Phone,
  Mail,
  Globe,
} from "lucide-react";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { Skeleton } from "@workspace/ui/components/skeleton";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "@workspace/ui/components/dialog";
import { Textarea } from "@workspace/ui/components/textarea";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";

import { usePlanner } from "@/hooks/useMatchData";
import { toast } from "sonner";
import { apiClient } from "@/utils/api";
import { RequirementCategory } from "@/types/match.types";

export default function PlannerDetailPage() {
  const { id } = useParams<{ id: string }>();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("overview");
  const [isMatchDialogOpen, setIsMatchDialogOpen] = useState(false);
  const [isContactDialogOpen, setIsContactDialogOpen] = useState(false);
  const [matchMessage, setMatchMessage] = useState("");
  const [contactInfo, setContactInfo] = useState("");
  const [contactMessage, setContactMessage] = useState("");
  const [contactMethod, setContactMethod] = useState("");
  const [category, setCategory] = useState<RequirementCategory | "">("");

  const { planner, loading, error, refresh } = usePlanner(id);

  const handleBack = () => {
    router.back();
  };

  const handleShare = () => {
    navigator.share?.({
      title: `${planner?.tenantUser?.user.name} - 规划师`,
      text: planner?.introduction,
      url: window.location.href,
    });
  };

  const handleApplyMatch = async () => {
    if (!matchMessage.trim()) {
      toast.error("请填写申请说明");
      return;
    }

    if (!category) {
      toast.error("请选择需求类别");
      return;
    }

    try {
      const { success, error, message } = await apiClient.request(
        `/api/match/apply/${id}`,
        {
          method: "POST",
          body: JSON.stringify({
            message: matchMessage,
            contactInfo: contactInfo,
            category,
          }),
        },
      );

      if (success) {
        toast.success(message || "申请匹配成功");
        setIsMatchDialogOpen(false);
        setMatchMessage("");
        setContactInfo("");
        setCategory("");
      } else {
        toast.error(message || "申请失败");
      }
    } catch (err) {
      toast.error("申请失败，请重试");
    }
  };

  const handleContact = async () => {
    if (!contactMessage.trim()) {
      toast.error("请填写联系消息");
      return;
    }

    try {
      const { success, error, message } = await apiClient.request(
        `/api/match/contact/${id}`,
        {
          method: "POST",
          body: JSON.stringify({
            message: contactMessage,
            contactMethod: contactMethod,
          }),
        },
      );

      if (success) {
        toast.success(message || "消息已发送");
        setIsContactDialogOpen(false);
        setContactMessage("");
        setContactMethod("");
      } else {
        toast.error(message || "发送失败");
      }
    } catch (err) {
      toast.error("发送失败，请重试");
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* 头部骨架屏 */}
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-10" />
          <div className="flex-1">
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-32" />
          </div>
        </div>

        {/* 内容骨架屏 */}
        <div className="grid gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent className="space-y-4">
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </CardContent>
            </Card>
          </div>
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-32 w-full" />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (error || !planner) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="text-center py-12">
          <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">规划师不存在</h2>
          <p className="text-muted-foreground mb-4">
            {error || "无法找到该规划师"}
          </p>
          <div className="flex items-center justify-center gap-2">
            <Button onClick={handleBack} variant="outline">
              返回
            </Button>
            <Button onClick={refresh}>重试</Button>
          </div>
        </div>
      </div>
    );
  }

  const renderStars = (rating?: number) => {
    if (!rating) return "暂无评分";
    return (
      <div className="flex items-center gap-1">
        {Array.from({ length: 5 }).map((_, i) => (
          <Star
            key={i}
            className={`h-4 w-4 ${
              i < Math.floor(rating)
                ? "text-yellow-400 fill-current"
                : "text-gray-300"
            }`}
          />
        ))}
        <span className="ml-1 text-sm font-medium">{rating}</span>
      </div>
    );
  };

  return (
    <div className="container mx-auto px-4 py-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleBack}
            className="gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            返回
          </Button>

          <div>
            <h1 className="text-2xl font-bold">
              {planner.tenantUser?.user.name}
            </h1>
            <div className="flex items-center gap-2 mt-1">
              <span className="text-sm text-muted-foreground">
                {planner.title || "专业规划师"}
              </span>
              {planner.matchProfile?.isAcceptingMatch ? (
                <Badge variant="default" className="text-xs">
                  接受匹配
                </Badge>
              ) : (
                <Badge variant="secondary" className="text-xs">
                  暂不接单
                </Badge>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleShare}
            className="gap-2"
          >
            <Share2 className="h-4 w-4" />
            分享
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleContact}>
                <MessageCircle className="h-4 w-4 mr-2" />
                联系规划师
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Star className="h-4 w-4 mr-2" />
                收藏规划师
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* 左侧主要内容 */}
        <div className="lg:col-span-2">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">概览</TabsTrigger>
              <TabsTrigger value="experience">经历</TabsTrigger>
              <TabsTrigger value="reviews">评价</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* 基本信息 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={planner.tenantUser?.user.avatar} />
                      <AvatarFallback>
                        {planner.tenantUser?.user.name.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    规划师简介
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <p className="text-muted-foreground leading-relaxed">
                      {planner.introduction || "暂无简介"}
                    </p>
                  </div>

                  <div className="grid gap-4 sm:grid-cols-2">
                    <div className="flex items-center gap-2">
                      <Award className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        经验: {planner.experience || 0} 年
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        已服务: {planner.totalStudents || 0} 位学生
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        成功率: {((planner.successRate || 0) * 100).toFixed(0)}%
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        响应时间: {planner.matchProfile?.responseTime || 24}{" "}
                        小时
                      </span>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">擅长领域</h4>
                    <div className="flex flex-wrap gap-2">
                      {planner.specialties?.map((specialty, index) => (
                        <Badge key={index} variant="secondary">
                          {specialty}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 服务配置 */}
              {planner.matchProfile && (
                <Card>
                  <CardHeader>
                    <CardTitle>服务配置</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid gap-4 sm:grid-cols-2">
                      <div>
                        <h4 className="font-medium mb-2">年级偏好</h4>
                        <div className="flex flex-wrap gap-1">
                          {planner.matchProfile.preferredGrades?.map(
                            (grade, index) => (
                              <Badge
                                key={index}
                                variant="outline"
                                className="text-xs"
                              >
                                {grade}
                              </Badge>
                            ),
                          )}
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium mb-2">科目偏好</h4>
                        <div className="flex flex-wrap gap-1">
                          {planner.matchProfile.preferredSubjects?.map(
                            (subject, index) => (
                              <Badge
                                key={index}
                                variant="outline"
                                className="text-xs"
                              >
                                {subject}
                              </Badge>
                            ),
                          )}
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">服务地区</h4>
                      <div className="flex flex-wrap gap-1">
                        {planner.matchProfile.preferredLocations?.map(
                          (location, index) => (
                            <Badge
                              key={index}
                              variant="outline"
                              className="text-xs"
                            >
                              {location}
                            </Badge>
                          ),
                        )}
                      </div>
                    </div>

                    {planner.matchProfile.priceRange && (
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">
                          咨询费用: ¥
                          {planner.matchProfile.priceRange.consultationFee?.min}
                          -
                          {planner.matchProfile.priceRange.consultationFee?.max}
                        </span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="experience">
              <Card>
                <CardHeader>
                  <CardTitle>工作经历</CardTitle>
                  <CardDescription>了解规划师的专业背景和经验</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <div className="flex-1">
                        <div className="font-medium">
                          {planner.experience || 0} 年规划经验
                        </div>
                        <div className="text-sm text-muted-foreground">
                          累计服务 {planner.totalStudents || 0} 位学生
                        </div>
                      </div>
                    </div>

                    {planner.matchProfile?.expertise?.certifications?.map(
                      (cert, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg"
                        >
                          <Award className="h-5 w-5 text-blue-600" />
                          <div className="flex-1">
                            <div className="font-medium">{cert}</div>
                            <div className="text-sm text-muted-foreground">
                              专业认证
                            </div>
                          </div>
                        </div>
                      ),
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="reviews">
              <Card>
                <CardHeader>
                  <CardTitle>学生评价</CardTitle>
                  <CardDescription>查看其他学生对规划师的评价</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <Star className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">暂无评价信息</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* 右侧边栏 */}
        <div className="space-y-6">
          {/* 评分信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5 text-yellow-600" />
                评分信息
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center space-y-2">
                <div className="text-3xl font-bold text-yellow-600">
                  {planner.rating || "暂无"}
                </div>
                {renderStars(planner.rating)}
                <div className="text-sm text-muted-foreground">
                  基于历史服务评价
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 联系规划师 */}
          <Card>
            <CardHeader>
              <CardTitle>联系规划师</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Dialog
                open={isMatchDialogOpen}
                onOpenChange={setIsMatchDialogOpen}
              >
                <DialogTrigger asChild>
                  <Button
                    className="w-full gap-2"
                    disabled={!planner.matchProfile?.isAcceptingMatch}
                  >
                    <Users className="h-4 w-4" />
                    {planner.matchProfile?.isAcceptingMatch
                      ? "申请匹配"
                      : "暂不接单"}
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>申请匹配</DialogTitle>
                    <DialogDescription>
                      向 {planner.tenantUser?.user.name} 发送匹配申请
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="category">需求类别</Label>
                      <Select value={category} onValueChange={(value) => setCategory(value as RequirementCategory)}>
                        <SelectTrigger>
                          <SelectValue placeholder="请选择需求类别" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={RequirementCategory.LEARNING_ABILITY}>学习能力提升</SelectItem>
                          <SelectItem value={RequirementCategory.PSYCHOLOGICAL_SUPPORT}>心理支持</SelectItem>
                          <SelectItem value={RequirementCategory.COLLEGE_APPLICATION}>大学申请</SelectItem>
                          <SelectItem value={RequirementCategory.SPECIAL_ADMISSION}>特殊招生</SelectItem>
                          <SelectItem value={RequirementCategory.HONGKONG_MACAO}>港澳台升学</SelectItem>
                          <SelectItem value={RequirementCategory.ENGLISH_COUNTRIES}>英语国家留学</SelectItem>
                          <SelectItem value={RequirementCategory.NON_ENGLISH_COUNTRIES}>非英语国家留学</SelectItem>
                          <SelectItem value={RequirementCategory.ACADEMIC_PLANNING}>学术规划</SelectItem>
                          <SelectItem value={RequirementCategory.CAREER_PLANNING}>职业规划</SelectItem>
                          <SelectItem value={RequirementCategory.STUDY_ABROAD}>出国留学</SelectItem>
                          <SelectItem value={RequirementCategory.EXAM_PREPARATION}>考试准备</SelectItem>
                          <SelectItem value={RequirementCategory.EXTRACURRICULAR}>课外活动</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="message">申请说明</Label>
                      <Textarea
                        id="message"
                        placeholder="请简单描述您的需求和期望..."
                        value={matchMessage}
                        onChange={(e) => setMatchMessage(e.target.value)}
                        rows={4}
                      />
                    </div>
                    <div>
                      <Label htmlFor="contact">联系方式</Label>
                      <Input
                        id="contact"
                        placeholder="手机号或微信号"
                        value={contactInfo}
                        onChange={(e) => setContactInfo(e.target.value)}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsMatchDialogOpen(false);
                        setMatchMessage("");
                        setContactInfo("");
                        setCategory("");
                      }}
                    >
                      取消
                    </Button>
                    <Button onClick={handleApplyMatch}>发送申请</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <Dialog
                open={isContactDialogOpen}
                onOpenChange={setIsContactDialogOpen}
              >
                <DialogTrigger asChild>
                  <Button variant="outline" className="w-full gap-2">
                    <MessageCircle className="h-4 w-4" />
                    直接联系
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>联系规划师</DialogTitle>
                    <DialogDescription>
                      向 {planner.tenantUser?.user.name} 发送消息
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="contactMessage">消息内容</Label>
                      <Textarea
                        id="contactMessage"
                        placeholder="请输入您想咨询的问题..."
                        value={contactMessage}
                        onChange={(e) => setContactMessage(e.target.value)}
                        rows={4}
                      />
                    </div>
                    <div>
                      <Label htmlFor="contactMethod">您的联系方式</Label>
                      <Input
                        id="contactMethod"
                        placeholder="手机号或微信号（可选）"
                        value={contactMethod}
                        onChange={(e) => setContactMethod(e.target.value)}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setIsContactDialogOpen(false)}
                    >
                      取消
                    </Button>
                    <Button onClick={handleContact}>发送消息</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </CardContent>
          </Card>

          {/* 快速操作 */}
          <Card>
            <CardHeader>
              <CardTitle>快速操作</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button
                variant="outline"
                className="w-full justify-start gap-2"
                onClick={() => router.push("/community/match/planners")}
              >
                <Users className="h-4 w-4" />
                浏览其他规划师
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start gap-2"
                onClick={() => router.push("/community/match/create")}
              >
                <TrendingUp className="h-4 w-4" />
                发布匹配需求
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
