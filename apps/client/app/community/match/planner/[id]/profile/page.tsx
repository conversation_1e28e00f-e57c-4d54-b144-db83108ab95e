"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { 
  ArrowLeft, 
  User, 
  Clock, 
  MapPin, 
  Calendar, 
  Award, 
  Star, 
  BookOpen, 
  TrendingUp,
  MessageCircle,
  History,
  Bookmark,
  Check,
  X,
  ChevronRight,
  Users
} from "lucide-react";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@workspace/ui/components/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@workspace/ui/components/avatar";
import { Badge } from "@workspace/ui/components/badge";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Progress } from "@workspace/ui/components/progress";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@workspace/ui/components/dropdown-menu";
import { Separator } from "@workspace/ui/components/separator";
import { toast } from "sonner";

import { apiClient } from "@/utils/api";

export default function PlannerProfilePage() {
  const params = useParams();
  const router = useRouter();
  const plannerId = params.id as string;

  const [planner, setPlanner] = useState<any>(null);
  const [categoryPerformance, setCategoryPerformance] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPlannerData = async () => {
      setLoading(true);
      try {
        // 获取规划师信息
        const plannerResult = await apiClient.request(`/api/match/planner/${plannerId}/profile`);
        
        // 获取规划师分类表现
        const performanceResult = await apiClient.request(`/api/match/planner/${plannerId}/performance`);
        
        if (plannerResult.success && performanceResult.success) {
          setPlanner(plannerResult.data);
          setCategoryPerformance(performanceResult.data || []);
        } else {
          setError("获取规划师数据失败");
        }
      } catch (err) {
        setError("网络错误，请稍后重试");
      } finally {
        setLoading(false);
      }
    };

    if (plannerId) {
      fetchPlannerData();
    }
  }, [plannerId]);

  const handleCreateMatchRequest = () => {
    // 跳转到创建匹配请求页面，并预填规划师ID
    router.push(`/community/match/create?plannerId=${plannerId}`);
  };

  const handleContact = () => {
    toast.info("即将开放联系功能", {
      description: "稍后您可以直接与规划师沟通"
    });
  };

  // 渲染星级评分
  const renderStars = (rating?: number) => {
    if (!rating) return "暂无评分";
    
    return (
      <div className="flex items-center">
        {Array.from({ length: 5 }).map((_, i) => (
          <Star 
            key={i} 
            className={`h-4 w-4 ${i < Math.floor(rating) ? "text-yellow-400 fill-yellow-400" : "text-muted-foreground"}`} 
          />
        ))}
        <span className="ml-1 text-sm font-medium">{rating}</span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="mb-6">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
        </div>
        
        <div className="space-y-6">
          {/* 骨架屏 */}
          <div className="flex flex-col md:flex-row gap-6">
            <div className="md:w-1/3">
              <Card>
                <CardContent className="p-6">
                  <div className="flex flex-col items-center text-center space-y-4">
                    <Skeleton className="h-24 w-24 rounded-full" />
                    <Skeleton className="h-6 w-40" />
                    <Skeleton className="h-4 w-32" />
                    <div className="flex gap-2">
                      <Skeleton className="h-8 w-24" />
                      <Skeleton className="h-8 w-24" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div className="md:w-2/3">
              <Card>
                <CardHeader>
                  <Skeleton className="h-6 w-40" />
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !planner) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="mb-6">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
        </div>
        
        <Card>
          <CardContent className="p-6 text-center">
            <div className="text-destructive text-lg mb-2">
              {error || "未找到规划师信息"}
            </div>
            <Button onClick={() => router.back()}>返回上一页</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // 从此处开始渲染规划师信息
  const user = planner.tenantUser?.user;
  const matchProfile = planner.matchProfile || {};

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <Button variant="ghost" onClick={() => router.back()} className="mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          返回
        </Button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* 左侧: 规划师基本信息 */}
        <div className="md:col-span-1 space-y-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center text-center">
                <Avatar className="h-32 w-32">
                  <AvatarImage src={user.avatar} alt={user.name} />
                  <AvatarFallback className="text-3xl">
                    {user.name?.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                
                <h2 className="text-2xl font-bold mt-4">{user.name}</h2>
                
                {planner.title && (
                  <p className="text-muted-foreground">{planner.title}</p>
                )}
                
                {!matchProfile.isAcceptingMatch && (
                  <Badge variant="outline" className="text-yellow-600 border-yellow-300 bg-yellow-50 mt-2">
                    暂不接单
                  </Badge>
                )}
                
                <div className="flex items-center justify-center gap-2 mt-4">
                  {renderStars(planner.rating)}
                  <span className="text-muted-foreground">|</span>
                  <span className="text-sm">{(matchProfile.successRate * 100).toFixed(0)}% 成功率</span>
                </div>
                
                <div className="grid grid-cols-2 gap-4 w-full mt-6">
                  <div className="flex flex-col items-center text-center">
                    <div className="text-2xl font-bold">{planner.totalStudents || 0}</div>
                    <div className="text-xs text-muted-foreground">已服务学生</div>
                  </div>
                  
                  <div className="flex flex-col items-center text-center">
                    <div className="text-2xl font-bold">{planner.experience || 0}</div>
                    <div className="text-xs text-muted-foreground">从业年限</div>
                  </div>
                </div>
                
                <div className="flex flex-wrap gap-2 w-full justify-center mt-6">
                  {planner.specialties?.map((specialty: string) => (
                    <Badge key={specialty} variant="secondary">
                      {specialty}
                    </Badge>
                  ))}
                </div>
                
                <div className="flex items-center gap-3 w-full mt-6">
                  <Button className="flex-1" onClick={handleCreateMatchRequest}>
                    申请匹配
                  </Button>
                  <Button variant="outline" className="flex-1" onClick={handleContact}>
                    <MessageCircle className="h-4 w-4 mr-2" />
                    联系
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* 服务偏好 */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">服务偏好</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* 服务地区 */}
                <div>
                  <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground mb-2">
                    <MapPin className="h-4 w-4" />
                    服务地区
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {matchProfile.preferredLocations?.map((location: string) => (
                      <Badge key={location} variant="outline" className="text-xs">
                        {location}
                      </Badge>
                    )) || "暂无数据"}
                  </div>
                </div>
                
                {/* 年级 */}
                <div>
                  <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground mb-2">
                    <BookOpen className="h-4 w-4" />
                    擅长年级
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {matchProfile.preferredGrades?.map((grade: string) => (
                      <Badge key={grade} variant="outline" className="text-xs">
                        {grade}
                      </Badge>
                    )) || "暂无数据"}
                  </div>
                </div>
                
                {/* 科目 */}
                <div>
                  <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground mb-2">
                    <Award className="h-4 w-4" />
                    擅长科目
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {matchProfile.preferredSubjects?.map((subject: string) => (
                      <Badge key={subject} variant="outline" className="text-xs">
                        {subject}
                      </Badge>
                    )) || "暂无数据"}
                  </div>
                </div>
                
                {/* 其他信息 */}
                <div className="pt-4 space-y-2 border-t">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">响应时间</div>
                    <div className="text-sm font-medium">
                      {matchProfile.responseTime || "--"}小时内
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">最大接单量</div>
                    <div className="text-sm font-medium">
                      {matchProfile.maxConcurrent || "--"}个
                    </div>
                  </div>
                  
                  {matchProfile.basePrice && (
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-muted-foreground">起步价格</div>
                      <div className="text-sm font-medium">
                        ¥{matchProfile.basePrice}/小时
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* 右侧: 详细信息和数据 */}
        <div className="md:col-span-2 space-y-6">
          <Tabs defaultValue="about">
            <TabsList className="mb-4">
              <TabsTrigger value="about">简介</TabsTrigger>
              <TabsTrigger value="performance">服务表现</TabsTrigger>
              <TabsTrigger value="reviews">评价</TabsTrigger>
            </TabsList>
            
            <TabsContent value="about" className="space-y-6">
              {/* 个人简介 */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">个人简介</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm leading-relaxed">
                    {planner.introduction || "该规划师暂无简介"}
                  </p>
                </CardContent>
              </Card>
              
              {/* 教育背景 */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">教育背景</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {planner.education ? (
                      planner.education.map((edu: any, index: number) => (
                        <div key={index} className="border-l-2 border-primary/30 pl-4 py-1">
                          <div className="font-medium">{edu.school}</div>
                          <div className="text-sm text-muted-foreground">
                            {edu.degree} · {edu.major} · {edu.year}
                          </div>
                        </div>
                      ))
                    ) : (
                      <p className="text-sm text-muted-foreground">暂无教育背景信息</p>
                    )}
                  </div>
                </CardContent>
              </Card>
              
              {/* 工作经历 */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">工作经历</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {planner.workExperience ? (
                      planner.workExperience.map((work: any, index: number) => (
                        <div key={index} className="border-l-2 border-primary/30 pl-4 py-1">
                          <div className="font-medium">{work.company}</div>
                          <div className="text-sm">{work.position}</div>
                          <div className="text-sm text-muted-foreground">
                            {work.startYear} - {work.endYear || "至今"}
                          </div>
                        </div>
                      ))
                    ) : (
                      <p className="text-sm text-muted-foreground">暂无工作经历信息</p>
                    )}
                  </div>
                </CardContent>
              </Card>
              
              {/* 成功案例 */}
              <Card>
                <CardHeader className="pb-2 flex flex-row items-center justify-between">
                  <CardTitle className="text-lg">成功案例</CardTitle>
                  <Button variant="ghost" size="sm" className="gap-1">
                    查看全部
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {planner.successCases ? (
                      planner.successCases.slice(0, 3).map((caseItem: any, index: number) => (
                        <div key={index} className="border rounded-lg p-3 hover:bg-muted/50 transition-colors">
                          <div className="font-medium">{caseItem.title}</div>
                          <div className="text-sm text-muted-foreground mt-1">
                            {caseItem.description}
                          </div>
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant="outline" className="text-xs">
                              {caseItem.category}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {caseItem.year}
                            </Badge>
                          </div>
                        </div>
                      ))
                    ) : (
                      <p className="text-sm text-muted-foreground">暂无成功案例</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="performance" className="space-y-6">
              {/* 总体表现 */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">匹配统计</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="flex flex-col items-center text-center">
                      <Users className="h-5 w-5 text-primary mb-1" />
                      <div className="text-2xl font-bold">{planner.totalStudents || 0}</div>
                      <div className="text-xs text-muted-foreground">已服务学生</div>
                    </div>
                    
                    <div className="flex flex-col items-center text-center">
                      <TrendingUp className="h-5 w-5 text-green-500 mb-1" />
                      <div className="text-2xl font-bold">
                        {(matchProfile.successRate * 100).toFixed(0)}%
                      </div>
                      <div className="text-xs text-muted-foreground">匹配成功率</div>
                    </div>
                    
                    <div className="flex flex-col items-center text-center">
                      <Clock className="h-5 w-5 text-blue-500 mb-1" />
                      <div className="text-2xl font-bold">{matchProfile.responseTime || "--"}</div>
                      <div className="text-xs text-muted-foreground">平均响应时间(小时)</div>
                    </div>
                    
                    <div className="flex flex-col items-center text-center">
                      <Star className="h-5 w-5 text-yellow-500 mb-1" />
                      <div className="text-2xl font-bold">{planner.rating?.toFixed(1) || "--"}</div>
                      <div className="text-xs text-muted-foreground">评分(5分制)</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* 分类表现 */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">服务分类表现</CardTitle>
                  <CardDescription>各个服务分类下的表现数据</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {categoryPerformance.length > 0 ? (
                      categoryPerformance.map((item, index) => (
                        <div key={index} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="font-medium">{item.category}</div>
                            <Badge variant={item.successRate >= 0.7 ? "default" : "outline"}>
                              {(item.successRate * 100).toFixed(0)}% 成功率
                            </Badge>
                          </div>
                          
                          <div className="flex items-center gap-2 text-sm">
                            <div className="w-24 text-muted-foreground">总请求数:</div>
                            <div>{item.totalRequests}</div>
                          </div>
                          
                          <div className="flex items-center gap-2 text-sm">
                            <div className="w-24 text-muted-foreground">已确认匹配:</div>
                            <div>{item.confirmedRequests}</div>
                          </div>
                          
                          <div className="flex items-center gap-2 text-sm">
                            <div className="w-24 text-muted-foreground">平均评分:</div>
                            <div className="flex items-center">
                              {item.avgRating ? (
                                <>
                                  {renderStars(item.avgRating)}
                                  <span className="ml-2">({item.evaluationCount}条评价)</span>
                                </>
                              ) : (
                                "暂无评分"
                              )}
                            </div>
                          </div>
                          
                          <Progress 
                            value={item.successRate * 100} 
                            className="h-2"
                          />
                          
                          {index < categoryPerformance.length - 1 && <Separator className="my-4" />}
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-4">
                        <p className="text-muted-foreground">暂无服务分类数据</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
              
              {/* 学生数量统计 */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">服务容量</CardTitle>
                  <CardDescription>当前服务学生数量和最大容量</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-muted-foreground">当前服务学生</div>
                      <div className="font-medium">{planner.students?.length || 0} 人</div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-muted-foreground">最大服务容量</div>
                      <div className="font-medium">{matchProfile.maxConcurrent || "--"} 人</div>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="flex items-center justify-between text-sm">
                        <span>使用率</span>
                        <span className="font-medium">
                          {matchProfile.maxConcurrent 
                            ? ((planner.students?.length || 0) / matchProfile.maxConcurrent * 100).toFixed(0) 
                            : 0}%
                        </span>
                      </div>
                      <Progress 
                        value={matchProfile.maxConcurrent 
                          ? ((planner.students?.length || 0) / matchProfile.maxConcurrent * 100) 
                          : 0
                        } 
                        className="h-2"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="reviews" className="space-y-6">
              {/* 评价概览 */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">评价概览</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col md:flex-row items-center gap-6">
                    <div className="flex flex-col items-center">
                      <div className="text-5xl font-bold">{planner.rating?.toFixed(1) || "--"}</div>
                      <div className="mt-2">{renderStars(planner.rating)}</div>
                      <div className="text-sm text-muted-foreground mt-1">
                        基于 {planner.reviewsCount || 0} 条评价
                      </div>
                    </div>
                    
                    <div className="flex-1 space-y-2">
                      {[5, 4, 3, 2, 1].map((star) => (
                        <div key={star} className="flex items-center gap-2">
                          <div className="text-sm text-muted-foreground w-4">{star}</div>
                          <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                          <Progress 
                            value={planner.ratingDistribution?.[star] || 0} 
                            className="h-2 flex-1"
                          />
                          <div className="text-sm w-8">
                            {planner.ratingDistribution?.[star] 
                              ? ((planner.ratingDistribution[star] / planner.reviewsCount) * 100).toFixed(0) 
                              : 0}%
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* 评价列表 */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">学生评价</CardTitle>
                </CardHeader>
                <CardContent>
                  {planner.reviews && planner.reviews.length > 0 ? (
                    <div className="space-y-6">
                      {planner.reviews.map((review: any, index: number) => (
                        <div key={index} className="space-y-2">
                          <div className="flex items-start gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarFallback>
                                {review.student?.name?.charAt(0) || "S"}
                              </AvatarFallback>
                            </Avatar>
                            
                            <div className="flex-1">
                              <div className="flex items-center justify-between">
                                <div className="font-medium">{review.student?.name || "匿名学生"}</div>
                                <div className="text-sm text-muted-foreground">
                                  {new Date(review.createdAt).toLocaleDateString()}
                                </div>
                              </div>
                              
                              <div className="mt-1">{renderStars(review.rating)}</div>
                              
                              <p className="text-sm mt-2">{review.content}</p>
                              
                              {review.tags && review.tags.length > 0 && (
                                <div className="flex flex-wrap gap-1 mt-2">
                                  {review.tags.map((tag: string, tagIndex: number) => (
                                    <Badge key={tagIndex} variant="outline" className="text-xs">
                                      {tag}
                                    </Badge>
                                  ))}
                                </div>
                              )}
                              
                              {/* 规划师回复 */}
                              {review.reply && (
                                <div className="bg-muted/50 p-3 rounded-md mt-3">
                                  <div className="text-sm font-medium">规划师回复:</div>
                                  <p className="text-sm mt-1">{review.reply}</p>
                                </div>
                              )}
                            </div>
                          </div>
                          
                          {index < planner.reviews.length - 1 && <Separator className="my-4" />}
                        </div>
                      ))}
                      
                      {planner.reviewsCount > planner.reviews.length && (
                        <div className="text-center mt-4">
                          <Button variant="outline" size="sm">
                            查看更多评价
                          </Button>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Star className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">暂无评价</p>
                      <p className="text-sm text-muted-foreground mt-1">该规划师尚未收到学生评价</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
