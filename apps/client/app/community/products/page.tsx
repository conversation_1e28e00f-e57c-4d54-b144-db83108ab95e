"use client";

import {
  IconCalendar,
  IconMapPin,
  IconUsers,
  IconAward,
  IconStar,
  IconBuilding,
  IconTrendingUp,
  IconSchool,
} from "@tabler/icons-react";

import { Badge } from "@workspace/ui/components/badge";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  ArrowRight,
  Eye,
  Clock,
  Calendar,
  MapPin,
  Users,
  Award,
  Building,
  TrendingUp,
} from "lucide-react";
import Link from "next/link";
import { unstable_ViewTransition as ViewTransition } from "react";
import { useEffect, useState } from "react";

// 严格按照 schema.prisma 定义的产品类型
type ProductType =
  | "STUDY_CAMP"
  | "CERTIFICATE"
  | "BACKGROUND"
  | "SCHOOL_LINK"
  | "INSTITUTION"
  | "OTHER";
type ProductStatus = "DRAFT" | "ACTIVE" | "INACTIVE" | "SOLD_OUT";

// 产品分类接口（对应 ProductCategory 模型）
interface ProductCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  type: ProductType;
  icon?: string;
  order: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  // 统计字段（关联查询）
  _count?: {
    products: number;
  };
}

// 合作方接口（对应 Partner 模型）
interface Partner {
  id: string;
  name: string;
  code: string;
  logo?: string;
  contactName: string;
  contactPhone: string;
  contactEmail: string;
  address?: string;
  cooperationType: string[];
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 产品接口（对应 Product 模型）
interface Product {
  id: string;
  name: string;
  code: string;
  type: ProductType;
  categoryId: string;
  category: ProductCategory;
  description: string;
  features: string[];
  highlights?: any; // JSON 类型
  images: string[];
  brochureUrl?: string;
  price?: number; // Decimal 类型
  priceUnit?: string;
  priceNote?: string;
  duration?: string;
  location?: string;
  startDate?: Date;
  endDate?: Date;
  capacity?: number;
  minParticipants?: number;
  targetAudience: string[];
  ageRange?: string;
  gradeRange?: string;
  partnerId?: string;
  partner?: Partner;
  status: ProductStatus;
  priority: number;
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords: string[];
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  createdById: string; // 创建者 ID
  // 关联统计字段
  _count?: {
    inquiries: number;
  };
}

// 模拟产品分类数据
const mockProductCategories: ProductCategory[] = [
  {
    id: "1",
    name: "研学营",
    slug: "study-camp",
    type: "STUDY_CAMP",
    icon: "IconSchool",
    order: 1,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    _count: { products: 18 },
  },
  {
    id: "2",
    name: "证书认证",
    slug: "certificate",
    type: "CERTIFICATE",
    icon: "IconAward",
    order: 2,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    _count: { products: 12 },
  },
  {
    id: "3",
    name: "背景提升",
    slug: "background",
    type: "BACKGROUND",
    icon: "IconTrendingUp",
    order: 3,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    _count: { products: 25 },
  },
  {
    id: "4",
    name: "名校链接",
    slug: "school-link",
    type: "SCHOOL_LINK",
    icon: "IconStar",
    order: 4,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    _count: { products: 8 },
  },
  {
    id: "5",
    name: "机构提升",
    slug: "institution",
    type: "INSTITUTION",
    icon: "IconBuilding",
    order: 5,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    _count: { products: 15 },
  },
  {
    id: "6",
    name: "其他服务",
    slug: "other",
    type: "OTHER",
    icon: "IconUsers",
    order: 6,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    _count: { products: 9 },
  },
];

// 模拟合作方数据
const mockPartners: Partner[] = [
  {
    id: "1",
    name: "清华大学继续教育学院",
    code: "tsinghua-edu",
    logo: "https://images.unsplash.com/photo-1562774053-701939374585?w=100&h=100&fit=crop",
    contactName: "李教授",
    contactPhone: "010-12345678",
    contactEmail: "<EMAIL>",
    address: "北京市海淀区清华园1号",
    cooperationType: ["研学营", "证书认证"],
    description: "清华大学官方继续教育机构",
    isActive: true,
  },
  {
    id: "2",
    name: "北京师范大学教育集团",
    code: "bnu-group",
    logo: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop",
    contactName: "张主任",
    contactPhone: "010-87654321",
    contactEmail: "<EMAIL>",
    cooperationType: ["背景提升", "师资培训"],
    description: "专业的教育培训机构",
    isActive: true,
  },
];

// 模拟产品数据
const mockProducts: Product[] = [
  {
    id: "1",
    name: "清华大学人工智能科技营",
    code: "tsinghua-ai-camp-2024",
    type: "STUDY_CAMP" as ProductType,
    categoryId: "1",
    category: { name: "研学营" },
    description:
      "由清华大学计算机系主办的人工智能主题科技营，为期7天6夜，深入了解AI前沿技术，参观实验室，与教授面对面交流，体验大学生活。",
    features: [
      "清华大学官方主办",
      "知名教授亲自授课",
      "实验室参观体验",
      "小班制教学(20人)",
      "颁发结业证书",
      "推荐信机会",
    ],
    highlights: {
      content: "国内顶尖AI研学体验，助力科技特长生升学背景提升",
    },
    images: [
      "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=800&h=400&fit=crop",
      "https://images.unsplash.com/photo-1523580494863-6f3031224c94?w=800&h=400&fit=crop",
      "https://images.unsplash.com/photo-1531482615713-2afd69097998?w=800&h=400&fit=crop",
    ],
    brochureUrl: "https://example.com/brochure-ai-camp.pdf",
    price: 12800,
    priceUnit: "人",
    priceNote: "包含住宿、餐饮、教材、证书",
    duration: "7天6夜",
    location: "北京清华大学",
    startDate: new Date("2024-07-15"),
    endDate: new Date("2024-07-21"),
    capacity: 20,
    minParticipants: 15,
    targetAudience: ["高中生", "科技特长生", "理工科爱好者"],
    ageRange: "15-18岁",
    gradeRange: "高一至高三",
    partnerId: "1",
    partner: mockPartners[0],
    status: "ACTIVE" as ProductStatus,
    priority: 100,
    metaTitle: "清华大学人工智能科技营 - 顶尖AI研学体验",
    metaDescription:
      "清华大学官方AI科技营，7天6夜深度体验，知名教授授课，实验室参观，助力升学背景提升",
    metaKeywords: ["清华大学", "人工智能", "科技营", "研学", "升学背景"],
    publishedAt: new Date("2024-03-01"),
    createdAt: new Date("2024-02-20"),
    updatedAt: new Date("2024-03-05"),
  },
  {
    id: "2",
    name: "学业规划师职业技能证书",
    code: "career-planner-cert-2024",
    type: "CERTIFICATE" as ProductType,
    categoryId: "2",
    category: { name: "证书认证" },
    description:
      "国家认可的学业规划师职业技能证书，通过系统培训和考核，获得权威认证，提升职业竞争力。",
    features: [
      "国家人社部认可",
      "权威机构颁发",
      "终身有效证书",
      "在线学习+线下考试",
      "就业推荐服务",
      "持续教育支持",
    ],
    highlights: {
      content: "权威认证，职业发展必备，就业推荐，终身学习",
    },
    images: [
      "https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=800&h=400&fit=crop",
      "https://images.unsplash.com/photo-1521791136064-7986c2920216?w=800&h=400&fit=crop",
    ],
    brochureUrl: "https://example.com/career-planner-cert.pdf",
    price: 3980,
    priceUnit: "人",
    priceNote: "包含培训费、考试费、证书费",
    duration: "3个月",
    location: "线上+线下",
    startDate: new Date("2024-04-01"),
    endDate: new Date("2024-06-30"),
    capacity: 100,
    minParticipants: 30,
    targetAudience: ["教育工作者", "咨询师", "职场人士"],
    ageRange: "22-50岁",
    gradeRange: "大专及以上学历",
    partnerId: "2",
    partner: mockPartners[1],
    status: "ACTIVE" as ProductStatus,
    priority: 90,
    metaTitle: "学业规划师职业技能证书 - 权威认证培训",
    metaDescription:
      "国家认可的学业规划师职业技能证书，权威培训，就业推荐，职业发展必备",
    metaKeywords: ["学业规划师", "职业技能证书", "权威认证", "培训"],
    publishedAt: new Date("2024-02-15"),
    createdAt: new Date("2024-02-01"),
    updatedAt: new Date("2024-02-20"),
  },
  {
    id: "3",
    name: "哈佛大学暑期学术项目",
    code: "harvard-summer-program-2024",
    type: "BACKGROUND" as ProductType,
    categoryId: "3",
    category: { name: "背景提升" },
    description:
      "哈佛大学官方暑期学术项目，为期4周，体验世界顶尖大学的学术氛围，获得教授推荐信，显著提升申请竞争力。",
    features: [
      "哈佛大学官方项目",
      "顶尖教授授课",
      "获得学分认证",
      "教授推荐信",
      "同伴网络建立",
      "文化体验活动",
    ],
    highlights: {
      content: "世界顶尖学府体验，教授推荐信，国际化背景提升",
    },
    images: [
      "https://images.unsplash.com/photo-1562774053-701939374585?w=800&h=400&fit=crop",
      "https://images.unsplash.com/photo-1541339907198-e08756dedf3f?w=800&h=400&fit=crop",
      "https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=800&h=400&fit=crop",
    ],
    brochureUrl: "https://example.com/harvard-summer.pdf",
    price: 68000,
    priceUnit: "人",
    priceNote: "不含机票、签证费用",
    duration: "4周",
    location: "美国波士顿哈佛大学",
    startDate: new Date("2024-07-01"),
    endDate: new Date("2024-07-28"),
    capacity: 25,
    minParticipants: 20,
    targetAudience: ["高中生", "本科生", "准留学生"],
    ageRange: "16-22岁",
    gradeRange: "高二及以上",
    status: "ACTIVE" as ProductStatus,
    priority: 95,
    metaTitle: "哈佛大学暑期学术项目 - 世界顶尖学府体验",
    metaDescription:
      "哈佛大学官方暑期项目，教授推荐信，学分认证，显著提升申请竞争力",
    metaKeywords: ["哈佛大学", "暑期项目", "背景提升", "推荐信", "留学"],
    publishedAt: new Date("2024-01-15"),
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-20"),
  },
  {
    id: "4",
    name: "牛津大学学者推荐计划",
    code: "oxford-scholar-recommendation-2024",
    type: "SCHOOL_LINK" as ProductType,
    categoryId: "4",
    category: { name: "名校链接" },
    description:
      "与牛津大学知名学者建立直接联系，获得学术指导和推荐信支持，为顶尖大学申请提供强有力的背景支撑。",
    features: [
      "牛津大学学者一对一指导",
      "个性化学术方案",
      "权威推荐信",
      "学术论文指导",
      "申请材料润色",
      "面试辅导",
    ],
    highlights: {
      content: "顶尖学者推荐，学术背景提升，申请成功率显著提高",
    },
    images: [
      "https://images.unsplash.com/photo-1544006659-f0b21884ce1d?w=800&h=400&fit=crop",
      "https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=800&h=400&fit=crop",
    ],
    brochureUrl: "https://example.com/oxford-scholar.pdf",
    price: 45000,
    priceUnit: "人",
    priceNote: "6个月指导周期",
    duration: "6个月",
    location: "线上指导",
    startDate: new Date("2024-04-01"),
    endDate: new Date("2024-09-30"),
    capacity: 10,
    minParticipants: 5,
    targetAudience: ["高中生", "本科生", "研究生申请者"],
    ageRange: "16-25岁",
    gradeRange: "高二及以上",
    status: "ACTIVE" as ProductStatus,
    priority: 85,
    metaTitle: "牛津大学学者推荐计划 - 顶尖学者指导",
    metaDescription:
      "牛津大学知名学者一对一指导，权威推荐信，学术背景提升，申请成功率提升",
    metaKeywords: ["牛津大学", "学者推荐", "名校申请", "推荐信", "学术指导"],
    publishedAt: new Date("2024-02-01"),
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-02-05"),
  },
  {
    id: "5",
    name: "教育机构品牌升级方案",
    code: "edu-institution-upgrade-2024",
    type: "INSTITUTION" as ProductType,
    categoryId: "5",
    category: { name: "机构提升" },
    description:
      "为教育培训机构提供全方位品牌升级服务，包括课程体系设计、师资培训、营销推广等，助力机构快速发展。",
    features: [
      "品牌定位策划",
      "课程体系设计",
      "师资培训认证",
      "营销推广方案",
      "运营管理指导",
      "持续跟踪服务",
    ],
    highlights: {
      content: "全方位机构升级，专业团队服务，快速提升竞争力",
    },
    images: [
      "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&h=400&fit=crop",
      "https://images.unsplash.com/photo-1600880292203-757bb62b4baf?w=800&h=400&fit=crop",
    ],
    brochureUrl: "https://example.com/institution-upgrade.pdf",
    price: 98000,
    priceUnit: "机构",
    priceNote: "一年服务周期",
    duration: "12个月",
    location: "线上+实地",
    startDate: new Date("2024-03-01"),
    endDate: new Date("2025-02-28"),
    capacity: 20,
    minParticipants: 10,
    targetAudience: ["教育机构", "培训中心", "创业者"],
    ageRange: "不限",
    gradeRange: "机构负责人",
    status: "ACTIVE" as ProductStatus,
    priority: 80,
    metaTitle: "教育机构品牌升级方案 - 全方位机构提升",
    metaDescription:
      "专业的教育机构品牌升级服务，课程设计、师资培训、营销推广一站式解决",
    metaKeywords: ["教育机构", "品牌升级", "师资培训", "营销推广", "运营管理"],
    publishedAt: new Date("2024-01-10"),
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "6",
    name: "国际竞赛指导服务",
    code: "international-competition-2024",
    type: "OTHER" as ProductType,
    categoryId: "6",
    category: { name: "其他服务" },
    description:
      "专业的国际学科竞赛指导服务，涵盖数学、物理、化学、生物、信息学等多个学科，助力学生在国际舞台展现才华。",
    features: [
      "多学科竞赛覆盖",
      "资深教练团队",
      "个性化训练方案",
      "模拟竞赛练习",
      "心理素质培养",
      "获奖机会提升",
    ],
    highlights: {
      content: "专业竞赛指导，提升获奖概率，国际视野培养",
    },
    images: [
      "https://images.unsplash.com/photo-1509062522246-3755977927d7?w=800&h=400&fit=crop",
      "https://images.unsplash.com/photo-1581726690015-c9861fa5057f?w=800&h=400&fit=crop",
    ],
    brochureUrl: "https://example.com/competition-guide.pdf",
    price: 15800,
    priceUnit: "人",
    priceNote: "单学科一年指导",
    duration: "12个月",
    location: "线上+线下",
    startDate: new Date("2024-04-01"),
    endDate: new Date("2025-03-31"),
    capacity: 50,
    minParticipants: 20,
    targetAudience: ["中学生", "学科特长生", "竞赛爱好者"],
    ageRange: "13-18岁",
    gradeRange: "初二至高三",
    status: "ACTIVE" as ProductStatus,
    priority: 75,
    metaTitle: "国际竞赛指导服务 - 专业竞赛培训",
    metaDescription:
      "专业的国际学科竞赛指导，多学科覆盖，资深教练，提升获奖概率",
    metaKeywords: ["国际竞赛", "学科竞赛", "竞赛指导", "特长生", "获奖"],
    publishedAt: new Date("2024-02-20"),
    createdAt: new Date("2024-02-10"),
    updatedAt: new Date("2024-02-25"),
  },
];

export default function ProductsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch products and categories
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch products
        const productsResponse = await fetch(
          "/api/products?limit=6&status=ACTIVE",
        );
        if (!productsResponse.ok) {
          throw new Error("获取产品数据失败");
        }
        const productsData = await productsResponse.json();

        // Transform API response to match expected format
        const transformedProducts =
          productsData.data?.map?.((product: any) => ({
            ...product,
            createdAt: new Date(product.createdAt),
            updatedAt: new Date(product.updatedAt),
            publishedAt: product.publishedAt
              ? new Date(product.publishedAt)
              : null,
            startDate: product.startDate ? new Date(product.startDate) : null,
            endDate: product.endDate ? new Date(product.endDate) : null,
            category: {
              ...product.category,
              createdAt: new Date(product.category.createdAt),
              updatedAt: new Date(product.category.updatedAt),
            },
            partner: product.partner
              ? {
                  ...product.partner,
                  createdAt: new Date(product.partner.createdAt),
                  updatedAt: new Date(product.partner.updatedAt),
                }
              : undefined,
          })) || [];

        setProducts(transformedProducts);

        // For categories, we'll use mock data for now since we haven't implemented the categories API yet
        setCategories(mockProductCategories);
      } catch (err) {
        console.error("获取数据失败:", err);
        setError(err instanceof Error ? err.message : "获取数据失败");
        // Fallback to mock data on error
        setProducts(mockProducts);
        setCategories(mockProductCategories);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="flex flex-1 flex-col items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-1 flex-col items-center justify-center">
        <div className="text-center">
          <p className="text-destructive mb-4">{error}</p>
          <Button onClick={() => window.location.reload()} variant="outline">
            重新加载
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col">
      {/* Header Section */}
      <div className="px-4 py-6 lg:px-6">
        <div className="mb-6">
          <h1 className="text-2xl font-semibold">产品服务</h1>
          <p className="text-muted-foreground">
            精选优质教育产品，助力学业规划和职业发展
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
        <Card className="@container/card">
          <CardHeader>
            <CardDescription>合作院校</CardDescription>
            <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
              50+ 所
            </CardTitle>
          </CardHeader>
          <CardFooter className="flex-col items-start gap-1.5 text-sm">
            <div className="line-clamp-1 flex gap-2 font-medium">
              包含清华、北大等顶尖院校
            </div>
            <div className="text-muted-foreground">权威合作保障</div>
          </CardFooter>
        </Card>
        <Card className="@container/card">
          <CardHeader>
            <CardDescription>服务学员</CardDescription>
            <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
              8,000+ 人
            </CardTitle>
          </CardHeader>
          <CardFooter className="flex-col items-start gap-1.5 text-sm">
            <div className="line-clamp-1 flex gap-2 font-medium">
              今年新增 2,000+ 人
            </div>
            <div className="text-muted-foreground">口碑推荐率 98%</div>
          </CardFooter>
        </Card>
        <Card className="@container/card">
          <CardHeader>
            <CardDescription>成功案例</CardDescription>
            <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
              5,200+ 个
            </CardTitle>
          </CardHeader>
          <CardFooter className="flex-col items-start gap-1.5 text-sm">
            <div className="line-clamp-1 flex gap-2 font-medium">
              名校录取率 85%
            </div>
            <div className="text-muted-foreground">专业服务成果显著</div>
          </CardFooter>
        </Card>
        <Card className="@container/card">
          <CardHeader>
            <CardDescription>满意度</CardDescription>
            <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
              98.5%
            </CardTitle>
          </CardHeader>
          <CardFooter className="flex-col items-start gap-1.5 text-sm">
            <div className="line-clamp-1 flex gap-2 font-medium">
              持续保持高水准
            </div>
            <div className="text-muted-foreground">用户信赖之选</div>
          </CardFooter>
        </Card>
      </div>

      {/* Product Categories */}
      <div className="px-4 py-8 lg:px-6">
        <h2 className="mb-6 text-xl font-semibold">产品分类</h2>
        <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-6">
          {mockProductCategories.map((category) => (
            <Card
              key={category.id}
              className="cursor-pointer transition-all hover:shadow-sm"
              onClick={() =>
                console.log(`Navigate to category: ${category.slug}`)
              }
            >
              <CardContent className="flex flex-col items-center p-4 text-center">
                <div className="mb-3 flex size-12 items-center justify-center rounded-full bg-primary/10">
                  {category.icon === "IconSchool" && (
                    <IconSchool className="size-6 text-primary" />
                  )}
                  {category.icon === "IconAward" && (
                    <IconAward className="size-6 text-primary" />
                  )}
                  {category.icon === "IconTrendingUp" && (
                    <IconTrendingUp className="size-6 text-primary" />
                  )}
                  {category.icon === "IconStar" && (
                    <IconStar className="size-6 text-primary" />
                  )}
                  {category.icon === "IconBuilding" && (
                    <IconBuilding className="size-6 text-primary" />
                  )}
                  {category.icon === "IconUsers" && (
                    <IconUsers className="size-6 text-primary" />
                  )}
                </div>
                <h3 className="font-medium">{category.name}</h3>
                <p className="text-sm text-muted-foreground">
                  {category.productCount} 个产品
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Featured Products */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-2xl font-bold">精品推荐</h2>
            <Button variant="ghost" className="gap-2">
              查看全部 <ArrowRight className="h-4 w-4" />
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockProducts.map((product) => {
              // 产品类型标签映射
              const typeBadge = {
                STUDY_CAMP: "研学营",
                CERTIFICATE: "证书认证",
                BACKGROUND: "背景提升",
                SCHOOL_LINK: "名校链接",
                INSTITUTION: "机构提升",
                OTHER: "其他服务",
              }[product.type];

              // 状态标签映射
              const statusBadge = {
                DRAFT: "草稿",
                ACTIVE: "热招中",
                INACTIVE: "已下架",
                SOLD_OUT: "已满员",
              }[product.status];

              const statusColor = {
                DRAFT: "secondary",
                ACTIVE: "default",
                INACTIVE: "secondary",
                SOLD_OUT: "destructive",
              }[product.status] as "default" | "secondary" | "destructive";

              return (
                <Link
                  key={product.id}
                  href={`/community/products/${product.id}`}
                  className="block"
                >
                  <Card className="hover:shadow-lg transition-all group cursor-pointer product-card">
                    <CardHeader className="p-0">
                      <ViewTransition name={`product-image-${product.id}`}>
                        <div className="relative overflow-hidden">
                          <img
                            src={product.images[0]}
                            alt={product.name}
                            className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                            <div className="absolute bottom-4 left-4 right-4">
                              <Button
                                size="sm"
                                className="w-full gap-2 cursor-pointer"
                              >
                                <Eye className="h-4 w-4" />
                                查看详情
                              </Button>
                            </div>
                          </div>
                          <Badge
                            className={`absolute top-4 right-4`}
                            variant={statusColor}
                          >
                            {statusBadge}
                          </Badge>
                          {product.priority >= 90 && (
                            <Badge className="absolute top-4 left-4 bg-primary">
                              推荐
                            </Badge>
                          )}
                        </div>
                      </ViewTransition>
                    </CardHeader>
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="secondary">{typeBadge}</Badge>
                        <Badge variant="outline">{product.category.name}</Badge>
                      </div>

                      <CardTitle className="mb-2 group-hover:text-primary transition-colors line-clamp-2">
                        {product.name}
                      </CardTitle>

                      <CardDescription className="mb-4 line-clamp-3">
                        {product.description}
                      </CardDescription>

                      {/* 产品特色 */}
                      <div className="flex flex-wrap gap-1 mb-4">
                        {product.features
                          .slice(0, 3)
                          .map((feature, featureIndex) => (
                            <Badge
                              key={featureIndex}
                              variant="outline"
                              className="text-xs"
                            >
                              {feature}
                            </Badge>
                          ))}
                      </div>

                      {/* 产品信息 */}
                      <div className="space-y-2 mb-4 text-sm text-muted-foreground">
                        {product.duration && (
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            <span>时长：{product.duration}</span>
                          </div>
                        )}
                        {product.location && (
                          <div className="flex items-center gap-1">
                            <MapPin className="h-4 w-4" />
                            <span>地点：{product.location}</span>
                          </div>
                        )}
                        {product.capacity && (
                          <div className="flex items-center gap-1">
                            <Users className="h-4 w-4" />
                            <span>名额：{product.capacity}人</span>
                          </div>
                        )}
                        {product.startDate && (
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            <span>
                              开始：{product.startDate.toLocaleDateString()}
                            </span>
                          </div>
                        )}
                      </div>

                      {/* 目标人群 */}
                      {product.targetAudience.length > 0 && (
                        <div className="mb-4">
                          <p className="text-xs text-muted-foreground mb-1">
                            适合人群：
                          </p>
                          <div className="flex flex-wrap gap-1">
                            {product.targetAudience
                              .slice(0, 3)
                              .map((audience, index) => (
                                <Badge
                                  key={index}
                                  variant="outline"
                                  className="text-xs"
                                >
                                  {audience}
                                </Badge>
                              ))}
                          </div>
                        </div>
                      )}

                      {/* 合作方信息 */}
                      {product.partner && (
                        <div className="flex items-center gap-2 mb-3">
                          {product.partner.logo && (
                            <img
                              src={product.partner.logo}
                              alt={product.partner.name}
                              className="w-6 h-6 rounded-full"
                            />
                          )}
                          <div className="text-sm">
                            <span className="font-medium text-primary">
                              {product.partner.name}
                            </span>
                          </div>
                        </div>
                      )}
                    </CardContent>
                    <CardFooter className="pt-0">
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center gap-2">
                          {product.price ? (
                            <div className="flex items-center gap-2">
                              <div className="text-lg font-bold text-primary">
                                ¥{product.price.toLocaleString()}
                              </div>
                              {product.priceUnit && (
                                <div className="text-sm text-muted-foreground">
                                  /{product.priceUnit}
                                </div>
                              )}
                            </div>
                          ) : (
                            <div className="text-lg font-bold text-primary">
                              价格面议
                            </div>
                          )}
                        </div>
                        <Button variant="outline" size="sm">
                          立即咨询
                        </Button>
                      </div>
                      {product.priceNote && (
                        <p className="text-xs text-muted-foreground mt-2">
                          {product.priceNote}
                        </p>
                      )}
                    </CardFooter>
                  </Card>
                </Link>
              );
            })}
          </div>
        </div>
      </section>

      {/* Service Features Section */}
      <section className="py-12 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">服务优势</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              专业团队，权威合作，一站式服务，助力每一位学员实现梦想
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                icon: <IconAward className="w-8 h-8 text-primary" />,
                title: "权威合作",
                description:
                  "与清华、北大等50+顶尖院校深度合作，提供官方认证服务",
              },
              {
                icon: <IconUsers className="w-8 h-8 text-primary" />,
                title: "专业团队",
                description: "汇聚海内外知名学者、教授和行业专家，提供专业指导",
              },
              {
                icon: <IconTrendingUp className="w-8 h-8 text-primary" />,
                title: "成果显著",
                description: "8000+学员成功案例，名校录取率85%，满意度98.5%",
              },
              {
                icon: <IconBuilding className="w-8 h-8 text-primary" />,
                title: "全程服务",
                description: "从咨询到申请，从培训到认证，提供一站式专业服务",
              },
            ].map((feature, index) => (
              <Card
                key={index}
                className="text-center hover:shadow-lg transition-shadow"
              >
                <CardContent className="p-6">
                  <div className="flex justify-center mb-4">
                    <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center">
                      {feature.icon}
                    </div>
                  </div>
                  <h3 className="text-xl font-bold mb-2">{feature.title}</h3>
                  <p className="text-muted-foreground">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
