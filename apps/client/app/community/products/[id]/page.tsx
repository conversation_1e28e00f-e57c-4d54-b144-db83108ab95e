"use client";
import { useParams } from "next/navigation";
import { Badge } from "@workspace/ui/components/badge";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  <PERSON>,
  CardContent,
  Card<PERSON>eader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Separator } from "@workspace/ui/components/separator";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ist,
  Ta<PERSON>Trigger,
} from "@workspace/ui/components/tabs";
import {
  ArrowLeft,
  Calendar,
  MapPin,
  Users,
  Clock,
  Building,
  Download,
  Share,
  Heart,
  CheckCircle,
  Star,
} from "lucide-react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { unstable_ViewTransition as ViewTransition } from "react";
import { useEffect, useState } from "react";

// 使用与 products/page.tsx 相同的类型定义
type ProductType =
  | "STUDY_CAMP"
  | "CERTIFICATE"
  | "BACKGROUND"
  | "SCHOOL_LINK"
  | "INSTITUTION"
  | "OTHER";
type ProductStatus = "DRAFT" | "ACTIVE" | "INACTIVE" | "SOLD_OUT";

interface ProductCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  type: ProductType;
  icon?: string;
  order: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface Partner {
  id: string;
  name: string;
  code: string;
  logo?: string;
  contactName: string;
  contactPhone: string;
  contactEmail: string;
  address?: string;
  cooperationType: string[];
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface Product {
  id: string;
  name: string;
  code: string;
  type: ProductType;
  categoryId: string;
  category: ProductCategory;
  description: string;
  features: string[];
  highlights?: any;
  images: string[];
  brochureUrl?: string;
  price?: number;
  priceUnit?: string;
  priceNote?: string;
  duration?: string;
  location?: string;
  startDate?: Date;
  endDate?: Date;
  capacity?: number;
  minParticipants?: number;
  targetAudience: string[];
  ageRange?: string;
  gradeRange?: string;
  partnerId?: string;
  partner?: Partner;
  status: ProductStatus;
  priority: number;
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords: string[];
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  createdById: string;
  _count?: {
    inquiries: number;
  };
}

export default function ProductDetailPage() {
  const { id } = useParams<{ id: string }>();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch product data
  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/products/${id}`);
        if (!response.ok) {
          if (response.status === 404) {
            notFound();
          }
          throw new Error("获取产品详情失败");
        }

        const productData = await response.json();

        // Transform API response to match expected format
        const transformedProduct = {
          ...(productData.data ?? {}),
          createdAt: new Date(productData.data.createdAt),
          updatedAt: new Date(productData.data.updatedAt),
          publishedAt: productData.data.publishedAt
            ? new Date(productData.data.publishedAt)
            : null,
          startDate: productData.data.startDate
            ? new Date(productData.data.startDate)
            : null,
          endDate: productData.data.endDate
            ? new Date(productData.data.endDate)
            : null,
          category: {
            ...(productData.data.category ?? {}),
            createdAt: new Date(productData.data.category.createdAt),
            updatedAt: new Date(productData.data.category.updatedAt),
          },
          partner: productData.data.partner
            ? {
                ...(productData.data.partner ?? {}),
                createdAt: new Date(productData.data.partner.createdAt),
                updatedAt: new Date(productData.data.partner.updatedAt),
              }
            : undefined,
        };

        setProduct(transformedProduct);
      } catch (err) {
        console.error("获取产品详情失败:", err);
        setError(err instanceof Error ? err.message : "获取产品详情失败");
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [id]);

  if (loading) {
    return (
      <div className="flex flex-1 flex-col items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    );
  }

  if (!product) {
    notFound();
  }

  // 产品类型标签映射
  const typeBadge = {
    STUDY_CAMP: "研学营",
    CERTIFICATE: "证书认证",
    BACKGROUND: "背景提升",
    SCHOOL_LINK: "名校链接",
    INSTITUTION: "机构提升",
    OTHER: "其他服务",
  }[product.type];

  // 状态标签映射
  const statusBadge = {
    DRAFT: "草稿",
    ACTIVE: "热招中",
    INACTIVE: "已下架",
    SOLD_OUT: "已满员",
  }[product.status];

  const statusColor = {
    DRAFT: "secondary",
    ACTIVE: "default",
    INACTIVE: "secondary",
    SOLD_OUT: "destructive",
  }[product.status] as "default" | "secondary" | "destructive";

  return (
    <div className="flex flex-1 flex-col">
      {/* Header */}
      <div className="px-4 py-6 lg:px-6">
        <div className="mb-6">
          <Link
            href="/community/products"
            className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground mb-4"
          >
            <ArrowLeft className="h-4 w-4" />
            返回产品列表
          </Link>
          <div className="flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-2xl font-semibold">{product.name}</h1>
              <p className="text-muted-foreground">{product.code}</p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" className="gap-2">
                <Heart className="h-4 w-4" />
                收藏
              </Button>
              <Button variant="outline" size="sm" className="gap-2">
                <Share className="h-4 w-4" />
                分享
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="px-4 lg:px-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Product Images */}
            <div className="mb-8">
              <ViewTransition name={`product-image-${product.id}`}>
                <div className="relative mb-4">
                  <img
                    src={product.images[0]}
                    alt={product.name}
                    className="w-full h-64 md:h-96 object-cover rounded-lg"
                  />
                  <div className="absolute top-4 right-4 flex gap-2">
                    <Badge variant={statusColor}>{statusBadge}</Badge>
                    {product.priority >= 90 && (
                      <Badge className="bg-primary">推荐</Badge>
                    )}
                  </div>
                </div>
              </ViewTransition>

              {product.images.length > 1 && (
                <div className="grid grid-cols-3 gap-2">
                  {product.images.slice(1).map((image, index) => (
                    <img
                      key={index}
                      src={image}
                      alt={`${product.name} - ${index + 2}`}
                      className="w-full h-24 object-cover rounded cursor-pointer hover:opacity-80 transition-opacity"
                    />
                  ))}
                </div>
              )}
            </div>

            {/* Product Details Tabs */}
            <Tabs defaultValue="overview" className="mb-8">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">概述</TabsTrigger>
                <TabsTrigger value="features">特色</TabsTrigger>
                <TabsTrigger value="schedule">安排</TabsTrigger>
                <TabsTrigger value="requirements">要求</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>产品介绍</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground leading-relaxed">
                      {product.description}
                    </p>
                    {product.highlights && (
                      <div className="mt-4 p-4 bg-primary/5 rounded-lg border-l-4 border-primary">
                        <p className="font-medium text-primary">
                          ✨ {product.highlights.content}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Partner Information */}
                {product.partner && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Building className="h-5 w-5" />
                        合作方信息
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-start gap-4">
                        {product.partner.logo && (
                          <img
                            src={product.partner.logo}
                            alt={product.partner.name}
                            className="w-16 h-16 rounded-lg object-cover"
                          />
                        )}
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg">
                            {product.partner.name}
                          </h3>
                          {product.partner.description && (
                            <p className="text-muted-foreground mb-2">
                              {product.partner.description}
                            </p>
                          )}
                          <div className="space-y-1 text-sm">
                            <p>
                              <span className="font-medium">联系人：</span>
                              {product.partner.contactName}
                            </p>
                            <p>
                              <span className="font-medium">电话：</span>
                              {product.partner.contactPhone}
                            </p>
                            <p>
                              <span className="font-medium">邮箱：</span>
                              {product.partner.contactEmail}
                            </p>
                            {product.partner.address && (
                              <p>
                                <span className="font-medium">地址：</span>
                                {product.partner.address}
                              </p>
                            )}
                          </div>
                          {product.partner.cooperationType.length > 0 && (
                            <div className="mt-3">
                              <p className="text-sm font-medium mb-1">
                                合作类型：
                              </p>
                              <div className="flex flex-wrap gap-1">
                                {product.partner.cooperationType.map(
                                  (type, index) => (
                                    <Badge
                                      key={index}
                                      variant="outline"
                                      className="text-xs"
                                    >
                                      {type}
                                    </Badge>
                                  ),
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="features" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>产品特色</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {product.features.map((feature, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-3 p-3 rounded-lg bg-muted/50"
                        >
                          <CheckCircle className="h-5 w-5 text-primary" />
                          <span>{feature}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="schedule" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>时间安排</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {product.startDate && product.endDate && (
                        <div className="flex items-center gap-2 text-sm">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>
                            活动时间：{product.startDate.toLocaleDateString()} -{" "}
                            {product.endDate.toLocaleDateString()}
                          </span>
                        </div>
                      )}
                      {product.duration && (
                        <div className="flex items-center gap-2 text-sm">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span>活动时长：{product.duration}</span>
                        </div>
                      )}
                      {product.location && (
                        <div className="flex items-center gap-2 text-sm">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <span>活动地点：{product.location}</span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="requirements" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>参与要求</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {product.targetAudience.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-2">目标人群</h4>
                        <div className="flex flex-wrap gap-2">
                          {product.targetAudience.map((audience, index) => (
                            <Badge key={index} variant="outline">
                              {audience}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {product.ageRange && (
                      <div>
                        <h4 className="font-medium mb-2">年龄要求</h4>
                        <p className="text-muted-foreground">
                          {product.ageRange}
                        </p>
                      </div>
                    )}

                    {product.gradeRange && (
                      <div>
                        <h4 className="font-medium mb-2">年级要求</h4>
                        <p className="text-muted-foreground">
                          {product.gradeRange}
                        </p>
                      </div>
                    )}

                    {product.capacity && (
                      <div>
                        <h4 className="font-medium mb-2">招生名额</h4>
                        <p className="text-muted-foreground">
                          总名额 {product.capacity} 人
                        </p>
                        {product.minParticipants && (
                          <p className="text-muted-foreground text-sm">
                            最低开班人数：{product.minParticipants} 人
                          </p>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Pricing Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>价格信息</span>
                  <Badge variant="secondary">{typeBadge}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {product.price ? (
                    <div>
                      <div className="flex items-baseline gap-2">
                        <span className="text-3xl font-bold text-primary">
                          ¥{product.price.toLocaleString()}
                        </span>
                        {product.priceUnit && (
                          <span className="text-muted-foreground">
                            / {product.priceUnit}
                          </span>
                        )}
                      </div>
                      {product.priceNote && (
                        <p className="text-sm text-muted-foreground mt-1">
                          {product.priceNote}
                        </p>
                      )}
                    </div>
                  ) : (
                    <div className="text-2xl font-bold text-primary">
                      价格面议
                    </div>
                  )}

                  <Separator />

                  <div className="space-y-3">
                    <Button className="w-full" size="lg">
                      立即报名
                    </Button>
                    <Button variant="outline" className="w-full" size="lg">
                      咨询详情
                    </Button>
                    {product.brochureUrl && (
                      <Button
                        variant="ghost"
                        className="w-full gap-2"
                        size="lg"
                      >
                        <Download className="h-4 w-4" />
                        下载宣传册
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Product Info Card */}
            <Card>
              <CardHeader>
                <CardTitle>产品信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span>产品类别：{product.category.name}</span>
                </div>

                {product.duration && (
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>时长：{product.duration}</span>
                  </div>
                )}

                {product.location && (
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>地点：{product.location}</span>
                  </div>
                )}

                {product.capacity && (
                  <div className="flex items-center gap-2 text-sm">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span>名额：{product.capacity} 人</span>
                  </div>
                )}

                {product.publishedAt && (
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>
                      发布时间：{product.publishedAt.toLocaleDateString()}
                    </span>
                  </div>
                )}

                {product._count?.inquiries && (
                  <div className="flex items-center gap-2 text-sm">
                    <Star className="h-4 w-4 text-muted-foreground" />
                    <span>咨询次数：{product._count.inquiries} 次</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Contact Card */}
            <Card>
              <CardHeader>
                <CardTitle>联系我们</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm space-y-2">
                  <p>
                    <span className="font-medium">咨询电话：</span>400-123-4567
                  </p>
                  <p>
                    <span className="font-medium">客服微信：</span>qyqm-teachers
                  </p>
                  <p>
                    <span className="font-medium">工作时间：</span>周一至周五
                    9:00-18:00
                  </p>
                </div>
                <Separator />
                <div className="text-xs text-muted-foreground">
                  <p>如有任何疑问，欢迎随时联系我们的专业顾问团队</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
