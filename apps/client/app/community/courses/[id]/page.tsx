"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  Clock,
  Users,
  BookO<PERSON>,
  CheckCircle,
  ArrowLeft,
  Star,
  Eye,
} from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import { useParams } from "next/navigation";
import { unstable_ViewTransition as ViewTransition } from "react";
import Link from "next/link";
import { motion } from "framer-motion";

// 数据类型定义（对应 schema.prisma）
type CourseLevel = "BEGINNER" | "INTERMEDIATE" | "ADVANCED" | "ALL";
type CourseStatus = "DRAFT" | "REVIEWING" | "PUBLISHED" | "OFFLINE";

interface Course {
  id: string;
  title: string;
  subtitle?: string;
  description: string;
  cover: string;
  previewVideo?: string;
  categoryId: string;
  category: {
    name: string;
  };
  tags: string[];
  level: CourseLevel;
  duration: number; // 总时长（分钟）
  lessonsCount: number; // 课时数
  instructorName: string;
  instructorTitle?: string;
  instructorAvatar?: string;
  instructorBio?: string;
  price: number;
  originalPrice?: number;
  isFree: boolean;
  status: CourseStatus;
  viewCount: number;
  enrollCount: number;
  rating?: number;
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface CourseChapter {
  id: string;
  courseId: string;
  title: string;
  description?: string;
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

interface CourseLesson {
  id: string;
  chapterId: string;
  title: string;
  description?: string;
  videoUrl: string;
  videoDuration: number; // 视频时长（秒）
  videoSize?: number; // 视频大小（字节）
  order: number;
  isFree: boolean; // 是否免费试看
  createdAt: Date;
  updatedAt: Date;
}

interface CourseEnrollment {
  id: string;
  courseId: string;
  userId: string;
  enrolledAt: Date;
  completedAt?: Date;
  progress: number; // 0-100
  lastAccessAt?: Date;
  isPaid: boolean;
  paidAmount?: number;
}

interface LessonProgress {
  id: string;
  lessonId: string;
  userId: string;
  watchedDuration: number; // 已观看时长（秒）
  lastPosition: number; // 上次观看位置（秒）
  isCompleted: boolean;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface ChapterWithLessons extends CourseChapter {
  lessons: CourseLesson[];
}

interface CourseWithChapters extends Course {
  chapters: ChapterWithLessons[];
}

// 模拟课程数据
const mockCourseData: CourseWithChapters[] = [
  {
    id: "1",
    title: "学业规划师认证基础课程",
    subtitle: "从零开始掌握学业规划核心理论",
    description:
      "深入学习学业规划理论基础，掌握专业咨询技能，为学生提供科学的学业指导。本课程涵盖教育心理学、职业发展理论、咨询技巧等核心内容，是学业规划师职业发展的必修课程。",
    cover:
      "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=2071&h=1200&fit=crop",
    previewVideo: "https://example.com/preview1.mp4",
    categoryId: "1",
    category: { name: "基础理论" },
    tags: ["基础", "认证", "理论", "心理学"],
    level: "BEGINNER" as CourseLevel,
    duration: 1440, // 24小时 = 1440分钟
    lessonsCount: 24,
    instructorName: "李明教授",
    instructorTitle: "教育学博士",
    instructorAvatar:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
    instructorBio:
      "北京师范大学教育学博士，从事学业规划研究15年，主导多项国家级教育项目，著有《学业规划理论与实践》等专著。",
    price: 0,
    originalPrice: 299,
    isFree: true,
    status: "PUBLISHED" as CourseStatus,
    viewCount: 15420,
    enrollCount: 1200,
    rating: 4.8,
    publishedAt: new Date("2024-01-15"),
    createdAt: new Date("2024-01-10"),
    updatedAt: new Date("2024-01-20"),
    chapters: [
      {
        id: "chapter1",
        courseId: "1",
        title: "学业规划基础理论",
        description: "理解学业规划的核心概念和理论基础，为后续学习奠定基础",
        order: 1,
        createdAt: new Date("2024-01-10"),
        updatedAt: new Date("2024-01-15"),
        lessons: [
          {
            id: "lesson1-1",
            chapterId: "chapter1",
            title: "什么是学业规划",
            description: "学业规划的定义、重要性及其在个人发展中的作用",
            videoUrl: "https://example.com/lesson1-1.mp4",
            videoDuration: 876, // 14:36
            order: 1,
            isFree: true,
            createdAt: new Date("2024-01-10"),
            updatedAt: new Date("2024-01-15"),
          },
          {
            id: "lesson1-2",
            chapterId: "chapter1",
            title: "学业规划的发展历程",
            description: "回顾学业规划理论的发展历程和主要流派",
            videoUrl: "https://example.com/lesson1-2.mp4",
            videoDuration: 1200, // 20:00
            order: 2,
            isFree: true,
            createdAt: new Date("2024-01-10"),
            updatedAt: new Date("2024-01-15"),
          },
          {
            id: "lesson1-3",
            chapterId: "chapter1",
            title: "学业规划师的角色定位",
            description: "明确学业规划师的职责、能力要求和职业道德",
            videoUrl: "https://example.com/lesson1-3.mp4",
            videoDuration: 900, // 15:00
            order: 3,
            isFree: false,
            createdAt: new Date("2024-01-10"),
            updatedAt: new Date("2024-01-15"),
          },
        ],
      },
      {
        id: "chapter2",
        courseId: "1",
        title: "教育心理学基础",
        description: "掌握教育心理学的基本理论，理解学生的心理发展规律",
        order: 2,
        createdAt: new Date("2024-01-12"),
        updatedAt: new Date("2024-01-17"),
        lessons: [
          {
            id: "lesson2-1",
            chapterId: "chapter2",
            title: "认知发展理论",
            description: "皮亚杰认知发展理论在学业规划中的应用",
            videoUrl: "https://example.com/lesson2-1.mp4",
            videoDuration: 1080, // 18:00
            order: 1,
            isFree: false,
            createdAt: new Date("2024-01-12"),
            updatedAt: new Date("2024-01-17"),
          },
          {
            id: "lesson2-2",
            chapterId: "chapter2",
            title: "学习动机理论",
            description: "了解不同类型的学习动机及其培养方法",
            videoUrl: "https://example.com/lesson2-2.mp4",
            videoDuration: 960, // 16:00
            order: 2,
            isFree: false,
            createdAt: new Date("2024-01-12"),
            updatedAt: new Date("2024-01-17"),
          },
        ],
      },
    ],
  },
  {
    id: "2",
    title: "高中学业规划实战指南",
    subtitle: "针对高中生的专业规划方法",
    description:
      "专门针对高中阶段学生的学业规划实战课程，包含选科指导、大学专业选择、升学路径规划等实用内容。通过真实案例分析，帮助规划师掌握高中生涯规划的核心技能。",
    cover:
      "https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=2071&h=1200&fit=crop",
    previewVideo: "https://example.com/preview2.mp4",
    categoryId: "2",
    category: { name: "实战技能" },
    tags: ["高中", "实战", "升学", "选科"],
    level: "INTERMEDIATE" as CourseLevel,
    duration: 1800, // 30小时
    lessonsCount: 30,
    instructorName: "张芳老师",
    instructorTitle: "高级学业规划师",
    instructorAvatar:
      "https://images.unsplash.com/photo-1494790108755-2616b412c1d0?w=150&h=150&fit=crop&crop=face",
    instructorBio:
      "华东师范大学教育硕士，高级学业规划师，专注高中生涯规划12年，服务学生超过3000人。",
    price: 199,
    originalPrice: 399,
    isFree: false,
    status: "PUBLISHED" as CourseStatus,
    viewCount: 8750,
    enrollCount: 650,
    rating: 4.9,
    publishedAt: new Date("2024-02-01"),
    createdAt: new Date("2024-01-25"),
    updatedAt: new Date("2024-02-05"),
    chapters: [
      {
        id: "chapter2-1",
        courseId: "2",
        title: "高中生涯规划概览",
        description: "了解高中生涯规划的重要性、基本流程和关键节点",
        order: 1,
        createdAt: new Date("2024-01-25"),
        updatedAt: new Date("2024-01-30"),
        lessons: [
          {
            id: "lesson2-1-1",
            chapterId: "chapter2-1",
            title: "高中生涯规划的重要性",
            description:
              "为什么高中阶段是生涯规划的关键期，如何把握这个黄金时间",
            videoUrl: "https://example.com/lesson2-1-1.mp4",
            videoDuration: 960, // 16:00
            order: 1,
            isFree: true,
            createdAt: new Date("2024-01-25"),
            updatedAt: new Date("2024-01-30"),
          },
          {
            id: "lesson2-1-2",
            chapterId: "chapter2-1",
            title: "高中三年规划时间轴",
            description: "详解高一到高三各阶段的规划重点和关键任务",
            videoUrl: "https://example.com/lesson2-1-2.mp4",
            videoDuration: 1140, // 19:00
            order: 2,
            isFree: true,
            createdAt: new Date("2024-01-25"),
            updatedAt: new Date("2024-01-30"),
          },
          {
            id: "lesson2-1-3",
            chapterId: "chapter2-1",
            title: "常见规划误区与避坑指南",
            description: "分析高中生涯规划中的常见误区，提供实用的避坑策略",
            videoUrl: "https://example.com/lesson2-1-3.mp4",
            videoDuration: 840, // 14:00
            order: 3,
            isFree: false,
            createdAt: new Date("2024-01-25"),
            updatedAt: new Date("2024-01-30"),
          },
        ],
      },
      {
        id: "chapter2-2",
        courseId: "2",
        title: "选科指导与专业匹配",
        description: "掌握新高考选科策略，学会专业与选科的匹配分析",
        order: 2,
        createdAt: new Date("2024-01-27"),
        updatedAt: new Date("2024-02-02"),
        lessons: [
          {
            id: "lesson2-2-1",
            chapterId: "chapter2-2",
            title: "新高考选科全解析",
            description: "深入了解3+1+2、3+3等选科模式的特点和策略",
            videoUrl: "https://example.com/lesson2-2-1.mp4",
            videoDuration: 1320, // 22:00
            order: 1,
            isFree: false,
            createdAt: new Date("2024-01-27"),
            updatedAt: new Date("2024-02-02"),
          },
          {
            id: "lesson2-2-2",
            chapterId: "chapter2-2",
            title: "专业与选科匹配分析",
            description: "如何根据目标专业制定最优选科方案",
            videoUrl: "https://example.com/lesson2-2-2.mp4",
            videoDuration: 1080, // 18:00
            order: 2,
            isFree: false,
            createdAt: new Date("2024-01-27"),
            updatedAt: new Date("2024-02-02"),
          },
          {
            id: "lesson2-2-3",
            chapterId: "chapter2-2",
            title: "选科决策工具与实战演练",
            description: "使用专业的选科决策工具，通过真实案例进行实战演练",
            videoUrl: "https://example.com/lesson2-2-3.mp4",
            videoDuration: 1560, // 26:00
            order: 3,
            isFree: false,
            createdAt: new Date("2024-01-27"),
            updatedAt: new Date("2024-02-02"),
          },
        ],
      },
      {
        id: "chapter2-3",
        courseId: "2",
        title: "升学路径规划",
        description: "全面了解各种升学路径，制定个性化的升学策略",
        order: 3,
        createdAt: new Date("2024-01-29"),
        updatedAt: new Date("2024-02-04"),
        lessons: [
          {
            id: "lesson2-3-1",
            chapterId: "chapter2-3",
            title: "强基计划申请策略",
            description: "深入解析强基计划的申请条件、流程和成功策略",
            videoUrl: "https://example.com/lesson2-3-1.mp4",
            videoDuration: 1440, // 24:00
            order: 1,
            isFree: false,
            createdAt: new Date("2024-01-29"),
            updatedAt: new Date("2024-02-04"),
          },
          {
            id: "lesson2-3-2",
            chapterId: "chapter2-3",
            title: "综合评价招生详解",
            description: "了解综合评价招生的特点、优势和申请技巧",
            videoUrl: "https://example.com/lesson2-3-2.mp4",
            videoDuration: 1200, // 20:00
            order: 2,
            isFree: false,
            createdAt: new Date("2024-01-29"),
            updatedAt: new Date("2024-02-04"),
          },
          {
            id: "lesson2-3-3",
            chapterId: "chapter2-3",
            title: "艺术类专业升学指导",
            description: "艺术类专业的升学路径、考试要求和备考策略",
            videoUrl: "https://example.com/lesson2-3-3.mp4",
            videoDuration: 1080, // 18:00
            order: 3,
            isFree: false,
            createdAt: new Date("2024-01-29"),
            updatedAt: new Date("2024-02-04"),
          },
        ],
      },
    ],
  },
];

// 模拟用户报名数据
const mockEnrollment: CourseEnrollment = {
  id: "enrollment1",
  courseId: "1",
  userId: "user1",
  enrolledAt: new Date("2024-01-20"),
  progress: 65,
  lastAccessAt: new Date("2024-01-25"),
  isPaid: false,
};

// 模拟课时进度数据
const mockLessonProgress: LessonProgress[] = [
  {
    id: "progress1",
    lessonId: "lesson1-1",
    userId: "user1",
    watchedDuration: 876, // 完全看完
    lastPosition: 876,
    isCompleted: true,
    completedAt: new Date("2024-01-21"),
    createdAt: new Date("2024-01-21"),
    updatedAt: new Date("2024-01-21"),
  },
  {
    id: "progress2",
    lessonId: "lesson1-2",
    userId: "user1",
    watchedDuration: 800, // 部分观看
    lastPosition: 800,
    isCompleted: false,
    createdAt: new Date("2024-01-22"),
    updatedAt: new Date("2024-01-22"),
  },
];

const Course = () => {
  const params = useParams();
  const courseId = params.id as string;
  const [currentLesson, setCurrentLesson] = useState<string>("lesson1-1");

  // 根据courseId获取课程数据
  const courseData =
    mockCourseData.find((course) => course.id === courseId) ||
    mockCourseData[0];

  // 获取用户的报名信息
  const enrollment = mockEnrollment;

  // 格式化时长
  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  // 检查课时是否已完成
  const isLessonCompleted = (lessonId: string): boolean => {
    return mockLessonProgress.some(
      (progress) => progress.lessonId === lessonId && progress.isCompleted,
    );
  };

  if (!courseData) {
    return <div>课程未找到</div>;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Back Button */}
      <div className="container ml-8 pt-5">
        <Link href="/community/courses">
          <Button variant="ghost" className="gap-2 mb-4">
            <ArrowLeft className="w-4 h-4" />
            返回课程列表
          </Button>
        </Link>
      </div>

      {/* Hero Video Section */}
      <div className="w-full md:w-[60vw] rounded-lg relative h-[64vh] min-h-[500px] overflow-hidden mx-auto">
        {/* Background Image/Video */}
        <div className="absolute inset-0">
          <ViewTransition name={`course-image-${courseId}`}>
            <img
              src={`https://images.unsplash.com/photo-${
                courseId === "1"
                  ? "1522202176988-66273c2fd55f"
                  : courseId === "2"
                    ? "1503676260728-1c00da094a0b"
                    : courseId === "3"
                      ? "1516321318423-f06f85e504b3"
                      : courseId === "4"
                        ? "1513475382585-d06e58bcb0e0"
                        : courseId === "5"
                          ? "1523050854058-8df90110c9f1"
                          : "1488190211105-8b0e65b80b4e"
              }?w=2071&h=1200&fit=crop`}
              alt="Course background"
              className="w-full h-full object-cover"
            />
          </ViewTransition>
          {/* Dark overlay for better text readability */}
          <motion.div
            className="absolute inset-0 bg-black/40"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, ease: "easeOut" }}
          />
          {/* Bottom gradient overlay for smooth transition */}
          <motion.div
            className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-background via-background/80 to-transparent"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.52, delay: 0.24, ease: "easeOut" }}
          />
        </div>

        {/* Play button centered */}
        <div className="absolute inset-0 flex items-center justify-center">
          <Button
            size="lg"
            className="rounded-full h-20 w-20 p-0 bg-white/20 hover:bg-white/30 backdrop-blur-sm border border-white/20"
          >
            <Play className="w-10 h-10 text-white ml-1" />
          </Button>
        </div>

        {/* Course Info Overlay */}
        <div className="absolute bottom-0 left-0 right-0 p-8">
          <div className="container mx-auto max-w-4xl">
            <div className="flex items-center mb-4">
              <Play className="w-6 h-6 mr-3 text-white" />
              <h1 className="text-4xl md:text-5xl font-bold text-white">
                {courseData.title}
              </h1>
            </div>

            {courseData.subtitle && (
              <p className="text-white/80 mb-2 text-lg">
                {courseData.subtitle}
              </p>
            )}

            <p className="text-white/90 mb-6 text-xl leading-relaxed max-w-3xl">
              {courseData.description}
            </p>

            <div className="flex flex-wrap items-center gap-4 mb-8">
              <Badge
                variant="secondary"
                className="gap-2 py-2 px-4 text-sm bg-white/10 text-white border-white/20 backdrop-blur-sm"
              >
                <BookOpen className="w-4 h-4" />
                {courseData.chapters.length} 章节
              </Badge>
              <Badge
                variant="secondary"
                className="gap-2 py-2 px-4 text-sm bg-white/10 text-white border-white/20 backdrop-blur-sm"
              >
                <Users className="w-4 h-4" />
                {courseData.lessonsCount} 课时
              </Badge>
              <Badge
                variant="secondary"
                className="gap-2 py-2 px-4 text-sm bg-white/10 text-white border-white/20 backdrop-blur-sm"
              >
                <Clock className="w-4 h-4" />
                {Math.floor(courseData.duration / 60)} 小时
              </Badge>
              <Badge
                variant="secondary"
                className="gap-2 py-2 px-4 text-sm bg-white/10 text-white border-white/20 backdrop-blur-sm"
              >
                <Star className="w-4 h-4" />
                {courseData.rating} 评分
              </Badge>
              <Badge
                variant="secondary"
                className="gap-2 py-2 px-4 text-sm bg-white/10 text-white border-white/20 backdrop-blur-sm"
              >
                <Eye className="w-4 h-4" />
                {courseData.viewCount} 浏览
              </Badge>
            </div>

            <div className="flex items-center gap-4 mb-6">
              {courseData.instructorAvatar && (
                <img
                  src={courseData.instructorAvatar}
                  alt={courseData.instructorName}
                  className="w-12 h-12 rounded-full border-2 border-white/20"
                />
              )}
              <div className="text-white">
                <div className="font-semibold">{courseData.instructorName}</div>
                {courseData.instructorTitle && (
                  <div className="text-white/80 text-sm">
                    {courseData.instructorTitle}
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center gap-4">
              <Button size="lg" className="px-8 py-3 text-lg font-semibold">
                {enrollment
                  ? "继续学习"
                  : courseData.isFree
                    ? "免费开始学习"
                    : `立即购买 ¥${courseData.price}`}
              </Button>
              {!courseData.isFree && (
                <div className="text-white">
                  <div className="text-2xl font-bold">¥{courseData.price}</div>
                  {courseData.originalPrice &&
                    courseData.originalPrice > courseData.price && (
                      <div className="text-white/60 line-through text-sm">
                        原价 ¥{courseData.originalPrice}
                      </div>
                    )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Course Chapters */}
      <div className="container mx-auto px-8 py-12 max-w-4xl">
        <div className="space-y-16">
          {courseData.chapters.map((chapter, chapterIndex) => (
            <div key={chapter.id} className="space-y-8">
              {/* Chapter Header */}
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Badge
                    variant="outline"
                    className="px-3 py-1 text-sm font-medium"
                  >
                    第 {chapterIndex + 1} 章
                  </Badge>
                </div>
                <h2 className="text-2xl md:text-3xl font-bold text-foreground">
                  {chapter.title}
                </h2>
                {chapter.description && (
                  <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl">
                    {chapter.description}
                  </p>
                )}
              </div>

              {/* Lessons */}
              <div className="space-y-3">
                {chapter.lessons.map((lesson, lessonIndex) => {
                  const isCompleted = isLessonCompleted(lesson.id);
                  const isCurrentLesson = currentLesson === lesson.id;

                  return (
                    <div
                      key={lesson.id}
                      className={`group p-6 transition-all cursor-pointer rounded-lg ${
                        isCurrentLesson
                          ? "bg-primary/5 ring-2 ring-primary/20"
                          : "hover:bg-muted/50"
                      }`}
                      onClick={() => setCurrentLesson(lesson.id)}
                    >
                      <div className="flex items-start gap-4">
                        <div className="flex-shrink-0 mt-1">
                          {isCompleted ? (
                            <CheckCircle className="w-6 h-6 text-green-500" />
                          ) : (
                            <div className="w-6 h-6 rounded-full border-2 border-muted-foreground/30 flex items-center justify-center text-xs text-muted-foreground font-medium">
                              {lessonIndex + 1}
                            </div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between gap-4 mb-3">
                            <div className="flex items-center gap-2">
                              <h3 className="font-semibold text-foreground text-lg group-hover:text-primary transition-colors">
                                {lesson.title}
                              </h3>
                              {lesson.isFree && (
                                <Badge variant="secondary" className="text-xs">
                                  免费试看
                                </Badge>
                              )}
                            </div>
                            <Badge
                              variant="outline"
                              className="gap-1 flex-shrink-0"
                            >
                              <Clock className="w-3 h-3" />
                              {formatDuration(lesson.videoDuration)}
                            </Badge>
                          </div>
                          {lesson.description && (
                            <p className="text-muted-foreground leading-relaxed">
                              {lesson.description}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Course;
