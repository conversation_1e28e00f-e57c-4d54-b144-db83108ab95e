"use client";

import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>con<PERSON><PERSON>,
  IconCalendar,
  IconChartBar,
  IconSchool,
  IconUsers,
} from "@tabler/icons-react";

import { Badge } from "@workspace/ui/components/badge";
import { Button } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { ArrowRight, Play, Star, Clock, Users, BookOpen, Eye } from "lucide-react";
import Link from "next/link";
import { unstable_ViewTransition as ViewTransition } from "react";

// 模拟数据类型定义（对应 schema.prisma）
type CourseLevel = 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'ALL';
type CourseStatus = 'DRAFT' | 'REVIEWING' | 'PUBLISHED' | 'OFFLINE';

interface CourseCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  icon: string;
  order: number;
  isActive: boolean;
  courseCount: number; // 统计字段
}

interface Course {
  id: string;
  title: string;
  subtitle?: string;
  description: string;
  cover: string;
  previewVideo?: string;
  categoryId: string;
  category: {
    name: string;
  };
  tags: string[];
  level: CourseLevel;
  duration: number; // 总时长（分钟）
  lessonsCount: number; // 课时数
  instructorName: string;
  instructorTitle?: string;
  instructorAvatar?: string;
  instructorBio?: string;
  price: number;
  originalPrice?: number;
  isFree: boolean;
  status: CourseStatus;
  viewCount: number;
  enrollCount: number;
  rating?: number;
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface CourseEnrollment {
  id: string;
  courseId: string;
  userId: string;
  enrolledAt: Date;
  completedAt?: Date;
  progress: number; // 0-100
  lastAccessAt?: Date;
  isPaid: boolean;
  paidAmount?: number;
}

// 模拟课程分类数据
const mockCourseCategories: CourseCategory[] = [
  { id: '1', name: "基础理论", slug: "basic-theory", icon: "IconBook", order: 1, isActive: true, courseCount: 25 },
  { id: '2', name: "实战技能", slug: "practical-skills", icon: "IconSchool", order: 2, isActive: true, courseCount: 32 },
  { id: '3', name: "案例分析", slug: "case-analysis", icon: "IconChartBar", order: 3, isActive: true, courseCount: 18 },
  { id: '4', name: "政策解读", slug: "policy-interpretation", icon: "IconCalendar", order: 4, isActive: true, courseCount: 15 },
  { id: '5', name: "心理辅导", slug: "psychological-counseling", icon: "IconUsers", order: 5, isActive: true, courseCount: 20 },
  { id: '6', name: "认证考试", slug: "certification-exam", icon: "IconAward", order: 6, isActive: true, courseCount: 10 },
];

// 模拟课程数据
const mockCourses: Course[] = [
  {
    id: '1',
    title: '学业规划师认证基础课程',
    subtitle: '从零开始掌握学业规划核心理论',
    description: '深入学习学业规划理论基础，掌握专业咨询技能，为学生提供科学的学业指导。本课程涵盖教育心理学、职业发展理论、咨询技巧等核心内容。',
    cover: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=240&fit=crop',
    previewVideo: 'https://example.com/preview1.mp4',
    categoryId: '1',
    category: { name: '基础理论' },
    tags: ['基础', '认证', '理论'],
    level: 'BEGINNER' as CourseLevel,
    duration: 1440, // 24小时 = 1440分钟
    lessonsCount: 24,
    instructorName: '李明教授',
    instructorTitle: '教育学博士',
    instructorAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
    instructorBio: '北京师范大学教育学博士，从事学业规划研究15年',
    price: 0,
    originalPrice: 299,
    isFree: true,
    status: 'PUBLISHED' as CourseStatus,
    viewCount: 15420,
    enrollCount: 1200,
    rating: 4.8,
    publishedAt: new Date('2024-01-15'),
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-20'),
  },
  {
    id: '2',
    title: '高中学业规划实战指南',
    subtitle: '针对高中生的专业规划方法',
    description: '专门针对高中阶段学生的学业规划实战课程，包含选科指导、大学专业选择、升学路径规划等实用内容。',
    cover: 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=400&h=240&fit=crop',
    categoryId: '2',
    category: { name: '实战技能' },
    tags: ['高中', '实战', '升学'],
    level: 'INTERMEDIATE' as CourseLevel,
    duration: 1800, // 30小时
    lessonsCount: 30,
    instructorName: '张芳老师',
    instructorTitle: '高级学业规划师',
    instructorAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b412c1d0?w=100&h=100&fit=crop&crop=face',
    price: 199,
    originalPrice: 399,
    isFree: false,
    status: 'PUBLISHED' as CourseStatus,
    viewCount: 8750,
    enrollCount: 650,
    rating: 4.9,
    publishedAt: new Date('2024-02-01'),
    createdAt: new Date('2024-01-25'),
    updatedAt: new Date('2024-02-05'),
  },
  {
    id: '3',
    title: '大学生涯规划与职业发展',
    subtitle: '帮助大学生明确职业方向',
    description: '面向大学生的职业规划课程，涵盖专业选择、实习规划、求职准备、职业发展等全方位内容，助力大学生顺利进入职场。',
    cover: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=240&fit=crop',
    categoryId: '2',
    category: { name: '实战技能' },
    tags: ['大学生', '职业规划', '求职'],
    level: 'INTERMEDIATE' as CourseLevel,
    duration: 2160, // 36小时
    lessonsCount: 36,
    instructorName: '王强博士',
    instructorTitle: '职业发展专家',
    instructorAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
    instructorBio: '清华大学管理学博士，10年企业HR经验',
    price: 299,
    originalPrice: 499,
    isFree: false,
    status: 'PUBLISHED' as CourseStatus,
    viewCount: 12350,
    enrollCount: 890,
    rating: 4.7,
    publishedAt: new Date('2024-02-10'),
    createdAt: new Date('2024-02-05'),
    updatedAt: new Date('2024-02-15'),
  },
  {
    id: '4',
    title: '家庭教育与学业规划',
    subtitle: '家长必修的教育指导课',
    description: '专为家长设计的课程，教授如何在家庭教育中融入学业规划理念，帮助孩子建立正确的学习观和人生观。',
    cover: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=240&fit=crop',
    categoryId: '5',
    category: { name: '心理辅导' },
    tags: ['家庭教育', '亲子沟通', '心理健康'],
    level: 'BEGINNER' as CourseLevel,
    duration: 1200, // 20小时
    lessonsCount: 20,
    instructorName: '陈美丽老师',
    instructorTitle: '家庭教育专家',
    instructorAvatar: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=100&h=100&fit=crop&crop=face',
    instructorBio: '心理学硕士，20年家庭教育咨询经验',
    price: 0,
    originalPrice: 199,
    isFree: true,
    status: 'PUBLISHED' as CourseStatus,
    viewCount: 8920,
    enrollCount: 1450,
    rating: 4.6,
    publishedAt: new Date('2024-03-01'),
    createdAt: new Date('2024-02-25'),
    updatedAt: new Date('2024-03-05'),
  },
  {
    id: '5',
    title: '升学政策解读与申请策略',
    subtitle: '掌握最新升学政策动态',
    description: '详细解读各地升学政策，包括高考改革、强基计划、综合评价等招生方式，提供实用的申请策略和技巧。',
    cover: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=400&h=240&fit=crop',
    categoryId: '4',
    category: { name: '政策解读' },
    tags: ['升学政策', '高考改革', '强基计划'],
    level: 'ADVANCED' as CourseLevel,
    duration: 900, // 15小时
    lessonsCount: 15,
    instructorName: '刘教授',
    instructorTitle: '教育政策研究专家',
    instructorAvatar: 'https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=100&h=100&fit=crop&crop=face',
    price: 399,
    originalPrice: 599,
    isFree: false,
    status: 'PUBLISHED' as CourseStatus,
    viewCount: 6780,
    enrollCount: 420,
    rating: 4.9,
    publishedAt: new Date('2024-03-10'),
    createdAt: new Date('2024-03-05'),
    updatedAt: new Date('2024-03-15'),
  },
  {
    id: '6',
    title: '心理测评工具应用实战',
    subtitle: '科学测评助力精准规划',
    description: '学习各种心理测评工具的使用方法，包括职业兴趣测试、性格测试、能力测评等，提升规划的科学性和准确性。',
    cover: 'https://images.unsplash.com/photo-1488190211105-8b0e65b80b4e?w=400&h=240&fit=crop',
    categoryId: '2',
    category: { name: '实战技能' },
    tags: ['心理测评', '工具应用', '数据分析'],
    level: 'ADVANCED' as CourseLevel,
    duration: 1500, // 25小时
    lessonsCount: 25,
    instructorName: '赵心理师',
    instructorTitle: '心理测评专家',
    instructorAvatar: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=100&h=100&fit=crop&crop=face',
    price: 499,
    isFree: false,
    status: 'PUBLISHED' as CourseStatus,
    viewCount: 5640,
    enrollCount: 320,
    rating: 4.8,
    publishedAt: new Date('2024-03-20'),
    createdAt: new Date('2024-03-15'),
    updatedAt: new Date('2024-03-25'),
  },
];

// 模拟用户的课程注册数据
const mockEnrollments: CourseEnrollment[] = [
  {
    id: '1',
    courseId: '1',
    userId: 'user1',
    enrolledAt: new Date('2024-01-20'),
    progress: 65,
    lastAccessAt: new Date('2024-01-25'),
    isPaid: false,
  },
  {
    id: '2',
    courseId: '2',
    userId: 'user1',
    enrolledAt: new Date('2024-02-01'),
    progress: 30,
    lastAccessAt: new Date('2024-02-10'),
    isPaid: true,
    paidAmount: 199,
  },
];

export default function CoursesPage() {
  return (
    <div className="flex flex-1 flex-col">
      {/* Header Section */}
      <div className="px-4 py-6 lg:px-6">
        <div className="mb-6">
          <h1 className="text-2xl font-semibold">课程学习</h1>
          <p className="text-muted-foreground">
            系统化课程体系，助力学业规划师职业发展
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
        <Card className="@container/card">
          <CardHeader>
            <CardDescription>学习总时长</CardDescription>
            <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
              126.5 小时
            </CardTitle>
          </CardHeader>
          <CardFooter className="flex-col items-start gap-1.5 text-sm">
            <div className="line-clamp-1 flex gap-2 font-medium">
              本月新增 12.5 小时
            </div>
            <div className="text-muted-foreground">持续学习，保持进步</div>
          </CardFooter>
        </Card>
        <Card className="@container/card">
          <CardHeader>
            <CardDescription>已完成课程</CardDescription>
            <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
              24 门
            </CardTitle>
          </CardHeader>
          <CardFooter className="flex-col items-start gap-1.5 text-sm">
            <div className="line-clamp-1 flex gap-2 font-medium">
              本月完成 3 门
            </div>
            <div className="text-muted-foreground">系统化学习成果显著</div>
          </CardFooter>
        </Card>
        <Card className="@container/card">
          <CardHeader>
            <CardDescription>获得证书</CardDescription>
            <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
              8 个
            </CardTitle>
          </CardHeader>
          <CardFooter className="flex-col items-start gap-1.5 text-sm">
            <div className="line-clamp-1 flex gap-2 font-medium">
              专业认证持续积累
            </div>
            <div className="text-muted-foreground">提升职业竞争力</div>
          </CardFooter>
        </Card>
        <Card className="@container/card">
          <CardHeader>
            <CardDescription>学习排名</CardDescription>
            <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
              前 5%
            </CardTitle>
          </CardHeader>
          <CardFooter className="flex-col items-start gap-1.5 text-sm">
            <div className="line-clamp-1 flex gap-2 font-medium">
              超越 95% 的学员
            </div>
            <div className="text-muted-foreground">学习表现优异</div>
          </CardFooter>
        </Card>
      </div>

      {/* Course Categories */}
      <div className="px-4 py-8 lg:px-6">
        <h2 className="mb-6 text-xl font-semibold">课程分类</h2>
        <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-6">
          {mockCourseCategories.map((category) => (
            <Card
              key={category.id}
              className="cursor-pointer transition-all hover:shadow-sm"
              onClick={() => console.log(`Navigate to category: ${category.slug}`)}
            >
              <CardContent className="flex flex-col items-center p-4 text-center">
                <div className="mb-3 flex size-12 items-center justify-center rounded-full bg-primary/10">
                  {category.icon === 'IconBook' && <IconBook className="size-6 text-primary" />}
                  {category.icon === 'IconSchool' && <IconSchool className="size-6 text-primary" />}
                  {category.icon === 'IconChartBar' && <IconChartBar className="size-6 text-primary" />}
                  {category.icon === 'IconCalendar' && <IconCalendar className="size-6 text-primary" />}
                  {category.icon === 'IconUsers' && <IconUsers className="size-6 text-primary" />}
                  {category.icon === 'IconAward' && <IconAward className="size-6 text-primary" />}
                </div>
                <h3 className="font-medium">{category.name}</h3>
                <p className="text-sm text-muted-foreground">
                  {category.courseCount} 门课程
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Featured Courses */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-2xl font-bold">精品推荐</h2>
            <Button variant="ghost" className="gap-2">
              查看全部 <ArrowRight className="h-4 w-4" />
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockCourses.map((course) => {
              // 获取该课程的报名信息（如果存在）
              const enrollment = mockEnrollments.find(e => e.courseId === course.id);
              
              // 课程级别标签映射
              const levelBadge = {
                'BEGINNER': '入门级',
                'INTERMEDIATE': '进阶级', 
                'ADVANCED': '高级',
                'ALL': '全级别'
              }[course.level];

              return (
                <Link key={course.id} href={`/community/courses/${course.id}`} className="block">
                  <Card className="hover:shadow-lg transition-all group cursor-pointer course-card">
                    <CardHeader className="p-0">
                      <ViewTransition name={`course-image-${course.id}`}>
                        <div className="relative overflow-hidden">
                          <img
                            src={course.cover}
                            alt={course.title}
                            className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                            <div className="absolute bottom-4 left-4 right-4">
                              <Button
                                size="sm"
                                className="w-full gap-2 cursor-pointer"
                              >
                                <Play className="h-4 w-4" />
                                {enrollment ? '继续学习' : '立即学习'}
                              </Button>
                            </div>
                          </div>
                          {course.isFree && (
                            <Badge className="absolute top-4 right-4 bg-green-500">
                              免费
                            </Badge>
                          )}
                          {course.viewCount > 10000 && (
                            <Badge className="absolute top-4 left-4 bg-primary">
                              热门
                            </Badge>
                          )}
                        </div>
                      </ViewTransition>
                    </CardHeader>
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="secondary">{levelBadge}</Badge>
                        <Badge variant="outline">{course.category.name}</Badge>
                        {course.rating && (
                          <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 fill-warning text-warning" />
                            <span className="text-sm font-medium">{course.rating}</span>
                          </div>
                        )}
                      </div>
                      
                      <CardTitle className="mb-2 group-hover:text-primary transition-colors line-clamp-2">
                        {course.title}
                      </CardTitle>
                      
                      {course.subtitle && (
                        <p className="text-sm text-muted-foreground mb-2 line-clamp-1">
                          {course.subtitle}
                        </p>
                      )}
                      
                      <CardDescription className="mb-4 line-clamp-2">
                        {course.description}
                      </CardDescription>
                      
                      {/* 讲师信息 */}
                      <div className="flex items-center gap-2 mb-3">
                        {course.instructorAvatar && (
                          <img 
                            src={course.instructorAvatar} 
                            alt={course.instructorName}
                            className="w-6 h-6 rounded-full"
                          />
                        )}
                        <div className="text-sm">
                          <span className="font-medium">{course.instructorName}</span>
                          {course.instructorTitle && (
                            <span className="text-muted-foreground ml-1">• {course.instructorTitle}</span>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
                        <span className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {course.lessonsCount}课时
                        </span>
                        <span className="flex items-center gap-1">
                          <Users className="h-4 w-4" />
                          {course.enrollCount}人学习
                        </span>
                        <span className="flex items-center gap-1">
                          <Eye className="h-4 w-4" />
                          {course.viewCount}
                        </span>
                      </div>

                      {/* 学习进度条（仅已报名课程显示） */}
                      {enrollment && (
                        <div className="mt-4">
                          <div className="flex justify-between items-center mb-1">
                            <span className="text-xs text-muted-foreground">
                              学习进度
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {enrollment.progress}%
                            </span>
                          </div>
                          <div className="w-full bg-muted rounded-full h-2">
                            <div
                              className="bg-primary h-2 rounded-full"
                              style={{ width: `${enrollment.progress}%` }}
                            ></div>
                          </div>
                        </div>
                      )}
                      
                      {/* 标签 */}
                      {course.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-3">
                          {course.tags.slice(0, 3).map((tag, tagIndex) => (
                            <Badge key={tagIndex} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </CardContent>
                    <CardFooter className="pt-0">
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center gap-2">
                          {course.isFree ? (
                            <div className="text-lg font-bold text-green-600">免费</div>
                          ) : (
                            <div className="flex items-center gap-2">
                              <div className="text-lg font-bold text-primary">¥{course.price}</div>
                              {course.originalPrice && course.originalPrice > course.price && (
                                <div className="text-sm text-muted-foreground line-through">
                                  ¥{course.originalPrice}
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                        <Button variant="outline" size="sm">
                          {enrollment ? '继续学习' : '立即学习'}
                        </Button>
                      </div>
                    </CardFooter>
                  </Card>
                </Link>
              );
            })}
          </div>
        </div>
      </section>

      {/* Learning Path Section */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">学习路径</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              系统化的学习路径设计，从入门到精通，循序渐进提升专业能力
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="relative">
              {/* Timeline Line */}
              <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary via-accent to-success"></div>

              {[
                {
                  level: "入门级",
                  title: "理论基础",
                  description: "学业规划基础理论、教育心理学、职业发展理论",
                  courses: 8,
                  duration: "40课时",
                  badgeClass: "bg-primary/10 text-primary",
                  circleClass: "bg-primary",
                },
                {
                  level: "进阶级",
                  title: "实战技能",
                  description: "咨询技能、案例分析、沟通技巧、测评工具使用",
                  courses: 12,
                  duration: "60课时",
                  badgeClass: "bg-primary/20 text-primary",
                  circleClass: "bg-primary/65",
                },
                {
                  level: "专家级",
                  title: "高级应用",
                  description: "复杂案例处理、团体咨询、师资培训、督导技能",
                  courses: 6,
                  duration: "30课时",
                  badgeClass: "bg-primary/30 text-primary",
                  circleClass: "bg-primary/25",
                },
              ].map((path, index) => (
                <div
                  key={index}
                  className="relative flex items-center mb-12 last:mb-0"
                >
                  <div
                    className={`w-16 h-16 ${path.circleClass} rounded-full flex items-center justify-center text-white font-bold text-lg z-10`}
                  >
                    {index + 1}
                  </div>
                  <Card className="ml-8 flex-1 hover:shadow-lg transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <Badge className={`${path.badgeClass} mb-2`}>
                            {path.level}
                          </Badge>
                          <h3 className="text-xl font-bold mb-2">
                            {path.title}
                          </h3>
                          <p className="text-muted-foreground mb-4">
                            {path.description}
                          </p>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <BookOpen className="h-4 w-4" />
                              {path.courses}门课程
                            </span>
                            <span className="flex items-center gap-1">
                              <Clock className="h-4 w-4" />
                              {path.duration}
                            </span>
                          </div>
                        </div>
                        <Button className="gap-2">
                          开始学习
                          <ArrowRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
