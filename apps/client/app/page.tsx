import Link from "next/link";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
} from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import {
  GraduationCap,
  Users,
  BookOpen,
  Target,
  Star,
  ArrowRight,
  CheckCircle,
  Globe,
  TrendingUp,
  Shield,
  Heart,
  Award,
} from "lucide-react";

// 平台数据统计
const platformStats = {
  totalPlanners: 2680,
  totalStudents: 15420,
  successfulMatches: 8750,
  satisfactionRate: 98.5,
};

// 特色功能
const features = [
  {
    icon: Users,
    title: "智能匹配系统",
    description: "基于学生需求和规划师专长的精准匹配算法",
    color: "text-blue-600",
    bgColor: "bg-blue-100",
  },
  {
    icon: Target,
    title: "个性化规划",
    description: "针对每个学生的具体情况制定专属升学方案",
    color: "text-green-600",
    bgColor: "bg-green-100",
  },
  {
    icon: BookOpen,
    title: "专业课程体系",
    description: "涵盖升学规划、职业发展等全方位教育内容",
    color: "text-purple-600",
    bgColor: "bg-purple-100",
  },
  {
    icon: Globe,
    title: "国际化视野",
    description: "整合海内外优质教育资源，拓宽学生发展路径",
    color: "text-orange-600",
    bgColor: "bg-orange-100",
  },
];

// 服务对象
const serviceTargets = [
  {
    title: "专业规划师",
    description: "为资深教育规划师提供事业发展平台",
    benefits: [
      "丰富客源渠道",
      "专业能力认证",
      "收入显著提升",
      "个人品牌建设",
    ],
    icon: GraduationCap,
    color: "border-blue-200 bg-blue-50",
  },
  {
    title: "教育机构",
    description: "为学校、培训机构提供专业规划师资源",
    benefits: [
      "优质师资输送",
      "课程体系设计",
      "教学质量提升",
      "品牌影响力扩大",
    ],
    icon: Shield,
    color: "border-green-200 bg-green-50",
  },
];

// 成功案例数据
const successStories = [
  {
    name: "李老师",
    background: "资深升学规划师",
    result: "月收入从8k提升至25k，成功服务180+学生",
    achievement: "平台金牌规划师",
    rating: 5,
  },
  {
    name: "北京新东方",
    background: "知名教育培训机构",
    result: "通过平台获得30位优质规划师，业务增长200%",
    achievement: "年度最佳合作机构",
    rating: 5,
  },
  {
    name: "王规划师",
    background: "专业艺术升学指导",
    result: "入驻平台6个月，客户满意度99%，预约爆满",
    achievement: "艺术类专家认证",
    rating: 5,
  },
];

export default function HomePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center space-y-8">
            {/* 主标题 */}
            <div className="space-y-4">
              <Badge variant="secondary" className="mb-4">
                <Star className="w-4 h-4 mr-1" />
                专业教育规划平台
              </Badge>
              <h1 className="text-4xl md:text-6xl font-bold tracking-tight">
                专业规划师平台
                <br />
                <span className="text-blue-600">开启教育事业新篇章</span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                为优秀规划师提供展示平台和客源渠道，为教育机构输送专业师资，
                助力规划师事业发展，共建教育行业生态
              </p>
            </div>

            {/* CTA 按钮 */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/community">
                <Button size="lg" className="text-lg px-8 py-3">
                  成为规划师
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/workspace">
                <Button
                  variant="outline"
                  size="lg"
                  className="text-lg px-8 py-3"
                >
                  机构合作
                </Button>
              </Link>
            </div>

            {/* 平台数据 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">
                  {platformStats.totalPlanners.toLocaleString()}+
                </div>
                <div className="text-sm text-muted-foreground">专业规划师</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">
                  {platformStats.totalStudents.toLocaleString()}+
                </div>
                <div className="text-sm text-muted-foreground">服务学生</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">
                  {platformStats.successfulMatches.toLocaleString()}+
                </div>
                <div className="text-sm text-muted-foreground">成功匹配</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-600">
                  {platformStats.satisfactionRate}%
                </div>
                <div className="text-sm text-muted-foreground">满意度</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-6 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold">平台特色</h2>
            <p className="text-xl text-muted-foreground">
              先进技术与专业服务相结合，为教育规划提供全方位支持
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card
                key={index}
                className="border-0 shadow-lg hover:shadow-xl transition-shadow"
              >
                <CardContent className="p-6 text-center space-y-4">
                  <div
                    className={`w-16 h-16 mx-auto rounded-full ${feature.bgColor} flex items-center justify-center`}
                  >
                    <feature.icon className={`w-8 h-8 ${feature.color}`} />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-lg font-semibold">{feature.title}</h3>
                    <p className="text-sm text-muted-foreground">
                      {feature.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Service Targets Section */}
      <section className="py-20 px-6 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold">服务对象</h2>
            <p className="text-xl text-muted-foreground">
              面向规划师和教育机构，提供差异化专业服务
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {serviceTargets.map((target, index) => (
              <Card
                key={index}
                className={`border-2 ${target.color} hover:shadow-lg transition-shadow`}
              >
                <CardHeader className="text-center pb-4">
                  <div className="w-20 h-20 mx-auto rounded-full bg-white shadow-md flex items-center justify-center mb-4">
                    <target.icon className="w-10 h-10 text-blue-600" />
                  </div>
                  <CardTitle className="text-2xl">{target.title}</CardTitle>
                  <CardDescription className="text-base">
                    {target.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  {target.benefits.map((benefit, benefitIndex) => (
                    <div key={benefitIndex} className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                      <span className="text-sm">{benefit}</span>
                    </div>
                  ))}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Success Stories Section */}
      <section className="py-20 px-6 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold">成功案例</h2>
            <p className="text-xl text-muted-foreground">
              真实的成功故事，见证专业规划的力量
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {successStories.map((story, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6 space-y-4">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="font-medium">{story.name}</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {story.background}
                  </p>
                  <div className="space-y-2">
                    <p className="font-medium text-green-600">{story.result}</p>
                    <p className="text-xs text-muted-foreground">
                      {story.achievement}
                    </p>
                  </div>
                  <div className="flex items-center gap-1">
                    {[...Array(story.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="w-4 h-4 fill-yellow-400 text-yellow-400"
                      />
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-4xl mx-auto text-center space-y-8">
          <div className="space-y-4">
            <h2 className="text-3xl md:text-4xl font-bold">
              开启您的教育事业新篇章
            </h2>
            <p className="text-xl opacity-90">
              无论您是寻求平台展示的专业规划师，还是希望获得优质师资的教育机构，
              我们都为您准备了最适合的解决方案
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/community">
              <Button
                size="lg"
                variant="secondary"
                className="text-lg px-8 py-3"
              >
                <Heart className="mr-2 h-5 w-5" />
                成为认证规划师
              </Button>
            </Link>
            <Link href="/workspace">
              <Button
                size="lg"
                variant="outline"
                className="text-lg px-8 py-3 border-white text-white hover:bg-white hover:text-blue-600"
              >
                <Award className="mr-2 h-5 w-5" />
                申请机构合作
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-6 bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">青云清梦</h3>
              <p className="text-sm text-gray-400">
                专业的教育规划平台，为规划师提供展示舞台，为机构输送优质师资，共建教育生态。
              </p>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium">服务</h4>
              <div className="space-y-2 text-sm text-gray-400">
                <Link
                  href="/community/match"
                  className="block hover:text-white transition-colors"
                >
                  规划师匹配
                </Link>
                <Link
                  href="/community/courses"
                  className="block hover:text-white transition-colors"
                >
                  专业课程
                </Link>
                <Link
                  href="/community/products"
                  className="block hover:text-white transition-colors"
                >
                  特色研学营
                </Link>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium">社区</h4>
              <div className="space-y-2 text-sm text-gray-400">
                <Link
                  href="/community"
                  className="block hover:text-white transition-colors"
                >
                  规划师社区
                </Link>
                <Link
                  href="/workspace"
                  className="block hover:text-white transition-colors"
                >
                  工作台
                </Link>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium">关于我们</h4>
              <div className="space-y-2 text-sm text-gray-400">
                <div className="block">联系方式</div>
                <div className="block">隐私政策</div>
                <div className="block">服务条款</div>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400">
            <p>&copy; 2024 青云清梦教育规划平台. 保留所有权利.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
