"use client"; // Error boundaries must be Client Components

import { useEffect } from "react";
import { motion } from "framer-motion";

// Custom SVG Components
const FloatingIcon = ({
  children,
  delay = 0,
}: {
  children: React.ReactNode;
  delay?: number;
}) => (
  <motion.div
    className="absolute"
    animate={{
      y: [0, -15, 0],
      rotate: [0, 5, -5, 0],
      scale: [1, 1.05, 1],
    }}
    transition={{
      duration: 3,
      repeat: Infinity,
      ease: "easeInOut",
      delay,
    }}
  >
    {children}
  </motion.div>
);

const ErrorIllustration = () => (
  <motion.svg
    width="350"
    height="280"
    viewBox="0 0 350 280"
    className="w-full max-w-sm mx-auto"
    initial={{ opacity: 0, scale: 0.9 }}
    animate={{ opacity: 1, scale: 1 }}
    transition={{ duration: 0.8, ease: "easeOut" }}
  >
    {/* Background elements */}
    <motion.circle
      cx="80"
      cy="90"
      r="50"
      fill="#fef2f2"
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ delay: 0.2, duration: 0.6 }}
    />
    <motion.circle
      cx="270"
      cy="160"
      r="35"
      fill="#fef7ed"
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ delay: 0.3, duration: 0.6 }}
    />

    {/* Warning triangle */}
    <motion.g
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.4, duration: 0.6 }}
    >
      <path
        d="M 175 80 L 220 160 L 130 160 Z"
        fill="#fee2e2"
        stroke="#ef4444"
        strokeWidth="2"
      />
      <motion.path
        d="M 175 100 L 175 130"
        stroke="#ef4444"
        strokeWidth="3"
        strokeLinecap="round"
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{ delay: 0.8, duration: 0.5 }}
      />
      <motion.circle
        cx="175"
        cy="145"
        r="3"
        fill="#ef4444"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 1.2, duration: 0.3 }}
      />
    </motion.g>

    {/* Broken gear icon */}
    <motion.g
      initial={{ opacity: 0, rotate: -180 }}
      animate={{ opacity: 0.6, rotate: 0 }}
      transition={{ delay: 0.6, duration: 0.8 }}
    >
      <path
        d="M 100 200 L 110 195 L 115 205 L 125 200 L 130 210 L 140 205 L 145 215 L 155 210 L 160 220 L 150 225 L 155 235 L 145 240 L 140 230 L 130 235 L 125 225 L 115 230 L 110 220 L 100 225 L 95 215 L 85 220 L 80 210 L 90 205 L 85 195 L 95 190 Z"
        fill="none"
        stroke="#9ca3af"
        strokeWidth="2"
        strokeDasharray="5,5"
      />
    </motion.g>

    {/* Floating error particles */}
    <motion.g
      animate={{ rotate: 360 }}
      transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
    >
      <circle cx="60" cy="60" r="3" fill="#ef4444" opacity="0.4" />
      <circle cx="290" cy="80" r="2" fill="#f59e0b" opacity="0.5" />
      <circle cx="320" cy="200" r="4" fill="#ef4444" opacity="0.3" />
      <circle cx="40" cy="180" r="2" fill="#f59e0b" opacity="0.4" />
      <circle cx="300" cy="40" r="3" fill="#ef4444" opacity="0.5" />
    </motion.g>
  </motion.svg>
);

const ShakeText = ({ children }: { children: React.ReactNode }) => (
  <motion.div
    animate={{
      x: [0, -1, 1, 0],
    }}
    transition={{
      duration: 0.15,
      repeat: Infinity,
      repeatDelay: 2,
    }}
  >
    {children}
  </motion.div>
);

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error);
  }, [error]);

  const getErrorMessage = () => {
    if (error.message.includes("fetch")) return "网络连接出现问题";
    if (error.message.includes("timeout")) return "请求超时";
    if (error.message.includes("permission")) return "权限不足";
    return "系统遇到了意外错误";
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4">
      <div className="max-w-2xl mx-auto text-center">
        {/* Floating background icons */}
        <div className="relative">
          <FloatingIcon delay={0}>
            <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center text-red-600 top-8 left-12">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          </FloatingIcon>

          <FloatingIcon delay={1}>
            <div className="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center text-orange-600 top-24 right-16">
              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          </FloatingIcon>

          <FloatingIcon delay={2}>
            <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center text-yellow-600 bottom-16 left-20">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          </FloatingIcon>
        </div>

        {/* Main illustration */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          className="mb-8"
        >
          <ErrorIllustration />
        </motion.div>

        {/* Error message */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.7 }}
          className="mb-8"
        >
          <ShakeText>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
              哎呀，出错了！
            </h1>
          </ShakeText>
          <motion.p
            className="text-lg text-gray-600 mb-2 max-w-md mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.7 }}
          >
            {getErrorMessage()}
          </motion.p>
          <motion.p
            className="text-sm text-gray-500 mb-8 max-w-lg mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6, duration: 0.7 }}
          >
            不用担心，这种情况偶尔会发生。请尝试刷新页面，或者稍后再试。
          </motion.p>
        </motion.div>

        {/* Action buttons */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.7 }}
          className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8"
        >
          <motion.button
            onClick={reset}
            className="inline-flex items-center px-6 py-3 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 transition-colors duration-200 shadow-lg"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <svg
              className="w-5 h-5 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            重新尝试
          </motion.button>

          <motion.button
            onClick={() => window.location.reload()}
            className="inline-flex items-center px-6 py-3 bg-orange-600 text-white font-medium rounded-lg hover:bg-orange-700 transition-colors duration-200 shadow-lg"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <svg
              className="w-5 h-5 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            刷新页面
          </motion.button>

          <motion.button
            onClick={() => window.history.back()}
            className="inline-flex items-center px-6 py-3 bg-gray-600 text-white font-medium rounded-lg hover:bg-gray-700 transition-colors duration-200 shadow-lg"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <svg
              className="w-5 h-5 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
            返回上一页
          </motion.button>
        </motion.div>

        {/* Error details (development mode) */}
        {process.env.NODE_ENV === "development" && (
          <motion.details
            className="mt-8 text-left"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 0.7 }}
          >
            <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700 mb-2">
              查看错误详情 (开发模式)
            </summary>
            <div className="bg-gray-100 rounded-lg p-4 text-xs text-gray-700 font-mono overflow-auto max-h-40">
              <div className="mb-2">
                <strong>错误信息:</strong> {error.message}
              </div>
              {error.digest && (
                <div className="mb-2">
                  <strong>错误ID:</strong> {error.digest}
                </div>
              )}
              {error.stack && (
                <div>
                  <strong>堆栈信息:</strong>
                  <pre className="mt-1 whitespace-pre-wrap">{error.stack}</pre>
                </div>
              )}
            </div>
          </motion.details>
        )}

        {/* Additional help text */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.2, duration: 0.7 }}
          className="mt-8 text-sm text-gray-500"
        >
          <p>如果问题持续存在，请联系我们的技术支持团队获取帮助</p>
        </motion.div>
      </div>
    </div>
  );
}
