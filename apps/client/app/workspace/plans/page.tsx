"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import { Input } from "@workspace/ui/components/input";
import { 
  Plus, 
  Search, 
  Filter, 
  Calendar,
  User,
  Target,
  Clock,
  CheckCircle,
  AlertCircle,
  FileText,
  Brain
} from "lucide-react";

// 模拟数据 - 实际项目中会从 API 获取
const mockPlans = [
  {
    id: '1',
    title: '张小明 - 理科升学规划',
    description: '针对高二理科生的大学升学规划方案',
    type: '升学规划',
    studentName: '张小明',
    studentId: '1',
    status: 'ACTIVE',
    startDate: '2024-01-15',
    endDate: '2025-06-30',
    progress: 65,
    milestonesTotal: 8,
    milestonesCompleted: 5,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-12-01T10:00:00Z'
  },
  {
    id: '2',
    title: '李晓红 - 语言类专业规划',
    description: '文科生语言类专业选择和能力提升规划',
    type: '专业规划',
    studentName: '李晓红',
    studentId: '2',
    status: 'DRAFT',
    startDate: '2024-02-01',
    endDate: '2025-07-31',
    progress: 30,
    milestonesTotal: 6,
    milestonesCompleted: 2,
    createdAt: '2024-02-01T10:00:00Z',
    updatedAt: '2024-12-01T10:00:00Z'
  },
  {
    id: '3',
    title: '陈子豪 - 计算机专业规划',
    description: '计算机科学专业学习路径和技能发展规划',
    type: '职业规划',
    studentName: '陈子豪',
    studentId: '3',
    status: 'COMPLETED',
    startDate: '2024-01-20',
    endDate: '2024-11-30',
    progress: 100,
    milestonesTotal: 10,
    milestonesCompleted: 10,
    createdAt: '2024-01-20T10:00:00Z',
    updatedAt: '2024-11-30T10:00:00Z'
  }
];

const getStatusConfig = (status: string) => {
  switch (status) {
    case 'ACTIVE':
      return {
        label: '进行中',
        color: 'bg-green-100 text-green-800 border-green-200',
        icon: Clock
      };
    case 'COMPLETED':
      return {
        label: '已完成',
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: CheckCircle
      };
    case 'DRAFT':
      return {
        label: '草稿',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        icon: FileText
      };
    default:
      return {
        label: '未知',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        icon: AlertCircle
      };
  }
};

export default function PlansPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  const filteredPlans = mockPlans.filter(plan => {
    const matchesSearch = plan.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         plan.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         plan.type.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || plan.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const stats = {
    total: mockPlans.length,
    active: mockPlans.filter(p => p.status === 'ACTIVE').length,
    completed: mockPlans.filter(p => p.status === 'COMPLETED').length,
    draft: mockPlans.filter(p => p.status === 'DRAFT').length
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">规划方案</h1>
          <p className="text-muted-foreground">管理学生的学业和职业规划方案</p>
        </div>
        <Button onClick={() => router.push('/workspace/plans/new')}>
          <Plus className="w-4 h-4 mr-2" />
          创建规划
        </Button>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Target className="w-5 h-5 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">总规划数</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Clock className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">进行中</p>
                <p className="text-2xl font-bold text-green-600">{stats.active}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <CheckCircle className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">已完成</p>
                <p className="text-2xl font-bold text-blue-600">{stats.completed}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gray-100 rounded-lg">
                <FileText className="w-5 h-5 text-gray-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">草稿</p>
                <p className="text-2xl font-bold text-gray-600">{stats.draft}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 工具栏 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-1 gap-4 items-center">
              {/* 搜索框 */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="搜索规划方案、学生姓名..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* 状态筛选 */}
              <div className="flex gap-2">
                {[
                  { key: 'all', label: '全部', count: stats.total },
                  { key: 'ACTIVE', label: '进行中', count: stats.active },
                  { key: 'COMPLETED', label: '已完成', count: stats.completed },
                  { key: 'DRAFT', label: '草稿', count: stats.draft }
                ].map(filter => (
                  <Button
                    key={filter.key}
                    variant={filterStatus === filter.key ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setFilterStatus(filter.key)}
                    className="gap-2"
                  >
                    {filter.label}
                    <Badge variant="secondary" className="text-xs">
                      {filter.count}
                    </Badge>
                  </Button>
                ))}
              </div>
            </div>

            <div className="flex gap-2">
              <Button size="sm" variant="outline">
                <Filter className="w-4 h-4 mr-2" />
                筛选
              </Button>
              <Button size="sm" variant="outline">
                <Brain className="w-4 h-4 mr-2" />
                AI 生成
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 规划列表 */}
      {filteredPlans.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <Target className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">暂无规划方案</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm ? '没有找到匹配的规划方案' : '开始为学生创建第一个规划方案吧'}
            </p>
            <Button onClick={() => router.push('/workspace/plans/new')}>
              <Plus className="w-4 h-4 mr-2" />
              创建规划
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredPlans.map(plan => {
            const statusConfig = getStatusConfig(plan.status);
            const StatusIcon = statusConfig.icon;
            
            return (
              <Card key={plan.id} className="hover:shadow-lg transition-shadow cursor-pointer"
                    onClick={() => router.push(`/workspace/plans/${plan.id}`)}>
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-lg truncate">{plan.title}</CardTitle>
                      <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                        {plan.description}
                      </p>
                    </div>
                    <Badge variant="outline" className={statusConfig.color}>
                      <StatusIcon className="w-3 h-3 mr-1" />
                      {statusConfig.label}
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  {/* 学生信息 */}
                  <div className="flex items-center gap-2 text-sm">
                    <User className="w-4 h-4 text-muted-foreground" />
                    <span className="font-medium">{plan.studentName}</span>
                    <span className="text-muted-foreground">·</span>
                    <span className="text-muted-foreground">{plan.type}</span>
                  </div>

                  {/* 进度 */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">完成进度</span>
                      <span className="font-medium">{plan.progress}%</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full transition-all duration-300"
                        style={{ width: `${plan.progress}%` }}
                      />
                    </div>
                  </div>

                  {/* 里程碑 */}
                  <div className="flex items-center gap-4 text-sm">
                    <div className="flex items-center gap-1">
                      <Target className="w-4 h-4 text-muted-foreground" />
                      <span className="text-muted-foreground">里程碑:</span>
                      <span className="font-medium">
                        {plan.milestonesCompleted}/{plan.milestonesTotal}
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4 text-muted-foreground" />
                      <span className="text-muted-foreground">
                        {new Date(plan.endDate).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
}
