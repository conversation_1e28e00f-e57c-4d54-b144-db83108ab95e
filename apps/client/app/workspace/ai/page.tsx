"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import { Input } from "@workspace/ui/components/input";
import { Textarea } from "@workspace/ui/components/textarea";
import { 
  Brain, 
  Sparkles, 
  Target, 
  FileText, 
  Users, 
  Calendar,
  TrendingUp,
  BookOpen,
  Lightbulb,
  MessageSquare,
  Zap,
  ArrowRight,
  Clock,
  Star
} from "lucide-react";

// AI 工具配置
const aiTools = [
  {
    id: 'plan-generator',
    title: '智能规划生成器',
    description: '基于学生信息自动生成个性化学业规划方案',
    icon: Target,
    color: 'bg-blue-100 text-blue-600',
    category: '规划制定',
    features: ['个性化分析', '目标设定', '路径规划', '时间安排'],
    usage: 156
  },
  {
    id: 'milestone-creator',
    title: '里程碑智能设置',
    description: '根据规划目标自动分解并设置关键里程碑',
    icon: TrendingUp,
    color: 'bg-green-100 text-green-600',
    category: '目标管理',
    features: ['目标分解', '时间节点', '优先级排序', '进度跟踪'],
    usage: 89
  },
  {
    id: 'assessment-analyzer',
    title: '评估结果分析',
    description: '深度分析学生测评结果，提供专业解读和建议',
    icon: BookOpen,
    color: 'bg-purple-100 text-purple-600',
    category: '能力评估',
    features: ['结果解读', '能力画像', '发展建议', '对比分析'],
    usage: 234
  },
  {
    id: 'conversation-assistant',
    title: '咨询对话助手',
    description: '实时提供咨询建议和话术，提升沟通效果',
    icon: MessageSquare,
    color: 'bg-orange-100 text-orange-600',
    category: '沟通辅助',
    features: ['话术建议', '问题引导', '情绪识别', '记录整理'],
    usage: 67
  },
  {
    id: 'report-generator',
    title: '智能报告生成',
    description: '自动生成专业的学生评估和规划报告',
    icon: FileText,
    color: 'bg-indigo-100 text-indigo-600',
    category: '报告生成',
    features: ['模板选择', '数据整合', '图表生成', '专业排版'],
    usage: 123
  },
  {
    id: 'insight-analyzer',
    title: '学情洞察分析',
    description: '分析学生数据，发现潜在问题和机会',
    icon: Lightbulb,
    color: 'bg-yellow-100 text-yellow-600',
    category: '数据洞察',
    features: ['趋势分析', '风险预警', '机会识别', '建议推荐'],
    usage: 45
  }
];

// 最近使用的工具
const recentTools = [
  {
    id: 'plan-generator',
    studentName: '张小明',
    usedAt: '2024-12-19T14:30:00Z',
    result: '已生成理科升学规划方案'
  },
  {
    id: 'assessment-analyzer',
    studentName: '李晓红',
    usedAt: '2024-12-19T10:15:00Z',
    result: '完成兴趣测评分析报告'
  },
  {
    id: 'milestone-creator',
    studentName: '陈子豪',
    usedAt: '2024-12-18T16:45:00Z',
    result: '设置了8个关键里程碑'
  }
];

export default function AIPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = ['all', '规划制定', '目标管理', '能力评估', '沟通辅助', '报告生成', '数据洞察'];

  const filteredTools = aiTools.filter(tool => {
    const matchesSearch = tool.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tool.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || tool.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const totalUsage = aiTools.reduce((sum, tool) => sum + tool.usage, 0);

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <Brain className="w-8 h-8 text-primary" />
            AI 智能助手
          </h1>
          <p className="text-muted-foreground">利用人工智能提升规划师工作效率</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Zap className="w-4 h-4 mr-2" />
            使用记录
          </Button>
          <Button>
            <Sparkles className="w-4 h-4 mr-2" />
            自定义工具
          </Button>
        </div>
      </div>

      {/* 统计概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Brain className="w-5 h-5 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">可用工具</p>
                <p className="text-2xl font-bold">{aiTools.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Zap className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">本月使用</p>
                <p className="text-2xl font-bold">{totalUsage}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">服务学生</p>
                <p className="text-2xl font-bold">23</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Star className="w-5 h-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">效率提升</p>
                <p className="text-2xl font-bold">85%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 主要内容区域 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 搜索和筛选 */}
          <Card>
            <CardContent className="p-4">
              <div className="space-y-4">
                <div className="relative">
                  <Brain className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="搜索 AI 工具..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <div className="flex flex-wrap gap-2">
                  {categories.map(category => (
                    <Button
                      key={category}
                      variant={selectedCategory === category ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedCategory(category)}
                    >
                      {category === 'all' ? '全部' : category}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* AI 工具列表 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {filteredTools.map(tool => {
              const IconComponent = tool.icon;
              
              return (
                <Card key={tool.id} className="hover:shadow-lg transition-all cursor-pointer group"
                      onClick={() => router.push(`/workspace/ai/${tool.id}`)}>
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-lg ${tool.color}`}>
                          <IconComponent className="w-5 h-5" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{tool.title}</CardTitle>
                          <Badge variant="secondary" className="text-xs mt-1">
                            {tool.category}
                          </Badge>
                        </div>
                      </div>
                      <ArrowRight className="w-4 h-4 text-muted-foreground group-hover:text-primary transition-colors" />
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-3">
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {tool.description}
                    </p>
                    
                    <div className="flex flex-wrap gap-1">
                      {tool.features.slice(0, 3).map((feature, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                      {tool.features.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{tool.features.length - 3}
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <span>本月使用 {tool.usage} 次</span>
                      <div className="flex items-center gap-1">
                        <Zap className="w-3 h-3" />
                        <span>快速启动</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* 侧边栏 */}
        <div className="space-y-6">
          {/* 快速操作 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="w-5 h-5" />
                快速操作
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                <Target className="w-4 h-4 mr-2" />
                为新学生生成规划
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <FileText className="w-4 h-4 mr-2" />
                批量生成报告
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <TrendingUp className="w-4 h-4 mr-2" />
                分析学生进展
              </Button>
            </CardContent>
          </Card>

          {/* 最近使用 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                最近使用
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {recentTools.map((item, index) => {
                const tool = aiTools.find(t => t.id === item.id);
                if (!tool) return null;
                
                const IconComponent = tool.icon;
                
                return (
                  <div key={index} className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50 cursor-pointer">
                    <div className={`p-1.5 rounded ${tool.color}`}>
                      <IconComponent className="w-3 h-3" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{item.studentName}</p>
                      <p className="text-xs text-muted-foreground truncate">{item.result}</p>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {new Date(item.usedAt).toLocaleDateString()}
                    </span>
                  </div>
                );
              })}
            </CardContent>
          </Card>

          {/* AI 使用提示 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="w-5 h-5" />
                使用提示
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm text-muted-foreground">
              <p>• 使用详细的学生信息可以获得更准确的 AI 建议</p>
              <p>• 定期更新学生数据以保持 AI 分析的准确性</p>
              <p>• 结合专业经验验证 AI 生成的内容</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
