"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import { Input } from "@workspace/ui/components/input";
import { Textarea } from "@workspace/ui/components/textarea";
import { Label } from "@workspace/ui/components/label";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { 
  ArrowLeft, 
  Target, 
  Brain, 
  Sparkles, 
  User, 
  BookOpen,
  Calendar,
  TrendingUp,
  FileText,
  Save,
  Wand2,
  CheckCircle,
  Clock,
  AlertTriangle
} from "lucide-react";

// 模拟学生数据
const mockStudents = [
  { id: '1', name: '张小明', grade: '高二', school: '北京四中' },
  { id: '2', name: '李晓红', grade: '初三', school: '清华附中' },
  { id: '3', name: '陈子豪', grade: '高二', school: '人大附中' }
];

export default function PlanGeneratorPage() {
  const router = useRouter();
  const [selectedStudent, setSelectedStudent] = useState<string>('');
  const [planType, setPlanType] = useState<string>('');
  const [timeframe, setTimeframe] = useState<string>('');
  const [goals, setGoals] = useState<string>('');
  const [currentSituation, setCurrentSituation] = useState<string>('');
  const [preferences, setPreferences] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedPlan, setGeneratedPlan] = useState<any>(null);

  const planTypes = [
    { value: 'academic', label: '学业规划' },
    { value: 'career', label: '职业规划' },
    { value: 'college', label: '升学规划' },
    { value: 'skill', label: '技能发展规划' },
    { value: 'comprehensive', label: '综合规划' }
  ];

  const timeframes = [
    { value: 'short', label: '短期 (3-6个月)' },
    { value: 'medium', label: '中期 (6个月-1年)' },
    { value: 'long', label: '长期 (1-3年)' },
    { value: 'extended', label: '超长期 (3年以上)' }
  ];

  const handleGenerate = async () => {
    if (!selectedStudent || !planType || !timeframe) {
      alert('请填写必要信息');
      return;
    }

    setIsGenerating(true);
    
    // 模拟 AI 生成过程
    setTimeout(() => {
      const mockPlan = {
        title: `${mockStudents.find(s => s.id === selectedStudent)?.name} - ${planTypes.find(p => p.value === planType)?.label}`,
        overview: '基于学生当前情况和目标，AI 为其制定了个性化的发展规划。该规划充分考虑了学生的兴趣特长、学业基础和未来发展方向。',
        objectives: [
          '提升核心学科成绩至年级前20%',
          '培养批判性思维和创新能力',
          '建立良好的学习习惯和时间管理能力',
          '明确未来专业方向和职业目标'
        ],
        phases: [
          {
            name: '基础巩固阶段',
            duration: '1-3个月',
            description: '夯实基础知识，建立学习体系',
            tasks: [
              '完成基础知识梳理和查漏补缺',
              '建立个人学习档案和进度跟踪',
              '制定每日学习计划和作息时间表'
            ]
          },
          {
            name: '能力提升阶段',
            duration: '4-8个月',
            description: '重点提升核心能力和专业技能',
            tasks: [
              '参加相关竞赛或项目实践',
              '深入学习目标专业相关课程',
              '培养领导力和团队协作能力'
            ]
          },
          {
            name: '目标冲刺阶段',
            duration: '9-12个月',
            description: '全力冲刺最终目标',
            tasks: [
              '完成重要考试或申请准备',
              '积累实习或志愿服务经验',
              '完善个人作品集和简历'
            ]
          }
        ],
        milestones: [
          { name: '基础评估完成', deadline: '1个月后', priority: 'high' },
          { name: '学习计划制定', deadline: '2个月后', priority: 'high' },
          { name: '第一次阶段性测评', deadline: '3个月后', priority: 'medium' },
          { name: '专业方向确定', deadline: '6个月后', priority: 'high' },
          { name: '实践项目完成', deadline: '9个月后', priority: 'medium' },
          { name: '最终目标达成', deadline: '12个月后', priority: 'high' }
        ],
        recommendations: [
          '建议每周进行一次进度回顾和调整',
          '保持与家长和老师的定期沟通',
          '注重身心健康，保持良好的生活习惯',
          '积极参与课外活动，培养综合素质'
        ]
      };
      
      setGeneratedPlan(mockPlan);
      setIsGenerating(false);
    }, 3000);
  };

  const handleSavePlan = () => {
    // 这里会调用 API 保存规划到数据库
    alert('规划已保存！');
    router.push('/workspace/plans');
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 头部 */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" onClick={() => router.push('/workspace/ai')}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          返回
        </Button>
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Target className="w-6 h-6 text-primary" />
            智能规划生成器
          </h1>
          <p className="text-muted-foreground">基于学生信息自动生成个性化学业规划方案</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 输入表单 */}
        <div className="lg:col-span-1 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                基本信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="student">选择学生</Label>
                <Select value={selectedStudent} onValueChange={setSelectedStudent}>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择学生" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockStudents.map(student => (
                      <SelectItem key={student.id} value={student.id}>
                        {student.name} - {student.grade} - {student.school}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="planType">规划类型</Label>
                <Select value={planType} onValueChange={setPlanType}>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择规划类型" />
                  </SelectTrigger>
                  <SelectContent>
                    {planTypes.map(type => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="timeframe">时间范围</Label>
                <Select value={timeframe} onValueChange={setTimeframe}>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择时间范围" />
                  </SelectTrigger>
                  <SelectContent>
                    {timeframes.map(frame => (
                      <SelectItem key={frame.value} value={frame.value}>
                        {frame.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="w-5 h-5" />
                详细信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="goals">目标描述</Label>
                <Textarea
                  id="goals"
                  placeholder="请描述学生的具体目标和期望..."
                  value={goals}
                  onChange={(e) => setGoals(e.target.value)}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="currentSituation">当前情况</Label>
                <Textarea
                  id="currentSituation"
                  placeholder="请描述学生的当前学习情况、成绩水平、兴趣特长等..."
                  value={currentSituation}
                  onChange={(e) => setCurrentSituation(e.target.value)}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="preferences">特殊要求</Label>
                <Textarea
                  id="preferences"
                  placeholder="请描述任何特殊要求或偏好..."
                  value={preferences}
                  onChange={(e) => setPreferences(e.target.value)}
                  rows={2}
                />
              </div>

              <Button 
                onClick={handleGenerate} 
                disabled={isGenerating}
                className="w-full"
              >
                {isGenerating ? (
                  <>
                    <Brain className="w-4 h-4 mr-2 animate-spin" />
                    AI 生成中...
                  </>
                ) : (
                  <>
                    <Wand2 className="w-4 h-4 mr-2" />
                    生成规划方案
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* 生成结果 */}
        <div className="lg:col-span-2">
          {isGenerating ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Brain className="w-16 h-16 text-primary mx-auto mb-4 animate-pulse" />
                <h3 className="text-lg font-semibold mb-2">AI 正在分析学生信息</h3>
                <p className="text-muted-foreground mb-4">
                  正在基于学生的具体情况生成个性化规划方案...
                </p>
                <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                  <div className="w-2 h-2 bg-primary rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                  <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                </div>
              </CardContent>
            </Card>
          ) : generatedPlan ? (
            <div className="space-y-6">
              {/* 规划概览 */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Sparkles className="w-5 h-5 text-primary" />
                      {generatedPlan.title}
                    </CardTitle>
                    <Button onClick={handleSavePlan}>
                      <Save className="w-4 h-4 mr-2" />
                      保存规划
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{generatedPlan.overview}</p>
                </CardContent>
              </Card>

              {/* 目标列表 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    核心目标
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {generatedPlan.objectives.map((objective: string, index: number) => (
                      <li key={index} className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span>{objective}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              {/* 阶段规划 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    阶段规划
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {generatedPlan.phases.map((phase: any, index: number) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold">{phase.name}</h4>
                          <Badge variant="outline">{phase.duration}</Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-3">{phase.description}</p>
                        <ul className="space-y-1">
                          {phase.tasks.map((task: string, taskIndex: number) => (
                            <li key={taskIndex} className="text-sm flex items-center gap-2">
                              <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                              {task}
                            </li>
                          ))}
                        </ul>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* 关键里程碑 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="w-5 h-5" />
                    关键里程碑
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {generatedPlan.milestones.map((milestone: any, index: number) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${
                            milestone.priority === 'high' ? 'bg-red-500' :
                            milestone.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                          }`} />
                          <span className="font-medium">{milestone.name}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Clock className="w-4 h-4" />
                          {milestone.deadline}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* 建议事项 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="w-5 h-5" />
                    重要建议
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {generatedPlan.recommendations.map((recommendation: string, index: number) => (
                      <li key={index} className="flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-yellow-500 rounded-full mt-2" />
                        <span className="text-sm">{recommendation}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <Target className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">准备生成规划方案</h3>
                <p className="text-muted-foreground">
                  请在左侧填写学生信息，然后点击"生成规划方案"按钮
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
