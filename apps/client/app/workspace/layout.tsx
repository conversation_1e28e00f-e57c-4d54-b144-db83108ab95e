import { SidebarLeft } from "@/components/layout/workspace/sidebar-left";
import { SidebarRight } from "@/components/layout/workspace/sidebar-right";
import { SiteHeader } from "@/components/site-header";
import { WorkspaceMenus } from "@/config/menu";
import {
  SidebarInset,
  SidebarProvider,
} from "@workspace/ui/components/sidebar";
import { headers } from "next/headers";

export default async function DashboardLayout({
  children,
}: React.PropsWithChildren) {
  const headersList = await headers();
  const tenantSubdomain = headersList.get("x-tenant-subdomain");
  const tenantMode = headersList.get("x-tenant-mode") === "true";

  console.log(
    "[Workspace Layout] 租户模式:",
    tenantMode,
    "子域名:",
    tenantSubdomain,
  );
  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <SidebarLeft menus={WorkspaceMenus} />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              {children}
            </div>
          </div>
        </div>
      </SidebarInset>
      <SidebarRight />
    </SidebarProvider>
  );
}
