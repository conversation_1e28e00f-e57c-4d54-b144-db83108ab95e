"use client";

import { NotificationSystem } from "@/components/crm/NotificationSystem";
import { Badge } from "@workspace/ui/components/badge";
import { Button } from "@workspace/ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@workspace/ui/components/tabs";
import {
    AlertCircle,
    Brain,
    Calendar,
    CheckCircle,
    Clock,
    Download,
    FileText,
    Filter,
    Heart,
    MessageSquare,
    Phone,
    Plus,
    Search,
    Star,
    Target,
    TrendingUp,
    Users,
    Zap
} from "lucide-react";
import { useState } from "react";

// 模拟数据
const mockStudents = [
  {
    id: '1',
    name: '张小明',
    grade: '高二',
    school: '北京四中',
    avatar: null,
    parentName: '张父',
    parentPhone: '13600136000',
    tags: ['理科强', '数学竞赛'],
    servicePhase: '方案制定',
    progress: 65,
    satisfaction: 5,
    urgency: 'medium',
    status: 'ACTIVE',
    totalSessions: 8,
    completedMilestones: 3,
    totalMilestones: 5,
    joinDate: '2024-01-15'
  },
  {
    id: '2',
    name: '李小红',
    grade: '高三',
    school: '清华附中',
    avatar: null,
    parentName: '李母',
    parentPhone: '13700137000',
    tags: ['文科强', '英语优秀'],
    servicePhase: '执行跟踪',
    progress: 80,
    satisfaction: 4,
    urgency: 'low',
    status: 'ACTIVE',
    totalSessions: 12,
    completedMilestones: 4,
    totalMilestones: 5,
    joinDate: '2024-02-20'
  },
  {
    id: '3',
    name: '王小华',
    grade: '高一',
    school: '人大附中',
    avatar: null,
    parentName: '王父',
    parentPhone: '13800138000',
    tags: ['艺术特长', '钢琴'],
    servicePhase: '初次咨询',
    progress: 20,
    satisfaction: 5,
    urgency: 'high',
    status: 'ACTIVE',
    totalSessions: 2,
    completedMilestones: 0,
    totalMilestones: 6,
    joinDate: '2024-11-10'
  }
];

const mockStats = {
  total: 45,
  active: 38,
  graduated: 5,
  inactive: 2,
  averageSatisfaction: 4.6,
  averageProgress: 67,
  thisMonth: {
    newStudents: 8,
    completedMilestones: 23,
    revenue: 125000,
    satisfaction: 4.8
  }
};

export default function CRMPage() {
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedStudents, setSelectedStudents] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");

  // 学生卡片组件
  const StudentCard = ({ student }: { student: typeof mockStudents[0] }) => {
    const isSelected = selectedStudents.includes(student.id);
    
    const getUrgencyColor = (urgency: string) => {
      switch (urgency) {
        case 'high': return 'border-l-red-500 bg-red-50';
        case 'medium': return 'border-l-yellow-500 bg-yellow-50';
        default: return 'border-l-green-500 bg-green-50';
      }
    };

    const getStatusBadge = (status: string) => {
      switch (status) {
        case 'ACTIVE': return <Badge className="bg-green-100 text-green-800">服务中</Badge>;
        case 'GRADUATED': return <Badge className="bg-blue-100 text-blue-800">已毕业</Badge>;
        default: return <Badge variant="secondary">暂停中</Badge>;
      }
    };

    return (
      <Card className={`cursor-pointer transition-all hover:shadow-md border-l-4 ${getUrgencyColor(student.urgency)} ${isSelected ? 'ring-2 ring-primary' : ''}`}>
        <CardContent className="p-4">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3 flex-1">
              {/* 头像 */}
              <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                <span className="text-primary font-medium text-lg">
                  {student.name.charAt(0)}
                </span>
              </div>
              
              {/* 基本信息 */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-semibold truncate">{student.name}</h4>
                  {getStatusBadge(student.status)}
                </div>
                <div className="text-sm text-muted-foreground mb-2">
                  {student.grade} • {student.school}
                </div>
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <span>进度: {student.progress}%</span>
                  <span>阶段: {student.servicePhase}</span>
                  <div className="flex items-center gap-1">
                    <span>满意度:</span>
                    {[...Array(5)].map((_, i) => (
                      <Star 
                        key={i}
                        className={`w-3 h-3 ${i < student.satisfaction ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* 快速操作 */}
            <div className="flex items-center gap-1 ml-4">
              <Button variant="ghost" size="sm" title="发送消息">
                <MessageSquare className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" title="拨打电话">
                <Phone className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" title="预约咨询">
                <Calendar className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" title="生成报告">
                <FileText className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* 标签和进度 */}
          <div className="mt-3 space-y-2">
            <div className="flex flex-wrap gap-1">
              {student.tags.map(tag => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-primary h-2 rounded-full transition-all"
                style={{ width: `${student.progress}%` }}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  // 统计卡片组件
  const StatsCard = ({ title, value, change, icon: Icon, color }: {
    title: string;
    value: string | number;
    change?: string;
    icon: any;
    color: string;
  }) => (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <div className="flex items-baseline gap-2">
              <p className="text-2xl font-bold">{value}</p>
              {change && (
                <span className="text-sm text-green-600 flex items-center gap-1">
                  <TrendingUp className="w-3 h-3" />
                  {change}
                </span>
              )}
            </div>
          </div>
          <div className={`p-3 rounded-lg ${color}`}>
            <Icon className="w-6 h-6 text-white" />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <header className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <Brain className="w-8 h-8 text-primary" />
              <h1 className="text-xl font-bold">智能CRM系统</h1>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                导出数据
              </Button>
              <Button size="sm">
                <Plus className="w-4 h-4 mr-2" />
                添加学生
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">总览</TabsTrigger>
            <TabsTrigger value="students">学生管理</TabsTrigger>
            <TabsTrigger value="analytics">数据分析</TabsTrigger>
            <TabsTrigger value="notifications">通知中心</TabsTrigger>
            <TabsTrigger value="settings">系统设置</TabsTrigger>
          </TabsList>

          {/* 总览页面 */}
          <TabsContent value="overview" className="space-y-6">
            {/* 统计卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <StatsCard
                title="总学生数"
                value={mockStats.total}
                change="+8 本月"
                icon={Users}
                color="bg-blue-500"
              />
              <StatsCard
                title="服务中"
                value={mockStats.active}
                change="+3 本月"
                icon={Clock}
                color="bg-green-500"
              />
              <StatsCard
                title="平均满意度"
                value={`${mockStats.averageSatisfaction}/5`}
                change="+0.2"
                icon={Heart}
                color="bg-red-500"
              />
              <StatsCard
                title="平均进度"
                value={`${mockStats.averageProgress}%`}
                change="+5%"
                icon={Target}
                color="bg-purple-500"
              />
            </div>

            {/* 智能洞察 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="w-5 h-5" />
                  智能洞察与建议
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-start gap-3">
                      <AlertCircle className="w-5 h-5 text-red-500 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-red-800">需要关注</h4>
                        <p className="text-sm text-red-700">
                          王小华的服务进度较慢，建议主动跟进
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-start gap-3">
                      <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-green-800">表现优秀</h4>
                        <p className="text-sm text-green-700">
                          本月学生满意度提升0.2分，服务质量持续改善
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 最近活动 */}
            <Card>
              <CardHeader>
                <CardTitle>最近学生活动</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockStudents.slice(0, 3).map(student => (
                    <div key={student.id} className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                      <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                        <span className="text-primary font-medium text-sm">
                          {student.name.charAt(0)}
                        </span>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{student.name}</p>
                        <p className="text-xs text-muted-foreground">
                          完成了《学习规划方案》里程碑 - 2小时前
                        </p>
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {student.servicePhase}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 学生管理页面 */}
          <TabsContent value="students" className="space-y-6">
            {/* 搜索和筛选 */}
            <Card>
              <CardContent className="p-6">
                <div className="flex gap-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                    <input
                      type="text"
                      placeholder="搜索学生姓名、学校..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                  <Button variant="outline">
                    <Filter className="w-4 h-4 mr-2" />
                    筛选
                  </Button>
                  <Button variant="outline">
                    批量操作
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 学生列表 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {mockStudents
                .filter(student => 
                  student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                  student.school.toLowerCase().includes(searchTerm.toLowerCase())
                )
                .map(student => (
                  <StudentCard key={student.id} student={student} />
                ))}
            </div>
          </TabsContent>

          {/* 数据分析页面 */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 服务阶段分布 */}
              <Card>
                <CardHeader>
                  <CardTitle>服务阶段分布</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { name: '初次咨询', count: 8, color: 'bg-blue-500' },
                      { name: '需求分析', count: 12, color: 'bg-purple-500' },
                      { name: '方案制定', count: 15, color: 'bg-green-500' },
                      { name: '执行跟踪', count: 8, color: 'bg-orange-500' },
                      { name: '成果验收', count: 2, color: 'bg-red-500' }
                    ].map(phase => (
                      <div key={phase.name} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${phase.color}`} />
                          <span className="text-sm">{phase.name}</span>
                        </div>
                        <span className="text-sm font-medium">{phase.count}人</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* 满意度分析 */}
              <Card>
                <CardHeader>
                  <CardTitle>满意度分布</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { rating: 5, count: 28, percentage: 62 },
                      { rating: 4, count: 12, percentage: 27 },
                      { rating: 3, count: 4, percentage: 9 },
                      { rating: 2, count: 1, percentage: 2 },
                      { rating: 1, count: 0, percentage: 0 }
                    ].map(item => (
                      <div key={item.rating} className="flex items-center gap-3">
                        <div className="flex items-center gap-1 w-16">
                          <span className="text-sm">{item.rating}</span>
                          <Star className="w-3 h-3 text-yellow-400 fill-current" />
                        </div>
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-yellow-400 h-2 rounded-full"
                            style={{ width: `${item.percentage}%` }}
                          />
                        </div>
                        <span className="text-sm text-muted-foreground w-8">
                          {item.count}人
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 月度趋势 */}
            <Card>
              <CardHeader>
                <CardTitle>月度服务趋势</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-end justify-between gap-4">
                  {[85, 88, 92, 89, 94, 96].map((value, index) => (
                    <div key={index} className="flex flex-col items-center gap-2">
                      <div 
                        className="bg-primary rounded-t"
                        style={{ height: `${value}%`, width: '40px' }}
                      />
                      <span className="text-xs text-muted-foreground">
                        {index + 1}月
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 通知中心页面 */}
          <TabsContent value="notifications" className="space-y-6">
            <NotificationSystem />
          </TabsContent>

          {/* 系统设置页面 */}
          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>功能特性演示</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="font-semibold">🎯 核心功能</h4>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        智能学生档案管理
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        服务进度可视化跟踪
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        AI辅助表单录入
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        移动端优化交互
                      </li>
                    </ul>
                  </div>
                  
                  <div className="space-y-4">
                    <h4 className="font-semibold">📱 移动端特性</h4>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        卡片滑动快速操作
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        语音录入支持
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        拍照文档识别
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        位置感知功能
                      </li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
} 