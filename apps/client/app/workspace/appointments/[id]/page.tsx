"use client";

import { use } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import { Textarea } from "@workspace/ui/components/textarea";
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Calendar,
  Clock,
  User,
  MapPin,
  Video,
  Phone,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileText,
  MessageSquare,
  Save
} from "lucide-react";
import { useState } from "react";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";

interface AppointmentDetailPageProps {
  params: Promise<{ id: string }>;
}

// 模拟数据 - 实际项目中会从 API 获取
const mockAppointment = {
  id: '1',
  title: '升学规划咨询',
  description: '讨论高考志愿填报和专业选择',
  studentName: '张小明',
  studentId: '1',
  startTime: new Date('2024-12-20T14:00:00'),
  endTime: new Date('2024-12-20T15:30:00'),
  type: 'ONLINE',
  location: 'https://meet.example.com/abc123',
  status: 'CONFIRMED',
  notes: '学生对计算机专业很感兴趣，需要详细介绍相关院校',
  summary: '',
  createdAt: new Date('2024-12-15T10:00:00'),
  updatedAt: new Date('2024-12-15T10:00:00')
};

const getStatusConfig = (status: string) => {
  switch (status) {
    case 'CONFIRMED':
      return {
        label: '已确认',
        color: 'bg-green-100 text-green-800 border-green-200',
        icon: CheckCircle
      };
    case 'PENDING':
      return {
        label: '待确认',
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        icon: AlertCircle
      };
    case 'COMPLETED':
      return {
        label: '已完成',
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: CheckCircle
      };
    case 'CANCELLED':
      return {
        label: '已取消',
        color: 'bg-red-100 text-red-800 border-red-200',
        icon: XCircle
      };
    default:
      return {
        label: '未知',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        icon: AlertCircle
      };
  }
};

const getTypeConfig = (type: string) => {
  switch (type) {
    case 'ONLINE':
      return { label: '线上会议', icon: Video, color: 'text-blue-600' };
    case 'OFFLINE':
      return { label: '线下面谈', icon: MapPin, color: 'text-green-600' };
    case 'PHONE':
      return { label: '电话咨询', icon: Phone, color: 'text-purple-600' };
    default:
      return { label: '未知', icon: AlertCircle, color: 'text-gray-600' };
  }
};

export default function AppointmentDetailPage({ params }: AppointmentDetailPageProps) {
  const { id } = use(params);
  const router = useRouter();
  const [summary, setSummary] = useState(mockAppointment.summary);
  const [isEditing, setIsEditing] = useState(false);

  const statusConfig = getStatusConfig(mockAppointment.status);
  const typeConfig = getTypeConfig(mockAppointment.type);
  const StatusIcon = statusConfig.icon;
  const TypeIcon = typeConfig.icon;

  const handleSaveSummary = () => {
    // 这里会调用 API 保存总结
    console.log('保存总结:', summary);
    setIsEditing(false);
  };

  const handleStatusChange = (newStatus: string) => {
    // 这里会调用 API 更新状态
    console.log('更新状态:', newStatus);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.push('/workspace/appointments')}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回预约列表
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{mockAppointment.title}</h1>
            <p className="text-muted-foreground">预约详情</p>
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => router.push(`/workspace/appointments/${id}/edit`)}>
            <Edit className="w-4 h-4 mr-2" />
            编辑
          </Button>
          <Button variant="outline" className="text-destructive hover:text-destructive">
            <Trash2 className="w-4 h-4 mr-2" />
            删除
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 主要内容 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  预约信息
                </CardTitle>
                <Badge variant="outline" className={statusConfig.color}>
                  <StatusIcon className="w-3 h-3 mr-1" />
                  {statusConfig.label}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground">学生</label>
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 text-muted-foreground" />
                    <span className="font-medium">{mockAppointment.studentName}</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground">预约类型</label>
                  <div className="flex items-center gap-2">
                    <TypeIcon className={`w-4 h-4 ${typeConfig.color}`} />
                    <span className={`font-medium ${typeConfig.color}`}>{typeConfig.label}</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground">开始时间</label>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-muted-foreground" />
                    <span className="font-medium">
                      {format(mockAppointment.startTime, 'yyyy年M月d日 HH:mm', { locale: zhCN })}
                    </span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground">结束时间</label>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-muted-foreground" />
                    <span className="font-medium">
                      {format(mockAppointment.endTime, 'yyyy年M月d日 HH:mm', { locale: zhCN })}
                    </span>
                  </div>
                </div>
                
                {mockAppointment.location && (
                  <div className="space-y-2 md:col-span-2">
                    <label className="text-sm font-medium text-muted-foreground">地点/链接</label>
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-muted-foreground" />
                      {mockAppointment.type === 'ONLINE' ? (
                        <a 
                          href={mockAppointment.location} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="font-medium text-blue-600 hover:text-blue-800 underline"
                        >
                          {mockAppointment.location}
                        </a>
                      ) : (
                        <span className="font-medium">{mockAppointment.location}</span>
                      )}
                    </div>
                  </div>
                )}
              </div>
              
              {mockAppointment.description && (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground">描述</label>
                  <p className="text-sm bg-muted/50 p-3 rounded-lg">{mockAppointment.description}</p>
                </div>
              )}
              
              {mockAppointment.notes && (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground">备注</label>
                  <p className="text-sm bg-muted/50 p-3 rounded-lg">{mockAppointment.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 咨询总结 */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  咨询总结
                </CardTitle>
                {!isEditing && (
                  <Button size="sm" onClick={() => setIsEditing(true)}>
                    <Edit className="w-4 h-4 mr-2" />
                    编辑总结
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {isEditing ? (
                <div className="space-y-4">
                  <Textarea
                    value={summary}
                    onChange={(e) => setSummary(e.target.value)}
                    placeholder="请输入本次咨询的总结..."
                    rows={6}
                  />
                  <div className="flex gap-2">
                    <Button onClick={handleSaveSummary}>
                      <Save className="w-4 h-4 mr-2" />
                      保存
                    </Button>
                    <Button variant="outline" onClick={() => setIsEditing(false)}>
                      取消
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="min-h-[100px] p-4 bg-muted/30 rounded-lg">
                  {summary ? (
                    <p className="whitespace-pre-wrap">{summary}</p>
                  ) : (
                    <p className="text-muted-foreground italic">暂无咨询总结</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 侧边栏 */}
        <div className="space-y-6">
          {/* 快速操作 */}
          <Card>
            <CardHeader>
              <CardTitle>快速操作</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {mockAppointment.status === 'PENDING' && (
                <Button 
                  variant="outline" 
                  className="w-full justify-start text-green-600"
                  onClick={() => handleStatusChange('CONFIRMED')}
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  确认预约
                </Button>
              )}
              {mockAppointment.status === 'CONFIRMED' && (
                <Button 
                  variant="outline" 
                  className="w-full justify-start text-blue-600"
                  onClick={() => handleStatusChange('COMPLETED')}
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  标记完成
                </Button>
              )}
              <Button variant="outline" className="w-full justify-start">
                <MessageSquare className="w-4 h-4 mr-2" />
                发送消息
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Calendar className="w-4 h-4 mr-2" />
                重新安排
              </Button>
            </CardContent>
          </Card>

          {/* 时间信息 */}
          <Card>
            <CardHeader>
              <CardTitle>时间信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2 text-sm">
                <Clock className="w-4 h-4 text-muted-foreground" />
                <span className="text-muted-foreground">创建时间:</span>
                <span>{format(mockAppointment.createdAt, 'yyyy-MM-dd HH:mm')}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Clock className="w-4 h-4 text-muted-foreground" />
                <span className="text-muted-foreground">更新时间:</span>
                <span>{format(mockAppointment.updatedAt, 'yyyy-MM-dd HH:mm')}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
