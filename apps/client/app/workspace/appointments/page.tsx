"use client";

import { Badge } from "@workspace/ui/components/badge";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Card, CardContent } from "@workspace/ui/components/card";
import { Input } from "@workspace/ui/components/input";
import {
    AlertCircle,
    Calendar,
    CalendarDays,
    CheckCircle,
    Clock,
    Filter,
    List,
    MapPin,
    Phone,
    Plus,
    Search,
    User,
    Video,
    XCircle
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";

// 模拟数据
const mockAppointments: Appointment[] = [
  {
    id: '1',
    title: '升学规划咨询',
    description: '讨论高考志愿填报和专业选择',
    studentName: '张小明',
    studentId: '1',
    startTime: new Date('2024-12-20T14:00:00'),
    endTime: new Date('2024-12-20T15:30:00'),
    type: 'ONLINE',
    location: 'https://meet.example.com/abc123',
    status: 'CONFIRMED',
    color: '#e3f2fd'
  },
  {
    id: '2',
    title: '语言能力评估',
    description: '英语水平测试和学习计划制定',
    studentName: '李晓红',
    studentId: '2',
    startTime: new Date('2024-12-21T10:00:00'),
    endTime: new Date('2024-12-21T11:00:00'),
    type: 'OFFLINE',
    location: '北京市朝阳区咨询室A',
    status: 'PENDING',
    color: '#fff3e0'
  },
  {
    id: '3',
    title: '编程学习指导',
    description: '计算机编程学习路径规划',
    studentName: '陈子豪',
    studentId: '3',
    startTime: new Date('2024-12-18T16:00:00'),
    endTime: new Date('2024-12-18T17:00:00'),
    type: 'PHONE',
    location: '电话咨询',
    status: 'COMPLETED',
    color: '#e8f5e8'
  },
  // 添加更多测试数据
  {
    id: '4',
    title: '数学辅导',
    studentName: '王小华',
    studentId: '4',
    startTime: new Date('2024-12-22T09:00:00'),
    endTime: new Date('2024-12-22T10:30:00'),
    type: 'ONLINE',
    status: 'CONFIRMED',
    color: '#f3e5f5'
  },
  {
    id: '5',
    title: '心理咨询',
    studentName: '刘明',
    studentId: '5',
    startTime: new Date('2024-12-23T15:00:00'),
    endTime: new Date('2024-12-23T16:00:00'),
    type: 'OFFLINE',
    location: '心理咨询室',
    status: 'CONFIRMED',
    color: '#e1f5fe'
  }
];

const getStatusConfig = (status: string) => {
  switch (status) {
    case 'CONFIRMED':
      return {
        label: '已确认',
        color: 'bg-green-100 text-green-800 border-green-200',
        icon: CheckCircle
      };
    case 'PENDING':
      return {
        label: '待确认',
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        icon: AlertCircle
      };
    case 'COMPLETED':
      return {
        label: '已完成',
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: CheckCircle
      };
    case 'CANCELLED':
      return {
        label: '已取消',
        color: 'bg-red-100 text-red-800 border-red-200',
        icon: XCircle
      };
    default:
      return {
        label: '未知',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        icon: AlertCircle
      };
  }
};

const getTypeConfig = (type: string) => {
  switch (type) {
    case 'ONLINE':
      return { label: '线上', icon: Video, color: 'text-blue-600' };
    case 'OFFLINE':
      return { label: '线下', icon: MapPin, color: 'text-green-600' };
    case 'PHONE':
      return { label: '电话', icon: Phone, color: 'text-purple-600' };
    default:
      return { label: '未知', icon: AlertCircle, color: 'text-gray-600' };
  }
};

export default function AppointmentsPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'scheduler' | 'list'>('scheduler');

  const filteredAppointments = mockAppointments.filter(appointment => {
    const matchesSearch = appointment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         appointment.studentName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || appointment.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const stats = {
    total: mockAppointments.length,
    pending: mockAppointments.filter(a => a.status === 'PENDING').length,
    confirmed: mockAppointments.filter(a => a.status === 'CONFIRMED').length,
    completed: mockAppointments.filter(a => a.status === 'COMPLETED').length
  };

  // 调度器事件处理
  const handleAppointmentClick = (appointment: Appointment) => {
    router.push(`/workspace/appointments/${appointment.id}`);
  };

  const handleCreateAppointment = (date: Date, time?: string) => {
    const params = new URLSearchParams();
    params.set('date', date.toISOString());
    if (time) params.set('time', time);
    router.push(`/workspace/appointments/new?${params.toString()}`);
  };

  const handleEditAppointment = (appointment: Appointment) => {
    router.push(`/workspace/appointments/${appointment.id}/edit`);
  };

  const handleDeleteAppointment = async (appointment: Appointment) => {
    if (confirm(`确定要删除预约"${appointment.title}"吗？`)) {
      // 这里会调用 API 删除预约
      console.log('删除预约:', appointment.id);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">预约管理</h1>
          <p className="text-muted-foreground">管理与学生的咨询预约</p>
        </div>
        <div className="flex items-center gap-2">
          {/* 视图切换 */}
          <div className="flex border rounded-lg">
            <Button
              size="sm"
              variant={viewMode === 'scheduler' ? 'default' : 'ghost'}
              onClick={() => setViewMode('scheduler')}
              className="rounded-r-none"
            >
              <CalendarDays className="w-4 h-4 mr-2" />
              日程视图
            </Button>
            <Button
              size="sm"
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              onClick={() => setViewMode('list')}
              className="rounded-l-none"
            >
              <List className="w-4 h-4 mr-2" />
              列表视图
            </Button>
          </div>
          <Button onClick={() => router.push('/workspace/appointments/new')}>
            <Plus className="w-4 h-4 mr-2" />
            新建预约
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Calendar className="w-5 h-5 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">总预约数</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <AlertCircle className="w-5 h-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">待确认</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">已确认</p>
                <p className="text-2xl font-bold text-green-600">{stats.confirmed}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <CheckCircle className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">已完成</p>
                <p className="text-2xl font-bold text-blue-600">{stats.completed}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>



      {/* 主要内容区域 */}
      {viewMode === 'scheduler' ? (
        <AppointmentScheduler
          appointments={mockAppointments}
          onAppointmentClick={handleAppointmentClick}
          onCreateAppointment={handleCreateAppointment}
          onEditAppointment={handleEditAppointment}
          onDeleteAppointment={handleDeleteAppointment}
        />
      ) : (
        <>
          {/* 列表视图的工具栏 */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                <div className="flex flex-1 gap-4 items-center">
                  {/* 搜索框 */}
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                    <Input
                      placeholder="搜索预约、学生姓名..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  {/* 状态筛选 */}
                  <div className="flex gap-2">
                    {[
                      { key: 'all', label: '全部', count: stats.total },
                      { key: 'PENDING', label: '待确认', count: stats.pending },
                      { key: 'CONFIRMED', label: '已确认', count: stats.confirmed },
                      { key: 'COMPLETED', label: '已完成', count: stats.completed }
                    ].map(filter => (
                      <Button
                        key={filter.key}
                        variant={filterStatus === filter.key ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setFilterStatus(filter.key)}
                        className="gap-2"
                      >
                        {filter.label}
                        <Badge variant="secondary" className="text-xs">
                          {filter.count}
                        </Badge>
                      </Button>
                    ))}
                  </div>
                </div>

                <Button size="sm" variant="outline">
                  <Filter className="w-4 h-4 mr-2" />
                  筛选
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 预约列表 */}
          {filteredAppointments.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">暂无预约</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm ? '没有找到匹配的预约' : '开始安排第一个学生咨询预约吧'}
            </p>
            <Button onClick={() => router.push('/workspace/appointments/new')}>
              <Plus className="w-4 h-4 mr-2" />
              新建预约
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredAppointments.map(appointment => {
            const statusConfig = getStatusConfig(appointment.status);
            const typeConfig = getTypeConfig(appointment.type);
            const StatusIcon = statusConfig.icon;
            const TypeIcon = typeConfig.icon;
            
            return (
              <Card key={appointment.id} className="hover:shadow-lg transition-shadow cursor-pointer"
                    onClick={() => router.push(`/workspace/appointments/${appointment.id}`)}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold truncate">{appointment.title}</h3>
                        <Badge variant="outline" className={statusConfig.color}>
                          <StatusIcon className="w-3 h-3 mr-1" />
                          {statusConfig.label}
                        </Badge>
                      </div>
                      
                      <p className="text-muted-foreground mb-3 line-clamp-2">
                        {appointment.description}
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4 text-muted-foreground" />
                          <span className="font-medium">{appointment.studentName}</span>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4 text-muted-foreground" />
                          <span>
                            {new Date(appointment.startTime).toLocaleString('zh-CN', {
                              month: 'short',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <TypeIcon className={`w-4 h-4 ${typeConfig.color}`} />
                          <span className={typeConfig.color}>{typeConfig.label}</span>
                          {appointment.type !== 'PHONE' && (
                            <span className="text-muted-foreground truncate">
                              · {appointment.location}
                            </span>
                          )}
                        </div>
                      </div>

                      {appointment.notes && (
                        <div className="mt-3 p-2 bg-muted/50 rounded text-sm">
                          <strong>备注：</strong>{appointment.notes}
                        </div>
                      )}

                      {appointment.summary && (
                        <div className="mt-3 p-2 bg-blue-50 rounded text-sm">
                          <strong>总结：</strong>{appointment.summary}
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
        </>
      )}
    </div>
  );
}
