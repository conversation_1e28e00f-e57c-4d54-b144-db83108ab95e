"use client";
import { useState, useMemo } from "react";

export type Student = {
  id: string;
  name: string;
  grade: string;
  status: string;
  tags: string[];
  parentName?: string;
  parentContact?: string;
};

// mock 数据
const MOCK_STUDENTS: Student[] = [
  {
    id: "1",
    name: "王小明",
    grade: "高一",
    status: "服务中",
    tags: ["理科", "重点跟进"],
    parentName: "王先生",
    parentContact: "13811112222",
  },
  {
    id: "2",
    name: "李晓红",
    grade: "初三",
    status: "已签约",
    tags: ["文科"],
    parentName: "李女士",
    parentContact: "13922223333",
  },
  {
    id: "3",
    name: "陈子豪",
    grade: "高二",
    status: "潜在客户",
    tags: ["理科"],
    parentName: "陈先生",
    parentContact: "13733334444",
  },
];

export function useStudentList() {
  const [search, setSearch] = useState("");
  const [tagFilter, setTagFilter] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string | null>(null);

  const filtered = useMemo(() => {
    return MOCK_STUDENTS.filter((student) => {
      const matchSearch =
        student.name.includes(search) || student.parentName?.includes(search);
      const matchTag = tagFilter ? student.tags.includes(tagFilter) : true;
      const matchStatus = statusFilter ? student.status === statusFilter : true;
      return matchSearch && matchTag && matchStatus;
    });
  }, [search, tagFilter, statusFilter]);

  return {
    students: filtered,
    search,
    setSearch,
    tagFilter,
    setTagFilter,
    statusFilter,
    setStatusFilter,
    allTags: Array.from(new Set(MOCK_STUDENTS.flatMap((s) => s.tags))),
    allStatuses: Array.from(new Set(MOCK_STUDENTS.map((s) => s.status))),
  };
}
