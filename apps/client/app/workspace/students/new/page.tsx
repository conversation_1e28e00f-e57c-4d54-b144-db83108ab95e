"use client";

import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Button } from "@workspace/ui/components/button";
import { ArrowLeft, UserPlus } from "lucide-react";
import { StudentForm } from "@/components/crm/StudentForm";
import { useCreateStudent } from "@/hooks/useCRMStudents";
import { toast } from "sonner";

export default function NewStudentPage() {
  const router = useRouter();
  const { createStudent, isCreating } = useCreateStudent();

  const handleSubmit = async (data: any) => {
    try {
      const newStudent = await createStudent(data);
      toast.success("学生创建成功！");
      router.push(`/workspace/students/${newStudent.id}`);
    } catch (error) {
      toast.error("创建学生失败，请重试");
      console.error("创建学生失败:", error);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 头部 */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" onClick={() => router.back()}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          返回
        </Button>
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <UserPlus className="w-6 h-6" />
            添加新学生
          </h1>
          <p className="text-muted-foreground">填写学生基本信息</p>
        </div>
      </div>

      {/* 表单 */}
      <StudentForm
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isLoading={isCreating}
      />
    </div>
  );
}
