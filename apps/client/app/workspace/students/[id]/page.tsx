"use client";

import { useStudentDetail } from "@/hooks/useCRMStudents";
import { Avatar, AvatarFallback, AvatarImage } from "@workspace/ui/components/avatar";
import { Badge } from "@workspace/ui/components/badge";
import { Button } from "@workspace/ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@workspace/ui/components/tabs";
import { motion } from "framer-motion";
import {
    Activity,
    ArrowLeft,
    BookOpen,
    Calendar,
    Clock,
    Edit,
    FileText,
    GraduationCap,
    Home,
    MessageCircle,
    School,
    Trash2
} from "lucide-react";
import { useRouter } from "next/navigation";
import { use, useState } from "react";

interface StudentDetailPageProps {
  params: Promise<{ id: string }>;
}

// 状态配置
const getStatusConfig = (status: string) => {
  switch (status) {
    case 'ACTIVE':
      return {
        label: '在读',
        color: 'bg-green-100 text-green-800 border-green-200',
        dot: 'bg-green-500'
      };
    case 'GRADUATED':
      return {
        label: '已毕业',
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        dot: 'bg-blue-500'
      };
    case 'INACTIVE':
      return {
        label: '暂停',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        dot: 'bg-gray-500'
      };
    default:
      return {
        label: '未知',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        dot: 'bg-gray-500'
      };
  }
};

// 计算年龄
const calculateAge = (birthday?: string | null) => {
  if (!birthday) return null;
  const today = new Date();
  const birthDate = new Date(birthday);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
};

export default function StudentDetailPage({ params }: StudentDetailPageProps) {
  const { id } = use(params);
  const router = useRouter();
  const { student, isLoading, error, updateStudent, isUpdating } = useStudentDetail(id);
  const [activeTab, setActiveTab] = useState('overview');

  if (isLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-48 mb-6" />
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 bg-muted rounded-full" />
                    <div className="space-y-2">
                      <div className="h-6 bg-muted rounded w-32" />
                      <div className="h-4 bg-muted rounded w-48" />
                    </div>
                  </div>
                </CardHeader>
              </Card>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !student) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-12 text-center">
            <div className="text-muted-foreground mb-4">
              {error ? '加载学生信息失败' : '学生不存在'}
            </div>
            <Button onClick={() => router.push('/workspace/students')}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回学生列表
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const statusConfig = getStatusConfig(student.status);
  const age = calculateAge(student.birthday);

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 头部导航 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.push('/workspace/students')}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回学生列表
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{student.name}</h1>
            <p className="text-muted-foreground">学生详情</p>
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline">
            <MessageCircle className="w-4 h-4 mr-2" />
            发消息
          </Button>
          <Button variant="outline" onClick={() => router.push(`/workspace/students/${id}/edit`)}>
            <Edit className="w-4 h-4 mr-2" />
            编辑
          </Button>
          <Button variant="outline" className="text-destructive hover:text-destructive">
            <Trash2 className="w-4 h-4 mr-2" />
            删除
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 主要内容区域 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 基本信息卡片 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <CardHeader>
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <Avatar className="h-16 w-16">
                      <AvatarImage src={student.avatar || undefined} />
                      <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-semibold text-xl">
                        {student.name.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <div className={`absolute -bottom-1 -right-1 w-5 h-5 rounded-full border-2 border-white ${statusConfig.dot}`} />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h2 className="text-xl font-semibold">{student.name}</h2>
                      <Badge variant="outline" className={statusConfig.color}>
                        {statusConfig.label}
                      </Badge>
                      {student.gender && (
                        <Badge variant="secondary">
                          {student.gender}
                          {age && ` · ${age}岁`}
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <GraduationCap className="w-4 h-4" />
                        <span>{student.grade || '未设置'}</span>
                      </div>
                      {student.school && (
                        <div className="flex items-center gap-1">
                          <School className="w-4 h-4" />
                          <span>{student.school}</span>
                        </div>
                      )}
                      {student.birthday && (
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          <span>{new Date(student.birthday).toLocaleDateString()}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardHeader>
            </Card>
          </motion.div>

          {/* 详细信息标签页 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">概览</TabsTrigger>
                <TabsTrigger value="academic">学业</TabsTrigger>
                <TabsTrigger value="family">家庭</TabsTrigger>
                <TabsTrigger value="notes">备注</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="w-5 h-5" />
                      基本信息
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-muted-foreground">姓名</label>
                        <p className="font-medium">{student.name}</p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-muted-foreground">性别</label>
                        <p className="font-medium">{student.gender || '未设置'}</p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-muted-foreground">出生日期</label>
                        <p className="font-medium">
                          {student.birthday ? new Date(student.birthday).toLocaleDateString() : '未设置'}
                        </p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-muted-foreground">联系电话</label>
                        <p className="font-medium">{student.phone || '未设置'}</p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-muted-foreground">邮箱</label>
                        <p className="font-medium">{student.email || '未设置'}</p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-muted-foreground">状态</label>
                        <Badge variant="outline" className={statusConfig.color}>
                          {statusConfig.label}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="academic" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BookOpen className="w-5 h-5" />
                      学业信息
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-muted-foreground">学校</label>
                        <p className="font-medium">{student.school || '未设置'}</p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-muted-foreground">年级</label>
                        <p className="font-medium">{student.grade || '未设置'}</p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-muted-foreground">专业</label>
                        <p className="font-medium">{student.major || '未设置'}</p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-muted-foreground">GPA</label>
                        <p className="font-medium">
                          {student.gpa ? student.gpa.toFixed(2) : '未设置'}
                        </p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-muted-foreground">排名</label>
                        <p className="font-medium">{student.rank || '未设置'}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="family" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Home className="w-5 h-5" />
                      家庭信息
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-muted-foreground">家长姓名</label>
                        <p className="font-medium">{student.parentName || '未设置'}</p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-muted-foreground">家长电话</label>
                        <p className="font-medium">{student.parentPhone || '未设置'}</p>
                      </div>
                      <div className="space-y-2 md:col-span-2">
                        <label className="text-sm font-medium text-muted-foreground">家长邮箱</label>
                        <p className="font-medium">{student.parentEmail || '未设置'}</p>
                      </div>
                      <div className="space-y-2 md:col-span-2">
                        <label className="text-sm font-medium text-muted-foreground">家庭背景</label>
                        <p className="font-medium">{student.familyBackground || '未设置'}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="notes" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="w-5 h-5" />
                      备注信息
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">标签</label>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {student.tags.length > 0 ? (
                            student.tags.map((tag, index) => (
                              <Badge key={index} variant="secondary">
                                {tag}
                              </Badge>
                            ))
                          ) : (
                            <p className="text-muted-foreground">暂无标签</p>
                          )}
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">备注</label>
                        <div className="mt-2 p-4 bg-muted/50 rounded-lg">
                          <p className="whitespace-pre-wrap">
                            {student.notes || '暂无备注'}
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </motion.div>
        </div>

        {/* 侧边栏 */}
        <div className="space-y-6">
          {/* 快速操作 */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <Card>
              <CardHeader>
                <CardTitle>快速操作</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  <MessageCircle className="w-4 h-4 mr-2" />
                  发送消息
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Calendar className="w-4 h-4 mr-2" />
                  安排预约
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <FileText className="w-4 h-4 mr-2" />
                  生成报告
                </Button>
              </CardContent>
            </Card>
          </motion.div>

          {/* 时间信息 */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            <Card>
              <CardHeader>
                <CardTitle>时间信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="w-4 h-4 text-muted-foreground" />
                  <span className="text-muted-foreground">创建时间:</span>
                  <span>{new Date(student.createdAt).toLocaleString()}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="w-4 h-4 text-muted-foreground" />
                  <span className="text-muted-foreground">更新时间:</span>
                  <span>{new Date(student.updatedAt).toLocaleString()}</span>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
