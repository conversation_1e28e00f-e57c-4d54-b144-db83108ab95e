"use client";

import { use } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent } from "@workspace/ui/components/card";
import { Button } from "@workspace/ui/components/button";
import { ArrowLeft, Edit } from "lucide-react";
import { StudentForm } from "@/components/crm/StudentForm";
import { useStudentDetail } from "@/hooks/useCRMStudents";
import { toast } from "sonner";

interface EditStudentPageProps {
  params: Promise<{ id: string }>;
}

export default function EditStudentPage({ params }: EditStudentPageProps) {
  const { id } = use(params);
  const router = useRouter();
  const { student, isLoading, error, updateStudent, isUpdating } = useStudentDetail(id);

  const handleSubmit = async (data: any) => {
    try {
      await updateStudent(data);
      toast.success("学生信息更新成功！");
      router.push(`/workspace/students/${id}`);
    } catch (error) {
      toast.error("更新学生信息失败，请重试");
      console.error("更新学生失败:", error);
    }
  };

  const handleCancel = () => {
    router.push(`/workspace/students/${id}`);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-48 mb-6" />
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="h-4 bg-muted rounded w-24" />
                    <div className="h-10 bg-muted rounded" />
                    <div className="h-4 bg-muted rounded w-32" />
                    <div className="h-10 bg-muted rounded" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error || !student) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-12 text-center">
            <div className="text-muted-foreground mb-4">
              {error ? '加载学生信息失败' : '学生不存在'}
            </div>
            <Button onClick={() => router.back()}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 头部 */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" onClick={() => router.push(`/workspace/students/${id}`)}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          返回
        </Button>
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Edit className="w-6 h-6" />
            编辑学生信息
          </h1>
          <p className="text-muted-foreground">编辑 {student.name} 的信息</p>
        </div>
      </div>

      {/* 表单 */}
      <StudentForm
        student={student}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isLoading={isUpdating}
      />
    </div>
  );
}
