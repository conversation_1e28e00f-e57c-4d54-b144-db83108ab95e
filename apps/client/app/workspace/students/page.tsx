"use client";

import { ModernStudentList } from "@/components/crm/ModernStudentList";
import { useCRMStudents } from "@/hooks/useCRMStudents";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

export default function StudentsPage() {
  const router = useRouter();
  const { students, isLoading } = useCRMStudents();

  const handleCreateStudent = () => {
    router.push("/workspace/students/new");
  };

  const handleViewStudent = (student: any) => {
    router.push(`/workspace/students/${student.id}`);
  };

  const handleEditStudent = (student: any) => {
    router.push(`/workspace/students/${student.id}/edit`);
  };

  const handleDeleteStudent = async (student: any) => {
    if (confirm(`确定要删除学生 ${student.name} 吗？`)) {
      try {
        const response = await fetch(`/api/crm/students/${student.id}`, {
          method: 'DELETE',
        });

        if (response.ok) {
          toast.success("学生删除成功");
          // 这里应该触发数据刷新，但现在先用简单的页面刷新
          window.location.reload();
        } else {
          throw new Error('删除失败');
        }
      } catch (error) {
        toast.error("删除学生失败");
        console.error("删除学生失败:", error);
      }
    }
  };

  const handleMessageStudent = (student: any) => {
    toast.info(`发送消息给 ${student.name} 的功能正在开发中`);
  };

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">学生管理</h1>
        <p className="text-muted-foreground">管理所有学生信息</p>
      </div>

      <ModernStudentList
        students={students}
        isLoading={isLoading}
        onCreateStudent={handleCreateStudent}
        onViewStudent={handleViewStudent}
        onEditStudent={handleEditStudent}
        onDeleteStudent={handleDeleteStudent}
        onMessageStudent={handleMessageStudent}
      />
    </div>
  );
}
