import { useStudentList } from './useStudentList';

export default function StudentListPage() {
  const {
    students,
    search,
    setSearch,
    tagFilter,
    setTagFilter,
    statusFilter,
    setStatusFilter,
    allTags,
    allStatuses,
  } = useStudentList();

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <div className="flex gap-2 mb-6 items-center">
        <input
          className="border px-3 py-2 rounded w-64"
          value={search}
          onChange={e => setSearch(e.target.value)}
          placeholder="搜索学生或家长"
        />
        <select
          className="border rounded px-2 py-2"
          value={tagFilter ?? ''}
          onChange={e => setTagFilter(e.target.value || null)}
        >
          <option value="">全部标签</option>
          {allTags.map(tag => (
            <option key={tag} value={tag}>{tag}</option>
          ))}
        </select>
        <select
          className="border rounded px-2 py-2"
          value={statusFilter ?? ''}
          onChange={e => setStatusFilter(e.target.value || null)}
        >
          <option value="">全部状态</option>
          {allStatuses.map(status => (
            <option key={status} value={status}>{status}</option>
          ))}
        </select>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {students.map(student => (
          <div key={student.id} className="bg-white shadow rounded-lg p-6 flex flex-col gap-2">
            <div className="font-semibold text-lg">{student.name}</div>
            <div className="text-gray-600">年级：{student.grade}</div>
            <div className="text-gray-600">状态：{student.status}</div>
            <div className="flex flex-wrap gap-1">
              {student.tags.map(tag => (
                <span key={tag} className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded text-xs">{tag}</span>
              ))}
            </div>
            <div className="text-gray-500 text-sm">家长：{student.parentName} {student.parentContact}</div>
          </div>
        ))}
        {students.length === 0 && <div className="text-center text-gray-400 col-span-3">暂无学生数据</div>}
      </div>
    </div>
  );
}
