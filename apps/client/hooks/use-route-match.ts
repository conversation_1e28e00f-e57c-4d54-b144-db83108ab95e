"use client";

import { usePathname } from "next/navigation";
import { useMemo } from "react";

/**
 * 路由匹配选项
 */
export interface RouteMatchOptions {
  /** 是否进行精确匹配，默认为 false */
  exact?: boolean;
  /** 是否忽略查询参数，默认为 true */
  ignoreQuery?: boolean;
  /** 是否忽略哈希，默认为 true */
  ignoreHash?: boolean;
  /** 是否区分大小写，默认为 false */
  caseSensitive?: boolean;
}

/**
 * 检查路径是否匹配
 */
function isPathMatch(
  currentPath: string,
  targetPath: string,
  options: RouteMatchOptions = {},
): boolean {
  const {
    exact = false,
    ignoreQuery = true,
    ignoreHash = true,
    caseSensitive = false,
  } = options;

  // 处理路径
  let current = currentPath;
  let target = targetPath;

  // 移除查询参数
  if (ignoreQuery) {
    current = current.split("?")[0]!;
    target = target.split("?")[0]!;
  }

  // 移除哈希
  if (ignoreHash) {
    current = current.split("#")[0]!;
    target = target.split("#")[0]!;
  }

  // 处理大小写
  if (!caseSensitive) {
    current = current.toLowerCase();
    target = target.toLowerCase();
  }

  // 确保路径以 / 开头
  if (!current.startsWith("/")) current = "/" + current;
  if (!target.startsWith("/")) target = "/" + target;

  // 移除末尾的 /（除非是根路径）
  if (current.length > 1 && current.endsWith("/")) {
    current = current.slice(0, -1);
  }
  if (target.length > 1 && target.endsWith("/")) {
    target = target.slice(0, -1);
  }

  if (exact) {
    return current === target;
  }

  // 非精确匹配：当前路径应该以目标路径开始
  return current === target || current.startsWith(target + "/");
}

/**
 * 路由高亮hook
 */
export function useRouteMatch(
  targetPath: string,
  options?: RouteMatchOptions,
): boolean {
  const pathname = usePathname();

  return useMemo(() => {
    return isPathMatch(pathname, targetPath, options);
  }, [pathname, targetPath, options]);
}

/**
 * 批量路由匹配hook - 用于检查多个路径
 */
export function useRouteMatches(
  paths: string[],
  options?: RouteMatchOptions,
): Record<string, boolean> {
  const pathname = usePathname();

  return useMemo(() => {
    const matches: Record<string, boolean> = {};

    paths.forEach((path) => {
      matches[path] = isPathMatch(pathname, path, options);
    });

    return matches;
  }, [pathname, paths, options]);
}

/**
 * 活跃路由hook - 返回第一个匹配的路由
 */
export function useActiveRoute(
  paths: string[],
  options?: RouteMatchOptions,
): string | null {
  const pathname = usePathname();

  return useMemo(() => {
    for (const path of paths) {
      if (isPathMatch(pathname, path, options)) {
        return path;
      }
    }
    return null;
  }, [pathname, paths, options]);
}

/**
 * 子路由匹配hook - 用于带有子菜单的导航项
 */
export function useSubRouteMatch(
  parentPath: string,
  subPaths: string[],
  options?: RouteMatchOptions,
): {
  isParentActive: boolean;
  activeSubPath: string | null;
  subMatches: Record<string, boolean>;
} {
  const pathname = usePathname();

  return useMemo(() => {
    // 检查父路径是否匹配
    const isParentActive = isPathMatch(pathname, parentPath, options);

    // 检查子路径匹配情况
    const subMatches: Record<string, boolean> = {};
    let activeSubPath: string | null = null;

    subPaths.forEach((subPath) => {
      const isMatch = isPathMatch(pathname, subPath, options);
      subMatches[subPath] = isMatch;

      if (isMatch && !activeSubPath) {
        activeSubPath = subPath;
      }
    });

    return {
      isParentActive,
      activeSubPath,
      subMatches,
    };
  }, [pathname, parentPath, subPaths, options]);
}

/**
 * 导航项类型定义
 */
export interface NavItem {
  title: string;
  url: string;
  isActive?: boolean;
  items?: NavItem[];
}

/**
 * 为导航项添加活跃状态的hook
 */
export function useNavItemsWithActiveState<T extends NavItem>(
  items: T[],
  options?: RouteMatchOptions,
): T[] {
  const pathname = usePathname();

  return useMemo(() => {
    // 首先找到所有精确匹配的子项路径
    const allSubPaths = items.flatMap(item => 
      item.items?.map(subItem => subItem.url) || []
    );
    
    // 检查是否有精确匹配的子项
    const exactMatchingSubPath = allSubPaths.find(path => 
      isPathMatch(pathname, path, { ...options, exact: true })
    );

    return items.map((item) => {
      // 如果有子项，检查子项的活跃状态
      let hasActiveChild = false;
      let updatedItems: NavItem[] | undefined;

      if (item.items && item.items.length > 0) {
        updatedItems = item.items.map((subItem) => {
          // 如果有精确匹配的子项，则只有精确匹配的子项才高亮
          const isSubActive = exactMatchingSubPath 
            ? subItem.url === exactMatchingSubPath
            : isPathMatch(pathname, subItem.url, options);
          
          if (isSubActive) {
            hasActiveChild = true;
          }
          return {
            ...subItem,
            isActive: isSubActive,
          };
        });
      }

      // 父项高亮逻辑
      let isParentActive = false;
      if (hasActiveChild) {
        // 如果有子项匹配，父项就高亮
        isParentActive = true;
      } else {
        // 如果没有子项匹配，检查父项本身是否匹配
        isParentActive = isPathMatch(pathname, item.url, options);
      }

      return {
        ...item,
        isActive: isParentActive,
        items: updatedItems,
      } as T;
    });
  }, [items, pathname, options]);
}
