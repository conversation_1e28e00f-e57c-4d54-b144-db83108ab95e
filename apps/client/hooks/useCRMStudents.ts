import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback, useMemo, useState } from 'react';

// 类型定义
export interface Student {
  id: string;
  name: string;
  avatar?: string;
  grade: string;
  school: string;
  tags: string[];
  status: "ACTIVE" | "GRADUATED" | "INACTIVE";
  parentName?: string;
  parentPhone?: string;
  parentEmail?: string;
  
  // 服务相关
  servicePhase: string;
  progress: number;
  nextAppointment?: string;
  satisfaction: number;
  urgency: "low" | "medium" | "high";
  
  // 统计数据
  totalSessions: number;
  completedMilestones: number;
  totalMilestones: number;
  joinDate: string;
  
  // 学业信息
  gpa?: number;
  rank?: number;
  major?: string;
  familyBackground?: string;
  notes?: string;
  
  // 时间戳
  createdAt: string;
  updatedAt: string;
}

export interface StudentFilters {
  search?: string;
  status?: string[];
  tags?: string[];
  grades?: string[];
  urgency?: string[];
  satisfactionRange?: [number, number];
  progressRange?: [number, number];
  servicePhase?: string[];
}

export interface StudentSortConfig {
  field: keyof Student;
  direction: 'asc' | 'desc';
}

// API函数
const api = {
  getStudents: async (filters?: StudentFilters): Promise<Student[]> => {
    const params = new URLSearchParams();
    if (filters?.search) params.append('search', filters.search);
    if (filters?.status?.length) params.append('status', filters.status.join(','));
    if (filters?.tags?.length) params.append('tags', filters.tags.join(','));
    
    const response = await fetch(`/api/crm/students?${params}`);
    if (!response.ok) throw new Error('Failed to fetch students');
    return response.json();
  },

  getStudent: async (id: string): Promise<Student> => {
    const response = await fetch(`/api/crm/students/${id}`);
    if (!response.ok) throw new Error('Failed to fetch student');
    return response.json();
  },

  createStudent: async (data: Omit<Student, 'id' | 'createdAt' | 'updatedAt'>): Promise<Student> => {
    const response = await fetch('/api/crm/students', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    if (!response.ok) throw new Error('Failed to create student');
    return response.json();
  },

  updateStudent: async (id: string, data: Partial<Student>): Promise<Student> => {
    const response = await fetch(`/api/crm/students/${id}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    if (!response.ok) throw new Error('Failed to update student');
    return response.json();
  },

  deleteStudent: async (id: string): Promise<void> => {
    const response = await fetch(`/api/crm/students/${id}`, {
      method: 'DELETE'
    });
    if (!response.ok) throw new Error('Failed to delete student');
  },

  getStudentStats: async (): Promise<{
    total: number;
    active: number;
    graduated: number;
    inactive: number;
    averageSatisfaction: number;
    averageProgress: number;
  }> => {
    const response = await fetch('/api/crm/students/stats');
    if (!response.ok) throw new Error('Failed to fetch stats');
    return response.json();
  }
};

// 主要Hook
export function useCRMStudents(initialFilters?: StudentFilters) {
  const queryClient = useQueryClient();
  
  // 状态管理
  const [filters, setFilters] = useState<StudentFilters>(initialFilters || {});
  const [sortConfig, setSortConfig] = useState<StudentSortConfig>({
    field: 'updatedAt',
    direction: 'desc'
  });
  const [selectedStudents, setSelectedStudents] = useState<string[]>([]);

  // 查询学生列表
  const {
    data: students = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['crm-students', filters],
    queryFn: () => api.getStudents(filters),
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });

  // 查询统计数据
  const { data: stats } = useQuery({
    queryKey: ['crm-students-stats'],
    queryFn: api.getStudentStats,
    staleTime: 10 * 60 * 1000, // 10分钟缓存
  });

  // 创建学生
  const createMutation = useMutation({
    mutationFn: api.createStudent,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['crm-students'] });
      queryClient.invalidateQueries({ queryKey: ['crm-students-stats'] });
    }
  });

  // 更新学生
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Student> }) => 
      api.updateStudent(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['crm-students'] });
      queryClient.invalidateQueries({ queryKey: ['crm-students-stats'] });
    }
  });

  // 删除学生
  const deleteMutation = useMutation({
    mutationFn: api.deleteStudent,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['crm-students'] });
      queryClient.invalidateQueries({ queryKey: ['crm-students-stats'] });
    }
  });

  // 处理后的学生数据（排序和筛选）
  const processedStudents = useMemo(() => {
    let result = [...students];

    // 本地搜索增强
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      result = result.filter(student =>
        student.name.toLowerCase().includes(searchTerm) ||
        student.school.toLowerCase().includes(searchTerm) ||
        student.parentName?.toLowerCase().includes(searchTerm) ||
        student.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    // 满意度范围筛选
    if (filters.satisfactionRange) {
      const [min, max] = filters.satisfactionRange;
      result = result.filter(student => 
        student.satisfaction >= min && student.satisfaction <= max
      );
    }

    // 进度范围筛选
    if (filters.progressRange) {
      const [min, max] = filters.progressRange;
      result = result.filter(student => 
        student.progress >= min && student.progress <= max
      );
    }

    // 排序
    result.sort((a, b) => {
      const aVal = a[sortConfig.field];
      const bVal = b[sortConfig.field];
      
      if (typeof aVal === 'string' && typeof bVal === 'string') {
        return sortConfig.direction === 'asc' 
          ? aVal.localeCompare(bVal)
          : bVal.localeCompare(aVal);
      }
      
      if (typeof aVal === 'number' && typeof bVal === 'number') {
        return sortConfig.direction === 'asc' ? aVal - bVal : bVal - aVal;
      }
      
      return 0;
    });

    return result;
  }, [students, filters, sortConfig]);

  // 分组数据
  const groupedStudents = useMemo(() => {
    const groups: Record<string, Student[]> = {};
    
    processedStudents.forEach(student => {
      const key = student.servicePhase || '未分配';
      if (!groups[key]) groups[key] = [];
      groups[key].push(student);
    });
    
    return groups;
  }, [processedStudents]);

  // 操作函数
  const updateFilters = useCallback((newFilters: Partial<StudentFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({});
  }, []);

  const updateSort = useCallback((field: keyof Student, direction?: 'asc' | 'desc') => {
    setSortConfig(prev => ({
      field,
      direction: direction || (prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc')
    }));
  }, []);

  const selectStudent = useCallback((id: string) => {
    setSelectedStudents(prev => 
      prev.includes(id) ? prev.filter(x => x !== id) : [...prev, id]
    );
  }, []);

  const selectAllStudents = useCallback(() => {
    setSelectedStudents(processedStudents.map(s => s.id));
  }, [processedStudents]);

  const clearSelection = useCallback(() => {
    setSelectedStudents([]);
  }, []);

  const createStudent = useCallback((data: Omit<Student, 'id' | 'createdAt' | 'updatedAt'>) => {
    return createMutation.mutateAsync(data);
  }, [createMutation]);

  const updateStudent = useCallback((id: string, data: Partial<Student>) => {
    return updateMutation.mutateAsync({ id, data });
  }, [updateMutation]);

  const deleteStudent = useCallback((id: string) => {
    return deleteMutation.mutateAsync(id);
  }, [deleteMutation]);

  const batchUpdateStudents = useCallback(async (ids: string[], data: Partial<Student>) => {
    const promises = ids.map(id => updateStudent(id, data));
    return Promise.all(promises);
  }, [updateStudent]);

  // 智能建议
  const suggestions = useMemo(() => {
    const urgentStudents = processedStudents.filter(s => s.urgency === 'high').length;
    const lowSatisfaction = processedStudents.filter(s => s.satisfaction < 3).length;
    const behindSchedule = processedStudents.filter(s => s.progress < 50).length;

    return {
      urgent: urgentStudents,
      lowSatisfaction,
      behindSchedule,
      needsAttention: urgentStudents + lowSatisfaction + behindSchedule
    };
  }, [processedStudents]);

  return {
    // 数据
    students: processedStudents,
    groupedStudents,
    stats,
    suggestions,
    
    // 状态
    isLoading,
    error,
    filters,
    sortConfig,
    selectedStudents,
    
    // 操作
    updateFilters,
    clearFilters,
    updateSort,
    selectStudent,
    selectAllStudents,
    clearSelection,
    refetch,
    
    // CRUD操作
    createStudent,
    updateStudent,
    deleteStudent,
    batchUpdateStudents,
    
    // 加载状态
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending,
  };
}

// 单个学生详情Hook
export function useStudentDetail(id: string) {
  const queryClient = useQueryClient();

  const {
    data: student,
    isLoading,
    error
  } = useQuery({
    queryKey: ['crm-student', id],
    queryFn: () => api.getStudent(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2分钟缓存
  });

  const updateMutation = useMutation({
    mutationFn: (data: Partial<Student>) => api.updateStudent(id, data),
    onSuccess: (updatedStudent) => {
      queryClient.setQueryData(['crm-student', id], updatedStudent);
      queryClient.invalidateQueries({ queryKey: ['crm-students'] });
    }
  });

  const updateStudent = useCallback((data: Partial<Student>) => {
    return updateMutation.mutateAsync(data);
  }, [updateMutation]);

  return {
    student,
    isLoading,
    error,
    updateStudent,
    isUpdating: updateMutation.isPending,
  };
}

// 批量操作Hook
export function useStudentBatchOperations() {
  const queryClient = useQueryClient();

  const batchUpdate = useMutation({
    mutationFn: async ({ ids, data }: { ids: string[]; data: Partial<Student> }) => {
      const promises = ids.map(id => api.updateStudent(id, data));
      return Promise.all(promises);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['crm-students'] });
    }
  });

  const batchDelete = useMutation({
    mutationFn: async (ids: string[]) => {
      const promises = ids.map(id => api.deleteStudent(id));
      return Promise.all(promises);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['crm-students'] });
    }
  });

  return {
    batchUpdate: batchUpdate.mutateAsync,
    batchDelete: batchDelete.mutateAsync,
    isBatchUpdating: batchUpdate.isPending,
    isBatchDeleting: batchDelete.isPending,
  };
} 