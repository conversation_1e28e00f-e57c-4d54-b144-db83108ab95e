"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useCallback, useEffect, useMemo, useState } from "react";

// 通知类型定义
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'reminder';
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  isStarred: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'student' | 'task' | 'system' | 'marketing' | 'reminder';
  actionUrl?: string;
  studentId?: string;
  metadata?: Record<string, any>;
  expiresAt?: Date;
}

// 通知偏好设置
export interface NotificationPreferences {
  enabled: boolean;
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  quietHours: {
    enabled: boolean;
    start: string; // HH:MM
    end: string; // HH:MM
  };
  categories: {
    student: boolean;
    task: boolean;
    system: boolean;
    marketing: boolean;
    reminder: boolean;
  };
  frequency: 'realtime' | 'hourly' | 'daily' | 'weekly';
  location: {
    enabled: boolean;
    radius: number; // 米
  };
}

// 智能推送规则
interface SmartPushRule {
  id: string;
  name: string;
  condition: (context: NotificationContext) => boolean;
  template: (context: NotificationContext) => Partial<Notification>;
  priority: number;
}

// 通知上下文
interface NotificationContext {
  currentTime: Date;
  userLocation?: GeolocationPosition;
  recentActivity: string[];
  studentData: any[];
  pendingTasks: any[];
  isWorkingHours: boolean;
  lastLoginTime: Date;
}

// Mock数据
const MOCK_NOTIFICATIONS: Notification[] = [
  {
    id: "1",
    type: "reminder",
    title: "学生跟进提醒",
    message: "王小明已经3天未联系，建议主动跟进",
    timestamp: new Date(Date.now() - 1000 * 60 * 30),
    isRead: false,
    isStarred: false,
    priority: "high",
    category: "student",
    studentId: "student1",
    actionUrl: "/workspace/crm?studentId=student1"
  },
  {
    id: "2",
    type: "success",
    title: "新学生签约",
    message: "恭喜！李晓红已成功签约高考规划服务",
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
    isRead: true,
    isStarred: true,
    priority: "medium",
    category: "student",
    studentId: "student2"
  },
  {
    id: "3",
    type: "warning",
    title: "任务即将逾期",
    message: "陈子豪的学习计划制定任务将在明天逾期",
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4),
    isRead: false,
    isStarred: false,
    priority: "high",
    category: "task",
    actionUrl: "/workspace/tasks"
  },
  {
    id: "4",
    type: "info",
    title: "系统更新",
    message: "CRM系统已更新到v2.1.0，新增智能推荐功能",
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24),
    isRead: true,
    isStarred: false,
    priority: "low",
    category: "system"
  }
];

const DEFAULT_PREFERENCES: NotificationPreferences = {
  enabled: true,
  emailNotifications: true,
  pushNotifications: true,
  smsNotifications: false,
  quietHours: {
    enabled: true,
    start: "22:00",
    end: "08:00"
  },
  categories: {
    student: true,
    task: true,
    system: true,
    marketing: false,
    reminder: true
  },
  frequency: "realtime",
  location: {
    enabled: false,
    radius: 1000
  }
};

// 智能推送规则
const SMART_PUSH_RULES: SmartPushRule[] = [
  {
    id: "student_followup",
    name: "学生跟进提醒",
    priority: 10,
    condition: (context) => {
      // 如果有学生超过3天未联系
      return context.studentData.some(student => 
        student.lastContactDate && 
        (context.currentTime.getTime() - new Date(student.lastContactDate).getTime()) > 3 * 24 * 60 * 60 * 1000
      );
    },
    template: (context) => ({
      type: "reminder",
      title: "学生跟进提醒",
      priority: "high",
      category: "student"
    })
  },
  {
    id: "task_deadline",
    name: "任务截止提醒",
    priority: 9,
    condition: (context) => {
      // 如果有任务即将在24小时内到期
      return context.pendingTasks.some(task => 
        task.deadline && 
        (new Date(task.deadline).getTime() - context.currentTime.getTime()) < 24 * 60 * 60 * 1000
      );
    },
    template: (context) => ({
      type: "warning",
      title: "任务即将逾期",
      priority: "high",
      category: "task"
    })
  },
  {
    id: "morning_summary",
    name: "晨间工作总结",
    priority: 5,
    condition: (context) => {
      const hour = context.currentTime.getHours();
      return hour === 9 && context.isWorkingHours;
    },
    template: (context) => ({
      type: "info",
      title: "今日工作概览",
      priority: "medium",
      category: "system"
    })
  }
];

export function useNotifications() {
  const queryClient = useQueryClient();
  const [preferences, setPreferences] = useState<NotificationPreferences>(DEFAULT_PREFERENCES);
  const [activeToasts, setActiveToasts] = useState<Notification[]>([]);

  // 获取通知列表
  const { data: notifications = [], isLoading } = useQuery({
    queryKey: ['notifications'],
    queryFn: async () => {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      return MOCK_NOTIFICATIONS;
    },
    refetchInterval: 30000 // 30秒刷新一次
  });

  // 标记通知已读
  const markAsReadMutation = useMutation({
    mutationFn: async (notificationIds: string[]) => {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 200));
      return notificationIds;
    },
    onSuccess: (notificationIds) => {
      queryClient.setQueryData(['notifications'], (old: Notification[] = []) =>
        old.map(notification =>
          notificationIds.includes(notification.id)
            ? { ...notification, isRead: true }
            : notification
        )
      );
    }
  });

  // 星标通知
  const toggleStarMutation = useMutation({
    mutationFn: async (notificationId: string) => {
      await new Promise(resolve => setTimeout(resolve, 200));
      return notificationId;
    },
    onSuccess: (notificationId) => {
      queryClient.setQueryData(['notifications'], (old: Notification[] = []) =>
        old.map(notification =>
          notification.id === notificationId
            ? { ...notification, isStarred: !notification.isStarred }
            : notification
        )
      );
    }
  });

  // 删除通知
  const deleteNotificationMutation = useMutation({
    mutationFn: async (notificationId: string) => {
      await new Promise(resolve => setTimeout(resolve, 200));
      return notificationId;
    },
    onSuccess: (notificationId) => {
      queryClient.setQueryData(['notifications'], (old: Notification[] = []) =>
        old.filter(notification => notification.id !== notificationId)
      );
    }
  });

  // 计算统计信息
  const stats = useMemo(() => {
    const unreadCount = notifications.filter(n => !n.isRead).length;
    const starredCount = notifications.filter(n => n.isStarred).length;
    const urgentCount = notifications.filter(n => n.priority === 'urgent' && !n.isRead).length;
    
    const categoryCounts = notifications.reduce((acc, notification) => {
      acc[notification.category] = (acc[notification.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total: notifications.length,
      unread: unreadCount,
      starred: starredCount,
      urgent: urgentCount,
      byCategory: categoryCounts
    };
  }, [notifications]);

  // 智能推送检查
  const checkSmartPush = useCallback(async () => {
    if (!preferences.enabled) return;

    const context: NotificationContext = {
      currentTime: new Date(),
      userLocation: undefined, // 需要获取地理位置
      recentActivity: [], // 需要从用户行为中获取
      studentData: [], // 需要从CRM数据中获取
      pendingTasks: [], // 需要从任务系统中获取
      isWorkingHours: isWorkingTime(),
      lastLoginTime: new Date(Date.now() - 1000 * 60 * 60 * 2) // 模拟2小时前登录
    };

    // 检查是否在静默时间
    if (preferences.quietHours.enabled && isQuietTime()) {
      return;
    }

    // 执行智能推送规则
    for (const rule of SMART_PUSH_RULES) {
      if (rule.condition(context)) {
        const notification = createSmartNotification(rule, context);
        if (shouldShowNotification(notification)) {
          showToast(notification);
        }
      }
    }
  }, [preferences]);

  // 工具函数
  const isWorkingTime = () => {
    const hour = new Date().getHours();
    return hour >= 9 && hour <= 18;
  };

  const isQuietTime = () => {
    if (!preferences.quietHours.enabled) return false;
    
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    const [startHour, startMin] = preferences.quietHours.start.split(':').map(Number);
    const [endHour, endMin] = preferences.quietHours.end.split(':').map(Number);
    const startTime = startHour * 60 + startMin;
    const endTime = endHour * 60 + endMin;
    
    if (startTime < endTime) {
      return currentTime >= startTime && currentTime <= endTime;
    } else {
      return currentTime >= startTime || currentTime <= endTime;
    }
  };

  const createSmartNotification = (rule: SmartPushRule, context: NotificationContext): Notification => {
    const template = rule.template(context);
    return {
      id: `smart_${rule.id}_${Date.now()}`,
      type: template.type || 'info',
      title: template.title || rule.name,
      message: template.message || `智能推送：${rule.name}`,
      timestamp: new Date(),
      isRead: false,
      isStarred: false,
      priority: template.priority || 'medium',
      category: template.category || 'system',
      ...template
    };
  };

  const shouldShowNotification = (notification: Notification): boolean => {
    return preferences.categories[notification.category];
  };

  const showToast = useCallback((notification: Notification) => {
    setActiveToasts(prev => [...prev, notification]);
    
    // 3秒后自动移除
    setTimeout(() => {
      setActiveToasts(prev => prev.filter(n => n.id !== notification.id));
    }, 3000);
  }, []);

  const dismissToast = useCallback((notificationId: string) => {
    setActiveToasts(prev => prev.filter(n => n.id !== notificationId));
  }, []);

  // 请求通知权限
  const requestNotificationPermission = useCallback(async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return false;
  }, []);

  // 发送浏览器通知
  const sendBrowserNotification = useCallback((notification: Notification) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/icon-192x192.png',
        badge: '/icon-192x192.png',
        tag: notification.id,
        requireInteraction: notification.priority === 'urgent',
        actions: notification.actionUrl ? [
          { action: 'view', title: '查看详情' }
        ] : undefined
      });
    }
  }, []);

  // 启动智能推送定时器
  useEffect(() => {
    if (!preferences.enabled) return;

    const interval = setInterval(checkSmartPush, 60000); // 每分钟检查一次
    return () => clearInterval(interval);
  }, [checkSmartPush, preferences.enabled]);

  // API方法
  const markAsRead = useCallback((notificationIds: string[]) => {
    markAsReadMutation.mutate(notificationIds);
  }, [markAsReadMutation]);

  const markAllAsRead = useCallback(() => {
    const unreadIds = notifications.filter(n => !n.isRead).map(n => n.id);
    if (unreadIds.length > 0) {
      markAsReadMutation.mutate(unreadIds);
    }
  }, [notifications, markAsReadMutation]);

  const toggleStar = useCallback((notificationId: string) => {
    toggleStarMutation.mutate(notificationId);
  }, [toggleStarMutation]);

  const deleteNotification = useCallback((notificationId: string) => {
    deleteNotificationMutation.mutate(notificationId);
  }, [deleteNotificationMutation]);

  const updatePreferences = useCallback((newPreferences: Partial<NotificationPreferences>) => {
    setPreferences(prev => ({ ...prev, ...newPreferences }));
  }, []);

  return {
    // 数据
    notifications,
    preferences,
    activeToasts,
    stats,
    isLoading,
    
    // 操作方法
    markAsRead,
    markAllAsRead,
    toggleStar,
    deleteNotification,
    updatePreferences,
    showToast,
    dismissToast,
    requestNotificationPermission,
    sendBrowserNotification,
    
    // 状态
    isQuietTime: isQuietTime(),
    isWorkingTime: isWorkingTime(),
    
    // 突变状态
    isMarkingAsRead: markAsReadMutation.isPending,
    isTogglingStarred: toggleStarMutation.isPending,
    isDeleting: deleteNotificationMutation.isPending
  };
} 