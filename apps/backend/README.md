# EduPlanner CRM

EduPlanner CRM 是一款面向 **6-22 岁学生学业规划师 / 规划机构** 的一体化云端 CRM SaaS。  
它帮助规划师：

- 统一管理学生档案与学情数据
- 标准化咨询流程与自动提醒，降低漏跟进率
- 一键生成家长报告、课程/营地推荐书，提升专业度
- 通过可视化 KPI 面板量化服务效果
- 结合付费订阅与分销抽佣，实现收入增长

> MVP 已聚焦「能卖、能用、能看效果」的核心闭环，目标 3-4 个月上线收付费用户。

---

## 核心特性 (MVP)

| 模块       | 主要功能                                                  |
| ---------- | --------------------------------------------------------- |
| 学生档案   | 基础信息、家长信息、自定义兴趣/目标标签、成绩/测评附件    |
| 咨询流程   | 流程模板、自定义节点、到期提醒 (Email / 企业微信 Webhook) |
| 进展日志   | 内部记录、家长可视化周报 (PDF / H5)                       |
| 产品推荐库 | 课程 / 夏校 / 竞赛条目管理，按标签 & 预算智能筛选         |
| 付费订阅   | 14 天试用 → 个人版 / 团队版年费，内置微信/支付宝收银台    |

---

## 技术栈

| 层级        | 技术                                 | 说明                                    |
| ----------- | ------------------------------------ | --------------------------------------- |
| 前端        | **Next.js 14 (app router, SSR+CSR)** | React UI、鉴权、国际化                  |
| 后端        | **Hono.js**                          | 轻量 TypeScript Web 框架，Edge-friendly |
| ORM         | **Prisma**                           | 与 MySQL/PlanetScale 适配               |
| 数据库      | **MySQL 8**                          | 主数据存储                              |
| 缓存 / 会话 | **Redis**                            | 热数据与任务提醒队列                    |
| 对象存储    | OSS / S3 兼容                        | 附件 / 报告 PDF                         |
| 部署        | Vercel + Fly.io / 自托管 K8s         | 前后端分离或同域部署均可                |

---

## 快速开始

```bash
git clone https://github.com/your-org/eduplanner-crm
cd eduplanner-crm

# 安装依赖
pnpm i

# 配置环境变量
cp .env.example .env.local
# 修改数据库 / Redis / OSS 等连接串

# 数据库迁移 & 种子数据
npx prisma migrate dev
npx prisma db seed

# 启动开发
pnpm dev
```

访问 http://localhost:3000 即可进入管理台 (默认账号见 seed)。

---

## 目录结构

```
apps/
  web/             # Next.js (前端 + SSR API routes)
packages/
  api/             # Hono.js API (可独立部署到 edge)
  db/              # Prisma schema & migrations
  ui/              # 可复用的设计系统组件
docs/              # 需求/技术/运维文档
```

---

## 部署说明

1. **数据库**: 初始化 MySQL，并执行 `prisma migrate deploy`
2. **Redis**: 启动 Redis，配置 `REDIS_URL`
3. **API**: 将 `packages/api` 部署至 Vercel Edge / Cloudflare Workers / Fly.io
4. **WEB**: 将 Next.js `apps/web` 部署至 Vercel 或自托管容器
5. **环境变量**: 请参见 `.env.example`

---

## 路线图 (Roadmap)

- ✅ MVP 核心功能 (v0.1)
- 🔜 家长端小程序 (v0.2)
- 🔜 AI 面谈纪要 & 推荐关键词生成 (v0.3)
- 🔜 市场分销 & 佣金结算 (v0.4)

---

## 贡献指南

我们欢迎 PR 与 Issue！详见 `CONTRIBUTING.md`。

---

To install dependencies:

```sh
bun install
```

To run:

```sh
bun run dev
```

open http://localhost:3000
