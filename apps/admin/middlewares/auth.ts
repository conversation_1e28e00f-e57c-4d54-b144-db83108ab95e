// 使用 jose 库的替代方案
import { AUTH_COOKIE_NAME } from "@/lib/auth/types";
import { NextResponse } from "next/server";
import type { MiddlewareFunction } from "./types";
import { jwtVerify } from "jose";

// JWT配置
const JWT_SECRET =
  process.env.JWT_SECRET || "your-super-secret-key-change-in-production";

// JWT载荷接口
interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  sessionId: string;
  iat?: number;
  exp?: number;
}

/**
 * 使用 jose 库验证 JWT
 */
async function verifyJWT(token: string): Promise<{
  success: boolean;
  user?: JWTPayload;
  message?: string;
}> {
  try {
    const secret = new TextEncoder().encode(JWT_SECRET);
    const { payload } = await jwtVerify(token, secret);

    return {
      success: true,
      user: payload as unknown as JWTPayload,
    };
  } catch (error) {
    console.error("JWT验证失败:", error);
    return { success: false, message: "无效的Token" };
  }
}

// 认证中间件 - 检查用户是否已登录
export const authMiddleware: MiddlewareFunction = async (context) => {
  const { request } = context;
  const { pathname } = request.nextUrl;

  try {
    // 1. 获取Token
    const token = request.cookies.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      console.log(`[认证失败] 未找到 token: ${pathname}`);
      const redirectUrl = new URL("/login", request.url);
      redirectUrl.searchParams.set("redirect", pathname);
      return NextResponse.redirect(redirectUrl);
    }

    // 2. 验证Token并获取用户信息
    const result = await verifyJWT(token.value);
    if (!result.success || !result.user) {
      console.log(`[认证失败] ${result.message}: ${pathname}`);
      const redirectUrl = new URL("/login", request.url);
      redirectUrl.searchParams.set("redirect", pathname);
      return NextResponse.redirect(redirectUrl);
    }

    const user = result.user;

    // 4. 继续处理请求
    console.log(`[认证成功] 用户: ${user.userId}, 路径: ${pathname}`);
    return NextResponse.next({
      headers: {
        "x-user-id": user.userId,
        "x-user-role": user.role,
      },
    });
  } catch (error) {
    console.error(`[认证错误] ${error}, 路径: ${pathname}`);
    return NextResponse.redirect(new URL("/login", request.url));
  }
};
