// 中间件系统类型定义
import type { NextRequest, NextResponse } from "next/server";

// 中间件上下文类型
export interface MiddlewareContext {
  request: NextRequest;
  response?: NextResponse;
  // 用户相关
  user?: {
    id: string;
    permissions: string[];
    role: string;
  };
  // 权限相关
  permissions?: string[];
  // 性能监控
  startTime?: number;
  // 自定义数据
  [key: string]: any;
}

// 中间件函数类型
export type MiddlewareFunction = (
  context: MiddlewareContext,
) => Promise<NextResponse | void> | NextResponse | void;

// 中间件配置类型
export interface MiddlewareConfig {
  // 匹配的路径模式
  paths?: string[];
  // 排除的路径模式
  excludePaths?: string[];
  // 是否启用
  enabled?: boolean;
  // 优先级
  priority?: number;
}

// 路径权限映射类型
export interface PathPermissionConfig {
  [path: string]: string[];
}

// 中间件执行结果类型
export type MiddlewareResult = {
  success: boolean;
  response?: NextResponse;
  error?: Error;
  duration?: number;
};
