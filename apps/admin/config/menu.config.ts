import { checkPermission } from "@/lib/auth/utils";
import {
  Bar<PERSON>hart3,
  Building2,
  CreditCard,
  FileText,
  FolderOpen,
  GraduationCap,
  LayoutDashboard,
  Megaphone,
  MessageSquare,
  Package,
  Settings,
  Shield,
  Users,
  type LucideIcon,
} from "lucide-react";
import { PERMISSIONS } from "./permissions.config";

export interface MenuItem {
  key: string;
  label: string;
  icon: LucideIcon;
  path: string;
  permissions?: string[];
  badge?: {
    text: string;
    variant?: "default" | "secondary" | "destructive" | "outline";
  };
  children?: MenuItem[];
}

export const menuConfig: MenuItem[] = [
  {
    key: "dashboard",
    label: "仪表盘",
    icon: LayoutDashboard,
    path: "/",
    permissions: [PERMISSIONS.ADMIN.DASHBOARD.VIEW],
  },
  {
    key: "content",
    label: "内容管理",
    icon: FileText,
    path: "#",
    permissions: [
      PERMISSIONS.CONTENT.POST.VIEW,
      PERMISSIONS.CONTENT.COMMENT.VIEW,
      PERMISSIONS.CONTENT.RESOURCE.VIEW,
    ],
    children: [
      {
        key: "posts",
        label: "帖子管理",
        icon: FileText,
        path: "/content/posts",
        permissions: [PERMISSIONS.CONTENT.POST.VIEW],
      },
      {
        key: "comments",
        label: "评论管理",
        icon: MessageSquare,
        path: "/content/comments",
        permissions: [PERMISSIONS.CONTENT.COMMENT.VIEW],
      },
      {
        key: "resources",
        label: "资源管理",
        icon: FolderOpen,
        path: "/content/resources",
        permissions: [PERMISSIONS.CONTENT.RESOURCE.VIEW],
      },
    ],
  },
  {
    key: "course",
    label: "课程管理",
    icon: GraduationCap,
    path: "#",
    permissions: [
      PERMISSIONS.COURSE.MANAGEMENT.VIEW,
      PERMISSIONS.COURSE.CATEGORY.VIEW,
      PERMISSIONS.COURSE.ENROLLMENT.VIEW,
    ],
    children: [
      {
        key: "courses",
        label: "课程列表",
        icon: GraduationCap,
        path: "/courses",
        permissions: [PERMISSIONS.COURSE.MANAGEMENT.VIEW],
      },
      {
        key: "course-categories",
        label: "课程分类",
        icon: FolderOpen,
        path: "/courses/categories",
        permissions: [PERMISSIONS.COURSE.CATEGORY.VIEW],
      },
      {
        key: "enrollments",
        label: "报名管理",
        icon: Users,
        path: "/courses/enrollments",
        permissions: [PERMISSIONS.COURSE.ENROLLMENT.VIEW],
      },
    ],
  },
  {
    key: "product",
    label: "产品管理",
    icon: Package,
    path: "#",
    permissions: [
      PERMISSIONS.PRODUCT.MANAGEMENT.VIEW,
      PERMISSIONS.PRODUCT.INQUIRY.VIEW,
      PERMISSIONS.PRODUCT.PARTNER.VIEW,
    ],
    children: [
      {
        key: "products",
        label: "产品列表",
        icon: Package,
        path: "/products",
        permissions: [PERMISSIONS.PRODUCT.MANAGEMENT.VIEW],
      },
      {
        key: "product-categories",
        label: "产品分类",
        icon: FolderOpen,
        path: "/products/categories",
        permissions: [PERMISSIONS.PRODUCT.CATEGORY.VIEW],
      },
      {
        key: "inquiries",
        label: "询价管理",
        icon: MessageSquare,
        path: "/products/inquiries",
        permissions: [PERMISSIONS.PRODUCT.INQUIRY.VIEW],
        badge: {
          text: "新",
          variant: "destructive",
        },
      },
      {
        key: "partners",
        label: "合作方管理",
        icon: Building2,
        path: "/products/partners",
        permissions: [PERMISSIONS.PRODUCT.PARTNER.VIEW],
      },
    ],
  },
  {
    key: "finance",
    label: "财务管理",
    icon: CreditCard,
    path: "#",
    permissions: [
      PERMISSIONS.FINANCE.SUBSCRIPTION.VIEW,
      PERMISSIONS.FINANCE.PAYMENT.VIEW,
      PERMISSIONS.FINANCE.INVOICE.VIEW,
      PERMISSIONS.FINANCE.COUPON.VIEW,
    ],
    children: [
      {
        key: "subscriptions",
        label: "订阅管理",
        icon: CreditCard,
        path: "/finance/subscriptions",
        permissions: [PERMISSIONS.FINANCE.SUBSCRIPTION.VIEW],
      },
      {
        key: "payments",
        label: "支付记录",
        icon: CreditCard,
        path: "/finance/payments",
        permissions: [PERMISSIONS.FINANCE.PAYMENT.VIEW],
      },
      {
        key: "invoices",
        label: "发票管理",
        icon: FileText,
        path: "/finance/invoices",
        permissions: [PERMISSIONS.FINANCE.INVOICE.VIEW],
      },
      {
        key: "coupons",
        label: "优惠券管理",
        icon: CreditCard,
        path: "/finance/coupons",
        permissions: [PERMISSIONS.FINANCE.COUPON.VIEW],
      },
    ],
  },
  {
    key: "users",
    label: "用户管理",
    icon: Users,
    path: "#",
    permissions: [PERMISSIONS.SYSTEM.USER.VIEW, PERMISSIONS.SYSTEM.TENANT.VIEW],
    children: [
      {
        key: "platform-users",
        label: "平台用户",
        icon: Users,
        path: "/users",
        permissions: [PERMISSIONS.SYSTEM.USER.VIEW],
      },
      {
        key: "tenants",
        label: "租户管理",
        icon: Building2,
        path: "/users/tenants",
        permissions: [PERMISSIONS.SYSTEM.TENANT.VIEW],
      },
    ],
  },
  {
    key: "system",
    label: "系统管理",
    icon: Settings,
    path: "#",
    permissions: [
      PERMISSIONS.ADMIN.ADMIN_USER.VIEW,
      PERMISSIONS.ADMIN.ANNOUNCEMENT.VIEW,
      PERMISSIONS.SYSTEM.PERMISSION.VIEW,
      PERMISSIONS.SYSTEM.CONFIG.VIEW,
      PERMISSIONS.SYSTEM.MONITOR.VIEW,
      PERMISSIONS.SYSTEM.LOG.VIEW,
    ],
    children: [
      {
        key: "admin-users",
        label: "管理员账号",
        icon: Shield,
        path: "/system/admins",
        permissions: [PERMISSIONS.ADMIN.ADMIN_USER.VIEW],
      },
      {
        key: "announcements",
        label: "系统公告",
        icon: Megaphone,
        path: "/system/announcements",
        permissions: [PERMISSIONS.ADMIN.ANNOUNCEMENT.VIEW],
      },
      {
        key: "permissions",
        label: "权限管理",
        icon: Shield,
        path: "/system/permissions",
        permissions: [PERMISSIONS.SYSTEM.PERMISSION.VIEW],
      },
      {
        key: "config",
        label: "系统配置",
        icon: Settings,
        path: "/system/config",
        permissions: [PERMISSIONS.SYSTEM.CONFIG.VIEW],
      },
      {
        key: "monitor",
        label: "系统监控",
        icon: BarChart3,
        path: "/system/monitor",
        permissions: [PERMISSIONS.SYSTEM.MONITOR.VIEW],
      },
      {
        key: "logs",
        label: "操作日志",
        icon: FileText,
        path: "/system/logs",
        permissions: [PERMISSIONS.SYSTEM.LOG.VIEW],
      },
    ],
  },
];

// 权限验证辅助函数
// 注意：权限验证辅助函数已经统一移动到 lib/auth/utils.ts
// 请从该文件导入以下函数：
// - checkPermission
// - hasAllPermissions
// - hasAnyPermission
// - hasModuleAccess

// 根据权限过滤菜单
export function getMenuItems(userPermissions: string[]): MenuItem[] {
  return filterMenuByPermissions(menuConfig, userPermissions);
}

function filterMenuByPermissions(
  items: MenuItem[],
  userPermissions: string[],
): MenuItem[] {
  return items
    .map((item) => {
      // 检查当前菜单项的权限
      if (item.permissions && item.permissions.length > 0) {
        const hasPermission = item.permissions.some((perm: string) =>
          checkPermission(userPermissions, perm),
        );

        if (!hasPermission) {
          return null;
        }
      }

      // 递归处理子菜单
      if (item.children) {
        const filteredChildren = filterMenuByPermissions(
          item.children,
          userPermissions,
        );

        // 如果没有可见的子菜单，隐藏父菜单
        if (filteredChildren.length === 0) {
          return null;
        }

        return {
          ...item,
          children: filteredChildren,
        };
      }

      return item;
    })
    .filter(Boolean) as MenuItem[];
}
