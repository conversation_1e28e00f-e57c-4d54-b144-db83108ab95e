import { PathPermissionConfig } from "@/middlewares";
import {
  Settings,
  DollarSign,
  Shield,
  FileText,
  GraduationCap,
  Package,
  Users,
  UserCheck,
  type LucideIcon,
} from "lucide-react";

// 权限模块信息定义
export interface ModuleInfo {
  name: string;
  icon: LucideIcon;
  color: "blue" | "green" | "purple" | "orange" | "yellow" | "pink";
  description?: string;
}

// 权限模块配置
export const MODULE_INFO: Record<string, ModuleInfo> = {
  SYSTEM: {
    name: "系统管理",
    icon: Settings,
    color: "blue",
    description: "系统级别的管理功能，包括用户、租户、配置等",
  },
  FINANCE: {
    name: "财务管理",
    icon: DollarSign,
    color: "green",
    description: "财务相关的管理功能，包括订阅、支付、发票等",
  },
  ADMIN: {
    name: "管理后台",
    icon: Shield,
    color: "purple",
    description: "管理后台的基础功能，包括仪表盘、公告、管理员等",
  },
  CONTENT: {
    name: "内容管理",
    icon: FileText,
    color: "orange",
    description: "内容相关的管理功能，包括帖子、评论、资源等",
  },
  COURSE: {
    name: "课程管理",
    icon: GraduationCap,
    color: "yellow",
    description: "课程相关的管理功能，包括课程、分类、报名等",
  },
  PRODUCT: {
    name: "产品管理",
    icon: Package,
    color: "pink",
    description: "产品相关的管理功能，包括产品、分类、询价等",
  },
  MATCH: {
    name: "匹配系统",
    icon: UserCheck,
    color: "blue",
    description: "匹配系统相关的管理功能，包括匹配管理、规划师管理、分类管理等",
  },
};

// 路径权限映射表（从原 middleware.ts 迁移）
export const PATH_PERMISSIONS: PathPermissionConfig = {
  "/": ["admin:dashboard:view"],
  "/content": [
    "content:post:view",
    "content:comment:view",
    "content:resource:view",
  ],
  "/content/posts": ["content:post:view"],
  "/content/comments": ["content:comment:view"],
  "/content/resources": ["content:resource:view"],
  "/courses": ["course:management:view"],
  "/courses/categories": ["course:category:view"],
  "/courses/enrollments": ["course:enrollment:view"],
  "/products": ["product:management:view"],
  "/products/categories": ["product:category:view"],
  "/products/inquiries": ["product:inquiry:view"],
  "/products/partners": ["product:partner:view"],
  "/match": ["match:management:view"],
  "/match/planners": ["match:planner:view"],
  "/match/categories": ["match:category:view"],
  "/match/recommendations": ["match:recommendation:view"],
  "/match/statistics": ["match:statistics:view"],
  "/finance/subscriptions": ["finance:subscription:view"],
  "/finance/payments": ["finance:payment:view"],
  "/finance/invoices": ["finance:invoice:view"],
  "/finance/coupons": ["finance:coupon:view"],
  "/users": ["system:user:view"],
  "/users/tenants": ["system:tenant:view"],
  "/system/admins": ["admin:admin_user:view"],
  "/system/announcements": ["admin:announcement:view"],
  "/system/permissions": ["system:permission:view"],
  "/system/config": ["system:config:view"],
  "/system/monitor": ["system:monitor:view"],
  "/system/logs": ["system:log:view"],
};
// 权限定义映射表
export const PERMISSIONS = {
  // ========== 系统管理权限 ==========
  SYSTEM: {
    USER: {
      VIEW: "system:user:view",
      CREATE: "system:user:create",
      EDIT: "system:user:edit",
      DELETE: "system:user:delete",
      BAN: "system:user:ban",
      RESET_PASSWORD: "system:user:reset_password",
    },
    TENANT: {
      VIEW: "system:tenant:view",
      CREATE: "system:tenant:create",
      EDIT: "system:tenant:edit",
      DELETE: "system:tenant:delete",
      SUSPEND: "system:tenant:suspend",
      CONFIG: "system:tenant:config",
    },
    PERMISSION: {
      VIEW: "system:permission:view",
      ASSIGN: "system:permission:assign",
    },
    CONFIG: {
      VIEW: "system:config:view",
      EDIT: "system:config:edit",
    },
    MONITOR: {
      VIEW: "system:monitor:view",
      EXPORT: "system:monitor:export",
    },
    LOG: {
      VIEW: "system:log:view",
      EXPORT: "system:log:export",
    },
  },

  // ========== 财务管理权限 ==========
  FINANCE: {
    SUBSCRIPTION: {
      VIEW: "finance:subscription:view",
      CREATE: "finance:subscription:create",
      EDIT: "finance:subscription:edit",
      CANCEL: "finance:subscription:cancel",
    },
    PAYMENT: {
      VIEW: "finance:payment:view",
      PROCESS: "finance:payment:process",
      REFUND: "finance:payment:refund",
      EXPORT: "finance:payment:export",
    },
    INVOICE: {
      VIEW: "finance:invoice:view",
      CREATE: "finance:invoice:create",
      VOID: "finance:invoice:void",
      DOWNLOAD: "finance:invoice:download",
    },
    COUPON: {
      VIEW: "finance:coupon:view",
      CREATE: "finance:coupon:create",
      EDIT: "finance:coupon:edit",
      DELETE: "finance:coupon:delete",
    },
  },

  // ========== 管理后台权限 ==========
  ADMIN: {
    DASHBOARD: {
      VIEW: "admin:dashboard:view",
    },
    ANNOUNCEMENT: {
      VIEW: "admin:announcement:view",
      CREATE: "admin:announcement:create",
      EDIT: "admin:announcement:edit",
      DELETE: "admin:announcement:delete",
    },
    ADMIN_USER: {
      VIEW: "admin:admin_user:view",
      CREATE: "admin:admin_user:create",
      EDIT: "admin:admin_user:edit",
      DELETE: "admin:admin_user:delete",
    },
  },

  // ========== 内容管理权限 ==========
  CONTENT: {
    POST: {
      VIEW: "content:post:view",
      CREATE: "content:post:create",
      EDIT: "content:post:edit",
      DELETE: "content:post:delete",
      PUBLISH: "content:post:publish",
      REVIEW: "content:post:review",
    },
    COMMENT: {
      VIEW: "content:comment:view",
      DELETE: "content:comment:delete",
      REVIEW: "content:comment:review",
    },
    RESOURCE: {
      VIEW: "content:resource:view",
      CREATE: "content:resource:create",
      EDIT: "content:resource:edit",
      DELETE: "content:resource:delete",
    },
  },

  // ========== 课程管理权限 ==========
  COURSE: {
    MANAGEMENT: {
      VIEW: "course:management:view",
      CREATE: "course:management:create",
      EDIT: "course:management:edit",
      DELETE: "course:management:delete",
      PUBLISH: "course:management:publish",
    },
    CATEGORY: {
      VIEW: "course:category:view",
      CREATE: "course:category:create",
      EDIT: "course:category:edit",
      DELETE: "course:category:delete",
    },
    ENROLLMENT: {
      VIEW: "course:enrollment:view",
      CREATE: "course:enrollment:create",
      EDIT: "course:enrollment:edit",
      DELETE: "course:enrollment:delete",
      MANAGE: "course:enrollment:manage",
      EXPORT: "course:enrollment:export",
      MARK_PAID: "course:enrollment:mark_paid",
    },
    REVIEW: {
      VIEW: "course:review:view",
      DELETE: "course:review:delete",
    },
  },

  // ========== 产品管理权限 ==========
  PRODUCT: {
    MANAGEMENT: {
      VIEW: "product:management:view",
      CREATE: "product:management:create",
      EDIT: "product:management:edit",
      DELETE: "product:management:delete",
      PUBLISH: "product:management:publish",
    },
    CATEGORY: {
      VIEW: "product:category:view",
      CREATE: "product:category:create",
      EDIT: "product:category:edit",
      DELETE: "product:category:delete",
    },
    INQUIRY: {
      VIEW: "product:inquiry:view",
      ASSIGN: "product:inquiry:assign",
      PROCESS: "product:inquiry:process",
      EXPORT: "product:inquiry:export",
    },
    PARTNER: {
      VIEW: "product:partner:view",
      CREATE: "product:partner:create",
      EDIT: "product:partner:edit",
      DELETE: "product:partner:delete",
    },
  },

  // ========== 匹配系统权限 ==========
  MATCH: {
    MANAGEMENT: {
      VIEW: "match:management:view",
      CREATE: "match:management:create",
      EDIT: "match:management:edit",
      DELETE: "match:management:delete",
      CONFIRM: "match:management:confirm",
      CANCEL: "match:management:cancel",
    },
    PLANNER: {
      VIEW: "match:planner:view",
      CREATE: "match:planner:create",
      EDIT: "match:planner:edit",
      DELETE: "match:planner:delete",
      PROFILE: "match:planner:profile",
      PERFORMANCE: "match:planner:performance",
    },
    CATEGORY: {
      VIEW: "match:category:view",
      CREATE: "match:category:create",
      EDIT: "match:category:edit",
      DELETE: "match:category:delete",
    },
    RECOMMENDATION: {
      VIEW: "match:recommendation:view",
      GENERATE: "match:recommendation:generate",
      EVALUATE: "match:recommendation:evaluate",
    },
    STATISTICS: {
      VIEW: "match:statistics:view",
      EXPORT: "match:statistics:export",
    },
  },
} as const;

// 角色默认权限配置
export const ROLE_PERMISSIONS = {
  // 超级管理员：所有权限
  SUPER_ADMIN: ["*"],

  // 平台管理员
  ADMIN: [
    "admin:*",
    "content:*",
    "course:*",
    "product:*",
    "system:user:*",
    "system:tenant:view",
    "system:tenant:edit",
    "system:monitor:view",
    "system:log:view",
    "finance:*",
  ],

  // 运营人员
  OPERATOR: [
    // 管理后台
    "admin:dashboard:view",
    "admin:announcement:view",

    // 内容管理
    "content:post:view",
    "content:post:review",
    "content:comment:view",
    "content:comment:delete",
    "content:resource:view",

    // 课程管理
    "course:management:view",
    "course:management:edit",
    "course:enrollment:view",
    "course:review:view",

    // 产品管理
    "product:management:view",
    "product:inquiry:view",
    "product:inquiry:process",

    // 基础查看权限
    "system:monitor:view",
  ],
} as const;

// 权限描述信息
export const PERMISSION_DESCRIPTIONS: Record<
  string,
  { name: string; description: string }
> = {
  // 系统管理
  "system:user:view": {
    name: "查看用户",
    description: "查看平台用户列表和详情",
  },
  "system:user:create": {
    name: "创建用户",
    description: "创建新的平台用户",
  },
  "system:user:edit": {
    name: "编辑用户",
    description: "编辑用户信息",
  },
  "system:user:delete": {
    name: "删除用户",
    description: "删除用户账号",
  },
  "system:user:ban": {
    name: "封禁用户",
    description: "封禁或解封用户账号",
  },
  "system:user:reset_password": {
    name: "重置密码",
    description: "重置用户密码",
  },

  // 租户管理
  "system:tenant:view": {
    name: "查看租户",
    description: "查看租户列表和详情",
  },
  "system:tenant:create": {
    name: "创建租户",
    description: "创建新的租户",
  },
  "system:tenant:edit": {
    name: "编辑租户",
    description: "编辑租户信息",
  },
  "system:tenant:delete": {
    name: "删除租户",
    description: "删除租户",
  },
  "system:tenant:suspend": {
    name: "暂停租户",
    description: "暂停或恢复租户服务",
  },

  // 内容管理
  "content:post:view": {
    name: "查看帖子",
    description: "查看社区帖子列表和详情",
  },
  "content:post:review": {
    name: "审核帖子",
    description: "审核社区帖子内容",
  },
  "content:comment:view": {
    name: "查看评论",
    description: "查看评论列表",
  },
  "content:comment:delete": {
    name: "删除评论",
    description: "删除不当评论",
  },

  // 课程管理
  "course:management:view": {
    name: "查看课程",
    description: "查看课程列表和详情",
  },
  "course:management:create": {
    name: "创建课程",
    description: "创建新课程",
  },
  "course:management:edit": {
    name: "编辑课程",
    description: "编辑课程信息",
  },
  "course:management:delete": {
    name: "删除课程",
    description: "删除课程",
  },
  "course:management:publish": {
    name: "发布课程",
    description: "发布或下架课程",
  },

  // 产品管理
  "product:management:view": {
    name: "查看产品",
    description: "查看产品列表和详情",
  },
  "product:inquiry:view": {
    name: "查看询价",
    description: "查看产品询价单",
  },
  "product:inquiry:process": {
    name: "处理询价",
    description: "处理产品询价单",
  },
};

// 注意：权限验证辅助函数已经统一移动到 lib/auth/utils.ts
// 请从该文件导入以下函数：
// - checkPermission (原 hasPermission)
// - hasAllPermissions
// - hasAnyPermission
// - hasModuleAccess
//
// // 权限定义文件
// export const MODULES = {
//   // 管理系统
//   SYSTEM: "system",
//   FINANCE: "finance",
//   ADMIN: "admin",
//   CONTENT: "content",
//   COURSE: "course",
//   PRODUCT: "product",
// } as const;
//
// export const ACTIONS = {
//   // 基础操作
//   VIEW: "view",
//   CREATE: "create",
//   EDIT: "edit",
//   DELETE: "delete",
//
//   // 扩展操作
//   PUBLISH: "publish",
//   REVIEW: "review",
//   APPROVE: "approve",
//   EXPORT: "export",
//   IMPORT: "import",
//   BAN: "ban",
//   SUSPEND: "suspend",
//   RESET_PASSWORD: "reset_password",
//
//   // 特殊操作
//   MANAGE: "manage", // 包含所有操作权限
//   CONFIG: "config", // 配置权限
//   ASSIGN: "assign", // 分配权限
//   PROCESS: "process", // 处理权限
//   REFUND: "refund", // 退款权限
//   VOID: "void", // 作废权限
//   DOWNLOAD: "download", // 下载权限
// } as const;
