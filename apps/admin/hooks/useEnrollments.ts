"use client";

import { useState, useEffect, useCallback } from "react";

// 定义接口类型
export interface EnrollmentUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

export interface EnrollmentCourse {
  id: string;
  title: string;
  instructorName: string;
  price: number;
  isFree: boolean;
}

export interface Enrollment {
  id: string;
  user: EnrollmentUser;
  course: EnrollmentCourse;
  enrolledAt: string;
  completedAt?: string;
  progress: number;
  isPaid: boolean;
  paidAmount?: number;
  paidAt?: string;
  lastAccessAt?: string;
}

export interface EnrollmentsQuery {
  search?: string;
  courseId?: string;
  userId?: string;
  isPaid?: boolean;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface EnrollmentsResponse {
  data: Enrollment[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface CreateEnrollmentData {
  userId: string;
  courseId: string;
  isPaid?: boolean;
  paidAmount?: number;
  notes?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
}

// 报名管理 hooks
export function useEnrollments(query: EnrollmentsQuery = {}) {
  const [enrollments, setEnrollments] = useState<Enrollment[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  const fetchEnrollments = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();

      if (query.search) params.append("search", query.search);
      if (query.courseId) params.append("courseId", query.courseId);
      if (query.userId) params.append("userId", query.userId);
      if (query.isPaid !== undefined)
        params.append("isPaid", query.isPaid.toString());
      if (query.page) params.append("page", query.page.toString());
      if (query.limit) params.append("limit", query.limit.toString());
      if (query.sortBy) params.append("sortBy", query.sortBy);
      if (query.sortOrder) params.append("sortOrder", query.sortOrder);

      const response = await fetch(`/api/enrollments?${params.toString()}`);
      const result: EnrollmentsResponse = await response.json();

      if (response.ok) {
        setEnrollments(result.data);
        setTotal(result.total);
        setTotalPages(result.totalPages);
      } else {
        throw new Error("获取报名列表失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取报名列表失败");
    } finally {
      setLoading(false);
    }
  }, [query]);

  useEffect(() => {
    fetchEnrollments();
  }, [fetchEnrollments]);

  return {
    enrollments,
    loading,
    error,
    total,
    totalPages,
    refetch: fetchEnrollments,
  };
}

// 单个报名详情 hook
export function useEnrollment(id: string) {
  const [enrollment, setEnrollment] = useState<Enrollment | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchEnrollment = useCallback(async () => {
    if (!id) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/enrollments/${id}`);
      const result = await response.json();

      if (response.ok) {
        setEnrollment(result);
      } else {
        throw new Error(result.error || "获取报名详情失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取报名详情失败");
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchEnrollment();
  }, [fetchEnrollment]);

  return {
    enrollment,
    loading,
    error,
    refetch: fetchEnrollment,
  };
}

// 报名操作 hooks
export function useEnrollmentActions() {
  const [loading, setLoading] = useState(false);

  const createEnrollment = async (
    data: CreateEnrollmentData,
  ): Promise<ApiResponse<Enrollment>> => {
    setLoading(true);
    try {
      const response = await fetch("/api/enrollments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok) {
        return { success: true, data: result };
      } else {
        return { success: false, message: result.error || "创建报名失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "创建报名失败",
      };
    } finally {
      setLoading(false);
    }
  };

  const updateEnrollment = async (
    id: string,
    data: Partial<CreateEnrollmentData>,
  ): Promise<ApiResponse<Enrollment>> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/enrollments/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok) {
        return { success: true, data: result };
      } else {
        return { success: false, message: result.error || "更新报名失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "更新报名失败",
      };
    } finally {
      setLoading(false);
    }
  };

  const deleteEnrollment = async (id: string): Promise<ApiResponse> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/enrollments/${id}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (response.ok) {
        return { success: true };
      } else {
        return { success: false, message: result.error || "删除报名失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "删除报名失败",
      };
    } finally {
      setLoading(false);
    }
  };

  const updatePaymentStatus = async (
    id: string,
    isPaid: boolean,
    paidAmount?: number,
  ): Promise<ApiResponse> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/enrollments/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ isPaid, paidAmount }),
      });

      const result = await response.json();

      if (response.ok) {
        return { success: true };
      } else {
        return { success: false, message: result.error || "更新支付状态失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "更新支付状态失败",
      };
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    createEnrollment,
    updateEnrollment,
    deleteEnrollment,
    updatePaymentStatus,
  };
}

// 报名统计 hook
export function useEnrollmentStats() {
  const [stats, setStats] = useState({
    totalEnrollments: 0,
    paidEnrollments: 0,
    unpaidEnrollments: 0,
    totalRevenue: 0,
    completionRate: 0,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/enrollments/dashboard");
      const result = await response.json();

      if (response.ok) {
        setStats(result);
      } else {
        throw new Error("获取统计数据失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取统计数据失败");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return {
    stats,
    loading,
    error,
    refetch: fetchStats,
  };
}

