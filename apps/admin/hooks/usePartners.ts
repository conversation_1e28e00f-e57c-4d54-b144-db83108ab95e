"use client";

import { useState, useEffect, useCallback } from "react";

// 接口类型定义
export interface Partner {
  id: string;
  name: string;
  code: string;
  logo?: string;
  contactName: string;
  contactPhone: string;
  contactEmail: string;
  address?: string;
  cooperationType: string[];
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  productCount?: number; // 关联产品数量
}

export interface PartnersQuery {
  search?: string;
  isActive?: boolean;
  cooperationType?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface PartnersResponse {
  data: Partner[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
}

// 合作方列表 hook
export function usePartners(query: PartnersQuery = {}) {
  const [partners, setPartners] = useState<Partner[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  const fetchPartners = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();

      if (query.search) params.append("search", query.search);
      if (query.isActive !== undefined) params.append("isActive", query.isActive.toString());
      if (query.cooperationType) params.append("cooperationType", query.cooperationType);
      if (query.page) params.append("page", query.page.toString());
      if (query.limit) params.append("limit", query.limit.toString());
      if (query.sortBy) params.append("sortBy", query.sortBy);
      if (query.sortOrder) params.append("sortOrder", query.sortOrder);

      const response = await fetch(`/api/partners?${params.toString()}`);
      const result: PartnersResponse = await response.json();

      if (response.ok) {
        setPartners(result.data);
        setTotal(result.total);
        setTotalPages(result.totalPages);
      } else {
        throw new Error("获取合作方列表失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取合作方列表失败");
    } finally {
      setLoading(false);
    }
  }, [query]);

  useEffect(() => {
    fetchPartners();
  }, [fetchPartners]);

  return {
    partners,
    loading,
    error,
    total,
    totalPages,
    refetch: fetchPartners,
  };
}

// 单个合作方 hook
export function usePartner(id: string) {
  const [partner, setPartner] = useState<Partner | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPartner = useCallback(async () => {
    if (!id) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/partners/${id}`);
      const result = await response.json();

      if (response.ok) {
        setPartner(result);
      } else {
        throw new Error(result.error || "获取合作方详情失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取合作方详情失败");
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchPartner();
  }, [fetchPartner]);

  return {
    partner,
    loading,
    error,
    refetch: fetchPartner,
  };
}

// 合作方操作 hooks
export function usePartnerActions() {
  const [loading, setLoading] = useState(false);

  const createPartner = async (
    data: Partial<Partner>,
  ): Promise<ApiResponse<Partner>> => {
    setLoading(true);
    try {
      const response = await fetch("/api/partners", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok) {
        return { success: true, data: result };
      } else {
        return { success: false, message: result.error || "创建合作方失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "创建合作方失败",
      };
    } finally {
      setLoading(false);
    }
  };

  const updatePartner = async (
    id: string,
    data: Partial<Partner>,
  ): Promise<ApiResponse<Partner>> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/partners/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok) {
        return { success: true, data: result };
      } else {
        return { success: false, message: result.error || "更新合作方失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "更新合作方失败",
      };
    } finally {
      setLoading(false);
    }
  };

  const deletePartner = async (id: string): Promise<ApiResponse> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/partners/${id}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (response.ok) {
        return { success: true };
      } else {
        return { success: false, message: result.error || "删除合作方失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "删除合作方失败",
      };
    } finally {
      setLoading(false);
    }
  };

  const togglePartnerStatus = async (
    id: string,
    isActive: boolean,
  ): Promise<ApiResponse> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/partners/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ isActive }),
      });

      const result = await response.json();

      if (response.ok) {
        return { success: true };
      } else {
        return { success: false, message: result.error || "更新合作方状态失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "更新合作方状态失败",
      };
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    createPartner,
    updatePartner,
    deletePartner,
    togglePartnerStatus,
  };
}

// 简单的合作方列表 hook（用于下拉选择等场景）
export function usePartnersList() {
  const [partners, setPartners] = useState<Pick<Partner, 'id' | 'name' | 'code'>[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPartners = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/partners?simple=true&isActive=true");
      const result = await response.json();

      if (response.ok) {
        setPartners(result.data);
      } else {
        throw new Error("获取合作方列表失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取合作方列表失败");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchPartners();
  }, [fetchPartners]);

  return {
    partners,
    loading,
    error,
    refetch: fetchPartners,
  };
}

