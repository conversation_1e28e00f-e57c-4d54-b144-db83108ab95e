"use client";

import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";

// 帖子类型枚举
export enum PostType {
  ARTICLE = "ARTICLE",
  SHARE = "SHARE", 
  QUESTION = "QUESTION",
  ANNOUNCEMENT = "ANNOUNCEMENT",
  EXPERIENCE = "EXPERIENCE",
}

// 帖子状态枚举
export enum PostStatus {
  DRAFT = "DRAFT",
  PENDING_REVIEW = "PENDING_REVIEW",
  PUBLISHED = "PUBLISHED",
  HIDDEN = "HIDDEN",
  DELETED = "DELETED",
}

// 帖子接口
export interface Post {
  id: string;
  title: string;
  content: string;
  summary?: string;
  cover?: string;
  type: PostType;
  status: PostStatus;
  viewCount: number;
  isTop: boolean;
  isRecommended: boolean;
  isOriginal: boolean;
  authorId: string;
  author?: {
    id: string;
    name: string;
    username: string;
    avatar?: string;
  };
  authorName?: string;
  authorAvatar?: string;
  categoryId?: string;
  category?: {
    id: string;
    name: string;
    slug: string;
  };
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
  tags?: Array<{
    id: string;
    name: string;
    slug: string;
  }>;
  _count?: {
    comments: number;
    likes: number;
    favorites: number;
  };
}

// 查询参数接口
export interface PostsQuery {
  page?: number;
  limit?: number;
  search?: string;
  type?: PostType;
  status?: PostStatus;
  authorId?: string;
  categoryId?: string;
  isTop?: boolean;
  isRecommended?: boolean;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

// 响应接口
export interface PostsResponse {
  data: Post[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 创建/更新帖子数据接口
export interface PostFormData {
  title: string;
  content: string;
  summary?: string;
  cover?: string;
  type: PostType;
  status: PostStatus;
  categoryId?: string;
  tags: string[];
  isTop: boolean;
  isRecommended: boolean;
  isOriginal: boolean;
}

// 获取帖子列表 hook
export function usePosts(query: PostsQuery = {}) {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  const fetchPosts = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      
      if (query.page) params.append("page", query.page.toString());
      if (query.limit) params.append("limit", query.limit.toString());
      if (query.search) params.append("search", query.search);
      if (query.type) params.append("type", query.type);
      if (query.status) params.append("status", query.status);
      if (query.authorId) params.append("authorId", query.authorId);
      if (query.categoryId) params.append("categoryId", query.categoryId);
      if (query.isTop !== undefined) params.append("isTop", query.isTop.toString());
      if (query.isRecommended !== undefined) params.append("isRecommended", query.isRecommended.toString());
      if (query.sortBy) params.append("sortBy", query.sortBy);
      if (query.sortOrder) params.append("sortOrder", query.sortOrder);

      const response = await fetch(`/api/content/posts?${params.toString()}`);
      const result = await response.json();

      if (response.ok && result.success) {
        setPosts(result.data.data);
        setTotal(result.data.total);
        setTotalPages(result.data.totalPages);
      } else {
        throw new Error(result.message || "获取帖子列表失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取帖子列表失败");
    } finally {
      setLoading(false);
    }
  }, [
    query.page,
    query.limit,
    query.search,
    query.type,
    query.status,
    query.authorId,
    query.categoryId,
    query.isTop,
    query.isRecommended,
    query.sortBy,
    query.sortOrder,
  ]);

  useEffect(() => {
    fetchPosts();
  }, [fetchPosts]);

  return {
    posts,
    loading,
    error,
    total,
    totalPages,
    refetch: fetchPosts,
  };
}

// 获取单个帖子 hook
export function usePost(id: string) {
  const [post, setPost] = useState<Post | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPost = useCallback(async () => {
    if (!id) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/content/posts/${id}`);
      const result = await response.json();

      if (response.ok && result.success) {
        setPost(result.data);
      } else {
        throw new Error(result.message || "获取帖子详情失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取帖子详情失败");
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchPost();
  }, [fetchPost]);

  return {
    post,
    loading,
    error,
    refetch: fetchPost,
  };
}

// 帖子操作 hook
export function usePostActions() {
  const [loading, setLoading] = useState(false);

  // 创建帖子
  const createPost = async (data: PostFormData) => {
    setLoading(true);
    try {
      const response = await fetch("/api/content/posts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        return { success: true, data: result.data };
      } else {
        return { success: false, message: result.message || "创建失败" };
      }
    } catch (error) {
      return { success: false, message: "创建失败" };
    } finally {
      setLoading(false);
    }
  };

  // 更新帖子
  const updatePost = async (id: string, data: Partial<PostFormData>) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/content/posts/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        return { success: true, data: result.data };
      } else {
        return { success: false, message: result.message || "更新失败" };
      }
    } catch (error) {
      return { success: false, message: "更新失败" };
    } finally {
      setLoading(false);
    }
  };

  // 删除帖子
  const deletePost = async (id: string) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/content/posts/${id}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (response.ok && result.success) {
        return { success: true };
      } else {
        return { success: false, message: result.message || "删除失败" };
      }
    } catch (error) {
      return { success: false, message: "删除失败" };
    } finally {
      setLoading(false);
    }
  };

  // 批量操作
  const batchUpdatePosts = async (ids: string[], updates: Partial<PostFormData>) => {
    setLoading(true);
    try {
      const response = await fetch("/api/content/posts/batch", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ids, updates }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        return { success: true };
      } else {
        return { success: false, message: result.message || "批量操作失败" };
      }
    } catch (error) {
      return { success: false, message: "批量操作失败" };
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    createPost,
    updatePost,
    deletePost,
    batchUpdatePosts,
  };
}
