"use client";

import { useState, useEffect, useCallback } from "react";
import { ProductType, ProductStatus, InquiryStatus } from "@workspace/database";

// 定义接口类型
export interface Product {
  id: string;
  name: string;
  code: string;
  type: ProductType;
  categoryId: string;
  description: string;
  features: string[];
  highlights?: any;
  images: string[];
  brochureUrl?: string;
  price?: number;
  priceUnit?: string;
  priceNote?: string;
  duration?: string;
  location?: string;
  startDate?: string;
  endDate?: string;
  capacity?: number;
  minParticipants?: number;
  targetAudience: string[];
  ageRange?: string;
  gradeRange?: string;
  partnerId?: string;
  status: ProductStatus;
  priority: number;
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords: string[];
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
  categoryName: string;
  partnerName?: string;
  inquiryCount?: number;
  partner?: Partner | null;
}

export interface ProductCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  type: ProductType;
  icon?: string;
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Partner {
  id: string;
  name: string;
  code: string;
  logo?: string;
  contactName: string;
  contactPhone: string;
  contactEmail: string;
  address?: string;
  cooperationType: string[];
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ProductInquiry {
  id: string;
  productId: string;
  productName: string;
  tenantId?: string;
  userId?: string;
  contactName: string;
  contactPhone: string;
  contactEmail?: string;
  organization?: string;
  participants?: number;
  expectedDate?: string;
  budget?: string;
  requirements?: string;
  message?: string;
  status: InquiryStatus;
  assignedToId?: string;
  assignedToName?: string;
  followUpNotes?: any;
  lastContactAt?: string;
  nextFollowUpAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ProductsQuery {
  categoryId?: string;
  type?: ProductType;
  status?: ProductStatus;
  partnerId?: string;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface ProductInquiriesQuery {
  productId?: string;
  status?: InquiryStatus;
  assignedToId?: string;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface ProductsResponse {
  data: Product[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ProductInquiriesResponse {
  data: ProductInquiry[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
}

// 产品管理 hooks
const defaultProductsQuery: ProductsQuery = {};

export function useProducts(query: ProductsQuery = defaultProductsQuery) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  const fetchProducts = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();

      if (query.categoryId) params.append("categoryId", query.categoryId);
      if (query.type) params.append("type", query.type);
      if (query.status) params.append("status", query.status);
      if (query.partnerId) params.append("partnerId", query.partnerId);
      if (query.search) params.append("search", query.search);
      if (query.page) params.append("page", query.page.toString());
      if (query.limit) params.append("limit", query.limit.toString());
      if (query.sortBy) params.append("sortBy", query.sortBy);
      if (query.sortOrder) params.append("sortOrder", query.sortOrder);

      const response = await fetch(`/api/products?${params.toString()}`);
      const result: ProductsResponse = await response.json();

      if (response.ok) {
        setProducts(result.data);
        setTotal(result.total);
        setTotalPages(result.totalPages);
      } else {
        throw new Error("获取产品列表失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取产品列表失败");
    } finally {
      setLoading(false);
    }
  }, [query]);

  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  return {
    products,
    loading,
    error,
    total,
    totalPages,
    refetch: fetchProducts,
  };
}

// 单个产品 hook
export function useProduct(id: string) {
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProduct = useCallback(async () => {
    if (!id) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/products/${id}`);
      const result = await response.json();

      if (response.ok) {
        setProduct(result);
      } else {
        throw new Error(result.error || "获取产品详情失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取产品详情失败");
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchProduct();
  }, [fetchProduct]);

  return {
    product,
    loading,
    error,
    refetch: fetchProduct,
  };
}

// 产品分类 hooks
export function useProductCategories(type?: ProductType) {
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCategories = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      if (type) params.append("type", type);

      const response = await fetch(
        `/api/products/categories?${params.toString()}`,
      );
      const result = await response.json();

      if (result.success) {
        setCategories(result.data);
      } else {
        throw new Error(result.message || "获取分类列表失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取分类列表失败");
    } finally {
      setLoading(false);
    }
  }, [type]);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  return {
    categories,
    loading,
    error,
    refetch: fetchCategories,
  };
}

// 合作方 hooks
export function usePartners() {
  const [partners, setPartners] = useState<Partner[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPartners = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/partners");
      const result = await response.json();

      if (result.success) {
        setPartners(result.data);
      } else {
        throw new Error(result.message || "获取合作方列表失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取合作方列表失败");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchPartners();
  }, [fetchPartners]);

  return {
    partners,
    loading,
    error,
    refetch: fetchPartners,
  };
}

// 产品询价 hooks
const defaultQuery: ProductInquiriesQuery = {};

export function useProductInquiries(query: ProductInquiriesQuery = defaultQuery) {
  const [inquiries, setInquiries] = useState<ProductInquiry[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  const fetchInquiries = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();

      if (query.productId) params.append("productId", query.productId);
      if (query.status) params.append("status", query.status);
      if (query.assignedToId) params.append("assignedToId", query.assignedToId);
      if (query.search) params.append("search", query.search);
      if (query.page) params.append("page", query.page.toString());
      if (query.limit) params.append("limit", query.limit.toString());
      if (query.sortBy) params.append("sortBy", query.sortBy);
      if (query.sortOrder) params.append("sortOrder", query.sortOrder);

      const response = await fetch(
        `/api/products/inquiries?${params.toString()}`,
      );
      const result: ProductInquiriesResponse = await response.json();

      if (response.ok) {
        setInquiries(result.data);
        setTotal(result.total);
        setTotalPages(result.totalPages);
      } else {
        throw new Error("获取询价列表失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取询价列表失败");
    } finally {
      setLoading(false);
    }
  }, [
    query.productId,
    query.status,
    query.assignedToId,
    query.search,
    query.page,
    query.limit,
    query.sortBy,
    query.sortOrder,
  ]);

  useEffect(() => {
    fetchInquiries();
  }, [fetchInquiries]);

  return {
    inquiries,
    loading,
    error,
    total,
    totalPages,
    refetch: fetchInquiries,
  };
}

// 产品操作 hooks
export function useProductActions() {
  const [loading, setLoading] = useState(false);

  const createProduct = async (
    data: Partial<Product>,
  ): Promise<ApiResponse<Product>> => {
    setLoading(true);
    try {
      const response = await fetch("/api/products", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok) {
        return { success: true, data: result };
      } else {
        return { success: false, message: result.error || "创建产品失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "创建产品失败",
      };
    } finally {
      setLoading(false);
    }
  };

  const updateProduct = async (
    id: string,
    data: Partial<Product>,
  ): Promise<ApiResponse<Product>> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/products/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok) {
        return { success: true, data: result };
      } else {
        return { success: false, message: result.error || "更新产品失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "更新产品失败",
      };
    } finally {
      setLoading(false);
    }
  };

  const deleteProduct = async (id: string): Promise<ApiResponse> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/products/${id}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (response.ok) {
        return { success: true };
      } else {
        return { success: false, message: result.error || "删除产品失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "删除产品失败",
      };
    } finally {
      setLoading(false);
    }
  };

  const updateProductStatus = async (
    id: string,
    status: ProductStatus,
  ): Promise<ApiResponse> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/products/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      });

      const result = await response.json();

      if (response.ok) {
        return { success: true };
      } else {
        return { success: false, message: result.error || "更新产品状态失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "更新产品状态失败",
      };
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    createProduct,
    updateProduct,
    deleteProduct,
    updateProductStatus,
  };
}

// 产品分类操作 hooks
export function useProductCategoryActions() {
  const [loading, setLoading] = useState(false);

  const createCategory = async (
    data: Partial<ProductCategory>,
  ): Promise<ApiResponse<ProductCategory>> => {
    setLoading(true);
    try {
      const response = await fetch("/api/products/categories", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.success) {
        return { success: true, data: result.data };
      } else {
        return { success: false, message: result.message || "创建分类失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "创建分类失败",
      };
    } finally {
      setLoading(false);
    }
  };

  const updateCategory = async (
    id: string,
    data: Partial<ProductCategory>,
  ): Promise<ApiResponse<ProductCategory>> => {
    setLoading(true);
    try {
      const response = await fetch("/api/products/categories", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ id, ...data }),
      });

      const result = await response.json();

      if (result.success) {
        return { success: true, data: result.data };
      } else {
        return { success: false, message: result.message || "更新分类失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "更新分类失败",
      };
    } finally {
      setLoading(false);
    }
  };

  const deleteCategory = async (id: string): Promise<ApiResponse> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/products/categories?id=${id}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (result.success) {
        return { success: true };
      } else {
        return { success: false, message: result.message || "删除分类失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "删除分类失败",
      };
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    createCategory,
    updateCategory,
    deleteCategory,
  };
}

// 单个产品分类 hook
export function useProductCategory(id: string) {
  const [category, setCategory] = useState<ProductCategory & { productCount?: number } | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCategory = useCallback(async () => {
    if (!id) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/products/categories/${id}`);
      const result = await response.json();

      if (result.success) {
        setCategory(result.data);
      } else {
        throw new Error(result.message || "获取分类详情失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取分类详情失败");
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchCategory();
  }, [fetchCategory]);

  return {
    category,
    loading,
    error,
    refetch: fetchCategory,
  };
}

// 询价操作 hooks
export function useProductInquiryActions() {
  const [loading, setLoading] = useState(false);

  const updateInquiry = async (
    id: string,
    data: Partial<ProductInquiry>,
  ): Promise<ApiResponse> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/products/inquiries/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.success) {
        return { success: true };
      } else {
        return {
          success: false,
          message: result.message || "更新询价失败",
        };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "更新询价失败",
      };
    } finally {
      setLoading(false);
    }
  };

  const updateInquiryStatus = async (
    id: string,
    status: InquiryStatus,
    assignedToId?: string,
  ): Promise<ApiResponse> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/products/inquiries`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ id, status, assignedToId }),
      });

      const result = await response.json();

      if (result.success) {
        return { success: true };
      } else {
        return {
          success: false,
          message: result.message || "更新询价状态失败",
        };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "更新询价状态失败",
      };
    } finally {
      setLoading(false);
    }
  };

  const addFollowUpNote = async (
    id: string,
    note: string,
    nextFollowUpAt?: string,
  ): Promise<ApiResponse> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/products/inquiries`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id,
          followUpNote: note,
          nextFollowUpAt,
          lastContactAt: new Date().toISOString(),
        }),
      });

      const result = await response.json();

      if (result.success) {
        return { success: true };
      } else {
        return {
          success: false,
          message: result.message || "添加跟进记录失败",
        };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "添加跟进记录失败",
      };
    } finally {
      setLoading(false);
    }
  };

  const deleteInquiry = async (id: string): Promise<ApiResponse> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/products/inquiries?id=${id}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (result.success) {
        return { success: true };
      } else {
        return { success: false, message: result.message || "删除询价失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "删除询价失败",
      };
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    updateInquiry,
    updateInquiryStatus,
    addFollowUpNote,
    deleteInquiry,
  };
}
