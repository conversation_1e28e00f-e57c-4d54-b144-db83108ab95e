"use client";

import { useState, useEffect, useCallback } from "react";
import { CourseStatus, CourseLevel } from "@workspace/database";

// 定义接口类型
export interface Course {
  id: string;
  title: string;
  subtitle?: string;
  cover: string;
  level: CourseLevel;
  duration: number;
  lessonsCount: number;
  price: number;
  isFree: boolean;
  status: CourseStatus;
  viewCount: number;
  enrollCount: number;
  rating?: number;
  categoryName: string;
  instructorName: string;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CourseCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  parentId?: string;
  parent?: CourseCategory;
  children: CourseCategory[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CoursesQuery {
  categoryId?: string;
  status?: CourseStatus;
  level?: CourseLevel;
  isFree?: boolean;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface CoursesResponse {
  data: Course[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
}

// 课程管理 hooks
export function useCourses(query: CoursesQuery = {}) {
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  const fetchCourses = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();

      if (query.categoryId) params.append("categoryId", query.categoryId);
      if (query.status) params.append("status", query.status);
      if (query.level) params.append("level", query.level);
      if (query.isFree !== undefined)
        params.append("isFree", query.isFree.toString());
      if (query.search) params.append("search", query.search);
      if (query.page) params.append("page", query.page.toString());
      if (query.limit) params.append("limit", query.limit.toString());
      if (query.sortBy) params.append("sortBy", query.sortBy);
      if (query.sortOrder) params.append("sortOrder", query.sortOrder);

      const response = await fetch(`/api/courses?${params.toString()}`);
      const result: CoursesResponse = await response.json();

      if (response.ok) {
        setCourses(result.data);
        setTotal(result.total);
        setTotalPages(result.totalPages);
      } else {
        throw new Error("获取课程列表失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取课程列表失败");
    } finally {
      setLoading(false);
    }
  }, [query]);

  useEffect(() => {
    fetchCourses();
  }, [fetchCourses]);

  return {
    courses,
    loading,
    error,
    total,
    totalPages,
    refetch: fetchCourses,
  };
}

// 单个课程 hook
export function useCourse(id: string) {
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCourse = useCallback(async () => {
    if (!id) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/courses/${id}`);
      const result = await response.json();

      if (response.ok) {
        setCourse(result);
      } else {
        throw new Error(result.error || "获取课程详情失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取课程详情失败");
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchCourse();
  }, [fetchCourse]);

  return {
    course,
    loading,
    error,
    refetch: fetchCourse,
  };
}

// 课程分类 hooks
export function useCourseCategories() {
  const [categories, setCategories] = useState<CourseCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCategories = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/courses/categories");
      const result = await response.json();

      if (result.success) {
        setCategories(result.data);
      } else {
        throw new Error(result.message || "获取分类列表失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取分类列表失败");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  return {
    categories,
    loading,
    error,
    refetch: fetchCategories,
  };
}

// 课程分类操作 hooks
export function useCategoryActions() {
  const [loading, setLoading] = useState(false);

  const createCategory = async (
    data: Partial<CourseCategory>,
  ): Promise<ApiResponse<CourseCategory>> => {
    setLoading(true);
    try {
      const response = await fetch("/api/courses/categories", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.success) {
        return { success: true, data: result.data };
      } else {
        return { success: false, message: result.message || "创建分类失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "创建分类失败",
      };
    } finally {
      setLoading(false);
    }
  };

  const updateCategory = async (
    id: string,
    data: Partial<CourseCategory>,
  ): Promise<ApiResponse<CourseCategory>> => {
    setLoading(true);
    try {
      const response = await fetch("/api/courses/categories", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ id, ...data }),
      });

      const result = await response.json();

      if (result.success) {
        return { success: true, data: result.data };
      } else {
        return { success: false, message: result.message || "更新分类失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "更新分类失败",
      };
    } finally {
      setLoading(false);
    }
  };

  const deleteCategory = async (id: string): Promise<ApiResponse> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/courses/categories?id=${id}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (result.success) {
        return { success: true };
      } else {
        return { success: false, message: result.message || "删除分类失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "删除分类失败",
      };
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    createCategory,
    updateCategory,
    deleteCategory,
  };
}

// 单个分类详情 hook
export function useCourseCategory(id: string) {
  const [category, setCategory] = useState<CourseCategory | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCategory = useCallback(async () => {
    if (!id) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/courses/categories/${id}`);
      const result = await response.json();

      if (result.success) {
        setCategory(result.data);
      } else {
        throw new Error(result.message || "获取分类详情失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取分类详情失败");
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchCategory();
  }, [fetchCategory]);

  return {
    category,
    loading,
    error,
    refetch: fetchCategory,
  };
}

// 课程操作 hooks
export function useCourseActions() {
  const [loading, setLoading] = useState(false);

  const createCourse = async (
    data: Partial<Course>,
  ): Promise<ApiResponse<Course>> => {
    setLoading(true);
    try {
      const response = await fetch("/api/courses", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok) {
        return { success: true, data: result };
      } else {
        return { success: false, message: result.error || "创建课程失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "创建课程失败",
      };
    } finally {
      setLoading(false);
    }
  };

  const updateCourse = async (
    id: string,
    data: Partial<Course>,
  ): Promise<ApiResponse<Course>> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/courses/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok) {
        return { success: true, data: result };
      } else {
        return { success: false, message: result.error || "更新课程失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "更新课程失败",
      };
    } finally {
      setLoading(false);
    }
  };

  const deleteCourse = async (id: string): Promise<ApiResponse> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/courses/${id}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (response.ok) {
        return { success: true };
      } else {
        return { success: false, message: result.error || "删除课程失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "删除课程失败",
      };
    } finally {
      setLoading(false);
    }
  };

  const updateCourseStatus = async (
    id: string,
    status: CourseStatus,
  ): Promise<ApiResponse> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/courses/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      });

      const result = await response.json();

      if (response.ok) {
        return { success: true };
      } else {
        return { success: false, message: result.error || "更新课程状态失败" };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "更新课程状态失败",
      };
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    createCourse,
    updateCourse,
    deleteCourse,
    updateCourseStatus,
  };
}
