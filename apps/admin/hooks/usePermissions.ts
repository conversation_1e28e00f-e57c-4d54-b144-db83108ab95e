import { useAuth } from "@/lib/auth/context";
import {
  checkPermissions,
  getModulePermissions,
  hasPermissionGroup,
  hasResourceAction,
  hasResourceActions,
  type PermissionCheckOptions,
} from "@/lib/auth/permissions";
import { useMemo } from "react";

// 权限相关的自定义 hooks
export function usePermissions() {
  const {
    permissions,
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
    hasModuleAccess,
  } = useAuth();

  return {
    permissions,
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
    hasModuleAccess,
  };
}

// 检查单个权限
export function useHasPermission(permission: string) {
  const { hasPermission } = useAuth();
  return useMemo(() => hasPermission(permission), [hasPermission, permission]);
}

// 检查多个权限（全部满足）
export function useHasAllPermissions(...permissions: string[]) {
  const { hasAllPermissions } = useAuth();
  return useMemo(
    () => hasAllPermissions(...permissions),
    [hasAllPermissions, permissions]
  );
}

// 检查多个权限（任一满足）
export function useHasAnyPermission(...permissions: string[]) {
  const { hasAnyPermission } = useAuth();
  return useMemo(
    () => hasAnyPermission(...permissions),
    [hasAnyPermission, permissions]
  );
}

// 检查模块访问权限
export function useHasModuleAccess(module: string) {
  const { hasModuleAccess } = useAuth();
  return useMemo(() => hasModuleAccess(module), [hasModuleAccess, module]);
}

// 高级权限检查
export function useCheckPermissions(
  requiredPermissions: string[],
  options: PermissionCheckOptions = {}
) {
  const { permissions } = useAuth();
  return useMemo(
    () => checkPermissions(permissions, requiredPermissions, options),
    [permissions, requiredPermissions, options]
  );
}

// 获取模块权限
export function useModulePermissions(module: string) {
  const { permissions } = useAuth();
  return useMemo(
    () => getModulePermissions(permissions, module),
    [permissions, module]
  );
}

// 检查资源操作权限
export function useHasResourceAction(resource: string, action: string) {
  const { permissions } = useAuth();
  return useMemo(
    () => hasResourceAction(permissions, resource, action),
    [permissions, resource, action]
  );
}

// 批量检查资源操作权限
export function useResourceActions(resource: string, actions: string[]) {
  const { permissions } = useAuth();
  return useMemo(
    () => hasResourceActions(permissions, resource, actions),
    [permissions, resource, actions]
  );
}

// 检查权限组
export function useHasPermissionGroup(
  group: Parameters<typeof hasPermissionGroup>[1]
) {
  const { permissions } = useAuth();
  return useMemo(
    () => hasPermissionGroup(permissions, group),
    [permissions, group]
  );
}

// 权限状态hook - 返回常用的权限检查结果
export function usePermissionStatus() {
  const { permissions } = useAuth();

  return useMemo(
    () => ({
      isSuperAdmin: permissions.includes("*"),
      isSystemAdmin: hasPermissionGroup(permissions, "SYSTEM_ADMIN"),
      isContentManager: hasPermissionGroup(permissions, "CONTENT_MANAGER"),
      isFinanceManager: hasPermissionGroup(permissions, "FINANCE_MANAGER"),
      isReadOnly: hasPermissionGroup(permissions, "READONLY"),
    }),
    [permissions]
  );
}
