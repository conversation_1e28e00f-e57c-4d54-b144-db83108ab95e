// Next.js 主中间件文件 - 使用模块化架构
import type { NextRequest } from "next/server";
import {
  composeMiddleware,
  requestLoggingMiddleware,
  authMiddleware,
  // conditionalMiddleware,
  // excludePathsMiddleware,
} from "./middlewares/index";

// 公开路径配置（不需要认证和权限检查）
const PUBLIC_PATHS = ["/login", "/auth", "/api/auth", "/error", "/403"];

// 静态资源路径（只需要日志记录）
const STATIC_PATHS = ["/api", "/_next", "/favicon.ico", "/public"];

// 检查是否为静态资源路径
function isStaticPath(pathname: string): boolean {
  return STATIC_PATHS.some((path) => pathname.startsWith(path));
}

// 检查是否为公开路径
function isPublicPath(pathname: string): boolean {
  return PUBLIC_PATHS.some((path) => pathname.startsWith(path));
}

// 主中间件函数
export default function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  console.log(`\n=== 中间件开始 ===`);
  console.log(`请求路径: ${pathname}`);
  console.log(`请求方法: ${request.method}`);

  // 静态资源路径 - 只记录日志
  if (isStaticPath(pathname)) {
    console.log(`[路由匹配] 静态资源路径: ${pathname}`);
    return composeMiddleware(requestLoggingMiddleware)(request);
  }

  // 公开路径 - 日志 + 基础处理
  if (isPublicPath(pathname)) {
    console.log(`[路由匹配] 公开路径: ${pathname}`);
    return composeMiddleware(
      requestLoggingMiddleware,
      // 可以在这里添加其他公开路径需要的中间件
      // 比如：rateLimitMiddleware, corsMiddleware 等
    )(request);
  }

  // 受保护的路径 - 完整的中间件链
  console.log(`[路由匹配] 受保护路径: ${pathname}`);
  return composeMiddleware(
    // 1. 请求日志记录
    requestLoggingMiddleware,
    // 2. 用户认证检查
    authMiddleware,
  )(request);
}

// 中间件配置 - 定义哪些路径需要经过中间件处理
export const config = {
  matcher: [
    /*
     * 匹配所有路径除了:
     * - api routes
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    "/((?!api|_next/static|_next/image|favicon.ico|public).*)",
  ],
};
