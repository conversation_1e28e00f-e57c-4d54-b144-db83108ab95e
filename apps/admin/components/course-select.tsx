"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Badge } from "@workspace/ui/components/badge";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import {
  Search,
  Clock,
  Users,
  Eye,
  Star,
  ChevronsUpDown,
  Loader2,
  X,
  GraduationCap,
} from "lucide-react";
import { cn } from "@workspace/ui/lib/utils";
import { CourseStatus, CourseLevel } from "@workspace/database";

// 课程接口定义
export interface Course {
  id: string;
  title: string;
  subtitle?: string;
  cover: string;
  level: CourseLevel;
  duration: number;
  lessonsCount: number;
  price: number;
  originalPrice?: number;
  isFree: boolean;
  status: CourseStatus;
  viewCount: number;
  enrollCount: number;
  rating?: number;
  categoryId: string;
  categoryName: string;
  instructorName: string;
  instructorTitle?: string;
  instructorAvatar?: string;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CourseSearchQuery {
  search?: string;
  categoryId?: string;
  status?: CourseStatus;
  level?: CourseLevel;
  isFree?: boolean;
  minPrice?: number;
  maxPrice?: number;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface CourseSearchResponse {
  data: Course[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Props 接口
export interface CourseSelectProps {
  value?: Course;
  onSelect: (course: Course) => void;
  onClear?: () => void;
  placeholder?: string;
  disabled?: boolean;
  filterStatus?: CourseStatus[];
  filterLevels?: CourseLevel[];
  showOnlyPublished?: boolean;
  className?: string;
}

export interface CourseSearchDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSelect: (course: Course) => void;
  filterStatus?: CourseStatus[];
  filterLevels?: CourseLevel[];
  showOnlyPublished?: boolean;
}

// 课程搜索 API hook
export function useCourseSearch(query: CourseSearchQuery = {}) {
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);

  const fetchCourses = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();

      if (query.search) params.append("search", query.search);
      if (query.categoryId) params.append("categoryId", query.categoryId);
      if (query.status) params.append("status", query.status);
      if (query.level) params.append("level", query.level);
      if (query.isFree !== undefined)
        params.append("isFree", query.isFree.toString());
      if (query.minPrice) params.append("minPrice", query.minPrice.toString());
      if (query.maxPrice) params.append("maxPrice", query.maxPrice.toString());
      if (query.page) params.append("page", query.page.toString());
      if (query.limit) params.append("limit", query.limit.toString());
      if (query.sortBy) params.append("sortBy", query.sortBy);
      if (query.sortOrder) params.append("sortOrder", query.sortOrder);

      const response = await fetch(`/api/courses?${params.toString()}`);
      const result: CourseSearchResponse = await response.json();

      if (response.ok) {
        setCourses(result.data);
        setTotal(result.total);
      } else {
        throw new Error("搜索课程失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "搜索课程失败");
    } finally {
      setLoading(false);
    }
  }, [query]);

  useEffect(() => {
    const timer = setTimeout(() => {
      fetchCourses();
    }, 300); // 防抖

    return () => clearTimeout(timer);
  }, [fetchCourses]);

  return {
    courses,
    loading,
    error,
    total,
    refetch: fetchCourses,
  };
}

// 课程搜索对话框组件
export function CourseSearchDialog({
  open,
  onOpenChange,
  onSelect,
  filterStatus = ["PUBLISHED"],
  filterLevels,
  showOnlyPublished = true,
}: CourseSearchDialogProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [levelFilter, setLevelFilter] = useState<string>("all");
  const [priceFilter, setPriceFilter] = useState<string>("all");

  const query = useMemo(
    () => ({
      search: searchTerm || undefined,
      status:
        statusFilter !== "all" &&
        filterStatus.includes(statusFilter as CourseStatus)
          ? (statusFilter as CourseStatus)
          : showOnlyPublished
            ? "PUBLISHED"
            : undefined,
      level:
        levelFilter !== "all" &&
        (!filterLevels || filterLevels.includes(levelFilter as CourseLevel))
          ? (levelFilter as CourseLevel)
          : undefined,
      isFree:
        priceFilter === "free"
          ? true
          : priceFilter === "paid"
            ? false
            : undefined,
      page: 1,
      limit: 50,
      sortBy: "updatedAt",
      sortOrder: "desc" as const,
    }),
    [
      searchTerm,
      statusFilter,
      levelFilter,
      priceFilter,
      filterStatus,
      filterLevels,
      showOnlyPublished,
    ],
  );

  const { courses, loading, error } = useCourseSearch(query);

  const handleCourseSelect = (course: Course) => {
    onSelect(course);
    onOpenChange?.(false);
  };

  // 格式化时长
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins > 0 ? `${mins}m` : ""}`;
    }
    return `${mins}m`;
  };

  // 状态样式
  const getStatusBadge = (status: CourseStatus) => {
    const variants = {
      DRAFT: { variant: "secondary" as const, label: "草稿" },
      REVIEWING: { variant: "default" as const, label: "审核中" },
      PUBLISHED: { variant: "default" as const, label: "已发布" },
      OFFLINE: { variant: "destructive" as const, label: "已下架" },
    };

    const config = variants[status];
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  // 级别样式
  const getLevelBadge = (level: CourseLevel) => {
    const colors: Record<CourseLevel, string> = {
      BEGINNER: "bg-green-100 text-green-800",
      INTERMEDIATE: "bg-blue-100 text-blue-800",
      ADVANCED: "bg-purple-100 text-purple-800",
      ALL: "bg-gray-100 text-gray-800",
    };

    const levelNames: Record<CourseLevel, string> = {
      BEGINNER: "初级",
      INTERMEDIATE: "中级",
      ADVANCED: "高级",
      ALL: "全部",
    };

    return (
      <Badge variant="outline" className={colors[level]}>
        {levelNames[level] || level}
      </Badge>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <GraduationCap className="mr-2 h-5 w-5" />
            选择课程
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 搜索和过滤 */}
          <div className="flex flex-col gap-2 md:flex-row">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索课程名称或讲师..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-[120px]">
                <SelectValue placeholder="状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                {filterStatus.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status === "PUBLISHED"
                      ? "已发布"
                      : status === "DRAFT"
                        ? "草稿"
                        : status === "REVIEWING"
                          ? "审核中"
                          : "已下架"}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {(!filterLevels || filterLevels.length > 1) && (
              <Select value={levelFilter} onValueChange={setLevelFilter}>
                <SelectTrigger className="w-full md:w-[120px]">
                  <SelectValue placeholder="级别" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部级别</SelectItem>
                  <SelectItem value="BEGINNER">初级</SelectItem>
                  <SelectItem value="INTERMEDIATE">中级</SelectItem>
                  <SelectItem value="ADVANCED">高级</SelectItem>
                </SelectContent>
              </Select>
            )}

            <Select value={priceFilter} onValueChange={setPriceFilter}>
              <SelectTrigger className="w-full md:w-[120px]">
                <SelectValue placeholder="价格" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部价格</SelectItem>
                <SelectItem value="free">免费</SelectItem>
                <SelectItem value="paid">付费</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 课程列表 */}
          <div className="border rounded-md max-h-[500px] overflow-auto">
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">搜索中...</span>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-32 text-red-500">
                {error}
              </div>
            ) : courses.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-32 text-muted-foreground">
                <GraduationCap className="h-8 w-8 mb-2" />
                <span>没有找到符合条件的课程</span>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>课程信息</TableHead>
                    <TableHead>分类/级别</TableHead>
                    <TableHead>时长</TableHead>
                    <TableHead>价格</TableHead>
                    <TableHead>数据</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {courses.map((course) => (
                    <TableRow key={course.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <img
                            src={course.cover}
                            alt={course.title}
                            className="h-12 w-16 rounded object-cover"
                          />
                          <div className="min-w-0 flex-1">
                            <p className="font-medium truncate">
                              {course.title}
                            </p>
                            {course.subtitle && (
                              <p className="text-sm text-muted-foreground truncate">
                                {course.subtitle}
                              </p>
                            )}
                            <div className="flex items-center text-xs text-muted-foreground mt-1">
                              <span>讲师: {course.instructorName}</span>
                              {course.instructorTitle && (
                                <span className="ml-2">
                                  • {course.instructorTitle}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <Badge variant="outline">
                            {course.categoryName}
                          </Badge>
                          {getLevelBadge(course.level)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="flex items-center">
                            <Clock className="mr-1 h-3 w-3" />
                            {formatDuration(course.duration)}
                          </div>
                          <div className="text-muted-foreground">
                            {course.lessonsCount} 课时
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {course.isFree ? (
                          <Badge variant="secondary">免费</Badge>
                        ) : (
                          <div className="space-y-1">
                            <div className="font-medium">¥{course.price}</div>
                            {course.originalPrice &&
                              course.originalPrice > course.price && (
                                <div className="text-xs text-muted-foreground line-through">
                                  ¥{course.originalPrice}
                                </div>
                              )}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1 text-sm">
                          <div className="flex items-center">
                            <Eye className="mr-1 h-3 w-3" />
                            {course.viewCount.toLocaleString()}
                          </div>
                          <div className="flex items-center">
                            <Users className="mr-1 h-3 w-3" />
                            {course.enrollCount}
                          </div>
                          {course.rating && (
                            <div className="flex items-center">
                              <Star className="mr-1 h-3 w-3 fill-yellow-400 text-yellow-400" />
                              {course.rating}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(course.status)}</TableCell>
                      <TableCell>
                        <Button
                          size="sm"
                          onClick={() => handleCourseSelect(course)}
                        >
                          选择
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </div>

          {courses.length > 0 && (
            <div className="text-sm text-muted-foreground text-center">
              找到 {courses.length} 个课程
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

// 主要的课程选择组件
export default function CourseSelect({
  value,
  onSelect,
  onClear,
  placeholder = "选择课程",
  disabled = false,
  filterStatus = ["PUBLISHED"],
  filterLevels,
  showOnlyPublished = true,
  className,
}: CourseSelectProps) {
  const [open, setOpen] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);

  const handleSelect = (course: Course) => {
    onSelect(course);
    setOpen(false);
  };

  const handleClear = () => {
    onClear?.();
    setOpen(false);
  };

  // 格式化时长
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins > 0 ? `${mins}m` : ""}`;
    }
    return `${mins}m`;
  };

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn("w-full justify-between min-h-[60px] h-auto py-2", className)}
            disabled={disabled}
          >
            {value ? (
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                <img
                  src={value.cover}
                  alt={value.title}
                  className="h-10 w-14 rounded object-cover flex-shrink-0"
                />
                <div className="min-w-0 flex-1 text-left">
                  <div className="font-medium truncate leading-tight mb-1">
                    {value.title}
                  </div>
                  <div className="flex items-center space-x-2 text-xs text-muted-foreground leading-tight">
                    <span>{value.instructorName}</span>
                    <span>•</span>
                    <span>{formatDuration(value.duration)}</span>
                    <span>•</span>
                    <span>{value.isFree ? "免费" : `¥${value.price}`}</span>
                  </div>
                </div>
              </div>
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
            <div className="flex items-center space-x-1 flex-shrink-0">
              {value && onClear && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleClear();
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0">
          <div className="p-2">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => {
                setOpen(false);
                setSearchOpen(true);
              }}
            >
              <Search className="mr-2 h-4 w-4" />
              搜索更多课程
            </Button>
          </div>
        </PopoverContent>
      </Popover>

      {/* 课程搜索对话框 */}
      <CourseSearchDialog
        open={searchOpen}
        onOpenChange={setSearchOpen}
        onSelect={handleSelect}
        filterStatus={filterStatus}
        filterLevels={filterLevels}
        showOnlyPublished={showOnlyPublished}
      />
    </>
  );
}
