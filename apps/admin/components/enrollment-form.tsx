"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Textarea } from "@workspace/ui/components/textarea";
import { Badge } from "@workspace/ui/components/badge";
import { Switch } from "@workspace/ui/components/switch";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@workspace/ui/components/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@workspace/ui/components/dialog";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@workspace/ui/components/alert";
import {
  User,
  GraduationCap,
  DollarSign,
  CreditCard,
  AlertCircle,
  Info,
  Loader2,
} from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";

import UserSearchSelect, { type User as UserType } from "./user-search-select";
import CourseSelect, { type Course as CourseType } from "./course-select";

// 表单验证 schema
const enrollmentFormSchema = z.object({
  userId: z.string().min(1, "请选择用户"),
  courseId: z.string().min(1, "请选择课程"),
  isPaid: z.boolean().default(false),
  paidAmount: z.number().optional(),
  paymentMethod: z.string().optional(),
  paymentNote: z.string().optional(),
  enrollmentNote: z.string().optional(),
  sendNotification: z.boolean().default(true),
});

type EnrollmentFormData = z.infer<typeof enrollmentFormSchema>;

// 支付方式选项
const PAYMENT_METHODS = [
  { value: "cash", label: "现金" },
  { value: "bank_transfer", label: "银行转账" },
  { value: "alipay", label: "支付宝" },
  { value: "wechat", label: "微信支付" },
  { value: "credit_card", label: "信用卡" },
  { value: "other", label: "其他" },
];

// Props 接口
export interface EnrollmentFormProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (enrollment: any) => void;
  onCancel?: () => void;
  defaultUser?: UserType;
  defaultCourse?: CourseType;
  mode?: "create" | "edit";
  enrollmentId?: string;
}

export interface EnrollmentFormDialogProps extends EnrollmentFormProps {
  trigger?: React.ReactNode;
}

// 报名创建/编辑表单组件
export function EnrollmentForm({
  onSuccess,
  onCancel,
  defaultUser,
  defaultCourse,
  mode = "create",
  enrollmentId,
}: EnrollmentFormProps) {
  const [selectedUser, setSelectedUser] = useState<UserType | undefined>(
    defaultUser,
  );
  const [selectedCourse, setSelectedCourse] = useState<CourseType | undefined>(
    defaultCourse,
  );
  const [loading, setLoading] = useState(false);
  const [existingEnrollment, setExistingEnrollment] = useState<any>(null);

  const form = useForm<EnrollmentFormData>({
    resolver: zodResolver(enrollmentFormSchema),
    defaultValues: {
      userId: defaultUser?.id || "",
      courseId: defaultCourse?.id || "",
      isPaid: false,
      sendNotification: true,
    },
  });

  const {
    watch,
    setValue,
    formState: { errors },
  } = form;
  const isPaid = watch("isPaid");
  const userId = watch("userId");
  const courseId = watch("courseId");

  // 检查是否已经报名
  useEffect(() => {
    const checkExistingEnrollment = async () => {
      if (!userId || !courseId || mode === "edit") return;

      try {
        const response = await fetch(
          `/api/courses/enrollments/check?userId=${userId}&courseId=${courseId}`,
        );
        const result = await response.json();

        if (result.exists) {
          setExistingEnrollment(result.enrollment);
        } else {
          setExistingEnrollment(null);
        }
      } catch (error) {
        console.error("检查报名状态失败:", error);
      }
    };

    checkExistingEnrollment();
  }, [userId, courseId, mode]);

  // 用户选择处理
  const handleUserSelect = (user: UserType) => {
    setSelectedUser(user);
    setValue("userId", user.id);
  };

  // 课程选择处理
  const handleCourseSelect = (course: CourseType) => {
    setSelectedCourse(course);
    setValue("courseId", course.id);

    // 如果是付费课程，自动设置价格
    if (!course.isFree) {
      setValue("paidAmount", course.price);
    } else {
      setValue("paidAmount", 0);
      setValue("isPaid", true); // 免费课程自动标记为已支付
    }
  };

  // 表单提交
  const onSubmit = async (data: EnrollmentFormData) => {
    if (!selectedUser || !selectedCourse) {
      toast.error("请选择用户和课程");
      return;
    }

    // 检查是否已经报名
    if (existingEnrollment && mode === "create") {
      toast.error("该用户已经报名了这个课程");
      return;
    }

    setLoading(true);

    try {
      const endpoint =
        mode === "create"
          ? "/api/courses/enrollments"
          : `/api/courses/enrollments/${enrollmentId}`;

      const method = mode === "create" ? "POST" : "PUT";

      const response = await fetch(endpoint, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          // 如果是免费课程，强制设置为已支付
          isPaid: selectedCourse.isFree ? true : data.isPaid,
          paidAmount: selectedCourse.isFree ? 0 : data.paidAmount,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(mode === "create" ? "报名创建成功" : "报名更新成功");
        onSuccess?.(result.data);
      } else {
        toast.error(
          result.message || (mode === "create" ? "创建失败" : "更新失败"),
        );
      }
    } catch (error) {
      toast.error("操作失败");
    } finally {
      setLoading(false);
    }
  };

  // 计算应付金额
  const calculateAmount = () => {
    if (!selectedCourse) return 0;
    if (selectedCourse.isFree) return 0;
    return selectedCourse.price;
  };

  return (
    <div className="space-y-6">
      {/* 已存在报名提醒 */}
      {existingEnrollment && mode === "create" && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>用户已报名</AlertTitle>
          <AlertDescription>
            该用户已经报名了这个课程，报名时间：
            {format(new Date(existingEnrollment.enrolledAt), "PPP", {
              locale: zhCN,
            })}
            {existingEnrollment.isPaid && (
              <Badge variant="default" className="ml-2">
                已支付
              </Badge>
            )}
          </AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* 用户选择 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center">
                  <User className="mr-2 h-4 w-4" />
                  选择用户
                </CardTitle>
                <CardDescription>
                  选择要报名的用户，支持快速创建新用户
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="userId"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <UserSearchSelect
                          value={selectedUser}
                          onSelect={handleUserSelect}
                          onClear={() => {
                            setSelectedUser(undefined);
                            setValue("userId", "");
                          }}
                          placeholder="搜索用户或创建新用户"
                          filterRoles={[
                            "COMMUNITY_USER",
                            "GUEST",
                            "PLANNER",
                            "TENANT_ADMIN",
                          ]}
                          allowCreate={true}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 显示选中用户信息 */}
                {selectedUser && (
                  <div className="mt-3 p-3 bg-muted/50 rounded-lg">
                    <div className="flex items-center space-x-2">
                      {selectedUser.avatar ? (
                        <img
                          src={selectedUser.avatar}
                          alt={selectedUser.name}
                          className="h-8 w-8 rounded-full"
                        />
                      ) : (
                        <div className="h-8 w-8 bg-primary/10 rounded-full flex items-center justify-center">
                          <User className="h-4 w-4" />
                        </div>
                      )}
                      <div className="flex-1 min-w-0">
                        <p className="font-medium truncate">
                          {selectedUser.name}
                        </p>
                        <p className="text-sm text-muted-foreground truncate">
                          {selectedUser.email}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 课程选择 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center">
                  <GraduationCap className="mr-2 h-4 w-4" />
                  选择课程
                </CardTitle>
                <CardDescription>选择要报名的课程</CardDescription>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="courseId"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <CourseSelect
                          value={selectedCourse}
                          onSelect={handleCourseSelect}
                          onClear={() => {
                            setSelectedCourse(undefined);
                            setValue("courseId", "");
                          }}
                          placeholder="搜索并选择课程"
                          showOnlyPublished={true}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 显示选中课程信息 */}
                {selectedCourse && (
                  <div className="mt-3 p-3 bg-muted/50 rounded-lg">
                    <div className="flex items-start space-x-3">
                      <img
                        src={selectedCourse.cover}
                        alt={selectedCourse.title}
                        className="h-12 w-16 rounded object-cover flex-shrink-0"
                      />
                      <div className="flex-1 min-w-0">
                        <p className="font-medium truncate">
                          {selectedCourse.title}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          讲师: {selectedCourse.instructorName}
                        </p>
                        <div className="flex items-center space-x-2 mt-1">
                          {selectedCourse.isFree ? (
                            <Badge variant="secondary">免费课程</Badge>
                          ) : (
                            <Badge variant="default">
                              ¥{selectedCourse.price}
                            </Badge>
                          )}
                          <Badge variant="outline">
                            {selectedCourse.lessonsCount} 课时
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 支付信息 */}
          {selectedCourse && !selectedCourse.isFree && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center">
                  <CreditCard className="mr-2 h-4 w-4" />
                  支付信息
                </CardTitle>
                <CardDescription>设置课程的支付状态和相关信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 支付状态 */}
                <FormField
                  control={form.control}
                  name="isPaid"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel>支付状态</FormLabel>
                        <FormDescription>
                          标记此报名是否已完成支付
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {/* 支付金额 */}
                <div className="grid gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="paidAmount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>支付金额</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <DollarSign className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                            <Input
                              type="number"
                              placeholder="0.00"
                              className="pl-8"
                              {...field}
                              onChange={(e) =>
                                field.onChange(parseFloat(e.target.value) || 0)
                              }
                            />
                          </div>
                        </FormControl>
                        <FormDescription>
                          课程原价: ¥{selectedCourse.price}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="paymentMethod"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>支付方式</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="选择支付方式" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {PAYMENT_METHODS.map((method) => (
                              <SelectItem
                                key={method.value}
                                value={method.value}
                              >
                                {method.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* 支付备注 */}
                <FormField
                  control={form.control}
                  name="paymentNote"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>支付备注</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="支付相关备注信息..."
                          className="resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        可记录支付流水号、转账记录等信息
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          )}

          {/* 其他设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center">
                <Info className="mr-2 h-4 w-4" />
                其他设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 报名备注 */}
              <FormField
                control={form.control}
                name="enrollmentNote"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>报名备注</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="报名相关备注信息..."
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      可记录报名来源、特殊要求等信息
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 通知设置 */}
              <FormField
                control={form.control}
                name="sendNotification"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                    <div className="space-y-0.5">
                      <FormLabel>发送通知</FormLabel>
                      <FormDescription>向用户发送报名成功通知</FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={loading || !selectedUser || !selectedCourse}
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {mode === "create" ? "创建报名" : "更新报名"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}

// 带对话框的报名表单组件
export function EnrollmentFormDialog({
  trigger,
  open,
  onOpenChange,
  ...props
}: EnrollmentFormDialogProps) {
  const [isOpen, setIsOpen] = useState(open || false);

  const handleOpenChange = (newOpen: boolean) => {
    setIsOpen(newOpen);
    onOpenChange?.(newOpen);
  };

  const handleSuccess = (enrollment: any) => {
    setIsOpen(false);
    props.onSuccess?.(enrollment);
  };

  const handleCancel = () => {
    setIsOpen(false);
    props.onCancel?.();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {props.mode === "edit" ? "编辑报名" : "创建课程报名"}
          </DialogTitle>
          <DialogDescription>
            {props.mode === "edit"
              ? "修改报名信息和支付状态"
              : "为用户创建新的课程报名记录"}
          </DialogDescription>
        </DialogHeader>
        <EnrollmentForm
          {...props}
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </DialogContent>
    </Dialog>
  );
}

export default EnrollmentForm;
