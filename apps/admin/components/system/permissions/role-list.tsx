"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Badge } from "@workspace/ui/components/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@workspace/ui/components/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { Label } from "@workspace/ui/components/label";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@workspace/ui/components/dropdown-menu";
import { Checkbox } from "@workspace/ui/components/checkbox";
import {
  Plus,
  Edit,
  Trash2,
  MoreVertical,
  Shield,
  Users,
  Crown,
  User<PERSON>heck,
  Setting<PERSON>,
} from "lucide-react";
import { toast } from "sonner";

// 角色类型图标映射
const roleIcons = {
  SUPER_ADMIN: Crown,
  ADMIN: Shield,
  OPERATOR: UserCheck,
  TENANT_ADMIN: Users,
  default: Settings,
};

// 模拟权限数据
const mockPermissions = [
  {
    id: "1",
    code: "system:permission:view",
    name: "权限查看",
    module: "system",
  },
  {
    id: "2",
    code: "system:permission:create",
    name: "权限创建",
    module: "system",
  },
  {
    id: "3",
    code: "system:permission:edit",
    name: "权限编辑",
    module: "system",
  },
  {
    id: "4",
    code: "system:permission:delete",
    name: "权限删除",
    module: "system",
  },
  { id: "5", code: "system:role:view", name: "角色查看", module: "system" },
  { id: "6", code: "system:role:create", name: "角色创建", module: "system" },
  { id: "7", code: "system:role:edit", name: "角色编辑", module: "system" },
  { id: "8", code: "system:role:delete", name: "角色删除", module: "system" },
  { id: "9", code: "user:profile:view", name: "用户资料查看", module: "user" },
  { id: "10", code: "user:profile:edit", name: "用户资料编辑", module: "user" },
];

// 角色数据接口
interface Role {
  id: string;
  code: string;
  name: string;
  description?: string;
  isSystem: boolean;
  permissionIds: string[];
  userCount: number;
  createdAt: string;
  updatedAt: string;
}

// 角色表单数据接口
interface RoleFormData {
  code: string;
  name: string;
  description: string;
  permissionIds: string[];
}

// 模拟角色数据
const mockRoles: Role[] = [
  {
    id: "1",
    code: "SUPER_ADMIN",
    name: "超级管理员",
    description: "系统最高权限，拥有所有功能权限",
    isSystem: true,
    permissionIds: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"],
    userCount: 1,
    createdAt: "2024-01-15T08:00:00Z",
    updatedAt: "2024-01-15T08:00:00Z",
  },
  {
    id: "2",
    code: "ADMIN",
    name: "系统管理员",
    description: "平台管理员，拥有大部分管理权限",
    isSystem: true,
    permissionIds: ["1", "5", "9", "10"],
    userCount: 3,
    createdAt: "2024-01-15T08:00:00Z",
    updatedAt: "2024-01-15T08:00:00Z",
  },
  {
    id: "3",
    code: "OPERATOR",
    name: "运营人员",
    description: "负责日常运营管理",
    isSystem: false,
    permissionIds: ["1", "5", "9"],
    userCount: 8,
    createdAt: "2024-01-15T08:00:00Z",
    updatedAt: "2024-01-15T08:00:00Z",
  },
  {
    id: "4",
    code: "TENANT_ADMIN",
    name: "租户管理员",
    description: "租户级别的管理员权限",
    isSystem: false,
    permissionIds: ["9", "10"],
    userCount: 15,
    createdAt: "2024-01-15T08:00:00Z",
    updatedAt: "2024-01-15T08:00:00Z",
  },
];

interface RoleListProps {
  searchQuery: string;
  canManage: boolean;
}

export function RoleList({ searchQuery, canManage }: RoleListProps) {
  const [roles, setRoles] = useState<Role[]>(mockRoles);
  const [permissions] = useState(mockPermissions);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [formData, setFormData] = useState<RoleFormData>({
    code: "",
    name: "",
    description: "",
    permissionIds: [],
  });

  // 过滤角色列表
  const filteredRoles = roles.filter((role) => {
    const matchesSearch =
      role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      role.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
      role.description?.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesSearch;
  });

  // 重置表单
  const resetForm = () => {
    setFormData({
      code: "",
      name: "",
      description: "",
      permissionIds: [],
    });
  };

  // 处理创建角色
  const handleCreateRole = () => {
    // 验证表单
    if (!formData.code || !formData.name) {
      toast.error("角色代码和名称为必填项");
      return;
    }

    // 检查角色代码是否已存在
    if (roles.some((r) => r.code === formData.code)) {
      toast.error("角色代码已存在");
      return;
    }

    const newRole: Role = {
      id: Date.now().toString(),
      ...formData,
      isSystem: false,
      userCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    setRoles([...roles, newRole]);
    setIsCreateDialogOpen(false);
    resetForm();

    toast.success(`角色 "${formData.name}" 已创建`);
  };

  // 处理编辑角色
  const handleEditRole = () => {
    if (!editingRole) return;

    // 验证表单
    if (!formData.code || !formData.name) {
      toast.error("角色代码和名称为必填项");
      return;
    }

    // 检查角色代码是否已存在（排除当前编辑的角色）
    if (
      roles.some((r) => r.code === formData.code && r.id !== editingRole.id)
    ) {
      toast.error("角色代码已存在");
      return;
    }

    const updatedRoles = roles.map((role) =>
      role.id === editingRole.id
        ? { ...role, ...formData, updatedAt: new Date().toISOString() }
        : role,
    );

    setRoles(updatedRoles);
    setIsEditDialogOpen(false);
    setEditingRole(null);
    resetForm();

    toast.success(`角色 "${formData.name}" 已更新`);
  };

  // 处理删除角色
  const handleDeleteRole = (role: Role) => {
    if (role.isSystem) {
      toast.error("系统内置角色不能删除");
      return;
    }

    if (role.userCount > 0) {
      toast.error("该角色下还有用户，请先移除用户后再删除");
      return;
    }

    setRoles(roles.filter((r) => r.id !== role.id));
    toast.success(`角色 "${role.name}" 已删除`);
  };

  // 开始编辑
  const startEdit = (role: Role) => {
    if (role.isSystem && !canManage) {
      toast.error("系统内置角色需要更高权限才能编辑");
      return;
    }

    setEditingRole(role);
    setFormData({
      code: role.code,
      name: role.name,
      description: role.description || "",
      permissionIds: role.permissionIds || [],
    });
    setIsEditDialogOpen(true);
  };

  // 获取角色图标
  const getRoleIcon = (code: string) => {
    const IconComponent =
      roleIcons[code as keyof typeof roleIcons] || roleIcons.default;
    return <IconComponent className="h-4 w-4" />;
  };

  // 处理权限选择
  const handlePermissionToggle = (permissionId: string) => {
    const newPermissionIds = formData.permissionIds.includes(permissionId)
      ? formData.permissionIds.filter((id) => id !== permissionId)
      : [...formData.permissionIds, permissionId];

    setFormData({ ...formData, permissionIds: newPermissionIds });
  };

  // 获取角色拥有的权限数量
  const getPermissionCount = (role: Role) => {
    return role.permissionIds?.length || 0;
  };

  return (
    <div className="space-y-4">
      {/* 操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground">
            共 {filteredRoles.length} 个角色
          </span>
        </div>

        {canManage && (
          <Dialog
            open={isCreateDialogOpen}
            onOpenChange={setIsCreateDialogOpen}
          >
            <DialogTrigger asChild>
              <Button onClick={() => resetForm()}>
                <Plus className="mr-2 h-4 w-4" />
                新建角色
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>创建新角色</DialogTitle>
                <DialogDescription>
                  创建一个新的系统角色，并为其分配相应的权限。
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="code" className="text-right">
                    角色代码 *
                  </Label>
                  <Input
                    id="code"
                    placeholder="例如: CUSTOM_ADMIN"
                    value={formData.code}
                    onChange={(e) =>
                      setFormData({ ...formData, code: e.target.value })
                    }
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    角色名称 *
                  </Label>
                  <Input
                    id="name"
                    placeholder="例如: 自定义管理员"
                    value={formData.name}
                    onChange={(e) =>
                      setFormData({ ...formData, name: e.target.value })
                    }
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="description" className="text-right">
                    描述
                  </Label>
                  <Textarea
                    id="description"
                    placeholder="角色描述（可选）"
                    value={formData.description}
                    onChange={(e) =>
                      setFormData({ ...formData, description: e.target.value })
                    }
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-start gap-4">
                  <Label className="text-right pt-2">权限配置</Label>
                  <div className="col-span-3 space-y-2">
                    <div className="max-h-40 overflow-y-auto border rounded-md p-2">
                      {permissions.map((permission) => (
                        <div
                          key={permission.id}
                          className="flex items-center space-x-2 py-1"
                        >
                          <Checkbox
                            id={`permission-${permission.id}`}
                            checked={formData.permissionIds.includes(
                              permission.id,
                            )}
                            onCheckedChange={() =>
                              handlePermissionToggle(permission.id)
                            }
                          />
                          <label
                            htmlFor={`permission-${permission.id}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                          >
                            {permission.name}
                          </label>
                          <code className="text-xs bg-muted px-1 py-0.5 rounded">
                            {permission.code}
                          </code>
                        </div>
                      ))}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      已选择 {formData.permissionIds.length} 个权限
                    </p>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                >
                  取消
                </Button>
                <Button onClick={handleCreateRole}>创建角色</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* 角色列表 */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>角色信息</TableHead>
              <TableHead>角色代码</TableHead>
              <TableHead>权限数量</TableHead>
              <TableHead>用户数量</TableHead>
              <TableHead>创建时间</TableHead>
              {canManage && <TableHead className="text-right">操作</TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredRoles.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={canManage ? 6 : 5}
                  className="text-center text-muted-foreground py-8"
                >
                  没有找到符合条件的角色
                </TableCell>
              </TableRow>
            ) : (
              filteredRoles.map((role) => (
                <TableRow key={role.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      {getRoleIcon(role.code)}
                      <div>
                        <div className="font-medium flex items-center space-x-2">
                          {role.name}
                          {role.isSystem && (
                            <Badge variant="secondary" className="text-xs">
                              系统角色
                            </Badge>
                          )}
                        </div>
                        {role.description && (
                          <div className="text-sm text-muted-foreground mt-1">
                            {role.description}
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <code className="text-sm bg-muted px-2 py-1 rounded">
                      {role.code}
                    </code>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {getPermissionCount(role)} 个权限
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">{role.userCount}</span>
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {new Date(role.createdAt).toLocaleDateString("zh-CN")}
                  </TableCell>
                  {canManage && (
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => startEdit(role)}>
                            <Edit className="mr-2 h-4 w-4" />
                            编辑角色
                          </DropdownMenuItem>
                          {!role.isSystem && (
                            <>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                className="text-destructive"
                                onClick={() => handleDeleteRole(role)}
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                删除角色
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  )}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* 编辑角色对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>编辑角色</DialogTitle>
            <DialogDescription>修改角色信息和权限配置。</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-code" className="text-right">
                角色代码 *
              </Label>
              <Input
                id="edit-code"
                placeholder="例如: CUSTOM_ADMIN"
                value={formData.code}
                onChange={(e) =>
                  setFormData({ ...formData, code: e.target.value })
                }
                className="col-span-3"
                disabled={editingRole?.isSystem}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-name" className="text-right">
                角色名称 *
              </Label>
              <Input
                id="edit-name"
                placeholder="例如: 自定义管理员"
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-description" className="text-right">
                描述
              </Label>
              <Textarea
                id="edit-description"
                placeholder="角色描述（可选）"
                value={formData.description}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label className="text-right pt-2">权限配置</Label>
              <div className="col-span-3 space-y-2">
                <div className="max-h-40 overflow-y-auto border rounded-md p-2">
                  {permissions.map((permission) => (
                    <div
                      key={permission.id}
                      className="flex items-center space-x-2 py-1"
                    >
                      <Checkbox
                        id={`edit-permission-${permission.id}`}
                        checked={formData.permissionIds.includes(permission.id)}
                        onCheckedChange={() =>
                          handlePermissionToggle(permission.id)
                        }
                      />
                      <label
                        htmlFor={`edit-permission-${permission.id}`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                      >
                        {permission.name}
                      </label>
                      <code className="text-xs bg-muted px-1 py-0.5 rounded">
                        {permission.code}
                      </code>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-muted-foreground">
                  已选择 {formData.permissionIds.length} 个权限
                </p>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
            >
              取消
            </Button>
            <Button onClick={handleEditRole}>保存更改</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 统计信息 */}
      <div className="text-sm text-muted-foreground">
        共 {filteredRoles.length} 个角色
        {searchQuery && ` - 搜索: "${searchQuery}"`}
      </div>
    </div>
  );
}
