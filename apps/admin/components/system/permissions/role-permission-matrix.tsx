"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Checkbox } from "@workspace/ui/components/checkbox";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  Shield,
  Users,
  Settings,
  Database,
  FileText,
  DollarSign,
  Calendar,
  MessageSquare,
  BarChart,
  Save,
  RotateCcw,
} from "lucide-react";
import { toast } from "sonner";
import React from "react";

// 权限模块图标映射
const moduleIcons = {
  system: Settings,
  user: Users,
  content: FileText,
  finance: DollarSign,
  data: Database,
  schedule: Calendar,
  communication: MessageSquare,
  analytics: <PERSON><PERSON><PERSON>,
  default: Shield,
};

// 模拟权限数据
const mockPermissions = [
  {
    id: "1",
    code: "system:permission:view",
    name: "权限查看",
    module: "system",
  },
  {
    id: "2",
    code: "system:permission:create",
    name: "权限创建",
    module: "system",
  },
  {
    id: "3",
    code: "system:permission:edit",
    name: "权限编辑",
    module: "system",
  },
  {
    id: "4",
    code: "system:permission:delete",
    name: "权限删除",
    module: "system",
  },
  { id: "5", code: "system:role:view", name: "角色查看", module: "system" },
  { id: "6", code: "system:role:create", name: "角色创建", module: "system" },
  { id: "7", code: "system:role:edit", name: "角色编辑", module: "system" },
  { id: "8", code: "system:role:delete", name: "角色删除", module: "system" },
  { id: "9", code: "user:profile:view", name: "用户资料查看", module: "user" },
  { id: "10", code: "user:profile:edit", name: "用户资料编辑", module: "user" },
  { id: "11", code: "user:list:view", name: "用户列表查看", module: "user" },
  { id: "12", code: "user:create", name: "用户创建", module: "user" },
  { id: "13", code: "content:post:view", name: "内容查看", module: "content" },
  {
    id: "14",
    code: "content:post:create",
    name: "内容创建",
    module: "content",
  },
  { id: "15", code: "content:post:edit", name: "内容编辑", module: "content" },
  {
    id: "16",
    code: "finance:payment:view",
    name: "支付查看",
    module: "finance",
  },
  {
    id: "17",
    code: "finance:invoice:create",
    name: "发票创建",
    module: "finance",
  },
  {
    id: "18",
    code: "analytics:report:view",
    name: "报表查看",
    module: "analytics",
  },
];

// 模拟角色数据
const mockRoles = [
  {
    id: "1",
    code: "SUPER_ADMIN",
    name: "超级管理员",
    isSystem: true,
    permissionIds: [
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "10",
      "11",
      "12",
      "13",
      "14",
      "15",
      "16",
      "17",
      "18",
    ],
  },
  {
    id: "2",
    code: "ADMIN",
    name: "系统管理员",
    isSystem: true,
    permissionIds: ["1", "5", "9", "11", "13", "16", "18"],
  },
  {
    id: "3",
    code: "OPERATOR",
    name: "运营人员",
    isSystem: false,
    permissionIds: ["1", "5", "9", "13", "14", "15"],
  },
  {
    id: "4",
    code: "TENANT_ADMIN",
    name: "租户管理员",
    isSystem: false,
    permissionIds: ["9", "10", "13", "14"],
  },
];

// 可用模块列表
const availableModules = [
  { value: "system", label: "系统管理" },
  { value: "user", label: "用户管理" },
  { value: "content", label: "内容管理" },
  { value: "finance", label: "财务管理" },
  { value: "analytics", label: "分析统计" },
];

interface Permission {
  id: string;
  code: string;
  name: string;
  module: string;
}

interface Role {
  id: string;
  code: string;
  name: string;
  isSystem: boolean;
  permissionIds: string[];
}

interface RolePermissionMatrixProps {
  canManage: boolean;
}

export function RolePermissionMatrix({ canManage }: RolePermissionMatrixProps) {
  const [roles, setRoles] = useState<Role[]>(mockRoles);
  const [permissions] = useState<Permission[]>(mockPermissions);
  const [selectedModule, setSelectedModule] = useState<string>("all");
  const [hasChanges, setHasChanges] = useState(false);
  const [originalRolePermissions, setOriginalRolePermissions] = useState<
    Record<string, string[]>
  >({});

  // 初始化原始权限状态
  useEffect(() => {
    const original: Record<string, string[]> = {};
    roles.forEach((role) => {
      original[role.id] = [...role.permissionIds];
    });
    setOriginalRolePermissions(original);
  }, []);

  // 过滤权限列表
  const filteredPermissions = permissions.filter((permission) => {
    return selectedModule === "all" || permission.module === selectedModule;
  });

  // 按模块分组权限
  const groupedPermissions = filteredPermissions.reduce(
    (groups, permission) => {
      const module = permission.module;
      if (!groups[module]) {
        groups[module] = [];
      }
      groups[module].push(permission);
      return groups;
    },
    {} as Record<string, Permission[]>,
  );

  // 检查角色是否拥有某个权限
  const hasPermission = (roleId: string, permissionId: string): boolean => {
    const role = roles.find((r) => r.id === roleId);
    return role?.permissionIds.includes(permissionId) || false;
  };

  // 切换权限状态
  const togglePermission = (roleId: string, permissionId: string) => {
    if (!canManage) return;

    const role = roles.find((r) => r.id === roleId);
    if (!role) return;

    // 系统角色需要特殊权限才能修改
    if (role.isSystem && role.code === "SUPER_ADMIN") {
      toast.error("超级管理员角色权限不能修改");
      return;
    }

    const updatedRoles = roles.map((r) => {
      if (r.id === roleId) {
        const newPermissionIds = r.permissionIds.includes(permissionId)
          ? r.permissionIds.filter((id) => id !== permissionId)
          : [...r.permissionIds, permissionId];
        return { ...r, permissionIds: newPermissionIds };
      }
      return r;
    });

    setRoles(updatedRoles);
    setHasChanges(true);
  };

  // 保存更改
  const handleSave = () => {
    // 这里应该调用API保存权限配置
    console.log("保存权限配置:", roles);

    // 更新原始状态
    const newOriginal: Record<string, string[]> = {};
    roles.forEach((role) => {
      newOriginal[role.id] = [...role.permissionIds];
    });
    setOriginalRolePermissions(newOriginal);
    setHasChanges(false);

    toast.success("权限配置已更新");
  };

  // 重置更改
  const handleReset = () => {
    const resetRoles = roles.map((role) => ({
      ...role,
      permissionIds: [...(originalRolePermissions[role.id] || [])],
    }));
    setRoles(resetRoles);
    setHasChanges(false);

    toast.success("权限配置已恢复到保存前的状态");
  };

  // 获取模块图标
  const getModuleIcon = (module: string) => {
    const IconComponent =
      moduleIcons[module as keyof typeof moduleIcons] || moduleIcons.default;
    return <IconComponent className="h-4 w-4" />;
  };

  // 获取模块标签
  const getModuleLabel = (module: string) => {
    const moduleConfig = availableModules.find((m) => m.value === module);
    return moduleConfig?.label || module;
  };

  // 计算角色的权限统计
  const getRolePermissionStats = (role: Role) => {
    const totalPermissions =
      selectedModule === "all"
        ? permissions.length
        : permissions.filter((p) => p.module === selectedModule).length;
    const rolePermissions =
      selectedModule === "all"
        ? role.permissionIds.length
        : role.permissionIds.filter((id) => {
            const permission = permissions.find((p) => p.id === id);
            return permission && permission.module === selectedModule;
          }).length;

    return { total: totalPermissions, granted: rolePermissions };
  };

  return (
    <div className="space-y-6">
      {/* 操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Select value={selectedModule} onValueChange={setSelectedModule}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="选择模块" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有模块</SelectItem>
              {availableModules.map((module) => (
                <SelectItem key={module.value} value={module.value}>
                  {module.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="text-sm text-muted-foreground">
            {selectedModule === "all"
              ? `显示所有 ${permissions.length} 个权限`
              : `显示 ${getModuleLabel(selectedModule)} 模块的 ${filteredPermissions.length} 个权限`}
          </div>
        </div>

        {canManage && hasChanges && (
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={handleReset}>
              <RotateCcw className="mr-2 h-4 w-4" />
              重置
            </Button>
            <Button size="sm" onClick={handleSave}>
              <Save className="mr-2 h-4 w-4" />
              保存更改
            </Button>
          </div>
        )}
      </div>

      {/* 角色权限统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {roles.map((role) => {
          const stats = getRolePermissionStats(role);
          const percentage =
            stats.total > 0
              ? Math.round((stats.granted / stats.total) * 100)
              : 0;

          return (
            <Card key={role.id}>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center space-x-2">
                  <span>{role.name}</span>
                  {role.isSystem && (
                    <Badge variant="secondary" className="text-xs">
                      系统
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats.granted}/{stats.total}
                </div>
                <p className="text-xs text-muted-foreground">
                  权限覆盖率 {percentage}%
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* 权限矩阵表格 */}
      <Card>
        <CardHeader>
          <CardTitle>权限矩阵</CardTitle>
          <CardDescription>
            查看和管理不同角色的权限分配。点击复选框可以切换权限状态。
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-80">权限</TableHead>
                  {roles.map((role) => (
                    <TableHead key={role.id} className="text-center min-w-32">
                      <div className="space-y-1">
                        <div className="font-medium">{role.name}</div>
                        {role.isSystem && (
                          <Badge variant="secondary" className="text-xs">
                            系统角色
                          </Badge>
                        )}
                      </div>
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {selectedModule === "all"
                  ? // 按模块分组显示
                    Object.entries(groupedPermissions).map(
                      ([module, modulePermissions]) => (
                        <React.Fragment key={module}>
                          {/* 模块分组标题行 */}
                          <TableRow className="bg-muted/50">
                            <TableCell className="font-medium">
                              <div className="flex items-center space-x-2">
                                {getModuleIcon(module)}
                                <span>{getModuleLabel(module)}</span>
                                <Badge variant="outline" className="text-xs">
                                  {modulePermissions.length} 个权限
                                </Badge>
                              </div>
                            </TableCell>
                            {roles.map((role) => (
                              <TableCell key={role.id} />
                            ))}
                          </TableRow>
                          {/* 权限行 */}
                          {modulePermissions.map((permission) => (
                            <TableRow key={permission.id}>
                              <TableCell>
                                <div className="pl-6">
                                  <div className="font-medium">
                                    {permission.name}
                                  </div>
                                  <code className="text-xs text-muted-foreground bg-muted px-1 py-0.5 rounded">
                                    {permission.code}
                                  </code>
                                </div>
                              </TableCell>
                              {roles.map((role) => (
                                <TableCell
                                  key={role.id}
                                  className="text-center"
                                >
                                  <Checkbox
                                    checked={hasPermission(
                                      role.id,
                                      permission.id,
                                    )}
                                    onCheckedChange={() =>
                                      togglePermission(role.id, permission.id)
                                    }
                                    disabled={
                                      !canManage ||
                                      (role.isSystem &&
                                        role.code === "SUPER_ADMIN")
                                    }
                                  />
                                </TableCell>
                              ))}
                            </TableRow>
                          ))}
                        </React.Fragment>
                      ),
                    )
                  : // 单模块显示
                    filteredPermissions.map((permission) => (
                      <TableRow key={permission.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{permission.name}</div>
                            <code className="text-xs text-muted-foreground bg-muted px-1 py-0.5 rounded">
                              {permission.code}
                            </code>
                          </div>
                        </TableCell>
                        {roles.map((role) => (
                          <TableCell key={role.id} className="text-center">
                            <Checkbox
                              checked={hasPermission(role.id, permission.id)}
                              onCheckedChange={() =>
                                togglePermission(role.id, permission.id)
                              }
                              disabled={
                                !canManage ||
                                (role.isSystem && role.code === "SUPER_ADMIN")
                              }
                            />
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* 说明信息 */}
      <div className="text-sm text-muted-foreground space-y-1">
        <p>• 系统角色的权限配置需要更高级别的权限才能修改</p>
        <p>• 超级管理员角色拥有所有权限，不能修改</p>
        <p>• 修改权限后请及时保存，避免数据丢失</p>
      </div>
    </div>
  );
}
