"use client";

import { useMemo } from "react";
import { Badge } from "@workspace/ui/components/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { Shield } from "lucide-react";
import {
  PERMISSIONS,
  PERMISSION_DESCRIPTIONS,
  MODULE_INFO,
} from "@/config/permissions.config";

interface PermissionListProps {
  searchQuery: string;
}

interface PermissionItem {
  module: string;
  moduleName: string;
  resource: string;
  action: string;
  code: string;
  name: string;
  description: string;
}

export function PermissionList({ searchQuery }: PermissionListProps) {
  // 将 PERMISSIONS 配置转换为扁平化的列表
  const permissionList = useMemo(() => {
    const list: PermissionItem[] = [];

    Object.entries(PERMISSIONS).forEach(([moduleKey, moduleData]) => {
      const moduleInfo = MODULE_INFO[moduleKey as keyof typeof MODULE_INFO];
      const moduleName = moduleInfo?.name || moduleKey;

      Object.entries(moduleData).forEach(([resourceKey, resourceData]) => {
        Object.entries(resourceData).forEach(([actionKey, code]) => {
          const permissionInfo = PERMISSION_DESCRIPTIONS[code as string] || {
            name: actionKey,
            description: "",
          };

          list.push({
            module: moduleKey,
            moduleName,
            resource: resourceKey,
            action: actionKey,
            code: code as string,
            name: permissionInfo.name,
            description: permissionInfo.description,
          });
        });
      });
    });

    return list;
  }, []);

  // 过滤权限列表
  const filteredPermissions = useMemo(() => {
    if (!searchQuery) return permissionList;

    const query = searchQuery.toLowerCase();
    return permissionList.filter((item) => {
      return (
        item.moduleName.toLowerCase().includes(query) ||
        item.resource.toLowerCase().includes(query) ||
        item.action.toLowerCase().includes(query) ||
        item.code.toLowerCase().includes(query) ||
        item.name.toLowerCase().includes(query) ||
        item.description.toLowerCase().includes(query)
      );
    });
  }, [permissionList, searchQuery]);

  // 计算统计信息
  const stats = useMemo(() => {
    const moduleStats = filteredPermissions.reduce(
      (acc, item) => {
        if (!acc[item.module]) {
          acc[item.module] = 0;
        }
        acc[item.module]!++;
        return acc;
      },
      {} as Record<string, number>,
    );

    return moduleStats;
  }, [filteredPermissions]);

  // 获取模块图标
  const getModuleIcon = (module: string) => {
    const moduleInfo = MODULE_INFO[module as keyof typeof MODULE_INFO];
    const IconComponent = moduleInfo?.icon || Shield;
    return <IconComponent className="h-4 w-4" />;
  };

  // 获取模块颜色
  const getModuleColor = (module: string) => {
    const moduleInfo = MODULE_INFO[module as keyof typeof MODULE_INFO];
    const colorMap = {
      blue: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
      green:
        "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      purple:
        "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
      orange:
        "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
      yellow:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      pink: "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200",
    };
    return (
      colorMap[moduleInfo?.color as keyof typeof colorMap] ||
      "bg-gray-100 text-gray-800"
    );
  };

  return (
    <div className="space-y-4">
      {/* 统计信息 */}
      {Object.keys(stats).length > 0 && (
        <div className="flex flex-wrap gap-2">
          {Object.entries(stats).map(([module, count]) => {
            const moduleInfo = MODULE_INFO[module as keyof typeof MODULE_INFO];
            return (
              <Badge
                key={module}
                variant="secondary"
                className="flex items-center gap-1"
              >
                {getModuleIcon(module)}
                <span>{moduleInfo?.name || module}</span>
                <span className="font-bold">{count}</span>
              </Badge>
            );
          })}
        </div>
      )}

      {/* 权限表格 */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[120px]">模块</TableHead>
              <TableHead className="w-[120px]">资源</TableHead>
              <TableHead className="w-[120px]">操作</TableHead>
              <TableHead className="w-[280px]">权限代码</TableHead>
              <TableHead className="w-[150px]">权限名称</TableHead>
              <TableHead>描述</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredPermissions.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={6}
                  className="text-center text-muted-foreground py-8"
                >
                  {searchQuery ? "没有找到匹配的权限" : "暂无权限数据"}
                </TableCell>
              </TableRow>
            ) : (
              filteredPermissions.map((permission, index) => {
                // 判断是否是模块的第一个权限
                const isFirstInModule =
                  index === 0 ||
                  filteredPermissions[index - 1]?.module !== permission.module;

                return (
                  <TableRow key={permission.code}>
                    <TableCell>
                      {isFirstInModule && (
                        <div className="flex items-center gap-2">
                          {getModuleIcon(permission.module)}
                          <Badge
                            variant="outline"
                            className={`${getModuleColor(permission.module)}`}
                          >
                            {permission.moduleName}
                          </Badge>
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="font-mono text-xs">
                        {permission.resource}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary" className="text-xs">
                        {permission.action}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <code className="text-sm bg-muted px-2 py-1 rounded font-mono">
                        {permission.code}
                      </code>
                    </TableCell>
                    <TableCell>
                      <span className="font-medium">{permission.name}</span>
                    </TableCell>
                    <TableCell className="text-muted-foreground text-sm">
                      {permission.description}
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>

      {/* 总计信息 */}
      <div className="text-sm text-muted-foreground text-right">
        共 {filteredPermissions.length} 个权限
        {searchQuery && ` - 搜索: "${searchQuery}"`}
      </div>
    </div>
  );
}
