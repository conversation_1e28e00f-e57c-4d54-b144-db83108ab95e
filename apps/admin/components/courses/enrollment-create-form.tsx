"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@workspace/ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Switch } from "@workspace/ui/components/switch";
import { Badge } from "@workspace/ui/components/badge";
import { Loader2, Plus } from "lucide-react";
import { toast } from "sonner";
import UserSearchSelect from "@/components/user-search-select";
import CourseSelect from "@/components/course-select";
import { useEnrollmentActions } from "@/hooks/useEnrollments";

// 表单验证模式
const enrollmentFormSchema = z.object({
  userId: z.string().min(1, "请选择用户"),
  courseId: z.string().min(1, "请选择课程"),
  isPaid: z.boolean().default(false),
  paidAmount: z.number().optional(),
  notes: z.string().optional(),
});

type EnrollmentFormData = z.infer<typeof enrollmentFormSchema>;

interface EnrollmentCreateFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function EnrollmentCreateForm({
  onSuccess,
  onCancel,
}: EnrollmentCreateFormProps) {
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [selectedCourse, setSelectedCourse] = useState<any>(null);
  const { createEnrollment, loading } = useEnrollmentActions();

  // 表单设置
  const form = useForm<EnrollmentFormData>({
    resolver: zodResolver(enrollmentFormSchema),
    defaultValues: {
      userId: "",
      courseId: "",
      isPaid: false,
      paidAmount: undefined,
      notes: "",
    },
  });

  // 监听支付状态变化
  const isPaid = form.watch("isPaid");

  // 处理表单提交
  const handleSubmit = async (data: EnrollmentFormData) => {
    try {
      const result = await createEnrollment({
        userId: data.userId,
        courseId: data.courseId,
        isPaid: data.isPaid,
        paidAmount: data.isPaid ? data.paidAmount : undefined,
        notes: data.notes,
      });

      if (result.success) {
        toast.success("课程报名创建成功");
        form.reset();
        setSelectedUser(null);
        setSelectedCourse(null);
        onSuccess?.();
      } else {
        toast.error(result.message || "创建失败，请重试");
      }
    } catch (error) {
      toast.error("创建失败，请重试");
      console.error("创建报名失败:", error);
    }
  };

  // 处理用户选择
  const handleUserSelect = (user: any) => {
    setSelectedUser(user);
    form.setValue("userId", user.id);
  };

  // 处理课程选择
  const handleCourseSelect = (course: any) => {
    setSelectedCourse(course);
    form.setValue("courseId", course.id);

    // 如果是付费课程，自动设置价格
    if (!course.isFree && course.price) {
      form.setValue("paidAmount", course.price);
    }
  };

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* 用户选择 */}
          <FormField
            control={form.control}
            name="userId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>选择用户 *</FormLabel>
                <FormControl>
                  <UserSearchSelect
                    value={selectedUser}
                    onSelect={handleUserSelect}
                    placeholder="搜索并选择用户..."
                    onClear={() => {
                      setSelectedUser(null);
                      form.setValue("userId", "");
                    }}
                    filterRoles={[
                      "COMMUNITY_USER",
                      "GUEST",
                      "PLANNER",
                      "TENANT_ADMIN",
                    ]}
                  />
                </FormControl>
                {selectedUser && (
                  <div className="flex items-center space-x-2 mt-2">
                    <Badge variant="outline">
                      已选择: {selectedUser.name} ({selectedUser.email})
                    </Badge>
                  </div>
                )}
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 课程选择 */}
          <FormField
            control={form.control}
            name="courseId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>选择课程 *</FormLabel>
                <FormControl>
                  <CourseSelect
                    value={selectedCourse}
                    onSelect={handleCourseSelect}
                    placeholder="搜索并选择课程..."
                    showOnlyPublished={true}
                  />
                </FormControl>
                {selectedCourse && (
                  <div className="flex items-center space-x-2 mt-2">
                    <Badge variant="outline">
                      已选择: {selectedCourse.title}
                    </Badge>
                    {selectedCourse.isFree ? (
                      <Badge variant="secondary">免费课程</Badge>
                    ) : (
                      <Badge variant="default">¥{selectedCourse.price}</Badge>
                    )}
                  </div>
                )}
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 支付状态 */}
          <FormField
            control={form.control}
            name="isPaid"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">支付状态</FormLabel>
                  <div className="text-sm text-muted-foreground">
                    标记该报名的支付状态
                  </div>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={selectedCourse?.isFree}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          {/* 支付金额 */}
          {isPaid && selectedCourse && !selectedCourse.isFree && (
            <FormField
              control={form.control}
              name="paidAmount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>支付金额</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                        ¥
                      </span>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="请输入支付金额"
                        className="pl-8"
                        {...field}
                        onChange={(e) => {
                          const value = e.target.value;
                          field.onChange(value ? parseFloat(value) : undefined);
                        }}
                        value={field.value || ""}
                      />
                    </div>
                  </FormControl>
                  <div className="text-sm text-muted-foreground">
                    课程原价: ¥{selectedCourse.price}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {/* 备注 */}
          <FormField
            control={form.control}
            name="notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>备注</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="添加备注信息（可选）"
                    className="min-h-[80px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              创建报名
            </Button>
          </div>
        </form>
      </Form>

      {/* 快速用户注册 */}
      <div className="border-t pt-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium">用户不存在？</h3>
            <p className="text-sm text-muted-foreground">
              快速创建新用户并直接报名课程
            </p>
          </div>
          <Button variant="outline" size="sm">
            <Plus className="mr-2 h-4 w-4" />
            快速注册用户
          </Button>
        </div>
      </div>
    </div>
  );
}
