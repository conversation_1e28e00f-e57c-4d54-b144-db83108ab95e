"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@workspace/ui/components/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { Badge } from "@workspace/ui/components/badge";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  Plus,
  Edit,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ader2,
  <PERSON><PERSON>er<PERSON><PERSON>,
  Book<PERSON><PERSON>,
} from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import { usePermissions } from "@/hooks/usePermissions";
import { PERMISSIONS } from "@/config/permissions.config";

// 分类数据类型
interface CourseCategory {
  id: string;
  name: string;
  description?: string;
  slug: string;
  parentId?: string;
  sortOrder: number;
  isActive: boolean;
  courseCount: number;
  createdAt: string;
  updatedAt: string;
  children?: CourseCategory[];
}

// 表单验证schema
const categoryFormSchema = z.object({
  name: z.string().min(1, "分类名称不能为空").max(50, "名称不能超过50个字符"),
  description: z.string().max(200, "描述不能超过200个字符").optional(),
  slug: z
    .string()
    .min(1, "URL别名不能为空")
    .regex(/^[a-z0-9-]+$/, "URL别名只能包含小写字母、数字和短横线"),
  parentId: z.string().optional(),
  sortOrder: z.number().min(0, "排序不能为负数"),
  isActive: z.boolean(),
});

type CategoryFormData = z.infer<typeof categoryFormSchema>;

// 模拟分类数据
const mockCategories: CourseCategory[] = [
  {
    id: "1",
    name: "语言考试",
    description: "各类语言能力考试培训",
    slug: "language-tests",
    sortOrder: 1,
    isActive: true,
    courseCount: 15,
    createdAt: "2024-01-01",
    updatedAt: "2024-01-01",
    children: [
      {
        id: "1-1",
        name: "托福",
        description: "TOEFL考试培训",
        slug: "toefl",
        parentId: "1",
        sortOrder: 1,
        isActive: true,
        courseCount: 8,
        createdAt: "2024-01-01",
        updatedAt: "2024-01-01",
      },
      {
        id: "1-2",
        name: "雅思",
        description: "IELTS考试培训",
        slug: "ielts",
        parentId: "1",
        sortOrder: 2,
        isActive: true,
        courseCount: 7,
        createdAt: "2024-01-01",
        updatedAt: "2024-01-01",
      },
    ],
  },
  {
    id: "2",
    name: "留学申请",
    description: "留学申请全流程指导",
    slug: "study-abroad",
    sortOrder: 2,
    isActive: true,
    courseCount: 12,
    createdAt: "2024-01-01",
    updatedAt: "2024-01-01",
  },
  {
    id: "3",
    name: "生涯规划",
    description: "职业生涯规划与发展",
    slug: "career-planning",
    sortOrder: 3,
    isActive: true,
    courseCount: 8,
    createdAt: "2024-01-01",
    updatedAt: "2024-01-01",
  },
];

interface CourseCategoryManagerProps {
  trigger?: React.ReactNode;
}

export function CourseCategoryManager({ trigger }: CourseCategoryManagerProps) {
  const { hasPermission } = usePermissions();
  const [categories, setCategories] = useState<CourseCategory[]>([]);
  const [selectedCategory, setSelectedCategory] =
    useState<CourseCategory | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [mode, setMode] = useState<"create" | "edit">("create");

  // 权限检查
  const canCreate = hasPermission(PERMISSIONS.COURSE.CATEGORY.CREATE);
  const canEdit = hasPermission(PERMISSIONS.COURSE.CATEGORY.EDIT);
  const canDelete = hasPermission(PERMISSIONS.COURSE.CATEGORY.DELETE);

  // 加载分类数据
  const loadCategories = async () => {
    try {
      const response = await fetch("/api/courses/category-tree");
      const result = await response.json();

      if (result.success) {
        setCategories(result.data || []);
      } else {
        toast.error(result.message || "获取分类列表失败");
      }
    } catch (error) {
      console.error("获取分类列表失败:", error);
      toast.error("获取分类列表失败，请重试");
    } finally {
    }
  };

  const form = useForm<CategoryFormData>({
    resolver: zodResolver(categoryFormSchema),
    defaultValues: {
      name: "",
      description: "",
      slug: "",
      parentId: "",
      sortOrder: 0,
      isActive: true,
    },
  });

  // 自动生成slug
  const watchName = form.watch("name");
  useEffect(() => {
    if (watchName && mode === "create") {
      const slug = watchName
        .toLowerCase()
        .replace(/[\s_]+/g, "-")
        .replace(/[^a-z0-9-]/g, "")
        .replace(/-+/g, "-")
        .replace(/^-|-$/g, "");
      form.setValue("slug", slug);
    }
  }, [watchName, form, mode]);

  // 获取所有分类（扁平化，用于父分类选择）
  const getAllCategories = () => {
    const result: CourseCategory[] = [];
    const traverse = (cats: CourseCategory[], level = 0) => {
      cats.forEach((cat) => {
        result.push({ ...cat, name: "  ".repeat(level) + cat.name });
        if (cat.children) {
          traverse(cat.children, level + 1);
        }
      });
    };
    traverse(categories);
    return result;
  };

  // 编辑分类
  const handleEdit = (category: CourseCategory) => {
    setMode("edit");
    setSelectedCategory(category);
    form.reset({
      name: category.name,
      description: category.description || "",
      slug: category.slug,
      parentId: category.parentId || "",
      sortOrder: category.sortOrder,
      isActive: category.isActive,
    });
    setIsFormOpen(true);
  };

  // 删除分类
  const handleDelete = async (category: CourseCategory) => {
    if (category.courseCount > 0) {
      toast.error("该分类下还有课程，无法删除");
      return;
    }

    if (!confirm(`确定要删除分类"${category.name}"吗？`)) {
      return;
    }

    try {
      const response = await fetch(
        `/api/courses/categories?id=${category.id}`,
        {
          method: "DELETE",
        },
      );

      const result = await response.json();

      if (result.success) {
        toast.success("分类删除成功");
        loadCategories(); // 重新加载数据
      } else {
        toast.error(result.message || "删除失败，请重试");
      }
    } catch (error) {
      console.error("删除失败:", error);
      toast.error("删除失败，请重试");
    }
  };

  // 提交表单
  const onSubmit = async (data: CategoryFormData) => {
    setLoading(true);
    try {
      const payload = {
        ...data,
        parentId: data.parentId || undefined,
      };

      if (mode === "create") {
        const response = await fetch("/api/courses/categories", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        });

        const result = await response.json();

        if (result.success) {
          toast.success("分类创建成功");
          setIsFormOpen(false);
          loadCategories(); // 重新加载数据
        } else {
          toast.error(result.message || "创建失败，请重试");
        }
      } else {
        const response = await fetch("/api/courses/categories", {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ...payload,
            id: selectedCategory?.id,
          }),
        });

        const result = await response.json();

        if (result.success) {
          toast.success("分类更新成功");
          setIsFormOpen(false);
          loadCategories(); // 重新加载数据
        } else {
          toast.error(result.message || "更新失败，请重试");
        }
      }
    } catch (error) {
      console.error("保存失败:", error);
      toast.error("保存失败，请重试");
    } finally {
      setLoading(false);
    }
  };

  // 渲染分类树
  const renderCategoryTree = (cats: CourseCategory[], level = 0) => {
    return cats.map((category) => (
      <div key={category.id}>
        <TableRow>
          <TableCell>
            <div
              className={`flex items-center`}
              style={{ paddingLeft: `${level * 20}px` }}
            >
              {level > 0 && (
                <div className="w-4 h-4 border-l border-b border-muted mr-2" />
              )}
              <FolderOpen className="h-4 w-4 mr-2 text-muted-foreground" />
              <span className="font-medium">{category.name}</span>
            </div>
          </TableCell>
          <TableCell>
            <code className="text-xs bg-muted px-2 py-1 rounded">
              {category.slug}
            </code>
          </TableCell>
          <TableCell>
            <Badge variant={category.isActive ? "default" : "secondary"}>
              {category.isActive ? "启用" : "禁用"}
            </Badge>
          </TableCell>
          <TableCell>
            <div className="flex items-center gap-1">
              <BookOpen className="h-4 w-4 text-muted-foreground" />
              {category.courseCount || 0}
            </div>
          </TableCell>
          <TableCell>{category.sortOrder}</TableCell>
          <TableCell>
            <time className="text-sm text-muted-foreground">
              {new Date(category.updatedAt).toLocaleDateString()}
            </time>
          </TableCell>
          <TableCell>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {canEdit && (
                  <DropdownMenuItem onClick={() => handleEdit(category)}>
                    <Edit className="h-4 w-4 mr-2" />
                    编辑
                  </DropdownMenuItem>
                )}
                {canDelete && (
                  <DropdownMenuItem
                    onClick={() => handleDelete(category)}
                    className="text-destructive"
                    disabled={category.courseCount > 0}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    删除
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </TableCell>
        </TableRow>
        {category.children && renderCategoryTree(category.children, level + 1)}
      </div>
    ));
  };

  return (
    <>
      {/* 分类表单对话框 */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {mode === "create" ? "创建分类" : "编辑分类"}
            </DialogTitle>
            <DialogDescription>
              {mode === "create" ? "创建新的课程分类" : "修改分类信息"}
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>分类名称 *</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入分类名称" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="slug"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>URL别名 *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="url-slug"
                        {...field}
                        onChange={(e) => {
                          const value = e.target.value
                            .toLowerCase()
                            .replace(/[^a-z0-9-]/g, "")
                            .replace(/-+/g, "-")
                            .replace(/^-|-$/g, "");
                          field.onChange(value);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>分类描述</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="请输入分类描述（可选）"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="parentId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>父分类</FormLabel>
                      <FormControl>
                        <select
                          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                          {...field}
                        >
                          <option value="">无（顶级分类）</option>
                          {getAllCategories()
                            .filter((cat) => cat.id !== selectedCategory?.id)
                            .map((cat) => (
                              <option key={cat.id} value={cat.id}>
                                {cat.name}
                              </option>
                            ))}
                        </select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="sortOrder"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>排序</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="0"
                          {...field}
                          onChange={(e) =>
                            field.onChange(Number(e.target.value))
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsFormOpen(false)}
                >
                  取消
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  {mode === "create" ? "创建" : "保存"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}
