"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Switch } from "@workspace/ui/components/switch";
import { Loader2, FolderTree } from "lucide-react";
import { toast } from "sonner";
import { FileUpload } from "@/components/upload/file-upload";

// 表单验证schema
const categoryFormSchema = z.object({
  name: z.string().min(1, "分类名称不能为空").max(50, "名称不能超过50个字符"),
  slug: z
    .string()
    .min(1, "标识符不能为空")
    .max(100, "标识符不能超过100个字符")
    .regex(/^[a-z0-9-]+$/, "标识符只能包含小写字母、数字和短横线"),
  description: z.string().max(500, "描述不能超过500个字符").optional(),
  parentId: z.string().optional(),
  icon: z.string().max(10, "图标不能超过10个字符").optional(),
  iconImage: z.string().url("请输入有效的图片URL").optional(),
  order: z.number().min(0, "排序不能为负数"),
  isActive: z.boolean(),
});

type CategoryFormData = z.infer<typeof categoryFormSchema>;

// 分类数据类型
interface CategoryOption {
  id: string;
  name: string;
  level: number;
  parentId?: string;
}

// 常用图标选项
const iconOptions = [
  { value: "🌐", label: "🌐 地球" },
  { value: "🎓", label: "🎓 教育" },
  { value: "🎯", label: "🎯 目标" },
  { value: "📚", label: "📚 书本" },
  { value: "💼", label: "💼 商务" },
  { value: "🖥", label: "🖥 技术" },
  { value: "🎨", label: "🎨 艺术" },
  { value: "⚖", label: "⚖ 法律" },
  { value: "🏭", label: "🏭 医疗" },
  { value: "🔬", label: "🔬 科学" },
];

interface CategoryFormProps {
  mode: "create" | "edit";
  initialData?: Partial<CategoryFormData> & { id?: string };
  onSuccess?: () => void;
}

export function CategoryForm({
  mode,
  initialData,
  onSuccess,
}: CategoryFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [parentCategories, setParentCategories] = useState<CategoryOption[]>(
    [],
  );
  const [loadingCategories, setLoadingCategories] = useState(true);

  const form = useForm<CategoryFormData>({
    resolver: zodResolver(categoryFormSchema),
    defaultValues: {
      name: "",
      slug: "",
      description: "",
      parentId: "",
      icon: "",
      iconImage: "",
      order: 0,
      isActive: true,
      ...initialData,
    },
  });

  const watchName = form.watch("name");

  // 获取父分类列表
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoadingCategories(true);
        const res = await fetch(
          "/api/courses/categories?includeChildren=false",
        );
        const result = await res.json();

        if (result.success) {
          // 构建层级结构用于显示
          const buildHierarchy = (
            categories: any[],
            parentId: string | null = null,
            level = 0,
          ): CategoryOption[] => {
            return categories
              .filter((cat) => cat.parentId === parentId)
              .flatMap((cat) => [
                { id: cat.id, name: cat.name, level, parentId: cat.parentId },
                ...buildHierarchy(categories, cat.id, level + 1),
              ]);
          };

          const hierarchy = buildHierarchy(result.data || []);
          setParentCategories(hierarchy);
        }
      } catch (error) {
        console.error("获取分类列表失败:", error);
      } finally {
        setLoadingCategories(false);
      }
    };

    fetchCategories();
  }, []);

  // 自动生成slug
  useEffect(() => {
    if (mode === "create" && watchName && !form.getValues("slug")) {
      // 改进的 slug 生成逻辑
      let slug = watchName.toLowerCase();

      // 如果包含中文，使用拼音或者提示用户手动输入
      if (/[\u4e00-\u9fa5]/.test(slug)) {
        // 可以集成拼音库，或者使用简单的映射
        slug = slug
          .replace(/语言/g, "language")
          .replace(/考试/g, "exam")
          .replace(/留学/g, "study-abroad")
          .replace(/申请/g, "application")
          .replace(/规划/g, "planning")
          .replace(/托福/g, "toefl")
          .replace(/雅思/g, "ielts")
          .replace(/本科/g, "undergraduate")
          .replace(/研究生/g, "graduate");
      }

      slug = slug
        .replace(/[^a-z0-9\s-]/g, "") // 移除特殊字符
        .replace(/\s+/g, "-") // 空格替换为短横线
        .replace(/-+/g, "-") // 多个短横线合并
        .replace(/^-+|-+$/g, ""); // 移除首尾短横线

      if (slug) {
        form.setValue("slug", slug);
      }
    }
  }, [watchName, mode, form]);

  // 提交表单
  const onSubmit = async (data: CategoryFormData) => {
    setLoading(true);
    try {
      const payload = {
        ...data,
        // 确保空值处理
        parentId: data.parentId || undefined,
        description: data.description || undefined,
        icon: data.icon || undefined,
      };

      if (mode === "create") {
        const res = await fetch("/api/courses/categories", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        });

        const result = await res.json();

        if (result.success) {
          toast.success("分类创建成功！");
          router.push("/courses/categories");
        } else {
          toast.error(result.message || "创建失败，请重试");
        }
      } else {
        const res = await fetch("/api/courses/categories", {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ...payload,
            id: initialData?.id,
          }),
        });

        const result = await res.json();

        if (result.success) {
          toast.success("分类更新成功！");
          onSuccess?.();
        } else {
          toast.error(result.message || "更新失败，请重试");
        }
      }
    } catch (error: Error | any) {
      console.error("保存失败:", error);
      toast.error(`保存失败，请重试`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto space-y-8">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="space-y-8">
            {/* 基本信息 */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium">基本信息</h3>
                <p className="text-sm text-muted-foreground">
                  填写分类的基本信息和描述
                </p>
              </div>

              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>分类名称</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入分类名称" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="slug"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>URL 标识符</FormLabel>
                      <FormControl>
                        <Input placeholder="category-slug" {...field} />
                      </FormControl>
                      <FormDescription>
                        用于URL的唯一标识，只能包含小写字母、数字和短横线
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>分类描述</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="简要描述该分类的用途和包含内容"
                          className="resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="parentId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>父分类</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          field.onChange(value === "none" ? "" : value);
                        }}
                        defaultValue={field.value || "none"}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择父分类（可选）" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">
                            无（作为顶级分类）
                          </SelectItem>
                          {loadingCategories ? (
                            <SelectItem value="loading" disabled>
                              加载中...
                            </SelectItem>
                          ) : (
                            parentCategories
                              .filter((cat) => cat.id !== initialData?.id)
                              .map((category) => (
                                <SelectItem
                                  key={category.id}
                                  value={category.id}
                                >
                                  {"  ".repeat(category.level)}
                                  {category.name}
                                </SelectItem>
                              ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        选择父分类可以创建层级结构
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* 显示设置 */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium">显示设置</h3>
                <p className="text-sm text-muted-foreground">
                  配置分类的显示方式和排序
                </p>
              </div>

              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="icon"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>分类图标</FormLabel>
                      <FormControl>
                        <Input placeholder="🌐" {...field} />
                      </FormControl>
                      <FormDescription>
                        选择一个 emoji 图标来代表该分类
                      </FormDescription>

                      {/* 图标快捷选择 */}
                      <div className="grid grid-cols-5 gap-2 pt-2">
                        {iconOptions.map((option) => (
                          <Button
                            key={option.value}
                            type="button"
                            variant={
                              field.value === option.value
                                ? "default"
                                : "outline"
                            }
                            size="sm"
                            onClick={() => field.onChange(option.value)}
                            className="h-10"
                          >
                            <span className="text-base">{option.value}</span>
                          </Button>
                        ))}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="iconImage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>自定义图标图片</FormLabel>
                      <FormControl>
                        <FileUpload
                          type="image"
                          folder="category-icons"
                          placeholder="点击上传自定义图标图片"
                          value={field.value}
                          onChange={field.onChange}
                          maxSize={1 * 1024 * 1024} // 1MB
                        />
                      </FormControl>
                      <FormDescription>
                        上传自定义图标图片（推荐64x64像素，最大1MB），如果上传了图片则优先使用图片而非emoji图标
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="order"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>排序</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="0"
                          {...field}
                          onChange={(e) =>
                            field.onChange(Number(e.target.value) || 0)
                          }
                        />
                      </FormControl>
                      <FormDescription>数字越小排序越靠前</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">启用分类</FormLabel>
                        <FormDescription>
                          禁用后该分类将不会在前台显示
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* 预览 */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium">预览</h3>
                <p className="text-sm text-muted-foreground">
                  查看分类的显示效果
                </p>
              </div>

              <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-4">
                <div className="flex items-center gap-2 mb-2">
                  {form.watch("icon") ? (
                    <span className="text-lg">{form.watch("icon")}</span>
                  ) : (
                    <FolderTree className="h-4 w-4 text-muted-foreground" />
                  )}
                  <span className="font-medium">
                    {form.watch("name") || "分类名称"}
                  </span>
                </div>
                {form.watch("description") && (
                  <p className="text-sm text-muted-foreground mb-2">
                    {form.watch("description")}
                  </p>
                )}
                <div className="text-xs text-muted-foreground">
                  URL: /categories/{form.watch("slug") || "category-slug"}
                </div>
              </div>
            </div>

            {/* 提交按钮 */}
            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                disabled={loading}
              >
                取消
              </Button>
              <Button type="submit" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {mode === "create" ? "创建分类" : "保存更改"}
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
