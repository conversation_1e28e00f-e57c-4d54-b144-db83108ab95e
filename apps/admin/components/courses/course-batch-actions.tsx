"use client";

import { useState } from "react";
import { Button } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@workspace/ui/components/alert-dialog";
import { Badge } from "@workspace/ui/components/badge";
import { toast } from "sonner";
import {
  ChevronDown,
  Trash2,
  Play,
  Pause,
  Archive,
  Loader2,
  CheckSquare,
} from "lucide-react";

interface Course {
  id: string;
  title: string;
  status: "DRAFT" | "REVIEWING" | "PUBLISHED" | "OFFLINE";
  enrollCount: number;
}

interface CourseBatchActionsProps {
  selectedCourses: Course[];
  onClearSelection: () => void;
  onRefresh?: () => void;
}

type BatchAction = "publish" | "unpublish" | "archive" | "delete";

export function CourseBatchActions({
  selectedCourses,
  onClearSelection,
  onRefresh,
}: CourseBatchActionsProps) {
  const [loading, setLoading] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    action: BatchAction;
    title: string;
    description: string;
    variant: "default" | "destructive";
  }>({
    open: false,
    action: "publish",
    title: "",
    description: "",
    variant: "default",
  });

  const selectedCount = selectedCourses.length;

  if (selectedCount === 0) {
    return null;
  }

  // 统计课程状态
  const statusCounts = selectedCourses.reduce(
    (acc, course) => {
      acc[course.status] = (acc[course.status] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>,
  );

  // 检查是否可以执行某个操作
  const canPublish = selectedCourses.some(
    (course) => course.status === "DRAFT" || course.status === "OFFLINE",
  );
  const canUnpublish = selectedCourses.some(
    (course) => course.status === "PUBLISHED",
  );
  const hasEnrollments = selectedCourses.some(
    (course) => course.enrollCount > 0,
  );

  // 处理批量操作
  const handleBatchAction = async (action: BatchAction) => {
    try {
      setLoading(true);
      const courseIds = selectedCourses.map((course) => course.id);

      // 这里应该调用实际的API
      switch (action) {
        case "publish":
          // await batchPublishCourses(courseIds);
          break;
        case "unpublish":
          // await batchUnpublishCourses(courseIds);
          break;
        case "archive":
          // await batchArchiveCourses(courseIds);
          break;
        case "delete":
          // await batchDeleteCourses(courseIds);
          break;
      }

      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 2000));

      const actionText = {
        publish: "发布",
        unpublish: "下架",
        archive: "归档",
        delete: "删除",
      };

      toast.success(`已成功${actionText[action]} ${selectedCount} 个课程`);
      onClearSelection();
      onRefresh?.();
    } catch (error) {
      console.error(`批量${action}失败:`, error);
      toast.error(`批量操作失败，请重试`);
    } finally {
      setLoading(false);
      setConfirmDialog({ ...confirmDialog, open: false });
    }
  };

  // 显示确认对话框
  const showConfirmDialog = (action: BatchAction) => {
    const configs = {
      publish: {
        title: "批量发布课程",
        description: `确定要发布选中的 ${selectedCount} 个课程吗？发布后课程将对学员可见。`,
        variant: "default" as const,
      },
      unpublish: {
        title: "批量下架课程",
        description: `确定要下架选中的 ${selectedCount} 个课程吗？${hasEnrollments ? "下架后已报名的学员将无法继续学习。" : ""}`,
        variant: "default" as const,
      },
      archive: {
        title: "批量归档课程",
        description: `确定要归档选中的 ${selectedCount} 个课程吗？归档后课程将不再显示在列表中。`,
        variant: "default" as const,
      },
      delete: {
        title: "批量删除课程",
        description: `确定要删除选中的 ${selectedCount} 个课程吗？${hasEnrollments ? "删除后已报名的学员将无法继续学习。" : ""}此操作不可撤销！`,
        variant: "destructive" as const,
      },
    };

    setConfirmDialog({
      open: true,
      action,
      ...configs[action],
    });
  };

  return (
    <>
      <div className="flex items-center gap-3 p-4 bg-muted/50 rounded-lg border">
        <div className="flex items-center gap-2">
          <CheckSquare className="h-4 w-4 text-primary" />
          <span className="font-medium">已选择 {selectedCount} 个课程</span>
        </div>

        {/* 状态统计 */}
        <div className="flex items-center gap-2">
          {Object.entries(statusCounts).map(([status, count]) => {
            const statusConfig = {
              DRAFT: { label: "草稿", variant: "secondary" as const },
              REVIEWING: { label: "审核中", variant: "default" as const },
              PUBLISHED: { label: "已发布", variant: "default" as const },
              OFFLINE: { label: "已下架", variant: "destructive" as const },
            };
            const config = statusConfig[status as keyof typeof statusConfig];
            return (
              <Badge key={status} variant={config.variant}>
                {config.label}: {count}
              </Badge>
            );
          })}
        </div>

        <div className="flex items-center gap-2 ml-auto">
          {/* 批量操作下拉菜单 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" disabled={loading}>
                {loading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <ChevronDown className="h-4 w-4 mr-2" />
                )}
                批量操作
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {canPublish && (
                <DropdownMenuItem onClick={() => showConfirmDialog("publish")}>
                  <Play className="h-4 w-4 mr-2" />
                  批量发布
                </DropdownMenuItem>
              )}
              {canUnpublish && (
                <DropdownMenuItem
                  onClick={() => showConfirmDialog("unpublish")}
                >
                  <Pause className="h-4 w-4 mr-2" />
                  批量下架
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={() => showConfirmDialog("archive")}>
                <Archive className="h-4 w-4 mr-2" />
                批量归档
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => showConfirmDialog("delete")}
                className="text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                批量删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* 取消选择 */}
          <Button variant="ghost" onClick={onClearSelection}>
            取消选择
          </Button>
        </div>
      </div>

      {/* 确认对话框 */}
      <AlertDialog
        open={confirmDialog.open}
        onOpenChange={(open) => setConfirmDialog({ ...confirmDialog, open })}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{confirmDialog.title}</AlertDialogTitle>
            <AlertDialogDescription>
              {confirmDialog.description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={loading}>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => handleBatchAction(confirmDialog.action)}
              disabled={loading}
              className={
                confirmDialog.variant === "destructive"
                  ? "bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  : ""
              }
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  处理中...
                </>
              ) : (
                "确认"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
