"use client";

import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@workspace/ui/components/alert-dialog";
import { Button } from "@workspace/ui/components/button";
import { toast } from "sonner";
import { Loader2, Trash2 } from "lucide-react";

interface Course {
  id: string;
  title: string;
  enrollCount: number;
  status: "DRAFT" | "REVIEWING" | "PUBLISHED" | "OFFLINE";
}

interface CourseDeleteDialogProps {
  course: Course | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function CourseDeleteDialog({
  course,
  open,
  onOpenChange,
  onSuccess,
}: CourseDeleteDialogProps) {
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    if (!course) return;

    try {
      setLoading(true);

      // 这里应该调用实际的删除API
      // await deleteCourse(course.id);

      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1500));

      toast.success(`课程「${course.title}」已删除`);
      onOpenChange(false);
      onSuccess?.();
    } catch (error) {
      console.error("删除课程失败:", error);
      toast.error("删除课程失败，请重试");
    } finally {
      setLoading(false);
    }
  };

  if (!course) return null;

  const hasEnrollments = course.enrollCount > 0;
  const isPublished = course.status === "PUBLISHED";

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-destructive" />
            删除课程
          </AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div className="space-y-3">
              <p>
                你确定要删除课程{" "}
                <span className="font-semibold">「{course.title}」</span> 吗？
              </p>

              {hasEnrollments && (
                <div className="p-3 bg-destructive/10 rounded-md border border-destructive/20">
                  <p className="text-sm text-destructive font-medium">
                    ! 注意事项
                  </p>
                  <p className="text-sm text-destructive mt-1">
                    该课程已有 {course.enrollCount}{" "}
                    名学员报名，删除后将影响学员的学习进度。
                  </p>
                </div>
              )}

              {isPublished && (
                <div className="p-3 bg-amber-50 rounded-md border border-amber-200">
                  <p className="text-sm text-amber-800 font-medium">📢 提醒</p>
                  <p className="text-sm text-amber-800 mt-1">
                    该课程当前为已发布状态，删除后用户将无法访问。
                  </p>
                </div>
              )}

              <p className="text-sm text-muted-foreground">
                此操作不可撤销，请谨慎操作。
              </p>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={loading}>取消</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={loading}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                删除中...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                确认删除
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
