"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import { Badge } from "@workspace/ui/components/badge";
import { Calendar } from "@workspace/ui/components/calendar";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Separator } from "@workspace/ui/components/separator";
import {
  Search,
  Filter,
  X,
  Calendar as CalendarIcon,
  DollarSign,
  Users,
  Clock,
  RotateCcw,
} from "lucide-react";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";

// 过滤条件类型
export interface CourseFilters {
  search: string;
  status: string;
  level: string;
  category: string;
  instructor: string;
  priceRange: {
    min: number | null;
    max: number | null;
  };
  isFree: string; // "all" | "free" | "paid"
  dateRange: {
    from: Date | null;
    to: Date | null;
  };
  enrollmentRange: {
    min: number | null;
    max: number | null;
  };
  sortBy: string;
  sortOrder: "asc" | "desc";
}

// 默认过滤条件
const defaultFilters: CourseFilters = {
  search: "",
  status: "all",
  level: "all",
  category: "all",
  instructor: "all",
  priceRange: { min: null, max: null },
  isFree: "all",
  dateRange: { from: null, to: null },
  enrollmentRange: { min: null, max: null },
  sortBy: "updatedAt",
  sortOrder: "desc",
};

// 模拟数据
const categories = [
  { id: "category-1", name: "语言考试" },
  { id: "category-2", name: "留学申请" },
  { id: "category-3", name: "生涯规划" },
  { id: "category-4", name: "学术提升" },
];

const instructors = [
  { id: "instructor-1", name: "张老师" },
  { id: "instructor-2", name: "李老师" },
  { id: "instructor-3", name: "王老师" },
  { id: "instructor-4", name: "陈老师" },
];

interface CourseAdvancedFiltersProps {
  filters: CourseFilters;
  onFiltersChange: (filters: CourseFilters) => void;
  onReset?: () => void;
}

export function CourseAdvancedFilters({
  filters,
  onFiltersChange,
  onReset,
}: CourseAdvancedFiltersProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState<CourseFilters>(filters);

  // 同步外部过滤条件变化
  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  // 更新过滤条件
  const updateFilter = <K extends keyof CourseFilters>(
    key: K,
    value: CourseFilters[K],
  ) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  // 重置过滤条件
  const handleReset = () => {
    setLocalFilters(defaultFilters);
    onFiltersChange(defaultFilters);
    onReset?.();
  };

  // 计算活跃过滤器数量
  const getActiveFiltersCount = () => {
    let count = 0;
    if (localFilters.search) count++;
    if (localFilters.status !== "all") count++;
    if (localFilters.level !== "all") count++;
    if (localFilters.category !== "all") count++;
    if (localFilters.instructor !== "all") count++;
    if (localFilters.isFree !== "all") count++;
    if (
      localFilters.priceRange.min !== null ||
      localFilters.priceRange.max !== null
    )
      count++;
    if (localFilters.dateRange.from || localFilters.dateRange.to) count++;
    if (
      localFilters.enrollmentRange.min !== null ||
      localFilters.enrollmentRange.max !== null
    )
      count++;
    return count;
  };

  const activeCount = getActiveFiltersCount();

  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* 基础搜索栏 */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索课程名称、讲师或内容..."
                  value={localFilters.search}
                  onChange={(e) => updateFilter("search", e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* 基础过滤器 */}
            <div className="flex gap-2">
              <Select
                value={localFilters.status}
                onValueChange={(value) => updateFilter("status", value)}
              >
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="DRAFT">草稿</SelectItem>
                  <SelectItem value="REVIEWING">审核中</SelectItem>
                  <SelectItem value="PUBLISHED">已发布</SelectItem>
                  <SelectItem value="OFFLINE">已下架</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={localFilters.level}
                onValueChange={(value) => updateFilter("level", value)}
              >
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="级别" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部级别</SelectItem>
                  <SelectItem value="BEGINNER">初级</SelectItem>
                  <SelectItem value="INTERMEDIATE">中级</SelectItem>
                  <SelectItem value="ADVANCED">高级</SelectItem>
                </SelectContent>
              </Select>

              {/* 高级过滤器触发按钮 */}
              <Popover open={isOpen} onOpenChange={setIsOpen}>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="relative">
                    <Filter className="h-4 w-4 mr-2" />
                    高级筛选
                    {activeCount > 0 && (
                      <Badge
                        variant="destructive"
                        className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs"
                      >
                        {activeCount}
                      </Badge>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-96 p-0" align="end">
                  <div className="p-4 space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">高级筛选</h4>
                      <Button variant="ghost" size="sm" onClick={handleReset}>
                        <RotateCcw className="h-4 w-4 mr-1" />
                        重置
                      </Button>
                    </div>

                    <Separator />

                    {/* 分类筛选 */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">课程分类</label>
                      <Select
                        value={localFilters.category}
                        onValueChange={(value) =>
                          updateFilter("category", value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择分类" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部分类</SelectItem>
                          {categories.map((cat) => (
                            <SelectItem key={cat.id} value={cat.id}>
                              {cat.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* 讲师筛选 */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">授课讲师</label>
                      <Select
                        value={localFilters.instructor}
                        onValueChange={(value) =>
                          updateFilter("instructor", value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择讲师" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部讲师</SelectItem>
                          {instructors.map((instructor) => (
                            <SelectItem
                              key={instructor.id}
                              value={instructor.id}
                            >
                              {instructor.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* 价格筛选 */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">价格筛选</label>
                      <Select
                        value={localFilters.isFree}
                        onValueChange={(value) => updateFilter("isFree", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="价格类型" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部课程</SelectItem>
                          <SelectItem value="free">免费课程</SelectItem>
                          <SelectItem value="paid">付费课程</SelectItem>
                        </SelectContent>
                      </Select>

                      {localFilters.isFree === "paid" && (
                        <div className="grid grid-cols-2 gap-2">
                          <Input
                            type="number"
                            placeholder="最低价格"
                            value={localFilters.priceRange.min || ""}
                            onChange={(e) =>
                              updateFilter("priceRange", {
                                ...localFilters.priceRange,
                                min: e.target.value
                                  ? Number(e.target.value)
                                  : null,
                              })
                            }
                          />
                          <Input
                            type="number"
                            placeholder="最高价格"
                            value={localFilters.priceRange.max || ""}
                            onChange={(e) =>
                              updateFilter("priceRange", {
                                ...localFilters.priceRange,
                                max: e.target.value
                                  ? Number(e.target.value)
                                  : null,
                              })
                            }
                          />
                        </div>
                      )}
                    </div>

                    {/* 报名人数筛选 */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">报名人数</label>
                      <div className="grid grid-cols-2 gap-2">
                        <Input
                          type="number"
                          placeholder="最少人数"
                          value={localFilters.enrollmentRange.min || ""}
                          onChange={(e) =>
                            updateFilter("enrollmentRange", {
                              ...localFilters.enrollmentRange,
                              min: e.target.value
                                ? Number(e.target.value)
                                : null,
                            })
                          }
                        />
                        <Input
                          type="number"
                          placeholder="最多人数"
                          value={localFilters.enrollmentRange.max || ""}
                          onChange={(e) =>
                            updateFilter("enrollmentRange", {
                              ...localFilters.enrollmentRange,
                              max: e.target.value
                                ? Number(e.target.value)
                                : null,
                            })
                          }
                        />
                      </div>
                    </div>

                    {/* 日期范围筛选 */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">更新时间</label>
                      <div className="grid grid-cols-2 gap-2">
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className="justify-start text-left font-normal"
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {localFilters.dateRange.from
                                ? format(localFilters.dateRange.from, "PPP", {
                                    locale: zhCN,
                                  })
                                : "开始日期"}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={
                                localFilters.dateRange.from || undefined
                              }
                              onSelect={(date) =>
                                updateFilter("dateRange", {
                                  ...localFilters.dateRange,
                                  from: date || null,
                                })
                              }
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>

                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className="justify-start text-left font-normal"
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {localFilters.dateRange.to
                                ? format(localFilters.dateRange.to, "PPP", {
                                    locale: zhCN,
                                  })
                                : "结束日期"}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={localFilters.dateRange.to || undefined}
                              onSelect={(date) =>
                                updateFilter("dateRange", {
                                  ...localFilters.dateRange,
                                  to: date || null,
                                })
                              }
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </div>
                    </div>

                    {/* 排序设置 */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">排序方式</label>
                      <div className="grid grid-cols-2 gap-2">
                        <Select
                          value={localFilters.sortBy}
                          onValueChange={(value) =>
                            updateFilter("sortBy", value)
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="updatedAt">更新时间</SelectItem>
                            <SelectItem value="createdAt">创建时间</SelectItem>
                            <SelectItem value="title">课程名称</SelectItem>
                            <SelectItem value="price">价格</SelectItem>
                            <SelectItem value="enrollCount">
                              报名人数
                            </SelectItem>
                            <SelectItem value="viewCount">浏览量</SelectItem>
                          </SelectContent>
                        </Select>

                        <Select
                          value={localFilters.sortOrder}
                          onValueChange={(value: "asc" | "desc") =>
                            updateFilter("sortOrder", value)
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="desc">降序</SelectItem>
                            <SelectItem value="asc">升序</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {/* 活跃过滤器标签 */}
          {activeCount > 0 && (
            <div className="flex flex-wrap gap-2">
              {localFilters.search && (
                <Badge variant="secondary" className="gap-1">
                  搜索: {localFilters.search}
                  <button
                    onClick={() => updateFilter("search", "")}
                    className="hover:text-destructive"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {localFilters.status !== "all" && (
                <Badge variant="secondary" className="gap-1">
                  状态: {localFilters.status}
                  <button
                    onClick={() => updateFilter("status", "all")}
                    className="hover:text-destructive"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {localFilters.level !== "all" && (
                <Badge variant="secondary" className="gap-1">
                  级别: {localFilters.level}
                  <button
                    onClick={() => updateFilter("level", "all")}
                    className="hover:text-destructive"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {/* 更多活跃过滤器标签... */}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
