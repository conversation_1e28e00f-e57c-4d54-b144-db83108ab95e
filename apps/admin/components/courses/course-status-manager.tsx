"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Textarea } from "@workspace/ui/components/textarea";
import { toast } from "sonner";
import { Loader2, Play, Pause, Archive, Eye, AlertCircle } from "lucide-react";

interface Course {
  id: string;
  title: string;
  status: "DRAFT" | "REVIEWING" | "PUBLISHED" | "OFFLINE";
}

interface CourseStatusManagerProps {
  courses: Course[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

// 状态配置
const STATUS_CONFIG = {
  DRAFT: {
    label: "草稿",
    description: "课程未发布，仅管理员可见",
    color: "secondary",
    icon: Eye,
    nextStates: ["REVIEWING", "PUBLISHED"],
  },
  REVIEWING: {
    label: "审核中",
    description: "课程等待审核",
    color: "default",
    icon: AlertCircle,
    nextStates: ["PUBLISHED", "DRAFT"],
  },
  PUBLISHED: {
    label: "已发布",
    description: "课程已上线，用户可见",
    color: "default",
    icon: Play,
    nextStates: ["OFFLINE"],
  },
  OFFLINE: {
    label: "已下架",
    description: "课程已下架，用户不可见",
    color: "destructive",
    icon: Archive,
    nextStates: ["DRAFT", "PUBLISHED"],
  },
} as const;

const STATUS_ACTIONS = {
  DRAFT: "保存为草稿",
  REVIEWING: "提交审核",
  PUBLISHED: "发布课程",
  OFFLINE: "下架课程",
} as const;

export function CourseStatusManager({
  courses,
  open,
  onOpenChange,
  onSuccess,
}: CourseStatusManagerProps) {
  const [selectedCourses, setSelectedCourses] = useState<string[]>([]);
  const [targetStatus, setTargetStatus] = useState<string>("");
  const [reason, setReason] = useState("");
  const [loading, setLoading] = useState(false);

  // 重置表单
  const resetForm = () => {
    setSelectedCourses([]);
    setTargetStatus("");
    setReason("");
    setLoading(false);
  };

  // 处理对话框关闭
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      resetForm();
    }
    onOpenChange(open);
  };

  // 切换课程选择
  const toggleCourseSelection = (courseId: string) => {
    setSelectedCourses((prev) =>
      prev.includes(courseId)
        ? prev.filter((id) => id !== courseId)
        : [...prev, courseId],
    );
  };

  // 全选/取消全选
  const toggleSelectAll = () => {
    if (selectedCourses.length === courses.length) {
      setSelectedCourses([]);
    } else {
      setSelectedCourses(courses.map((c) => c.id));
    }
  };

  // 获取可用的目标状态
  const getAvailableTargetStates = () => {
    if (selectedCourses.length === 0) return [];

    // 获取所有选中课程的当前状态
    const selectedCoursesData = courses.filter((c) =>
      selectedCourses.includes(c.id),
    );
    const currentStatuses = selectedCoursesData.map((c) => c.status);

    // 找到所有状态的交集可转换状态
    const allPossibleStates = Object.keys(STATUS_CONFIG) as Array<
      keyof typeof STATUS_CONFIG
    >;

    return allPossibleStates.filter((state) => {
      // 排除当前已有的状态
      if (currentStatuses.includes(state)) return false;

      // 检查是否所有选中课程都可以转换到这个状态
      return currentStatuses.every((currentStatus) => {
        const config =
          STATUS_CONFIG[currentStatus as keyof typeof STATUS_CONFIG];
        return config.nextStates.includes(state);
      });
    });
  };

  // 提交状态变更
  const handleSubmit = async () => {
    if (selectedCourses.length === 0 || !targetStatus) {
      toast.error("请选择课程和目标状态");
      return;
    }

    try {
      setLoading(true);

      // 这里应该调用实际的API
      // await updateCoursesStatus({
      //   courseIds: selectedCourses,
      //   status: targetStatus,
      //   reason: reason,
      // });

      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1500));

      const statusLabel =
        STATUS_CONFIG[targetStatus as keyof typeof STATUS_CONFIG].label;
      toast.success(
        `成功将 ${selectedCourses.length} 个课程状态更新为「${statusLabel}」`,
      );

      handleOpenChange(false);
      onSuccess?.();
    } catch (error) {
      console.error("状态更新失败:", error);
      toast.error("状态更新失败，请重试");
    } finally {
      setLoading(false);
    }
  };

  const availableStates = getAvailableTargetStates();
  const selectedCoursesData = courses.filter((c) =>
    selectedCourses.includes(c.id),
  );

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>批量管理课程状态</DialogTitle>
          <DialogDescription>
            选择课程并设置新的状态，支持批量操作
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 课程选择 */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">选择课程</h4>
              <Button variant="outline" size="sm" onClick={toggleSelectAll}>
                {selectedCourses.length === courses.length
                  ? "取消全选"
                  : "全选"}
              </Button>
            </div>

            <div className="max-h-60 overflow-y-auto border rounded-lg">
              {courses.map((course) => {
                const isSelected = selectedCourses.includes(course.id);
                const statusConfig = STATUS_CONFIG[course.status];
                const StatusIcon = statusConfig.icon;

                return (
                  <div
                    key={course.id}
                    className={`p-3 border-b last:border-b-0 cursor-pointer transition-colors ${
                      isSelected ? "bg-muted" : "hover:bg-muted/50"
                    }`}
                    onClick={() => toggleCourseSelection(course.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => toggleCourseSelection(course.id)}
                          className="rounded border-gray-300"
                        />
                        <div>
                          <p className="font-medium">{course.title}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <StatusIcon className="h-3 w-3" />
                            <Badge
                              variant={statusConfig.color as any}
                              className="text-xs"
                            >
                              {statusConfig.label}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {selectedCourses.length > 0 && (
              <p className="text-sm text-muted-foreground">
                已选择 {selectedCourses.length} 个课程
              </p>
            )}
          </div>

          {/* 目标状态选择 */}
          {selectedCourses.length > 0 && (
            <div className="space-y-3">
              <h4 className="font-medium">选择目标状态</h4>
              <Select value={targetStatus} onValueChange={setTargetStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="请选择新的状态" />
                </SelectTrigger>
                <SelectContent>
                  {availableStates.map((status) => {
                    const config = STATUS_CONFIG[status];
                    const StatusIcon = config.icon;
                    return (
                      <SelectItem key={status} value={status}>
                        <div className="flex items-center gap-2">
                          <StatusIcon className="h-4 w-4" />
                          <span>{config.label}</span>
                          <span className="text-xs text-muted-foreground">
                            - {config.description}
                          </span>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>

              {availableStates.length === 0 && (
                <p className="text-sm text-muted-foreground">
                  所选课程的当前状态无法进行批量变更
                </p>
              )}
            </div>
          )}

          {/* 变更原因 */}
          {targetStatus && (
            <div className="space-y-3">
              <h4 className="font-medium">变更原因（可选）</h4>
              <Textarea
                placeholder="请输入状态变更的原因..."
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                className="min-h-[80px]"
              />
            </div>
          )}

          {/* 操作预览 */}
          {targetStatus && selectedCoursesData.length > 0 && (
            <div className="p-4 bg-muted rounded-lg space-y-2">
              <h5 className="font-medium text-sm">操作预览</h5>
              <div className="text-sm text-muted-foreground">
                <p>
                  将 {selectedCoursesData.length} 个课程的状态更新为「
                  {
                    STATUS_CONFIG[targetStatus as keyof typeof STATUS_CONFIG]
                      .label
                  }
                  」
                </p>
                <div className="mt-2 space-y-1">
                  {selectedCoursesData.map((course) => {
                    const currentStatus = STATUS_CONFIG[course.status];
                    const newStatus =
                      STATUS_CONFIG[targetStatus as keyof typeof STATUS_CONFIG];
                    return (
                      <div
                        key={course.id}
                        className="flex items-center text-xs"
                      >
                        <span className="flex-1 truncate">{course.title}</span>
                        <span className="mx-2">:</span>
                        <span>{currentStatus.label}</span>
                        <span className="mx-1">→</span>
                        <span>{newStatus.label}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => handleOpenChange(false)}
            disabled={loading}
          >
            取消
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading || selectedCourses.length === 0 || !targetStatus}
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                更新中...
              </>
            ) : (
              <>确认更新 ({selectedCourses.length})</>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
