"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@workspace/ui/components/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Switch } from "@workspace/ui/components/switch";
import { Badge } from "@workspace/ui/components/badge";
import {
  Plus,
  X,
  Loader2,
  GripVertical,
  ChevronDown,
  ChevronRight,
  Play,
  Clock,
} from "lucide-react";
import { toast } from "sonner";
import { useCourseCategories } from "@/hooks/useCourses";
import { FileUpload } from "@/components/upload/file-upload";

// 课时数据结构
const lessonSchema = z.object({
  id: z.string().optional(), // 编辑时会有id
  title: z
    .string()
    .min(1, "课时标题不能为空")
    .max(100, "课时标题不能超过100个字符"),
  description: z.string().max(500, "课时描述不能超过500个字符").optional(),
  videoUrl: z.string().min(1, "请输入视频URL"),
  videoDuration: z.number().min(1, "视频时长必须大于0"),
  videoSize: z.number().optional(),
  order: z.number().min(0, "排序不能为负数"),
  isFree: z.boolean(),
});

// 章节数据结构
const chapterSchema = z.object({
  id: z.string().optional(), // 编辑时会有id
  title: z
    .string()
    .min(1, "章节标题不能为空")
    .max(100, "章节标题不能超过100个字符"),
  description: z.string().max(500, "章节描述不能超过500个字符").optional(),
  order: z.number().min(0, "排序不能为负数"),
  lessons: z.array(lessonSchema),
});

type LessonData = z.infer<typeof lessonSchema>;
type ChapterData = z.infer<typeof chapterSchema>;

// 课时管理组件
interface LessonManagerProps {
  lessons: LessonData[];
  onChange: (lessons: LessonData[]) => void;
}

function LessonManager({ lessons, onChange }: LessonManagerProps) {
  // 添加新课时
  const addLesson = () => {
    const newLesson: LessonData = {
      id: `temp-lesson-${Date.now()}`,
      title: "",
      description: "",
      videoUrl: "", // 临时占位符，用户需要替换
      videoDuration: 1,
      order: lessons.length,
      isFree: false,
    };
    onChange([...lessons, newLesson]);
  };

  // 更新课时
  const updateLesson = (index: number, field: keyof LessonData, value: any) => {
    const updatedLessons = [...lessons];
    const currentLesson = updatedLessons[index];
    if (currentLesson) {
      updatedLessons[index] = {
        ...currentLesson,
        [field]: value,
      };
      onChange(updatedLessons);
    }
  };

  // 删除课时
  const removeLesson = (index: number) => {
    const updatedLessons = lessons.filter((_, i) => i !== index);
    // 重新排序
    const reorderedLessons = updatedLessons.map((lesson, i) => ({
      ...lesson,
      order: i,
    }));
    onChange(reorderedLessons);
  };

  // 移动课时
  const moveLesson = (fromIndex: number, toIndex: number) => {
    const updatedLessons = [...lessons];
    const [movedLesson] = updatedLessons.splice(fromIndex, 1);
    if (!movedLesson) return;

    updatedLessons.splice(toIndex, 0, movedLesson);

    // 重新排序
    const reorderedLessons = updatedLessons.map((lesson, i) => ({
      ...lesson,
      order: i,
    }));
    onChange(reorderedLessons);
  };

  // 格式化时长显示
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    }
    return `${minutes}:${secs.toString().padStart(2, "0")}`;
  };

  return (
    <div className="space-y-3">
      {lessons.length === 0 ? (
        <div className="text-center py-6 text-muted-foreground">
          <p className="text-sm">暂无课时</p>
        </div>
      ) : (
        <div className="space-y-2">
          {lessons.map((lesson, index) => (
            <div
              key={lesson.id || index}
              className="border rounded p-3 space-y-3"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <GripVertical className="h-4 w-4 text-muted-foreground cursor-move" />
                  <Play className="h-4 w-4 text-blue-500" />
                  <span className="text-sm font-medium">课时 {index + 1}</span>
                  {lesson.isFree && (
                    <Badge variant="secondary" className="text-xs">
                      免费
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-1">
                  {index > 0 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => moveLesson(index, index - 1)}
                    >
                      ↑
                    </Button>
                  )}
                  {index < lessons.length - 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => moveLesson(index, index + 1)}
                    >
                      ↓
                    </Button>
                  )}
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeLesson(index)}
                    className="text-destructive hover:text-destructive"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-3">
                <div>
                  <label className="text-xs font-medium text-muted-foreground">
                    课时标题
                  </label>
                  <Input
                    placeholder="请输入课时标题"
                    value={lesson.title}
                    onChange={(e) =>
                      updateLesson(index, "title", e.target.value)
                    }
                    className="mt-1"
                  />
                </div>

                <div>
                  <label className="text-xs font-medium text-muted-foreground">
                    课时描述
                  </label>
                  <Textarea
                    placeholder="请输入课时描述（可选）"
                    value={lesson.description || ""}
                    onChange={(e) =>
                      updateLesson(index, "description", e.target.value)
                    }
                    className="mt-1 resize-none"
                    rows={2}
                  />
                </div>

                <div>
                  <label className="text-xs font-medium text-muted-foreground">
                    视频上传
                  </label>
                  <FileUpload
                    className="mt-1"
                    type="video"
                    folder="videos"
                    placeholder="点击上传预览视频"
                    value={lesson.videoUrl}
                    onChange={(url) => updateLesson(index, "videoUrl", url)}
                    maxSize={500 * 1024 * 1024} // 100MB
                  />
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="text-xs font-medium text-muted-foreground">
                      时长（秒）
                    </label>
                    <Input
                      type="number"
                      placeholder="0"
                      value={lesson.videoDuration}
                      onChange={(e) =>
                        updateLesson(
                          index,
                          "videoDuration",
                          Number(e.target.value) || 1,
                        )
                      }
                      className="mt-1"
                    />
                    {lesson.videoDuration > 0 && (
                      <p className="text-xs text-muted-foreground mt-1">
                        <Clock className="h-3 w-3 inline mr-1" />
                        {formatDuration(lesson.videoDuration)}
                      </p>
                    )}
                  </div>

                  <div className="flex items-center justify-end">
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={lesson.isFree}
                        onCheckedChange={(checked) =>
                          updateLesson(index, "isFree", checked)
                        }
                      />
                      <label className="text-xs font-medium">免费试看</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={addLesson}
        className="w-full"
      >
        <Plus className="mr-2 h-4 w-4" />
        添加课时
      </Button>
    </div>
  );
}

// 章节管理组件
interface ChapterManagerProps {
  chapters: ChapterData[];
  onChange: (chapters: ChapterData[]) => void;
}

function ChapterManager({ chapters, onChange }: ChapterManagerProps) {
  const [expandedChapters, setExpandedChapters] = useState<string[]>([]);

  // 添加新章节
  const addChapter = () => {
    const newChapter: ChapterData = {
      id: `temp-${Date.now()}`, // 临时ID
      title: "",
      description: "",
      order: chapters.length,
      lessons: [], // 初始化空的课时数组
    };
    onChange([...chapters, newChapter]);
    // 自动展开新章节
    setExpandedChapters([...expandedChapters, newChapter.id!]);
  };

  // 更新章节
  const updateChapter = (
    index: number,
    field: keyof ChapterData,
    value: any,
  ) => {
    const updatedChapters = [...chapters];
    const currentChapter = updatedChapters[index];
    if (currentChapter) {
      updatedChapters[index] = {
        ...currentChapter,
        [field]: value,
      };
      onChange(updatedChapters);
    }
  };

  // 删除章节
  const removeChapter = (index: number) => {
    const chapter = chapters[index];
    if (!chapter) return;

    const chapterId = chapter.id;
    const updatedChapters = chapters.filter((_, i) => i !== index);
    // 重新排序
    const reorderedChapters = updatedChapters.map((chapter, i) => ({
      ...chapter,
      order: i,
    }));
    onChange(reorderedChapters);

    // 从展开列表中移除
    if (chapterId) {
      setExpandedChapters(expandedChapters.filter((id) => id !== chapterId));
    }
  };

  // 移动章节
  const moveChapter = (fromIndex: number, toIndex: number) => {
    const updatedChapters = [...chapters];
    const [movedChapter] = updatedChapters.splice(fromIndex, 1);
    if (!movedChapter) return;

    updatedChapters.splice(toIndex, 0, movedChapter);

    // 重新排序
    const reorderedChapters = updatedChapters.map((chapter, i) => ({
      ...chapter,
      order: i,
    }));
    onChange(reorderedChapters);
  };

  // 切换章节展开状态
  const toggleChapter = (chapterId: string) => {
    if (expandedChapters.includes(chapterId)) {
      setExpandedChapters(expandedChapters.filter((id) => id !== chapterId));
    } else {
      setExpandedChapters([...expandedChapters, chapterId]);
    }
  };

  // 更新章节的课时
  const updateChapterLessons = (
    chapterIndex: number,
    lessons: LessonData[],
  ) => {
    updateChapter(chapterIndex, "lessons", lessons);
  };

  return (
    <div className="space-y-4">
      {chapters.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          <p>暂无章节，点击下方按钮添加第一个章节</p>
        </div>
      ) : (
        <div className="space-y-3">
          {chapters.map((chapter, index) => {
            const isExpanded = expandedChapters.includes(chapter.id || "");
            const lessonCount = chapter.lessons?.length || 0;
            const totalDuration =
              chapter.lessons?.reduce(
                (total, lesson) => total + (lesson.videoDuration || 0),
                0,
              ) || 0;

            return (
              <div key={chapter.id || index} className="border rounded-lg">
                {/* 章节头部 */}
                <div className="flex items-center justify-between p-4 bg-muted/50">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-1">
                      <GripVertical className="h-4 w-4 text-muted-foreground cursor-move" />
                      <button
                        type="button"
                        onClick={() => toggleChapter(chapter.id || "")}
                        className="p-1 hover:bg-background rounded"
                      >
                        {isExpanded ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </button>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          第 {index + 1} 章：{chapter.title || "未命名章节"}
                        </span>
                        <Badge variant="outline" className="text-xs">
                          {lessonCount} 课时
                        </Badge>
                        {totalDuration > 0 && (
                          <Badge variant="outline" className="text-xs">
                            <Clock className="h-3 w-3 mr-1" />
                            {Math.floor(totalDuration / 60)}分钟
                          </Badge>
                        )}
                      </div>
                      {chapter.description && (
                        <p className="text-sm text-muted-foreground mt-1">
                          {chapter.description}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {index > 0 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => moveChapter(index, index - 1)}
                      >
                        ↑
                      </Button>
                    )}
                    {index < chapters.length - 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => moveChapter(index, index + 1)}
                      >
                        ↓
                      </Button>
                    )}
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeChapter(index)}
                      className="text-destructive hover:text-destructive"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* 章节编辑表单 */}
                {isExpanded && (
                  <div className="p-4 space-y-4 border-t">
                    <div className="grid grid-cols-1 gap-4">
                      <div>
                        <label className="text-sm font-medium">章节标题</label>
                        <Input
                          placeholder="请输入章节标题"
                          value={chapter.title}
                          onChange={(e) =>
                            updateChapter(index, "title", e.target.value)
                          }
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium">章节描述</label>
                        <Textarea
                          placeholder="请输入章节描述（可选）"
                          value={chapter.description || ""}
                          onChange={(e) =>
                            updateChapter(index, "description", e.target.value)
                          }
                          className="mt-1 resize-none"
                          rows={2}
                        />
                      </div>
                    </div>

                    {/* 课时管理 */}
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium">课时内容</h4>
                        <Badge variant="secondary" className="text-xs">
                          {chapter.lessons?.length || 0} 个课时
                        </Badge>
                      </div>
                      <LessonManager
                        lessons={chapter.lessons || []}
                        onChange={(lessons) =>
                          updateChapterLessons(index, lessons)
                        }
                      />
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}

      <Button
        type="button"
        variant="outline"
        onClick={addChapter}
        className="w-full"
      >
        <Plus className="mr-2 h-4 w-4" />
        添加章节
      </Button>
    </div>
  );
}

// 表单验证schema
const courseFormSchema = z.object({
  title: z
    .string()
    .min(1, "课程标题不能为空")
    .max(100, "标题不能超过100个字符"),
  subtitle: z.string().max(200, "副标题不能超过200个字符").optional(),
  description: z.string().min(10, "课程描述至少需要10个字符"),
  cover: z.string().url("请输入有效的图片URL"),
  categoryId: z.string().min(1, "请选择课程分类"),
  level: z.enum(["BEGINNER", "INTERMEDIATE", "ADVANCED"], {
    required_error: "请选择课程级别",
  }),
  price: z.number().min(0, "价格不能为负数"),
  originalPrice: z.number().min(0, "原价不能为负数").optional(),
  isFree: z.boolean(),
  requireLogin: z.boolean(),
  instructorName: z.string().min(1, "讲师姓名不能为空"),
  instructorTitle: z.string().optional(),
  instructorAvatar: z.string().url("请输入有效的头像URL").optional(),
  instructorBio: z.string().optional(),
  previewVideo: z.string().url("请输入有效的视频URL").optional(),
  tags: z.array(z.string()),
  metaTitle: z.string().optional(),
  metaDescription: z.string().optional(),
  metaKeywords: z.array(z.string()),
  chapters: z.array(chapterSchema),
});

type CourseFormData = z.infer<typeof courseFormSchema>;

// 课程级别选项
const courseLevels = [
  { value: "BEGINNER", label: "初级" },
  { value: "INTERMEDIATE", label: "中级" },
  { value: "ADVANCED", label: "高级" },
];

interface CourseFormProps {
  mode: "create" | "edit";
  initialData?: Partial<CourseFormData> & { id?: string };
  onSuccess?: () => void;
}

export function CourseForm({ mode, initialData, onSuccess }: CourseFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [newTag, setNewTag] = useState("");
  const [newKeyword, setNewKeyword] = useState("");

  // 获取课程分类数据
  const {
    categories,
    loading: categoriesLoading,
    error: categoriesError,
  } = useCourseCategories();

  const form = useForm<CourseFormData>({
    resolver: zodResolver(courseFormSchema),
    defaultValues: {
      title: "",
      subtitle: "",
      description: "",
      cover: "",
      categoryId: "",
      level: "BEGINNER",
      price: 0,
      originalPrice: 0,
      isFree: false,
      requireLogin: true,
      instructorName: "",
      instructorTitle: "",
      instructorAvatar: "",
      instructorBio: "",
      previewVideo: "",
      tags: [],
      metaTitle: "",
      metaDescription: "",
      metaKeywords: [],
      chapters: [],
      ...initialData,
    },
  });

  const watchIsFree = form.watch("isFree");

  // 监听免费切换
  useEffect(() => {
    if (watchIsFree) {
      form.setValue("price", 0);
      form.setValue("originalPrice", 0);
    }
  }, [watchIsFree, form]);

  // 移除自动更新原价的逻辑，让用户可以自由编辑

  // 提交表单
  const onSubmit = async (data: CourseFormData) => {
    setLoading(true);
    try {
      // 这里应该调用实际的API
      if (mode === "create") {
        await fetch("/api/courses", {
          method: "POST",
          body: JSON.stringify(data),
        });
        toast.success("课程创建成功！");
        router.push("/courses");
      } else {
        await fetch("/api/courses", {
          method: "PATCH",
          body: JSON.stringify(data),
        });
        toast.success("课程更新成功！");
        onSuccess?.();
      }
    } catch (error) {
      console.error("保存失败:", error);
      toast.error("保存失败，请重试");
    } finally {
      setLoading(false);
    }
  };

  // 添加标签
  const addTag = () => {
    if (newTag && !form.getValues("tags").includes(newTag)) {
      const currentTags = form.getValues("tags");
      form.setValue("tags", [...currentTags, newTag]);
      setNewTag("");
    }
  };

  // 删除标签
  const removeTag = (tagToRemove: string) => {
    const currentTags = form.getValues("tags");
    form.setValue(
      "tags",
      currentTags.filter((tag) => tag !== tagToRemove),
    );
  };

  // 添加关键词
  const addKeyword = () => {
    if (newKeyword && !form.getValues("metaKeywords").includes(newKeyword)) {
      const currentKeywords = form.getValues("metaKeywords");
      form.setValue("metaKeywords", [...currentKeywords, newKeyword]);
      setNewKeyword("");
    }
  };

  // 删除关键词
  const removeKeyword = (keywordToRemove: string) => {
    const currentKeywords = form.getValues("metaKeywords");
    form.setValue(
      "metaKeywords",
      currentKeywords.filter((keyword) => keyword !== keywordToRemove),
    );
  };

  return (
    <div className="mx-auto">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          {/* 主要内容区域 - 左右布局 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* 左侧内容 - 占据2/3宽度 */}
            <div className="lg:col-span-2 space-y-8">
              {/* 基本信息 */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium">基本信息</h3>
                  <p className="text-sm text-muted-foreground">
                    填写课程的基本信息和详细描述
                  </p>
                </div>

                <div className="grid grid-cols-1 gap-6">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>课程标题</FormLabel>
                        <FormControl>
                          <Input placeholder="请输入课程标题" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="subtitle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>副标题</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="请输入副标题（可选）"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          副标题将显示在课程标题下方，用于补充说明
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="categoryId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>课程分类</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="选择课程分类" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {categoriesLoading ? (
                                <SelectItem value="loading" disabled>
                                  加载中...
                                </SelectItem>
                              ) : categoriesError ? (
                                <SelectItem value="error" disabled>
                                  加载失败，请重试
                                </SelectItem>
                              ) : (
                                categories.map((category) => (
                                  <SelectItem
                                    key={category.id}
                                    value={category.id}
                                  >
                                    {category.name}
                                  </SelectItem>
                                ))
                              )}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="level"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>课程级别</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="选择课程级别" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {courseLevels.map((level) => (
                                <SelectItem
                                  key={level.value}
                                  value={level.value}
                                >
                                  {level.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>课程描述</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="请详细描述课程内容、适用人群、学习目标等"
                            className="resize-none"
                            rows={4}
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          详细的课程描述有助于学生了解课程内容和价值
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="cover"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>课程封面</FormLabel>
                        <FormControl>
                          <FileUpload
                            type="image"
                            folder="courses"
                            placeholder="点击上传课程封面图片"
                            value={field.value}
                            onChange={field.onChange}
                            maxSize={5 * 1024 * 1024} // 5MB
                          />
                        </FormControl>
                        <FormDescription>
                          建议使用16:9比例的图片，分辨率至少800x450像素，最大5MB
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="previewVideo"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>预览视频</FormLabel>
                        <FormControl>
                          <FileUpload
                            type="video"
                            folder="videos"
                            placeholder="点击上传预览视频"
                            value={field.value}
                            onChange={field.onChange}
                            maxSize={1024 * 1024 * 1024} // 1GB
                          />
                        </FormControl>
                        <FormDescription>
                          预览视频可以帮助学生更好地了解课程内容，最大1GB
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* 标签管理 */}
                  <div className="space-y-3">
                    <FormLabel>课程标签</FormLabel>
                    <div className="flex flex-wrap gap-2">
                      {form.watch("tags").map((tag) => (
                        <Badge key={tag} variant="secondary" className="gap-1">
                          {tag}
                          <button
                            type="button"
                            onClick={() => removeTag(tag)}
                            className="hover:text-destructive"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Input
                        placeholder="添加标签"
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            addTag();
                          }
                        }}
                      />
                      <Button type="button" variant="outline" onClick={addTag}>
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    <FormDescription>
                      标签有助于学生搜索和发现相关课程
                    </FormDescription>
                  </div>
                </div>
              </div>

              {/* 讲师信息 */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium">讲师信息</h3>
                  <p className="text-sm text-muted-foreground">
                    填写课程讲师的详细信息
                  </p>
                </div>

                <div className="grid grid-cols-1 gap-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="instructorName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>讲师姓名</FormLabel>
                          <FormControl>
                            <Input placeholder="请输入讲师姓名" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="instructorTitle"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>讲师职称</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="请输入讲师职称（可选）"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="instructorAvatar"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>讲师头像</FormLabel>
                        <FormControl>
                          <FileUpload
                            type="image"
                            folder="instructors"
                            placeholder="点击上传讲师头像"
                            value={field.value}
                            onChange={field.onChange}
                            maxSize={2 * 1024 * 1024} // 2MB
                          />
                        </FormControl>
                        <FormDescription>
                          建议使用正方形图片，分辨率至少200x200像素，最大2MB
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="instructorBio"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>讲师简介</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="请输入讲师简介（可选）"
                            className="resize-none"
                            rows={3}
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          讲师简介有助于建立学生对课程的信任
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* 价格和访问设置 */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium">价格和访问设置</h3>
                  <p className="text-sm text-muted-foreground">
                    配置课程的价格和访问权限
                  </p>
                </div>

                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="isFree"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">免费课程</FormLabel>
                          <FormDescription>
                            开启后课程将免费向所有用户开放
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {!watchIsFree && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="price"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>现价</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="0"
                                {...field}
                                onChange={(e) =>
                                  field.onChange(Number(e.target.value) || 0)
                                }
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="originalPrice"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>原价</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="0"
                                {...field}
                                onChange={(e) =>
                                  field.onChange(Number(e.target.value) || 0)
                                }
                              />
                            </FormControl>
                            <FormDescription>
                              用于显示折扣信息，应大于等于现价
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}

                  <FormField
                    control={form.control}
                    name="requireLogin"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">需要登录</FormLabel>
                          <FormDescription>
                            开启后用户需要登录才能查看课程内容
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* SEO设置 */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium">SEO设置</h3>
                  <p className="text-sm text-muted-foreground">
                    优化课程在搜索引擎中的表现
                  </p>
                </div>

                <div className="grid grid-cols-1 gap-6">
                  <FormField
                    control={form.control}
                    name="metaTitle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>页面标题</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="请输入页面标题（可选）"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          如果不填写，将使用课程标题作为页面标题
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="metaDescription"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>页面描述</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="请输入页面描述（可选）"
                            className="resize-none"
                            rows={3}
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          页面描述将显示在搜索引擎结果中
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* 关键词管理 */}
                  <div className="space-y-3">
                    <FormLabel>SEO关键词</FormLabel>
                    <div className="flex flex-wrap gap-2">
                      {form.watch("metaKeywords").map((keyword) => (
                        <Badge
                          key={keyword}
                          variant="outline"
                          className="gap-1"
                        >
                          {keyword}
                          <button
                            type="button"
                            onClick={() => removeKeyword(keyword)}
                            className="hover:text-destructive"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Input
                        placeholder="添加关键词"
                        value={newKeyword}
                        onChange={(e) => setNewKeyword(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            addKeyword();
                          }
                        }}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={addKeyword}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    <FormDescription>关键词有助于搜索引擎优化</FormDescription>
                  </div>
                </div>
              </div>
            </div>

            {/* 右侧内容 - 章节管理 */}
            <div className="lg:col-span-1">
              <div className="sticky top-6 space-y-6">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium">章节管理</h3>
                    <p className="text-sm text-muted-foreground">
                      为课程添加章节，组织课程内容
                    </p>
                  </div>

                  <ChapterManager
                    chapters={form.watch("chapters")}
                    onChange={(chapters) => form.setValue("chapters", chapters)}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* 提交按钮 */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {mode === "create" ? "创建课程" : "保存更改"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
