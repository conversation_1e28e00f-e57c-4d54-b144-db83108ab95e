"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Badge } from "@workspace/ui/components/badge";
import {
  <PERSON><PERSON>,
  DialogContent,
  Dialog<PERSON>eader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import {
  Search,
  User,
  ChevronsUpDown,
  Users,
  Mail,
  Phone,
  UserPlus,
  Loader2,
  X,
} from "lucide-react";
import { cn } from "@workspace/ui/lib/utils";
import { UserRole } from "@workspace/database";
import { toast } from "sonner";

// 用户接口定义
export interface User {
  id: string;
  email: string;
  username: string;
  name: string;
  avatar?: string;
  phone?: string;
  bio?: string;
  isActive: boolean;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
  // 租户用户信息
  tenantUsers?: {
    id: string;
    role: UserRole;
    isActive: boolean;
    joinedAt: string;
    tenant: {
      id: string;
      name: string;
    };
  }[];
}

export interface UserSearchQuery {
  search?: string;
  role?: UserRole;
  isActive?: boolean;
  emailVerified?: boolean;
  page?: number;
  limit?: number;
}

export interface UserSearchResponse {
  data: User[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Props 接口
export interface UserSearchSelectProps {
  value?: User;
  onSelect: (user: User) => void;
  onClear?: () => void;
  placeholder?: string;
  disabled?: boolean;
  allowCreate?: boolean;
  filterRoles?: UserRole[];
  className?: string;
}

export interface UserSearchDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSelect: (user: User) => void;
  filterRoles?: UserRole[];
  allowCreate?: boolean;
}

export interface QuickCreateUserProps {
  onSuccess: (user: User) => void;
  onCancel: () => void;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

// 用户搜索 API hook
export function useUserSearch(query: UserSearchQuery = {}) {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);

  const fetchUsers = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();

      if (query.search) params.append("search", query.search);
      if (query.role) params.append("role", query.role);
      if (query.isActive !== undefined)
        params.append("isActive", query.isActive.toString());
      if (query.emailVerified !== undefined)
        params.append("emailVerified", query.emailVerified.toString());
      if (query.page) params.append("page", query.page.toString());
      if (query.limit) params.append("limit", query.limit.toString());

      const response = await fetch(`/api/users/search?${params.toString()}`);
      const result: UserSearchResponse = await response.json();

      if (response.ok) {
        setUsers(result.data);
        setTotal(result.total);
      } else {
        throw new Error("搜索用户失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "搜索用户失败");
    } finally {
      setLoading(false);
    }
  }, [query]);

  useEffect(() => {
    const timer = setTimeout(() => {
      fetchUsers();
    }, 300); // 防抖

    return () => clearTimeout(timer);
  }, [fetchUsers]);

  return {
    users,
    loading,
    error,
    total,
    refetch: fetchUsers,
  };
}

// 快速创建用户组件
export function QuickCreateUser({
  onSuccess,
  onCancel,
  open,
  onOpenChange,
}: QuickCreateUserProps) {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    username: "",
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setErrors({});

    try {
      // 基础验证
      const newErrors: Record<string, string> = {};
      if (!formData.name.trim()) newErrors.name = "请输入姓名";
      if (!formData.email.trim()) newErrors.email = "请输入邮箱";
      if (!formData.username.trim()) newErrors.username = "请输入用户名";

      if (Object.keys(newErrors).length > 0) {
        setErrors(newErrors);
        return;
      }

      const response = await fetch("/api/users", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          password: "123456", // 默认密码
        }),
      });

      const result = await response.json();

      if (response.ok) {
        onSuccess(result.data);
        setFormData({
          name: "",
          email: "",
          phone: "",
          username: "",
        });
      } else {
        setErrors({ form: result.message || "创建用户失败" });
      }
    } catch (error) {
      setErrors({ form: "创建用户失败" });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>快速创建用户</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">姓名 *</label>
            <Input
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              placeholder="请输入姓名"
              disabled={loading}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name}</p>
            )}
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">邮箱 *</label>
            <Input
              type="email"
              value={formData.email}
              onChange={(e) =>
                setFormData({ ...formData, email: e.target.value })
              }
              placeholder="请输入邮箱"
              disabled={loading}
            />
            {errors.email && (
              <p className="text-sm text-red-500">{errors.email}</p>
            )}
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">用户名 *</label>
            <Input
              value={formData.username}
              onChange={(e) =>
                setFormData({ ...formData, username: e.target.value })
              }
              placeholder="请输入用户名"
              disabled={loading}
            />
            {errors.username && (
              <p className="text-sm text-red-500">{errors.username}</p>
            )}
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">手机号</label>
            <Input
              value={formData.phone}
              onChange={(e) =>
                setFormData({ ...formData, phone: e.target.value })
              }
              placeholder="请输入手机号"
              disabled={loading}
            />
          </div>

          {/* <div className="space-y-2"> */}
          {/*   <label className="text-sm font-medium">角色</label> */}
          {/*   <Select */}
          {/*     value={formData.role} */}
          {/*     onValueChange={(value: UserRole) => */}
          {/*       setFormData({ ...formData, role: value }) */}
          {/*     } */}
          {/*     disabled={loading} */}
          {/*   > */}
          {/*     <SelectTrigger> */}
          {/*       <SelectValue /> */}
          {/*     </SelectTrigger> */}
          {/*     <SelectContent> */}
          {/*       <SelectItem value="STUDENT">学生</SelectItem> */}
          {/*       <SelectItem value="PARENT">家长</SelectItem> */}
          {/*       <SelectItem value="COMMUNITY_USER">社区用户</SelectItem> */}
          {/*     </SelectContent> */}
          {/*   </Select> */}
          {/* </div> */}

          {errors.form && (
            <div className="text-sm text-red-500">{errors.form}</div>
          )}

          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              创建用户
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}

// 用户搜索对话框组件
export function UserSearchDialog({
  open,
  onOpenChange,
  onSelect,
  filterRoles,
  allowCreate = true,
}: UserSearchDialogProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState<string>("all");
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  const query = useMemo(
    () => ({
      search: searchTerm || undefined,
      role:
        roleFilter !== "all" && filterRoles?.includes(roleFilter as UserRole)
          ? (roleFilter as UserRole)
          : undefined,
      isActive: true,
      page: 1,
      limit: 50,
    }),
    [searchTerm, roleFilter, filterRoles],
  );

  const { users, loading, error } = useUserSearch(query);

  // 过滤用户（如果有角色限制）
  const filteredUsers = useMemo(() => {
    if (!filterRoles || filterRoles.length === 0) return users;

    return users.filter((user) =>
      user.tenantUsers?.some((tu) => filterRoles.includes(tu.role)),
    );
  }, [users, filterRoles]);

  const handleUserSelect = (user: User) => {
    onSelect(user);
    onOpenChange?.(false);
  };

  const handleCreateSuccess = (user: User) => {
    toast.success("用户创建成功");
    setShowCreateDialog(false);
    handleUserSelect(user);
  };

  // 获取用户角色显示
  const getUserRoles = (user: User) => {
    if (!user.tenantUsers || user.tenantUsers.length === 0) {
      return ["社区用户"];
    }

    const roleNames: Record<UserRole, string> = {
      SUPER_ADMIN: "超级管理员",
      ADMIN: "管理员",
      OPERATOR: "运营人员",
      TENANT_ADMIN: "租户管理员",
      PLANNER: "规划师",
      STUDENT: "学生",
      PARENT: "家长",
      COMMUNITY_USER: "社区用户",
      GUEST: "访客",
    };

    return user.tenantUsers.map((tu) => roleNames[tu.role] || tu.role);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[800px] max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between pt-6">
              <span>选择用户</span>
              {allowCreate && (
                <Button
                  size="sm"
                  onClick={() => setShowCreateDialog(true)}
                  className="ml-2"
                >
                  <UserPlus className="mr-2 h-4 w-4" />
                  新建用户
                </Button>
              )}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {/* 搜索和过滤 */}
            <div className="flex space-x-2">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索用户名、姓名、邮箱..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>

              {filterRoles && filterRoles.length > 0 && (
                <Select value={roleFilter} onValueChange={setRoleFilter}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="角色" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部角色</SelectItem>
                    {filterRoles.map((role) => (
                      <SelectItem key={role} value={role}>
                        {role === "STUDENT"
                          ? "学生"
                          : role === "PARENT"
                            ? "家长"
                            : role === "PLANNER"
                              ? "规划师"
                              : role}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>

            {/* 用户列表 */}
            <div className="border rounded-md max-h-[400px] overflow-auto">
              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span className="ml-2">搜索中...</span>
                </div>
              ) : error ? (
                <div className="flex items-center justify-center h-32 text-red-500">
                  {error}
                </div>
              ) : filteredUsers.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-32 text-muted-foreground">
                  <Users className="h-8 w-8 mb-2" />
                  <span>没有找到符合条件的用户</span>
                  {allowCreate && searchTerm && (
                    <Button
                      variant="link"
                      size="sm"
                      onClick={() => setShowCreateDialog(true)}
                      className="mt-2"
                    >
                      创建新用户
                    </Button>
                  )}
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>用户信息</TableHead>
                      <TableHead>联系方式</TableHead>
                      <TableHead>角色</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            {user.avatar ? (
                              <img
                                src={user.avatar}
                                alt={user.name}
                                className="h-8 w-8 rounded-full"
                              />
                            ) : (
                              <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center">
                                <User className="h-4 w-4" />
                              </div>
                            )}
                            <div>
                              <p className="font-medium">{user.name}</p>
                              <p className="text-sm text-muted-foreground">
                                @{user.username}
                              </p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center text-sm">
                              <Mail className="mr-1 h-3 w-3" />
                              {user.email}
                            </div>
                            {user.phone && (
                              <div className="flex items-center text-sm text-muted-foreground">
                                <Phone className="mr-1 h-3 w-3" />
                                {user.phone}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {getUserRoles(user).map((role, index) => (
                              <Badge key={index} variant="secondary">
                                {role}
                              </Badge>
                            ))}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col space-y-1">
                            <Badge
                              variant={user.isActive ? "default" : "secondary"}
                            >
                              {user.isActive ? "正常" : "禁用"}
                            </Badge>
                            {user.emailVerified && (
                              <Badge variant="outline">已验证</Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Button
                            size="sm"
                            onClick={() => handleUserSelect(user)}
                          >
                            选择
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 快速创建用户对话框 */}
      <QuickCreateUser
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSuccess={handleCreateSuccess}
        onCancel={() => setShowCreateDialog(false)}
      />
    </>
  );
}

// 主要的用户搜索选择组件
export default function UserSearchSelect({
  value,
  onSelect,
  onClear,
  placeholder = "选择用户",
  disabled = false,
  allowCreate = true,
  filterRoles,
  className,
}: UserSearchSelectProps) {
  const [open, setOpen] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);

  const handleSelect = (user: User) => {
    onSelect(user);
    setOpen(false);
  };

  const handleClear = () => {
    onClear?.();
    setOpen(false);
  };

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn("w-full justify-between", className)}
            disabled={disabled}
          >
            {value ? (
              <div className="flex items-center space-x-2">
                {value.avatar ? (
                  <img
                    src={value.avatar}
                    alt={value.name}
                    className="h-5 w-5 rounded-full"
                  />
                ) : (
                  <User className="h-4 w-4" />
                )}
                <span className="truncate">{value.name}</span>
                <Badge variant="secondary">@{value.username}</Badge>
              </div>
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
            <div className="flex items-center space-x-1">
              {value && onClear && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleClear();
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0">
          <div className="p-2">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => {
                setOpen(false);
                setSearchOpen(true);
              }}
            >
              <Search className="mr-2 h-4 w-4" />
              搜索更多用户
            </Button>
          </div>
        </PopoverContent>
      </Popover>

      {/* 用户搜索对话框 */}
      <UserSearchDialog
        open={searchOpen}
        onOpenChange={setSearchOpen}
        onSelect={handleSelect}
        filterRoles={filterRoles}
        allowCreate={allowCreate}
      />
    </>
  );
}
