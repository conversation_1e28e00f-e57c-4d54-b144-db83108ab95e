"use client";

import {
  SidebarProvider,
  SidebarInset,
} from "@workspace/ui/components/sidebar";
import { Header } from "./header";
import { AppSidebar } from "./app-sidebar";
import { unstable_ViewTransition as ViewTransition } from "react";

interface AdminLayoutProps {
  children: React.ReactNode;
}

export function AdminLayout({ children }: AdminLayoutProps) {
  return (
    <div className="flex h-screen bg-background">
      <SidebarProvider>
        <ViewTransition name="transition-layout">
          <AppSidebar />
          <SidebarInset>
            <Header />
            <main className="flex-1 space-y-4 p-4 pt-6 overflow-y-auto bg-muted/10">
              {children}
            </main>
          </SidebarInset>
        </ViewTransition>
      </SidebarProvider>
    </div>
  );
}
