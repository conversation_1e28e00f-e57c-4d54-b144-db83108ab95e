"use client";

import { AuthContext } from "@/lib/auth/context";
import { Badge } from "@workspace/ui/components/badge";
import { Button } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { Bell, LogOut, MoreVertical, Settings, User } from "lucide-react";
import { useContext } from "react";
import { ThemeToggle } from "@/components/theme-toggle";

export function MobileMenu() {
  const { user, logout } = useContext(AuthContext)!;

  const handleLogout = async () => {
    await logout();
    window.location.href = "/login";
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="sm:hidden">
          <MoreVertical className="h-5 w-5" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">{user?.name}</p>
            <p className="text-xs leading-none text-muted-foreground">
              {user?.email}
            </p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        {/* 通知 */}
        <DropdownMenuItem>
          <Bell className="mr-2 h-4 w-4" />
          <span>通知</span>
          <Badge
            className="ml-auto h-5 w-5 rounded-full p-0 text-xs"
            variant="destructive"
          >
            3
          </Badge>
        </DropdownMenuItem>

        {/* 主题切换 */}
        <DropdownMenuItem asChild>
          <div className="flex w-full items-center">
            <span className="mr-2 h-4 w-4" />
            <span className="flex-1">主题</span>
            <ThemeToggle />
          </div>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem>
          <User className="mr-2 h-4 w-4" />
          <span>个人信息</span>
        </DropdownMenuItem>

        <DropdownMenuItem>
          <Settings className="mr-2 h-4 w-4" />
          <span>账号设置</span>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem onClick={handleLogout}>
          <LogOut className="mr-2 h-4 w-4" />
          <span>退出登录</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
