"use client";

import { useState, useEffect, useMemo, useCallback } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@workspace/ui/lib/utils";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarSeparator,
  useSidebar,
} from "@workspace/ui/components/sidebar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@workspace/ui/components/collapsible";
import { ChevronRight } from "lucide-react";
import { useAuth } from "@/lib/auth/context";
import { getMenuItems } from "@/config/menu.config";
import type { MenuItem } from "@/config/menu.config";

// 持久化存储 key
const STORAGE_KEY = "admin-sidebar-expanded";

export function AppSidebar() {
  const { user } = useAuth();
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const { state } = useSidebar();

  // 获取过滤后的菜单项 - 权限过滤已在 getMenuItems 中完成
  const menuItems = useMemo(
    () => getMenuItems(user?.permissions || []),
    [user?.permissions],
  );

  // 查找当前路径对应的父菜单项
  const findActiveParents = useCallback(
    (items: MenuItem[], path: string): string[] => {
      const parents: string[] = [];

      const traverse = (menuItems: MenuItem[], parentKeys: string[] = []) => {
        for (const item of menuItems) {
          const currentKeys = [...parentKeys];

          if (item.children) {
            // 检查子菜单是否有匹配的路径
            const hasActiveChild = item.children.some(
              (child) => path.startsWith(child.path) && child.path !== "#",
            );

            if (hasActiveChild) {
              parents.push(item.key);
            }

            // 递归检查子菜单
            traverse(item.children, [...currentKeys, item.key]);
          }
        }
      };

      traverse(items);
      return parents;
    },
    [],
  );

  // 初始化展开状态
  useEffect(() => {
    if (!isInitialized) {
      // 从 localStorage 恢复展开状态
      const stored = localStorage.getItem(STORAGE_KEY);
      let initialExpanded: string[] = [];

      if (stored) {
        try {
          initialExpanded = JSON.parse(stored);
        } catch (e) {
          console.error("解析存储的展开状态失败:", e);
        }
      }

      // 根据当前路径找到应该展开的父菜单
      const activeParents = findActiveParents(menuItems, pathname);

      // 合并存储的状态和当前激活状态
      const merged = [...new Set([...initialExpanded, ...activeParents])];
      setExpandedItems(merged);
      setIsInitialized(true);
    }
  }, [pathname, menuItems, findActiveParents, isInitialized]);

  // 保存展开状态到 localStorage
  useEffect(() => {
    if (isInitialized) {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(expandedItems));
    }
  }, [expandedItems, isInitialized]);

  // 切换展开状态
  const toggleExpanded = (key: string) => {
    setExpandedItems((prev) =>
      prev.includes(key) ? prev.filter((item) => item !== key) : [...prev, key],
    );
  };

  // 检查路径是否激活
  const isActivePath = (path: string, children?: MenuItem[]): boolean => {
    if (pathname === path) return true;
    if (children) {
      return children.some((child) => isActivePath(child.path, child.children));
    }
    return false;
  };

  // 渲染菜单项
  const renderMenuItem = (item: MenuItem) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.key);
    const isActive = isActivePath(item.path, item.children);

    if (hasChildren) {
      return (
        <Collapsible
          key={item.key}
          asChild
          open={isExpanded}
          onOpenChange={() => toggleExpanded(item.key)}
        >
          <SidebarMenuItem>
            <CollapsibleTrigger asChild>
              <SidebarMenuButton isActive={isActive} tooltip={item.label}>
                <item.icon />
                <span>{item.label}</span>
                <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
              </SidebarMenuButton>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarMenuSub>
                {item.children?.map((child) => (
                  <SidebarMenuSubItem key={child.key}>
                    <SidebarMenuSubButton
                      asChild
                      isActive={pathname === child.path}
                    >
                      <Link href={child.path}>
                        <child.icon />
                        <span>{child.label}</span>
                      </Link>
                    </SidebarMenuSubButton>
                  </SidebarMenuSubItem>
                ))}
              </SidebarMenuSub>
            </CollapsibleContent>
          </SidebarMenuItem>
        </Collapsible>
      );
    }

    return (
      <SidebarMenuItem key={item.key}>
        <SidebarMenuButton asChild isActive={isActive} tooltip={item.label}>
          <Link href={item.path}>
            <item.icon />
            <span>{item.label}</span>
          </Link>
        </SidebarMenuButton>
      </SidebarMenuItem>
    );
  };

  return (
    <Sidebar collapsible="icon" variant="sidebar" side="left">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <Link href="/">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                  <span className="text-xl font-bold">Q</span>
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">管理后台</span>
                  <span className="truncate text-xs">QYQM Teachers</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => renderMenuItem(item))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        {user && (
          <>
            <SidebarSeparator />
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton size="lg" asChild>
                  <div className="flex items-center">
                    <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                      <span className="text-sm font-medium">
                        {user.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-semibold">
                        {user.name}
                      </span>
                      <span className="truncate text-xs">{user.role}</span>
                    </div>
                  </div>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </>
        )}
      </SidebarFooter>
    </Sidebar>
  );
}
