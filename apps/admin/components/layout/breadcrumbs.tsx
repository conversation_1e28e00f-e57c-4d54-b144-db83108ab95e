"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { useMemo } from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@workspace/ui/components/breadcrumb";
import { useAuth } from "@/lib/auth/context";
import { getMenuItems } from "@/config/menu.config";
import type { MenuItem } from "@/config/menu.config";

interface BreadcrumbItem {
  label: string;
  href?: string;
  isActive?: boolean;
}

export function Breadcrumbs() {
  const pathname = usePathname();
  const { user } = useAuth();

  // 获取菜单项
  const menuItems = useMemo(
    () => getMenuItems(user?.permissions || []),
    [user?.permissions],
  );

  // 生成面包屑数据
  const breadcrumbs = useMemo(() => {
    const items: BreadcrumbItem[] = [{ label: "首页", href: "/" }];

    // 递归查找当前路径对应的菜单项
    const findMenuItem = (items: MenuItem[], path: string): MenuItem[] => {
      for (const item of items) {
        if (item.path === path) {
          return [item];
        }
        if (item.children) {
          const child = findMenuItem(item.children, path);
          if (child.length > 0) {
            return [item, ...child];
          }
        }
      }
      return [];
    };

    const menuPath = findMenuItem(menuItems, pathname);

    // 将菜单路径转换为面包屑
    menuPath.forEach((item, index) => {
      const isLast = index === menuPath.length - 1;
      items.push({
        label: item.label,
        href: isLast ? undefined : item.path,
        isActive: isLast,
      });
    });

    // 如果没有找到对应的菜单项，尝试根据路径生成
    if (menuPath.length === 0 && pathname !== "/") {
      const segments = pathname.split("/").filter(Boolean);
      segments.forEach((segment, index) => {
        const isLast = index === segments.length - 1;
        const href = "/" + segments.slice(0, index + 1).join("/");

        // 简单的路径转标题逻辑
        const label = segment.charAt(0).toUpperCase() + segment.slice(1);

        items.push({
          label,
          href: isLast ? undefined : href,
          isActive: isLast,
        });
      });
    }

    return items;
  }, [pathname, menuItems]);

  if (breadcrumbs.length <= 1) {
    return null;
  }

  // 在移动端只显示前2个和最后1个面包屑项
  const displayBreadcrumbs =
    breadcrumbs.length > 3
      ? [
          breadcrumbs[0],
          { label: "...", href: undefined, isActive: false },
          breadcrumbs[breadcrumbs.length - 1],
        ]
      : breadcrumbs;

  return (
    <Breadcrumb>
      <BreadcrumbList className="flex-wrap">
        {displayBreadcrumbs.map(
          (item, index) =>
            item && (
              <div key={index} className="flex items-center">
                <BreadcrumbItem>
                  {item.label === "..." ? (
                    <span className="text-muted-foreground">...</span>
                  ) : item.isActive ? (
                    <BreadcrumbPage className="max-w-32 truncate md:max-w-none">
                      {item.label}
                    </BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink asChild>
                      <Link
                        href={item.href!}
                        className="max-w-32 truncate md:max-w-none"
                      >
                        {item.label}
                      </Link>
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
                {index < displayBreadcrumbs.length - 1 && (
                  <BreadcrumbSeparator />
                )}
              </div>
            ),
        )}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
