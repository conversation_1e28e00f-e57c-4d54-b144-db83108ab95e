"use client";

import { useState, useEffect, useMemo, useCallback } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@workspace/ui/lib/utils";
import { Button } from "@workspace/ui/components/button";
import { ScrollArea } from "@workspace/ui/components/scroll-area";
import { Separator } from "@workspace/ui/components/separator";
import { ChevronDown, ChevronRight, Menu, X } from "lucide-react";
import { useAuth } from "@/lib/auth/context";
import { getMenuItems } from "@/config/menu.config";
import type { MenuItem } from "@/config/menu.config";

interface SidebarProps {
  className?: string;
  isCollapsed?: boolean;
  onToggle?: () => void;
}

// 持久化存储 key
const STORAGE_KEY = "admin-sidebar-expanded";

export function Sidebar({
  className,
  isCollapsed = false,
  onToggle,
}: SidebarProps) {
  const { user } = useAuth();
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [isMobile, setIsMobile] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // 获取过滤后的菜单项 - 权限过滤已在 getMenuItems 中完成
  const menuItems = useMemo(
    () => getMenuItems(user?.permissions || []),
    [user?.permissions],
  );

  // 检测是否为移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // 查找当前路径对应的父菜单项
  const findActiveParents = useCallback(
    (items: MenuItem[], path: string): string[] => {
      const parents: string[] = [];

      const traverse = (menuItems: MenuItem[], parentKeys: string[] = []) => {
        for (const item of menuItems) {
          const currentKeys = [...parentKeys];

          if (item.children) {
            // 检查子菜单是否有匹配的路径
            const hasActiveChild = item.children.some(
              (child) => path.startsWith(child.path) && child.path !== "#",
            );

            if (hasActiveChild) {
              parents.push(item.key);
            }

            // 递归检查子菜单
            traverse(item.children, [...currentKeys, item.key]);
          }
        }
      };

      traverse(items);
      return parents;
    },
    [],
  );

  // 初始化展开状态
  useEffect(() => {
    if (!isInitialized) {
      // 从 localStorage 恢复展开状态
      const stored = localStorage.getItem(STORAGE_KEY);
      let initialExpanded: string[] = [];

      if (stored) {
        try {
          initialExpanded = JSON.parse(stored);
        } catch (e) {
          console.error("解析存储的展开状态失败:", e);
        }
      }

      // 根据当前路径找到应该展开的父菜单
      const activeParents = findActiveParents(menuItems, pathname);

      // 合并存储的状态和当前激活状态
      const merged = [...new Set([...initialExpanded, ...activeParents])];
      setExpandedItems(merged);
      setIsInitialized(true);
    }
  }, [pathname, menuItems, findActiveParents, isInitialized]);

  // 保存展开状态到 localStorage
  useEffect(() => {
    if (isInitialized) {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(expandedItems));
    }
  }, [expandedItems, isInitialized]);

  // 切换展开状态
  const toggleExpanded = (key: string) => {
    setExpandedItems((prev) =>
      prev.includes(key) ? prev.filter((item) => item !== key) : [...prev, key],
    );
  };

  // 检查路径是否激活
  const isActivePath = (path: string, children?: MenuItem[]): boolean => {
    if (pathname === path) return true;
    if (children) {
      return children.some((child) => isActivePath(child.path, child.children));
    }
    return false;
  };

  // 渲染菜单项
  const renderMenuItem = (item: MenuItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.key);
    const isActive = isActivePath(item.path, item.children);

    return (
      <div key={item.key} className="space-y-1">
        {hasChildren ? (
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-start px-2 py-1.5 h-auto",
              level > 0 && "ml-4",
              isActive && "bg-accent text-accent-foreground",
            )}
            onClick={() => toggleExpanded(item.key)}
          >
            <item.icon className="mr-2 h-4 w-4 shrink-0" />
            {!isCollapsed && (
              <>
                <span className="flex-1 text-left">{item.label}</span>
                {isExpanded ? (
                  <ChevronDown className="h-3 w-3" />
                ) : (
                  <ChevronRight className="h-3 w-3" />
                )}
              </>
            )}
          </Button>
        ) : (
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-start px-2 py-1.5 h-auto",
              level > 0 && "ml-4",
              isActive && "bg-accent text-accent-foreground",
            )}
            asChild
          >
            <Link
              href={item.path}
              onClick={() => {
                // 移动端点击后自动关闭侧边栏
                if (isMobile && onToggle) {
                  onToggle();
                }
              }}
            >
              <item.icon className="mr-2 h-4 w-4 shrink-0" />
              {!isCollapsed && <span>{item.label}</span>}
            </Link>
          </Button>
        )}
        {hasChildren && isExpanded && !isCollapsed && (
          <div className="ml-2 space-y-1">
            {item.children?.map((child) => renderMenuItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={cn("relative bg-card", className)}>
      {/* 移动端关闭按钮 */}
      {onToggle && (
        <div className="flex h-14 items-center justify-between px-4 lg:hidden">
          <h2 className="text-lg font-semibold">管理后台</h2>
          <Button variant="ghost" size="sm" onClick={onToggle}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}

      {/* 桌面端标题 */}
      {!onToggle && (
        <div className="flex h-14 items-center px-4">
          {!isCollapsed && <h2 className="text-lg font-semibold">管理后台</h2>}
        </div>
      )}

      <Separator />

      {/* 菜单区域 */}
      <ScrollArea className="flex-1 px-3 py-4">
        <div className="space-y-2">
          {menuItems.map((item) => renderMenuItem(item))}
        </div>
      </ScrollArea>

      {/* 用户信息区域 */}
      {!isCollapsed && user && (
        <>
          <Separator />
          <div className="p-4">
            <div className="flex items-center space-x-3">
              <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                <span className="text-sm font-medium">
                  {user.name.charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{user.name}</p>
                <p className="text-xs text-muted-foreground truncate">
                  {user.role}
                </p>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
