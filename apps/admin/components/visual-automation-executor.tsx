"use client";

import { useState, useEffect, useRef } from "react";
import {
  MessageCircle,
  Play,
  Pause,
  Square,
  Eye,
  Check,
  X,
  Clock,
  Zap,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Badge,
} from "lucide-react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@workspace/ui/components/card";
import { ScrollArea } from "@workspace/ui/components/scroll-area";
import { Textarea } from "@workspace/ui/components/textarea";
import { cn } from "@workspace/ui/lib/utils";

interface AutomationStep {
  id: string;
  type: "click" | "fill" | "scroll" | "wait" | "screenshot" | "highlight";
  selector?: string;
  value?: string;
  duration?: number;
  description: string;
  status: "pending" | "executing" | "completed" | "failed";
}

interface ChatMessage {
  id: string;
  role: "user" | "ai";
  content: string;
  timestamp: Date;
  steps?: AutomationStep[];
}

export default function VisualAutomationExecutor() {
  const [steps, setSteps] = useState<AutomationStep[]>([]);
  const [currentStepIndex, setCurrentStepIndex] = useState(-1);
  const [isExecuting, setIsExecuting] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [chatVisible, setChatVisible] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState("");
  const [isAiThinking, setIsAiThinking] = useState(false);

  const executionIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
      if (executionIntervalRef.current) {
        clearTimeout(executionIntervalRef.current);
      }
    };
  }, []);

  const highlightElement = (selector: string, color: string = "#ef4444") => {
    const element = document.querySelector(selector);
    if (element) {
      const originalStyle = (element as HTMLElement).style.outline;
      const originalOffset = (element as HTMLElement).style.outlineOffset;

      (element as HTMLElement).style.outline = `3px solid ${color}`;
      (element as HTMLElement).style.outlineOffset = "2px";
      (element as HTMLElement).style.transition = "all 0.3s ease";

      // 滚动到元素位置
      element.scrollIntoView({
        behavior: "smooth",
        block: "center",
        inline: "center",
      });

      return () => {
        (element as HTMLElement).style.outline = originalStyle;
        (element as HTMLElement).style.outlineOffset = originalOffset;
      };
    }
    return null;
  };

  const executeStep = async (step: AutomationStep): Promise<boolean> => {
    try {
      setSteps((prev) =>
        prev.map((s) => (s.id === step.id ? { ...s, status: "executing" } : s)),
      );

      let cleanup: (() => void) | null = null;

      switch (step.type) {
        case "highlight":
          cleanup = highlightElement(step.selector!, "#10b981");
          await new Promise((resolve) =>
            setTimeout(resolve, step.duration || 1500),
          );
          break;

        case "click":
          cleanup = highlightElement(step.selector!, "#ef4444");
          await new Promise((resolve) => setTimeout(resolve, 800));

          const clickElement = document.querySelector(step.selector!);
          if (clickElement) {
            (clickElement as HTMLElement).click();
          } else {
            throw new Error(`Element not found: ${step.selector}`);
          }
          break;

        case "fill":
          cleanup = highlightElement(step.selector!, "#3b82f6");
          await new Promise((resolve) => setTimeout(resolve, 500));

          const fillElement = document.querySelector(step.selector!) as
            | HTMLInputElement
            | HTMLTextAreaElement;
          if (fillElement) {
            fillElement.focus();

            // 清空现有内容
            fillElement.value = "";
            fillElement.dispatchEvent(new Event("input", { bubbles: true }));

            // 模拟打字效果
            const text = step.value || "";
            for (let i = 0; i <= text.length; i++) {
              if (!isExecuting || isPaused) break;

              fillElement.value = text.substring(0, i);
              fillElement.dispatchEvent(new Event("input", { bubbles: true }));
              fillElement.dispatchEvent(new Event("change", { bubbles: true }));
              await new Promise((resolve) => setTimeout(resolve, 80));
            }
          } else {
            throw new Error(`Input element not found: ${step.selector}`);
          }
          break;

        case "scroll":
          const scrollElement = step.selector
            ? document.querySelector(step.selector)
            : window;

          if (scrollElement) {
            const scrollAmount = step.value ? parseInt(step.value) : 300;
            if (scrollElement === window) {
              window.scrollBy({
                top: scrollAmount,
                behavior: "smooth",
              });
            } else {
              (scrollElement as Element).scrollBy({
                top: scrollAmount,
                behavior: "smooth",
              });
            }
          }
          await new Promise((resolve) => setTimeout(resolve, 1000));
          break;

        case "wait":
          await new Promise((resolve) =>
            setTimeout(resolve, step.duration || 1000),
          );
          break;

        case "screenshot":
          console.log("Taking screenshot...");
          await new Promise((resolve) => setTimeout(resolve, 500));
          break;
      }

      // 清理高亮效果
      if (cleanup) {
        setTimeout(cleanup, 1200);
      }

      setSteps((prev) =>
        prev.map((s) => (s.id === step.id ? { ...s, status: "completed" } : s)),
      );

      return true;
    } catch (error) {
      console.error("Step execution failed:", error);
      setSteps((prev) =>
        prev.map((s) => (s.id === step.id ? { ...s, status: "failed" } : s)),
      );
      return false;
    }
  };

  const startExecution = async () => {
    if (steps.length === 0) return;

    setIsExecuting(true);
    setIsPaused(false);
    setCurrentStepIndex(0);

    for (let i = 0; i < steps.length; i++) {
      // 检查是否被暂停或停止
      while (isPaused && isExecuting) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      if (!isExecuting) break;

      setCurrentStepIndex(i);
      const success = await executeStep(steps[i]);

      if (!success) {
        break;
      }

      // 步骤间的延迟
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }

    setIsExecuting(false);
    setCurrentStepIndex(-1);
  };

  const pauseExecution = () => {
    setIsPaused(true);
  };

  const resumeExecution = () => {
    setIsPaused(false);
  };

  const stopExecution = () => {
    setIsExecuting(false);
    setIsPaused(false);
    setCurrentStepIndex(-1);

    // 重置所有步骤状态
    setSteps((prev) => prev.map((s) => ({ ...s, status: "pending" as const })));
  };

  const sendMessage = async () => {
    if (!input.trim() || isAiThinking) return;

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      role: "user",
      content: input.trim(),
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setIsAiThinking(true);

    let eventSource: EventSource | null = null;

    try {
      // 第一步：POST 请求创建消息
      const postResponse = await fetch("/api/ai/automation-stream", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: userMessage.content,
          currentUrl: window.location.href,
          pageContent: document.documentElement.outerHTML.substring(0, 15000),
        }),
      });

      if (!postResponse.ok) {
        throw new Error(`HTTP error! status: ${postResponse.status}`);
      }

      const { messageId } = await postResponse.json();

      // 第二步：建立 SSE 连接
      eventSource = new EventSource(
        `/api/ai/automation-stream?messageId=${messageId}`,
      );
      eventSourceRef.current = eventSource;

      let aiResponse = "";
      let steps: AutomationStep[] = [];
      let aiMessageAdded = false;

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          if (data.type === "message") {
            aiResponse += data.content;

            // 更新或添加AI消息
            setMessages((prev) => {
              const newMessages = [...prev];
              if (!aiMessageAdded) {
                newMessages.push({
                  id: `ai-${Date.now()}`,
                  role: "ai",
                  content: aiResponse,
                  timestamp: new Date(),
                });
                aiMessageAdded = true;
              } else {
                const lastMessage = newMessages[newMessages.length - 1];
                if (lastMessage && lastMessage.role === "ai") {
                  lastMessage.content = aiResponse;
                }
              }
              return newMessages;
            });
          } else if (data.type === "steps") {
            steps = data.steps;
            setSteps(steps);

            // 更新消息中的步骤
            setMessages((prev) => {
              const newMessages = [...prev];
              const lastMessage = newMessages[newMessages.length - 1];
              if (lastMessage && lastMessage.role === "ai") {
                lastMessage.steps = steps;
              }
              return newMessages;
            });
          } else if (data.type === "end") {
            // 结束流
            eventSource?.close();
            setIsAiThinking(false);
          } else if (data.type === "error") {
            throw new Error(data.message);
          }
        } catch (e) {
          console.error("Failed to parse SSE data:", e);
        }
      };

      eventSource.onerror = (error) => {
        console.error("SSE error:", error);
        eventSource!.close();
        setIsAiThinking(false);

        setMessages((prev) => [
          ...prev,
          {
            id: `ai-error-${Date.now()}`,
            role: "ai",
            content: "连接中断，请重试。",
            timestamp: new Date(),
          },
        ]);
      };
    } catch (error) {
      console.error("Failed to send message:", error);
      if (eventSource) {
        eventSource.close();
      }

      setMessages((prev) => [
        ...prev,
        {
          id: `ai-error-${Date.now()}`,
          role: "ai",
          content: `抱歉，处理请求时出现错误: ${error instanceof Error ? error.message : "未知错误"}`,
          timestamp: new Date(),
        },
      ]);
      setIsAiThinking(false);
    }
  };

  const getStepIcon = (status: AutomationStep["status"]) => {
    switch (status) {
      case "executing":
        return <Clock className="h-4 w-4 animate-spin text-blue-500" />;
      case "completed":
        return <Check className="h-4 w-4 text-green-500" />;
      case "failed":
        return <X className="h-4 w-4 text-red-500" />;
      default:
        return <Eye className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStepBadgeVariant = (status: AutomationStep["status"]) => {
    switch (status) {
      case "executing":
        return "default";
      case "completed":
        return "secondary";
      case "failed":
        return "destructive";
      default:
        return "outline";
    }
  };

  return (
    <>
      {/* 悬浮聊天按钮 */}
      <Button
        onClick={() => setChatVisible(!chatVisible)}
        className="fixed bottom-6 right-6 z-50 h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
        size="icon"
      >
        <MessageCircle className="h-6 w-6" />
      </Button>

      {/* 悬浮聊天窗口 */}
      {chatVisible && (
        <Card className="fixed bottom-20 right-6 w-96 h-[32rem] z-40 flex flex-col shadow-2xl">
          <CardHeader className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-t-lg">
            <CardTitle className="flex items-center gap-2">
              <Bot className="h-5 w-5" />
              AI 自动化助手
            </CardTitle>
            <CardDescription className="text-blue-100">
              告诉我你想要执行的操作
            </CardDescription>
          </CardHeader>

          <CardContent className="flex-1 flex flex-col p-0">
            {/* 消息区域 */}
            <ScrollArea className="flex-1 p-4">
              {messages.length === 0 && (
                <div className="text-muted-foreground text-sm space-y-2">
                  <p>👋 你好 newObjectccc！我可以帮你自动化操作这个网页。</p>
                  <p className="text-xs">试试说：</p>
                  <ul className="text-xs space-y-1 ml-2">
                    <li>• "点击登录按钮"</li>
                    <li>• "填写用户名为 test"</li>
                    <li>• "滚动到页面底部"</li>
                    <li>• "截图保存当前页面"</li>
                  </ul>
                </div>
              )}

              {messages.map((msg) => (
                <div
                  key={msg.id}
                  className={cn(
                    "mb-4 flex",
                    msg.role === "user" ? "justify-end" : "justify-start",
                  )}
                >
                  <div
                    className={cn(
                      "max-w-[80%] rounded-lg px-3 py-2 text-sm",
                      msg.role === "user"
                        ? "bg-blue-500 text-white"
                        : "bg-muted",
                    )}
                  >
                    <div className="flex items-center gap-2 mb-1">
                      {msg.role === "user" ? (
                        <User className="h-3 w-3" />
                      ) : (
                        <Bot className="h-3 w-3" />
                      )}
                      <span className="text-xs opacity-70">
                        {msg.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                    <p className="whitespace-pre-wrap">{msg.content}</p>
                    {msg.steps && msg.steps.length > 0 && (
                      <div className="mt-2 pt-2 border-t border-white/20">
                        <p className="text-xs opacity-80">
                          生成了 {msg.steps.length} 个自动化步骤
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              ))}

              {isAiThinking && (
                <div className="flex justify-start mb-4">
                  <div className="bg-muted rounded-lg px-3 py-2 text-sm">
                    <div className="flex items-center gap-2">
                      <Bot className="h-3 w-3" />
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div
                          className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                          style={{ animationDelay: "0.1s" }}
                        ></div>
                        <div
                          className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                          style={{ animationDelay: "0.2s" }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </ScrollArea>

            {/* 执行状态指示 */}
            {isExecuting && (
              <div className="px-4 py-2 bg-yellow-50 border-t border-yellow-200">
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    <Zap className="h-3 w-3 text-yellow-600" />
                    <span className="text-yellow-800">
                      正在执行自动化操作...
                    </span>
                  </div>
                  <div className="flex gap-1">
                    {!isPaused ? (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={pauseExecution}
                      >
                        <Pause className="h-3 w-3" />
                      </Button>
                    ) : (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={resumeExecution}
                      >
                        <Play className="h-3 w-3" />
                      </Button>
                    )}
                    <Button size="sm" variant="outline" onClick={stopExecution}>
                      <Square className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* 输入区域 */}
            <div className="p-4 border-t">
              <div className="flex gap-2">
                <Textarea
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault();
                      sendMessage();
                    }
                  }}
                  placeholder="告诉我你想要做什么..."
                  className="min-h-[60px] resize-none"
                  disabled={isAiThinking}
                />
                <Button
                  onClick={sendMessage}
                  disabled={isAiThinking || !input.trim()}
                  size="sm"
                  className="self-end"
                >
                  发送
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 执行控制面板 */}
      {steps.length > 0 && (
        <Card className="fixed top-4 right-4 w-80 max-h-96 z-40">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Zap className="h-5 w-5" />
              自动化执行控制
            </CardTitle>
            <div className="flex gap-2">
              {!isExecuting ? (
                <Button onClick={startExecution} size="sm">
                  <Play className="h-4 w-4 mr-2" />
                  开始执行
                </Button>
              ) : (
                <>
                  {!isPaused ? (
                    <Button
                      onClick={pauseExecution}
                      variant="outline"
                      size="sm"
                    >
                      <Pause className="h-4 w-4 mr-2" />
                      暂停
                    </Button>
                  ) : (
                    <Button onClick={resumeExecution} size="sm">
                      <Play className="h-4 w-4 mr-2" />
                      继续
                    </Button>
                  )}
                  <Button
                    onClick={stopExecution}
                    variant="destructive"
                    size="sm"
                  >
                    <Square className="h-4 w-4 mr-2" />
                    停止
                  </Button>
                </>
              )}
            </div>
          </CardHeader>

          <CardContent>
            <ScrollArea className="h-48">
              <div className="space-y-2">
                {steps.map((step, index) => (
                  <div
                    key={step.id}
                    className={cn(
                      "flex items-start gap-3 p-2 rounded-lg text-sm transition-colors",
                      index === currentStepIndex
                        ? "bg-blue-50 border-l-2 border-blue-500"
                        : step.status === "completed"
                          ? "bg-green-50"
                          : step.status === "failed"
                            ? "bg-red-50"
                            : "bg-gray-50",
                    )}
                  >
                    <div className="mt-0.5">{getStepIcon(step.status)}</div>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-gray-900 truncate">
                        {step.description}
                      </p>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant={getStepBadgeVariant(step.status)}>
                          {step.status === "executing"
                            ? "执行中"
                            : step.status === "completed"
                              ? "已完成"
                              : step.status === "failed"
                                ? "失败"
                                : "等待中"}
                        </Badge>
                        {step.selector && (
                          <code className="text-xs bg-gray-200 px-1 rounded truncate max-w-[150px]">
                            {step.selector}
                          </code>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </>
  );
}
