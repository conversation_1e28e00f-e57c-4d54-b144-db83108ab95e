"use client";

import { useState, useRef } from "react";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Progress } from "@workspace/ui/components/progress";
import { Upload, X, File, Image, Video, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { cn } from "@workspace/ui/lib/utils";

interface FileUploadProps {
  accept?: string;
  maxSize?: number; // in bytes
  folder?: string;
  value?: string;
  onChange?: (url: string) => void;
  onClear?: () => void;
  disabled?: boolean;
  className?: string;
  placeholder?: string;
  type?: "image" | "video" | "file";
}

export function FileUpload({
  accept,
  maxSize = 10 * 1024 * 1024, // 10MB default
  folder = "topic",
  value,
  onChange,
  onClear,
  disabled = false,
  className,
  placeholder = "点击上传文件或拖拽文件到此处",
  type = "file",
}: FileUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 根据类型设置默认的 accept
  const getDefaultAccept = () => {
    switch (type) {
      case "image":
        return "image/*";
      case "video":
        return "video/*";
      default:
        return "*/*";
    }
  };

  const actualAccept = accept || getDefaultAccept();

  // 获取文件图标
  const getFileIcon = () => {
    switch (type) {
      case "image":
        return <Image className="h-8 w-8 text-muted-foreground" />;
      case "video":
        return <Video className="h-8 w-8 text-muted-foreground" />;
      default:
        return <File className="h-8 w-8 text-muted-foreground" />;
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // 验证文件
  const validateFile = (file: File) => {
    if (file.size > maxSize) {
      toast.error(`文件大小不能超过 ${formatFileSize(maxSize)}`);
      return false;
    }

    if (actualAccept !== "*/*") {
      const acceptedTypes = actualAccept.split(",").map((t) => t.trim());
      const fileType = file.type;
      const isValid = acceptedTypes.some((acceptedType) => {
        if (acceptedType.endsWith("/*")) {
          return fileType.startsWith(acceptedType.slice(0, -1));
        }
        return fileType === acceptedType;
      });

      if (!isValid) {
        toast.error("不支持的文件类型");
        return false;
      }
    }

    return true;
  };

  // 上传文件
  const uploadFile = async (file: File) => {
    if (!validateFile(file)) return;

    setUploading(true);
    setProgress(0);

    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("folder", folder);

      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 90) return prev;
          return prev + Math.random() * 10;
        });
      }, 200);

      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        throw new Error("上传失败");
      }

      const result = await response.json();
      setProgress(100);

      if (result.url) {
        onChange?.(result.url);
        toast.success("文件上传成功！");
      } else {
        throw new Error("未获取到文件URL");
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("文件上传失败，请重试");
    } finally {
      setUploading(false);
      setProgress(0);
    }
  };

  // 处理文件选择
  const handleFileSelect = (files: FileList | null) => {
    if (!files || files.length === 0) return;
    const file = files[0];
    if (file) {
      uploadFile(file);
    }
  };

  // 处理拖拽
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    if (!disabled) {
      handleFileSelect(e.dataTransfer.files);
    }
  };

  // 清除文件
  const handleClear = () => {
    onClear?.();
    onChange?.("");
  };

  return (
    <div className={cn("space-y-2", className)}>
      {/* 上传区域 */}
      <div
        className={cn(
          "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
          dragOver && "border-primary bg-primary/5",
          disabled && "opacity-50 cursor-not-allowed",
          !disabled && "hover:border-primary/50 cursor-pointer",
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => !disabled && fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={actualAccept}
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
          disabled={disabled}
        />

        {uploading ? (
          <div className="space-y-2">
            <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
            <p className="text-sm text-muted-foreground">上传中...</p>
            <Progress value={progress} className="w-full" />
            <p className="text-xs text-muted-foreground">{progress.toFixed(0)}%</p>
          </div>
        ) : value ? (
          <div className="space-y-2">
            {type === "image" && (
              <img
                src={value}
                alt="已上传的图片"
                className="max-w-full max-h-48 mx-auto rounded-lg object-contain"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = "none";
                }}
              />
            )}
            {type === "video" && (
              <video
                src={value}
                className="max-w-full max-h-48 mx-auto rounded-lg"
                controls
              />
            )}
            <div className="flex items-center justify-center gap-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  window.open(value, "_blank");
                }}
              >
                查看
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  handleClear();
                }}
              >
                <X className="h-4 w-4" />
                删除
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            {getFileIcon()}
            <div>
              <p className="text-sm font-medium">{placeholder}</p>
              <p className="text-xs text-muted-foreground">
                最大文件大小：{formatFileSize(maxSize)}
              </p>
            </div>
            <Button type="button" variant="outline" disabled={disabled}>
              <Upload className="mr-2 h-4 w-4" />
              选择文件
            </Button>
          </div>
        )}
      </div>

      {/* 手动输入URL */}
      <div className="flex gap-2">
        <Input
          placeholder="或者直接输入文件URL"
          value={value || ""}
          onChange={(e) => onChange?.(e.target.value)}
          disabled={disabled}
        />
        {value && (
          <Button
            type="button"
            variant="outline"
            size="icon"
            onClick={handleClear}
            disabled={disabled}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}

