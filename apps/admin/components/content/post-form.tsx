"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { Textarea } from "@workspace/ui/components/textarea";
import { Switch } from "@workspace/ui/components/switch";
import { Badge } from "@workspace/ui/components/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Plus,
  X,
  Loader2,
  FileText,
  User,
  <PERSON>,
  Setting<PERSON>,
} from "lucide-react";
import { toast } from "sonner";
import { usePostActions, PostType, PostStatus, type Post } from "@/hooks/usePosts";
import { FileUpload } from "@/components/upload/file-upload";

// 帖子类型选项
const POST_TYPE_OPTIONS = [
  { value: PostType.ARTICLE, label: "文章" },
  { value: PostType.SHARE, label: "分享" },
  { value: PostType.QUESTION, label: "问题" },
  { value: PostType.ANNOUNCEMENT, label: "公告" },
  { value: PostType.EXPERIENCE, label: "经验分享" },
];

// 帖子状态选项
const POST_STATUS_OPTIONS = [
  { value: PostStatus.DRAFT, label: "草稿" },
  { value: PostStatus.PENDING_REVIEW, label: "待审核" },
  { value: PostStatus.PUBLISHED, label: "已发布" },
  { value: PostStatus.HIDDEN, label: "隐藏" },
];

// 表单验证 schema
const postFormSchema = z.object({
  title: z.string().min(1, "标题不能为空").max(200, "标题不能超过200个字符"),
  content: z.string().min(1, "内容不能为空"),
  summary: z.string().max(500, "摘要不能超过500个字符").optional(),
  cover: z.string().optional(),
  type: z.nativeEnum(PostType),
  status: z.nativeEnum(PostStatus),
  categoryId: z.string().optional(),
  tags: z.array(z.string()).default([]),
  isTop: z.boolean().default(false),
  isRecommended: z.boolean().default(false),
  isOriginal: z.boolean().default(true),
  authorId: z.string().min(1, "作者ID不能为空"),
});

type PostFormData = z.infer<typeof postFormSchema>;

interface PostFormProps {
  mode: "create" | "edit";
  initialData?: Partial<Post>;
  onSuccess?: (post: Post) => void;
  onCancel?: () => void;
}

export function PostForm({
  mode,
  initialData,
  onSuccess,
  onCancel,
}: PostFormProps) {
  const router = useRouter();
  const { createPost, updatePost, loading } = usePostActions();
  const [tagInput, setTagInput] = useState("");

  const form = useForm<PostFormData>({
    resolver: zodResolver(postFormSchema),
    defaultValues: {
      title: "",
      content: "",
      summary: "",
      cover: "",
      type: PostType.ARTICLE,
      status: PostStatus.DRAFT,
      categoryId: "",
      tags: [],
      isTop: false,
      isRecommended: false,
      isOriginal: true,
      authorId: "admin", // TODO: 从当前用户获取
      ...initialData,
      tags: initialData?.tags?.map(tag => typeof tag === 'string' ? tag : tag.name) || [],
    },
  });

  // 提交表单
  const onSubmit = async (data: PostFormData) => {
    try {
      let result;
      if (mode === "create") {
        result = await createPost(data);
      } else {
        result = await updatePost(initialData?.id!, data);
      }

      if (result.success) {
        toast.success(mode === "create" ? "帖子创建成功！" : "帖子更新成功！");
        if (onSuccess) {
          onSuccess(result.data!);
        } else {
          router.push("/content/posts");
        }
      } else {
        toast.error(result.message || "操作失败");
      }
    } catch (error) {
      console.error("保存失败:", error);
      toast.error("保存失败，请重试");
    }
  };

  // 添加标签
  const addTag = () => {
    const tag = tagInput.trim();
    if (tag && !form.getValues("tags").includes(tag)) {
      const currentTags = form.getValues("tags");
      form.setValue("tags", [...currentTags, tag]);
      setTagInput("");
    }
  };

  // 删除标签
  const removeTag = (tagToRemove: string) => {
    const currentTags = form.getValues("tags");
    form.setValue("tags", currentTags.filter(tag => tag !== tagToRemove));
  };

  // 处理标签输入键盘事件
  const handleTagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" || e.key === ",") {
      e.preventDefault();
      addTag();
    }
  };

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="mr-2 h-5 w-5" />
                基本信息
              </CardTitle>
              <CardDescription>
                填写帖子的基本信息
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>标题 *</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入帖子标题" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>帖子类型 *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择帖子类型" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {POST_TYPE_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>状态 *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择状态" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {POST_STATUS_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="summary"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>摘要</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="请输入帖子摘要（可选）"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      简要描述帖子内容，用于列表展示
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="content"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>内容 *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="请输入帖子内容"
                        className="min-h-[200px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="cover"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>封面图片</FormLabel>
                    <FormControl>
                      <FileUpload
                        type="image"
                        accept="image/*"
                        maxSize={5 * 1024 * 1024} // 5MB
                        folder="posts"
                        value={field.value}
                        onChange={field.onChange}
                        onClear={() => field.onChange("")}
                        placeholder="点击上传封面图片或拖拽图片到此处"
                      />
                    </FormControl>
                    <FormDescription>
                      支持 JPG、PNG、GIF 格式，最大 5MB
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* 标签管理 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Tag className="mr-2 h-5 w-5" />
                标签管理
              </CardTitle>
              <CardDescription>
                为帖子添加相关标签，便于分类和搜索
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex space-x-2">
                <Input
                  placeholder="输入标签名称"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={handleTagKeyPress}
                />
                <Button type="button" onClick={addTag} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {form.watch("tags").length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {form.watch("tags").map((tag, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-auto p-0 ml-1"
                        onClick={() => removeTag(tag)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* 高级设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="mr-2 h-5 w-5" />
                高级设置
              </CardTitle>
              <CardDescription>
                配置帖子的特殊属性和显示选项
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="isOriginal"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">原创内容</FormLabel>
                      <FormDescription>
                        标记此帖子为原创内容
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isTop"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">置顶显示</FormLabel>
                      <FormDescription>
                        将此帖子置顶显示
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isRecommended"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">推荐内容</FormLabel>
                      <FormDescription>
                        将此帖子标记为推荐内容
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                if (onCancel) {
                  onCancel();
                } else {
                  router.push("/content/posts");
                }
              }}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {mode === "create" ? "创建帖子" : "更新帖子"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}

export default PostForm;
