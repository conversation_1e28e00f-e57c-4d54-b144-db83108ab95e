"use client";

import { useAuth } from "@/lib/auth/context";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

interface RouteGuardProps {
  children: React.ReactNode;
  permissions?: string[];
  requireAuth?: boolean;
  fallbackUrl?: string;
}

export function RouteGuard({
  children,
  permissions = [],
  requireAuth = true,
  fallbackUrl = "/login",
}: RouteGuardProps) {
  const router = useRouter();
  const { user, isLoading, hasPermission } = useAuth();

  useEffect(() => {
    if (isLoading) return;

    // 检查认证
    if (requireAuth && !user) {
      router.push(fallbackUrl);
      return;
    }

    // 检查权限
    if (permissions.length > 0 && !hasPermission(permissions)) {
      router.push("/403");
    }
  }, [
    user,
    isLoading,
    requireAuth,
    permissions,
    router,
    fallbackUrl,
    hasPermission,
  ]);

  // 加载中
  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
      </div>
    );
  }

  // 未认证
  if (requireAuth && !user) {
    return null;
  }

  // 无权限
  if (permissions.length > 0 && !hasPermission(permissions)) {
    return null;
  }

  return <>{children}</>;
}

// 权限门控组件
interface PermissionGateProps {
  children: React.ReactNode;
  permissions?: string[];
  requireAll?: boolean;
  fallback?: React.ReactNode;
}

export function PermissionGate({
  children,
  permissions = [],
  requireAll = false,
  fallback = null,
}: PermissionGateProps) {
  const { hasAllPermissions, hasAnyPermission } = useAuth();

  // 没有权限要求，直接显示
  if (permissions.length === 0) {
    return <>{children}</>;
  }

  // 检查权限
  const hasAccess = requireAll
    ? hasAllPermissions(...permissions)
    : hasAnyPermission(...permissions);

  return hasAccess ? <>{children}</> : <>{fallback}</>;
}
