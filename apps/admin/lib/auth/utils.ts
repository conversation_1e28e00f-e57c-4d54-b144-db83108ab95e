// 认证相关工具函数

/**
 * 检查用户是否拥有指定权限
 * @param userPermissions 用户拥有的权限列表
 * @param requiredPermission 需要检查的权限
 * @returns 是否拥有权限
 *
 * 权限匹配规则：
 * 1. 超级管理员权限 "*" 匹配所有权限
 * 2. 精确匹配：完全相同的权限字符串
 * 3. 通配符匹配：如 "system:*" 匹配所有 "system:" 开头的权限
 */
export function checkPermission(
  userPermissions: string[],
  requiredPermission: string,
): boolean {
  // 超级管理员拥有所有权限
  if (userPermissions.includes("*")) return true;

  // 精确匹配
  if (userPermissions.includes(requiredPermission)) return true;

  // 通配符匹配
  const parts = requiredPermission.split(":");
  for (let i = parts.length - 1; i > 0; i--) {
    const wildcardPerm = parts.slice(0, i).join(":") + ":*";
    if (userPermissions.includes(wildcardPerm)) return true;
  }

  return false;
}

// 检查是否拥有所有权限
export function hasAllPermissions(
  userPermissions: string[],
  requiredPermissions: string[],
): boolean {
  return requiredPermissions.every((perm) =>
    checkPermission(userPermissions, perm),
  );
}

// 检查是否拥有任一权限
export function hasAnyPermission(
  userPermissions: string[],
  requiredPermissions: string[],
): boolean {
  return requiredPermissions.some((perm) =>
    checkPermission(userPermissions, perm),
  );
}

// 检查是否有模块访问权限
export function hasModuleAccess(
  userPermissions: string[],
  module: string,
): boolean {
  // 超级管理员拥有所有权限
  if (userPermissions.includes("*")) return true;

  // 检查是否有该模块的任何权限
  return userPermissions.some(
    (perm) => perm.startsWith(`${module}:`) || perm === `${module}:*`,
  );
}
