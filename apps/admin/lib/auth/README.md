# 权限验证系统文档

## 概述

Admin子项目的权限验证系统已经过优化和重构，消除了代码重复，提供了统一的权限验证接口。

## 核心模块

### 1. `/lib/auth/permissions.ts` - 权限验证核心模块

统一的权限验证函数库，提供了所有权限相关的验证逻辑。

#### 基础函数

```typescript
// 检查单个权限
checkPermission(userPermissions: string[], requiredPermission: string): boolean

// 检查所有权限（AND）
hasAllPermissions(userPermissions: string[], requiredPermissions: string[]): boolean

// 检查任一权限（OR）
hasAnyPermission(userPermissions: string[], requiredPermissions: string[]): boolean

// 检查模块访问权限
hasModuleAccess(userPermissions: string[], module: string): boolean
```

#### 高级函数

```typescript
// 高级权限检查，支持选项配置
checkPermissions(userPermissions: string[], requiredPermissions: string[], options?: PermissionCheckOptions): boolean

// 获取用户在某个模块下的所有权限
getModulePermissions(userPermissions: string[], module: string): string[]

// 检查资源操作权限
hasResourceAction(userPermissions: string[], resource: string, action: string): boolean

// 批量检查资源操作权限
hasResourceActions(userPermissions: string[], resource: string, actions: string[]): Record<string, boolean>

// 检查权限组
hasPermissionGroup(userPermissions: string[], group: string): boolean
```

### 2. `/hooks/usePermissions.ts` - React Hooks

提供了一系列React Hooks，方便在组件中使用权限验证。

```typescript
// 基础权限Hooks
usePermissions() // 获取所有权限相关函数
useHasPermission(permission: string) // 检查单个权限
useHasAllPermissions(...permissions: string[]) // 检查所有权限
useHasAnyPermission(...permissions: string[]) // 检查任一权限
useHasModuleAccess(module: string) // 检查模块权限

// 高级Hooks
useCheckPermissions(requiredPermissions: string[], options?: PermissionCheckOptions) // 高级权限检查
useModulePermissions(module: string) // 获取模块权限列表
useHasResourceAction(resource: string, action: string) // 检查资源操作权限
useResourceActions(resource: string, actions: string[]) // 批量检查资源操作
useHasPermissionGroup(group: string) // 检查权限组
usePermissionStatus() // 获取权限状态摘要
```

### 3. `/components/auth/route-guard.tsx` - 路由权限组件

```typescript
// 路由守卫组件
<RouteGuard permissions={["admin:dashboard:view"]} requireAuth>
  <YourComponent />
</RouteGuard>

// 权限门控组件
<PermissionGate permissions={["content:post:edit"]} fallback={<NoPermission />}>
  <EditButton />
</PermissionGate>
```

## 权限格式

权限采用冒号分隔的层级结构：`module:resource:action`

例如：

- `system:user:view` - 查看系统用户
- `content:post:edit` - 编辑内容帖子
- `finance:payment:refund` - 财务退款操作

### 通配符权限

- `*` - 超级管理员，拥有所有权限
- `module:*` - 模块级通配符，拥有该模块的所有权限
- `module:resource:*` - 资源级通配符，拥有该资源的所有操作权限

## 使用示例

### 在组件中使用

```tsx
import { useHasPermission, usePermissionStatus } from '@/hooks/usePermissions';

function MyComponent() {
  const canEditUser = useHasPermission('system:user:edit');
  const { isSuperAdmin, isContentManager } = usePermissionStatus();

  return (
    <div>
      {canEditUser && <EditUserButton />}
      {isSuperAdmin && <AdminPanel />}
      {isContentManager && <ContentManagerTools />}
    </div>
  );
}
```

### 在中间件中使用

```typescript
import { checkPermission } from '@/lib/auth/permissions';

export const customMiddleware: MiddlewareFunction = async (context) => {
  const userPermissions = context.permissions || [];
  
  if (!checkPermission(userPermissions, 'required:permission')) {
    return NextResponse.redirect(new URL('/403', request.url));
  }
};
```

### 批量权限检查

```tsx
import { useResourceActions } from '@/hooks/usePermissions';

function UserManagement() {
  const userActions = useResourceActions('system:user', ['view', 'create', 'edit', 'delete']);
  
  return (
    <div>
      {userActions.view && <UserList />}
      {userActions.create && <CreateUserButton />}
      {userActions.edit && <EditUserButton />}
      {userActions.delete && <DeleteUserButton />}
    </div>
  );
}
```

### 权限装饰器（用于类方法）

```typescript
import { RequirePermissions } from '@/lib/auth/permissions';

class UserService {
  permissions: string[] = []; // 需要设置用户权限

  @RequirePermissions(['system:user:create'])
  async createUser(data: UserData) {
    // 只有拥有创建用户权限的用户才能执行此方法
  }

  @RequirePermissions(['system:user:delete', 'system:user:ban'], { requireAll: true })
  async deleteAndBanUser(userId: string) {
    // 需要同时拥有删除和封禁权限
  }
}
```

## 最佳实践

1. **统一导入源**：始终从 `/lib/auth/permissions` 或 `/hooks/usePermissions` 导入权限函数
2. **避免硬编码**：使用 `/lib/permissions/definitions` 中定义的权限常量
3. **合理使用Hooks**：在React组件中优先使用Hooks进行权限检查
4. **权限分组**：对于复杂的权限组合，使用 `PermissionGroups` 进行管理
5. **错误处理**：在关键操作前进行权限检查，提供友好的错误提示

## 迁移指南

如果你的代码中有重复的权限验证函数，请按以下步骤迁移：

1. 删除本地的权限验证函数
2. 从 `/lib/auth/permissions` 导入对应的函数
3. 更新函数调用（API基本保持一致）
4. 测试权限验证功能是否正常

例如：

```typescript
// 旧代码
function checkPermission(userPermissions: string[], required: string) {
  // ... 本地实现
}

// 新代码
import { checkPermission } from '@/lib/auth/permissions';
// 直接使用导入的函数，无需其他改动
```
