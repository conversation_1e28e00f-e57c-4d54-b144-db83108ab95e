// 管理后台认证相关类型定义

// 认证相关常量
export const AUTH_COOKIE_NAME = "Authorization";
export const PERMISSIONS_COOKIE_NAME = "admin_permissions";

export interface AdminUser {
  id: string;
  email: string;
  username: string;
  name: string;
  avatar?: string;
  phone?: string;
  role: string;
  status: "ACTIVE" | "INACTIVE" | "SUSPENDED";
  permissions: string[];
  allowedModules: string[];
  lastLoginAt?: Date;
  lastLoginIp?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface AdminSession {
  id: string;
  adminId: string;
  token: string;
  ip?: string;
  userAgent?: string;
  expiresAt: Date;
  createdAt: Date;
}

export interface AuthContext {
  user: AdminUser | null;
  permissions: string[];
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
  hasPermission: (permission: string | string[]) => boolean;
  hasAllPermissions: (...permissions: string[]) => boolean;
  hasAnyPermission: (...permissions: string[]) => boolean;
  hasModuleAccess: (module: string) => boolean;
}

export interface LoginCredentials {
  email: string;
  password: string;
  remember?: boolean;
}

export interface LoginResponse {
  user: AdminUser;
  token: string;
  expiresAt: string;
}

export interface RouteGuardProps {
  children: React.ReactNode;
  permissions?: string[];
  requireAuth?: boolean;
  fallbackUrl?: string;
}
