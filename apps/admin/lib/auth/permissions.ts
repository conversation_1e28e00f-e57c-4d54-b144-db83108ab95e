/**
 * 权限验证模块
 * 集中管理所有权限相关的验证逻辑
 */

import {
  checkPermission,
  hasAllPermissions,
  hasAnyPermission,
  hasModuleAccess,
} from "./utils";

// 重新导出所有权限验证函数
export {
  checkPermission,
  hasAllPermissions,
  hasAnyPermission,
  hasModuleAccess,
};

// 权限验证的类型定义
export interface PermissionCheckOptions {
  /** 是否需要所有权限都满足 */
  requireAll?: boolean;
  /** 是否允许超级管理员跳过权限检查 */
  allowSuperAdmin?: boolean;
}

/**
 * 高级权限检查函数
 * @param userPermissions 用户拥有的权限列表
 * @param requiredPermissions 需要检查的权限列表
 * @param options 检查选项
 */
export function checkPermissions(
  userPermissions: string[],
  requiredPermissions: string[],
  options: PermissionCheckOptions = {},
): boolean {
  const { requireAll = false, allowSuperAdmin = true } = options;

  // 超级管理员检查
  if (allowSuperAdmin && userPermissions.includes("*")) {
    return true;
  }

  // 根据选项决定使用哪种检查方式
  if (requireAll) {
    return hasAllPermissions(userPermissions, requiredPermissions);
  } else {
    return hasAnyPermission(userPermissions, requiredPermissions);
  }
}

/**
 * 获取用户在某个模块下的所有权限
 * @param userPermissions 用户拥有的权限列表
 * @param module 模块名称
 */
export function getModulePermissions(
  userPermissions: string[],
  module: string,
): string[] {
  const modulePrefix = `${module}:`;

  return userPermissions.filter((permission) => {
    // 超级管理员
    if (permission === "*") return true;

    // 模块通配符权限
    if (permission === `${module}:*`) return true;

    // 具体的模块权限
    return permission.startsWith(modulePrefix);
  });
}

/**
 * 检查用户是否有某个资源的特定操作权限
 * @param userPermissions 用户权限列表
 * @param resource 资源名称 (如 "system:user")
 * @param action 操作名称 (如 "create", "edit", "delete")
 */
export function hasResourceAction(
  userPermissions: string[],
  resource: string,
  action: string,
): boolean {
  const permission = `${resource}:${action}`;
  return checkPermission(userPermissions, permission);
}

/**
 * 批量检查资源操作权限
 * @param userPermissions 用户权限列表
 * @param resource 资源名称
 * @param actions 操作列表
 */
export function hasResourceActions(
  userPermissions: string[],
  resource: string,
  actions: string[],
): Record<string, boolean> {
  const result: Record<string, boolean> = {};

  actions.forEach((action) => {
    result[action] = hasResourceAction(userPermissions, resource, action);
  });

  return result;
}

/**
 * 权限验证装饰器（用于类方法）
 * @param requiredPermissions 需要的权限
 * @param options 检查选项
 */
export function RequirePermissions(
  requiredPermissions: string[],
  options: PermissionCheckOptions = {},
) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = function (this: any, ...args: any[]) {
      const userPermissions = this.permissions || [];

      if (!checkPermissions(userPermissions, requiredPermissions, options)) {
        throw new Error(`权限不足: 需要权限 ${requiredPermissions.join(", ")}`);
      }

      return originalMethod.apply(this, args);
    };

    return descriptor;
  };
}

// 常用的权限组合
export const PermissionGroups = {
  // 系统管理员权限组
  SYSTEM_ADMIN: ["system:*", "admin:*"],

  // 内容管理员权限组
  CONTENT_MANAGER: ["content:*", "course:*", "product:*"],

  // 财务管理员权限组
  FINANCE_MANAGER: ["finance:*"],

  // 只读用户权限组
  READONLY: ["*:*:view"],
} as const;

/**
 * 检查用户是否属于某个权限组
 * @param userPermissions 用户权限列表
 * @param group 权限组名称
 */
export function hasPermissionGroup(
  userPermissions: string[],
  group: keyof typeof PermissionGroups,
): boolean {
  const groupPermissions = [...PermissionGroups[group]];
  return hasAnyPermission(userPermissions, groupPermissions);
}
