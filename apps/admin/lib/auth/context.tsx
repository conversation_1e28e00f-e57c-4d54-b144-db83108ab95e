"use client";

import { useRouter } from "next/navigation";
import React, { createContext, useContext, useEffect, useState } from "react";
import { toast } from "sonner";
import type {
  AdminUser,
  AuthContext as AuthContextType,
  LoginCredentials,
} from "./types";
import {
  checkPermission,
  hasAllPermissions,
  hasAnyPermission,
  hasModuleAccess,
} from "./utils";

export const AuthContext = createContext<AuthContextType | null>(null);

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<AdminUser | null>(null);
  const [permissions, setPermissions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  // 初始化认证状态
  useEffect(() => {
    const initAuth = async () => {
      try {
        // 验证cookie中的token并获取用户信息
        const response = await fetch("/api/auth/me");
        const result = await response.json();

        if (result.code === 1 && result.data?.user) {
          setUser(result.data.user);
          setPermissions(result.data.user.permissions || []);
        }
      } catch (error) {
        console.error("Auth initialization error:", error);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  // 登录函数
  const login = async (credentials: LoginCredentials) => {
    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(credentials),
      });

      const result = await response.json();

      if (result.code !== 1) {
        // 处理字段级错误
        if (result.errors) {
          const errorMessages = Object.values(result.errors).join(", ");
          throw new Error(errorMessages);
        }
        throw new Error(result.message || "登录失败");
      }

      // 保存认证信息 (token现在存在cookie中，不需要localStorage)
      setUser(result.data.user);
      setPermissions(result.data.user.permissions || []);

      toast.success("登录成功");
      router.push("/");
    } catch (error) {
      const message = error instanceof Error ? error.message : "登录失败";
      toast.error(message);
      throw error;
    }
  };

  // 登出函数
  const logout = async () => {
    try {
      // 调用登出API (token在cookie中，会自动发送)
      await fetch("/api/auth/logout", {
        method: "POST",
      });
    } catch (error) {
      console.error("Logout API error:", error);
    } finally {
      // 清除本地状态
      setUser(null);
      setPermissions([]);
      router.push("/login");
      toast.success("已退出登录");
    }
  };

  // 权限检查函数 - 直接使用从 utils 导入的函数
  const hasPermission = (permission: string | string[]): boolean => {
    if (Array.isArray(permission)) {
      return hasAllPermissions(permissions, permission);
    }
    return checkPermission(permissions, permission);
  };

  const value: AuthContextType = {
    user,
    permissions,
    isLoading,
    login,
    logout,
    hasPermission,
    hasAllPermissions: (...requiredPermissions: string[]) => 
      hasAllPermissions(permissions, requiredPermissions),
    hasAnyPermission: (...requiredPermissions: string[]) => 
      hasAnyPermission(permissions, requiredPermissions),
    hasModuleAccess: (module: string) => 
      hasModuleAccess(permissions, module),
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Hook for using auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
