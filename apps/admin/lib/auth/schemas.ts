import { z } from "zod";

// 登录表单 Schema
export const loginSchema = z.object({
  email: z
    .string({
      required_error: "邮箱是必填项",
    })
    .email({
      message: "请输入有效的邮箱地址",
    })
    .min(1, "邮箱不能为空"),
  password: z
    .string({
      required_error: "密码是必填项",
    })
    .min(6, "密码长度至少为6位")
    .max(50, "密码长度不能超过50位"),
  remember: z.boolean().optional().default(false),
});

export type LoginFormData = z.infer<typeof loginSchema>;

// API 响应通用结构
export const apiResponseSchema = z.object({
  code: z.number(),
  data: z.any().optional(),
  message: z.string(),
  errors: z.record(z.string()).optional(), // 字段级错误
});

export type ApiResponse<T = any> = {
  code: number;
  data?: T;
  message: string;
  errors?: Record<string, string>;
};

// 登录响应数据结构
export const loginResponseDataSchema = z.object({
  user: z.object({
    id: z.string(),
    email: z.string(),
    username: z.string(),
    name: z.string(),
    avatar: z.string().nullable(),
    phone: z.string().nullable(),
    role: z.string(),
    status: z.enum(["ACTIVE", "INACTIVE", "SUSPENDED"]),
    permissions: z.array(z.string()),
    allowedModules: z.array(z.string()),
    lastLoginAt: z.string().nullable(),
    lastLoginIp: z.string().nullable(),
    createdAt: z.string(),
    updatedAt: z.string(),
  }),
  token: z.string(),
  expiresAt: z.string(),
});

export type LoginResponseData = z.infer<typeof loginResponseDataSchema>;
