// 临时禁用 Geist 字体以解决 Turbopack 兼容性问题
// import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google"

import "@workspace/ui/globals.css";
import { Providers } from "@/components/providers";

// 使用系统字体作为替代
// const fontSans = Geist({
//   subsets: ["latin"],
//   variable: "--font-sans",
// })

// const fontMono = Geist_Mono({
//   subsets: ["latin"],
//   variable: "--font-mono",
// })

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning className="h-full overflow-hidden">
      <body className="font-sans antialiased">
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
