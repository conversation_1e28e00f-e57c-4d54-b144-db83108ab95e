"use client";

import Link from "next/link";
import { motion } from "framer-motion";

// Custom SVG Components
const FloatingIcon = ({
  children,
  delay = 0,
}: {
  children: React.ReactNode;
  delay?: number;
}) => (
  <motion.div
    className="absolute"
    animate={{
      y: [0, -20, 0],
      rotate: [0, 10, -10, 0],
    }}
    transition={{
      duration: 4,
      repeat: Infinity,
      ease: "easeInOut",
      delay,
    }}
  >
    {children}
  </motion.div>
);

const NotFoundIllustration = () => (
  <motion.svg
    width="400"
    height="300"
    viewBox="0 0 400 300"
    className="w-full max-w-md mx-auto"
    initial={{ opacity: 0, scale: 0.8 }}
    animate={{ opacity: 1, scale: 1 }}
    transition={{ duration: 1, ease: "easeOut" }}
  >
    {/* Background circles */}
    <motion.circle
      cx="100"
      cy="100"
      r="60"
      fill="#f3f4f6"
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ delay: 0.2, duration: 0.8 }}
    />
    <motion.circle
      cx="300"
      cy="180"
      r="40"
      fill="#e5e7eb"
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ delay: 0.4, duration: 0.8 }}
    />

    {/* 404 Text */}
    <motion.text
      x="200"
      y="150"
      textAnchor="middle"
      className="text-8xl font-bold fill-gray-200"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.6, duration: 0.8 }}
    >
      404
    </motion.text>

    {/* Sad face */}
    <motion.g
      initial={{ opacity: 0, scale: 0 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ delay: 1, duration: 0.5 }}
    >
      <circle cx="180" cy="120" r="3" fill="#6b7280" />
      <circle cx="220" cy="120" r="3" fill="#6b7280" />
      <path
        d="M 180 140 Q 200 125 220 140"
        stroke="#6b7280"
        strokeWidth="2"
        fill="none"
        strokeLinecap="round"
      />
    </motion.g>

    {/* Floating elements */}
    <motion.g
      animate={{ rotate: 360 }}
      transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
    >
      <circle cx="80" cy="80" r="4" fill="#3b82f6" opacity="0.6" />
      <circle cx="320" cy="60" r="3" fill="#10b981" opacity="0.6" />
      <circle cx="350" cy="200" r="5" fill="#f59e0b" opacity="0.6" />
      <circle cx="50" cy="220" r="3" fill="#ef4444" opacity="0.6" />
    </motion.g>
  </motion.svg>
);

const GlitchText = ({ children }: { children: React.ReactNode }) => (
  <motion.div
    className="relative"
    animate={{
      x: [0, -2, 2, 0],
    }}
    transition={{
      duration: 0.1,
      repeat: Infinity,
      repeatDelay: 3,
    }}
  >
    {children}
  </motion.div>
);

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
      <div className="max-w-2xl mx-auto text-center">
        {/* Floating background icons */}
        <div className="relative">
          <FloatingIcon delay={0}>
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 top-10 left-10">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          </FloatingIcon>

          <FloatingIcon delay={1}>
            <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center text-green-600 top-32 right-20">
              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </FloatingIcon>

          <FloatingIcon delay={2}>
            <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 bottom-20 left-16">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          </FloatingIcon>
        </div>

        {/* Main illustration */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="mb-8"
        >
          <NotFoundIllustration />
        </motion.div>

        {/* Error message */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.8 }}
          className="mb-8"
        >
          <GlitchText>
            <h1 className="text-4xl md:text-6xl font-bold text-gray-800 mb-4">
              页面走丢了
            </h1>
          </GlitchText>
          <motion.p
            className="text-lg text-gray-600 mb-8 max-w-md mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.8 }}
          >
            抱歉，您访问的页面不存在或已被移动。让我们帮您找到正确的方向！
          </motion.p>
        </motion.div>

        {/* Action buttons */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.8 }}
          className="flex flex-col sm:flex-row gap-4 justify-center items-center"
        >
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <button
              onClick={() => window.history.back()}
              className="inline-flex items-center px-6 py-3 bg-gray-600 text-white font-medium rounded-lg hover:bg-gray-700 transition-colors duration-200 shadow-lg"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M10 19l-7-7m0 0l7-7m-7 7h18"
                />
              </svg>
              返回上一级
            </button>
          </motion.div>

          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Link
              href="/"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200 shadow-lg"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
              回到首页
            </Link>
          </motion.div>
        </motion.div>

        {/* Additional help text */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1, duration: 0.8 }}
          className="mt-12 text-sm text-gray-500"
        >
          <p>如果您认为这是一个错误，请联系我们的技术支持团队</p>
        </motion.div>
      </div>
    </div>
  );
}
