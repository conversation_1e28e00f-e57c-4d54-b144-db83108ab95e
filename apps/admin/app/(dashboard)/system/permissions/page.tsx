"use client";

import { useState } from "react";
import { Input } from "@workspace/ui/components/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Alert, AlertDescription } from "@workspace/ui/components/alert";
import {
  Search,
  Shield,
  AlertTriangle,
  Key,
  Lock,
} from "lucide-react";
import { PermissionList } from "@/components/system/permissions/permission-list";
import { usePermissions } from "@/hooks/usePermissions";
import { PERMISSIONS, PERMISSION_DESCRIPTIONS } from "@/config/permissions.config";

export default function PermissionsPage() {
  const { hasPermission } = usePermissions();
  const [searchQuery, setSearchQuery] = useState("");

  // 权限检查
  const canViewPermissions = hasPermission("system:permission:view");

  // 计算权限总数
  const calculateTotalPermissions = () => {
    let total = 0;
    Object.values(PERMISSIONS).forEach((module) => {
      Object.values(module).forEach((resource) => {
        total += Object.keys(resource).length;
      });
    });
    return total;
  };

  // 计算模块数量
  const moduleCount = Object.keys(PERMISSIONS).length;

  if (!canViewPermissions) {
    return (
      <div className="container mx-auto py-8">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            您没有权限访问权限管理页面。请联系管理员获取相应权限。
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">权限管理</h1>
          <p className="text-muted-foreground">查看系统权限配置和权限说明</p>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总权限数</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{calculateTotalPermissions()}</div>
            <p className="text-xs text-muted-foreground">系统定义的权限总数</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">权限模块</CardTitle>
            <Key className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{moduleCount}</div>
            <p className="text-xs text-muted-foreground">
              按功能划分的权限模块
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">权限说明</CardTitle>
            <Lock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Object.keys(PERMISSION_DESCRIPTIONS).length}</div>
            <p className="text-xs text-muted-foreground">
              已定义权限说明文档
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>系统权限列表</CardTitle>
              <CardDescription>
                查看系统中所有权限的定义和说明，权限与路由对应，用于控制功能访问
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索权限..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8 w-64"
                />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <PermissionList searchQuery={searchQuery} />
        </CardContent>
      </Card>
    </div>
  );
}
