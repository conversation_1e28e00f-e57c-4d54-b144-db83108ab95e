"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { Button } from "@workspace/ui/components/button";
import { ArrowLeft, Loader2 } from "lucide-react";
import Link from "next/link";
import { usePermissions } from "@/hooks/usePermissions";
import { toast } from "sonner";
import { PERMISSIONS } from "@/config/permissions.config";
import { redirect } from "next/navigation";
import PostForm from "@/components/content/post-form";
import { usePost } from "@/hooks/usePosts";

export default function EditPostPage() {
  const params = useParams();
  const { hasPermission } = usePermissions();
  const { post, loading, error } = usePost(params.id as string);

  // 权限检查
  if (!hasPermission(PERMISSIONS.CONTENT.POST.EDIT)) {
    redirect("/403");
  }

  const handleSuccess = () => {
    toast.success("帖子更新成功");
    // 可以选择重新获取数据或导航到列表页
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">加载失败</h1>
          <p className="text-muted-foreground mt-2">{error}</p>
          <Link href="/content/posts">
            <Button className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回帖子列表
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面标题和导航 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/content/posts">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回帖子列表
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">编辑帖子</h1>
            <p className="text-muted-foreground mt-2">修改帖子信息和内容</p>
          </div>
        </div>
      </div>

      {/* 帖子表单 */}
      <PostForm mode="edit" initialData={post} onSuccess={handleSuccess} />
    </div>
  );
}
