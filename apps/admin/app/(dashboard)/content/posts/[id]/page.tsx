"use client";

import { useParams } from "next/navigation";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  ArrowLeft,
  Edit,
  FileText,
  User,
  Calendar,
  Eye,
  MessageSquare,
  Heart,
  Bookmark,
  Tag,
  Pin,
  Star,
  Loader2,
} from "lucide-react";
import Link from "next/link";
import { usePermissions } from "@/hooks/usePermissions";
import { PERMISSIONS } from "@/config/permissions.config";
import { redirect } from "next/navigation";
import { usePost, PostType, PostStatus } from "@/hooks/usePosts";
import Image from "next/image";

// 帖子类型映射
const POST_TYPE_MAP: Record<PostType, string> = {
  [PostType.ARTICLE]: "文章",
  [PostType.SHARE]: "分享",
  [PostType.QUESTION]: "问题",
  [PostType.ANNOUNCEMENT]: "公告",
  [PostType.EXPERIENCE]: "经验分享",
};

// 帖子状态映射
const POST_STATUS_MAP: Record<PostStatus, string> = {
  [PostStatus.DRAFT]: "草稿",
  [PostStatus.PENDING_REVIEW]: "待审核",
  [PostStatus.PUBLISHED]: "已发布",
  [PostStatus.HIDDEN]: "隐藏",
  [PostStatus.DELETED]: "已删除",
};

export default function PostDetailPage() {
  const params = useParams();
  const { hasPermission } = usePermissions();
  const { post, loading, error } = usePost(params.id as string);

  // 权限检查
  const canView = hasPermission(PERMISSIONS.CONTENT.POST.VIEW);
  const canEdit = hasPermission(PERMISSIONS.CONTENT.POST.EDIT);

  if (!canView) {
    redirect("/403");
  }

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">加载失败</h1>
          <p className="text-muted-foreground mt-2">{error}</p>
          <Link href="/content/posts">
            <Button className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回帖子列表
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面标题和导航 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/content/posts">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回帖子列表
            </Button>
          </Link>
          <div>
            <div className="flex items-center space-x-2">
              <h1 className="text-3xl font-bold">{post.title}</h1>
              {post.isTop && (
                <Pin className="h-5 w-5 text-red-500" />
              )}
              {post.isRecommended && (
                <Star className="h-5 w-5 text-yellow-500" />
              )}
            </div>
            <p className="text-muted-foreground mt-2">帖子详细信息</p>
          </div>
        </div>
        {canEdit && (
          <Link href={`/content/posts/${post.id}/edit`}>
            <Button>
              <Edit className="h-4 w-4 mr-2" />
              编辑
            </Button>
          </Link>
        )}
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* 主要内容 */}
        <div className="md:col-span-2 space-y-6">
          {/* 帖子内容 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="mr-2 h-5 w-5" />
                帖子内容
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {post.cover && (
                <div className="relative w-full h-64 rounded-lg overflow-hidden">
                  <Image
                    src={post.cover}
                    alt={post.title}
                    fill
                    className="object-cover"
                  />
                </div>
              )}

              {post.summary && (
                <div className="p-4 bg-muted rounded-lg">
                  <h4 className="font-medium mb-2">摘要</h4>
                  <p className="text-muted-foreground">{post.summary}</p>
                </div>
              )}

              <div className="prose max-w-none">
                <div className="whitespace-pre-wrap">{post.content}</div>
              </div>

              {post.tags && post.tags.length > 0 && (
                <div className="pt-4 border-t">
                  <div className="flex items-center space-x-2 mb-2">
                    <Tag className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">标签</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {post.tags.map((tag) => (
                      <Badge key={tag.id} variant="outline">
                        {tag.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 侧边栏信息 */}
        <div className="space-y-6">
          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="mr-2 h-5 w-5" />
                基本信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">类型</span>
                  <Badge variant="outline">
                    {POST_TYPE_MAP[post.type]}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">状态</span>
                  <Badge 
                    variant={post.status === PostStatus.PUBLISHED ? "default" : "secondary"}
                  >
                    {POST_STATUS_MAP[post.status]}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">原创</span>
                  <Badge variant={post.isOriginal ? "default" : "secondary"}>
                    {post.isOriginal ? "是" : "否"}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">创建时间</span>
                  <span className="text-sm text-muted-foreground">
                    {new Date(post.createdAt).toLocaleDateString()}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">更新时间</span>
                  <span className="text-sm text-muted-foreground">
                    {new Date(post.updatedAt).toLocaleDateString()}
                  </span>
                </div>

                {post.publishedAt && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">发布时间</span>
                    <span className="text-sm text-muted-foreground">
                      {new Date(post.publishedAt).toLocaleDateString()}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 作者信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="mr-2 h-5 w-5" />
                作者信息
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-3">
                {(post.author?.avatar || post.authorAvatar) && (
                  <div className="relative h-10 w-10 rounded-full overflow-hidden">
                    <Image
                      src={post.author?.avatar || post.authorAvatar!}
                      alt={post.author?.name || post.authorName!}
                      fill
                      className="object-cover"
                    />
                  </div>
                )}
                <div>
                  <p className="font-medium">{post.author?.name || post.authorName}</p>
                  {post.author?.username && (
                    <p className="text-sm text-muted-foreground">@{post.author.username}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 统计信息 */}
          <Card>
            <CardHeader>
              <CardTitle>统计信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Eye className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">浏览量</span>
                  </div>
                  <span className="font-medium">{post.viewCount}</span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <MessageSquare className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">评论数</span>
                  </div>
                  <span className="font-medium">{post._count?.comments || 0}</span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Heart className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">点赞数</span>
                  </div>
                  <span className="font-medium">{post._count?.likes || 0}</span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Bookmark className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">收藏数</span>
                  </div>
                  <span className="font-medium">{post._count?.favorites || 0}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 分类信息 */}
          {post.category && (
            <Card>
              <CardHeader>
                <CardTitle>分类信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline">{post.category.name}</Badge>
                  <span className="text-sm text-muted-foreground">
                    ({post.category.slug})
                  </span>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
