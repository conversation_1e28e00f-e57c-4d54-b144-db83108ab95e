"use client";

import { But<PERSON> } from "@workspace/ui/components/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { usePermissions } from "@/hooks/usePermissions";
import { PERMISSIONS } from "@/config/permissions.config";
import { redirect } from "next/navigation";
import PostForm from "@/components/content/post-form";

export default function CreatePostPage() {
  const { hasPermission } = usePermissions();

  // 权限检查
  if (!hasPermission(PERMISSIONS.CONTENT.POST.CREATE)) {
    redirect("/403");
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面标题和导航 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/content/posts">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回帖子列表
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">创建帖子</h1>
            <p className="text-muted-foreground mt-2">
              创建新的社区帖子，分享内容和经验
            </p>
          </div>
        </div>
      </div>

      {/* 帖子表单 */}
      <PostForm
        mode="create"
        onSuccess={() => {
          // 创建成功后的回调将在表单组件内处理
        }}
      />
    </div>
  );
}
