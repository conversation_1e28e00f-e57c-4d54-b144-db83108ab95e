"use client";

import { useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Badge } from "@workspace/ui/components/badge";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Search,
  MoreHorizontal,
  Eye,
  UserCheck,
  MessageSquare,
  Calendar,
  Phone,
  Mail,
  Loader2,
  Filter,
} from "lucide-react";
import { usePermissions } from "@/hooks/usePermissions";
import { useProductInquiries, useProductInquiryActions } from "@/hooks/useProducts";
import { PERMISSIONS } from "@/config/permissions.config";
import Link from "next/link";
import { toast } from "sonner";

// 询价状态映射
const INQUIRY_STATUS_MAP = {
  PENDING: { label: "待处理", variant: "secondary" as const },
  PROCESSING: { label: "处理中", variant: "default" as const },
  COMPLETED: { label: "已完成", variant: "default" as const },
  CANCELLED: { label: "已取消", variant: "destructive" as const },
} as const;

// 产品类型映射
const PRODUCT_TYPE_MAP = {
  STUDY_CAMP: "研学营",
  CERTIFICATE: "证书",
  BACKGROUND: "背景提升",
  SCHOOL_LINK: "名校链接",
  INSTITUTION: "机构提升",
  OTHER: "其他",
} as const;

export default function ProductInquiriesPage() {
  const router = useRouter();
  const { hasPermission } = usePermissions();
  const { inquiries, loading, refetch } = useProductInquiries();
  const { updateInquiry, loading: actionLoading } = useProductInquiryActions();

  // 权限检查
  const canView = hasPermission(PERMISSIONS.PRODUCT.INQUIRY.VIEW);
  const canProcess = hasPermission(PERMISSIONS.PRODUCT.INQUIRY.PROCESS);
  const canAssign = hasPermission(PERMISSIONS.PRODUCT.INQUIRY.ASSIGN);

  // 状态管理
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");

  // 权限检查
  if (!canView) {
    router.push("/403");
    return null;
  }

  // 过滤询价
  const filteredInquiries = useMemo(() => {
    if (!inquiries) return [];

    return inquiries.filter((inquiry) => {
      const matchesSearch = 
        inquiry.contactName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        inquiry.contactPhone.includes(searchTerm) ||
        (inquiry.contactEmail && inquiry.contactEmail.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (inquiry.product && inquiry.product.name.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesStatus = 
        statusFilter === "all" || inquiry.status === statusFilter;

      const matchesType = 
        typeFilter === "all" || 
        (inquiry.product && inquiry.product.type === typeFilter);

      return matchesSearch && matchesStatus && matchesType;
    });
  }, [inquiries, searchTerm, statusFilter, typeFilter]);

  // 更新询价状态
  const handleStatusUpdate = async (inquiryId: string, status: string) => {
    try {
      const result = await updateInquiry(inquiryId, { status });
      if (result.success) {
        toast.success("状态更新成功");
        refetch();
      } else {
        toast.error(result.message || "更新失败");
      }
    } catch (error) {
      toast.error("更新失败");
    }
  };

  // 统计信息
  const stats = useMemo(() => {
    if (!inquiries) return { total: 0, pending: 0, processing: 0, completed: 0 };

    return {
      total: inquiries.length,
      pending: inquiries.filter(i => i.status === "PENDING").length,
      processing: inquiries.filter(i => i.status === "PROCESSING").length,
      completed: inquiries.filter(i => i.status === "COMPLETED").length,
    };
  }, [inquiries]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">询价管理</h1>
          <p className="text-muted-foreground mt-2">
            管理产品询价请求，跟踪处理进度
          </p>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总询价数</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待处理</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.pending}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">处理中</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.processing}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已完成</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和过滤 */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="搜索联系人、电话、邮箱或产品名称..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="PENDING">待处理</SelectItem>
                  <SelectItem value="PROCESSING">处理中</SelectItem>
                  <SelectItem value="COMPLETED">已完成</SelectItem>
                  <SelectItem value="CANCELLED">已取消</SelectItem>
                </SelectContent>
              </Select>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  {Object.entries(PRODUCT_TYPE_MAP).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>联系人信息</TableHead>
                <TableHead>产品信息</TableHead>
                <TableHead>询价内容</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>提交时间</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredInquiries.map((inquiry) => (
                <TableRow key={inquiry.id}>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium">{inquiry.contactName}</div>
                      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                        <Phone className="h-3 w-3" />
                        <span>{inquiry.contactPhone}</span>
                      </div>
                      {inquiry.contactEmail && (
                        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                          <Mail className="h-3 w-3" />
                          <span>{inquiry.contactEmail}</span>
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {inquiry.product ? (
                      <div className="space-y-1">
                        <div className="font-medium">{inquiry.product.name}</div>
                        <Badge variant="outline" className="text-xs">
                          {PRODUCT_TYPE_MAP[inquiry.product.type as keyof typeof PRODUCT_TYPE_MAP] || inquiry.product.type}
                        </Badge>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">产品已删除</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="max-w-xs">
                      {inquiry.message ? (
                        <p className="text-sm line-clamp-2">{inquiry.message}</p>
                      ) : (
                        <span className="text-muted-foreground text-sm">无留言</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={INQUIRY_STATUS_MAP[inquiry.status as keyof typeof INQUIRY_STATUS_MAP]?.variant || "secondary"}>
                      {INQUIRY_STATUS_MAP[inquiry.status as keyof typeof INQUIRY_STATUS_MAP]?.label || inquiry.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {new Date(inquiry.createdAt).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {new Date(inquiry.createdAt).toLocaleTimeString()}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" disabled={actionLoading}>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/products/inquiries/${inquiry.id}`}>
                            <Eye className="h-4 w-4 mr-2" />
                            查看详情
                          </Link>
                        </DropdownMenuItem>
                        {canProcess && inquiry.status === "PENDING" && (
                          <DropdownMenuItem
                            onClick={() => handleStatusUpdate(inquiry.id, "PROCESSING")}
                          >
                            <UserCheck className="h-4 w-4 mr-2" />
                            开始处理
                          </DropdownMenuItem>
                        )}
                        {canProcess && inquiry.status === "PROCESSING" && (
                          <DropdownMenuItem
                            onClick={() => handleStatusUpdate(inquiry.id, "COMPLETED")}
                          >
                            <MessageSquare className="h-4 w-4 mr-2" />
                            标记完成
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredInquiries.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              {searchTerm || statusFilter !== "all" || typeFilter !== "all" 
                ? "没有找到匹配的询价记录" 
                : "暂无询价记录"}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
