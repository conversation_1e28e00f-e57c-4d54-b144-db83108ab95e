"use client";

import { But<PERSON> } from "@workspace/ui/components/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { usePermissions } from "@/hooks/usePermissions";
import { PERMISSIONS } from "@/config/permissions.config";
import { redirect } from "next/navigation";
import PartnerForm from "@/components/partners/partner-form";

export default function CreatePartnerPage() {
  const { hasPermission } = usePermissions();

  // 权限检查
  if (!hasPermission(PERMISSIONS.PRODUCT.PARTNER.CREATE)) {
    redirect("/403");
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面标题和导航 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/products/partners">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回合作方列表
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">添加合作方</h1>
            <p className="text-muted-foreground mt-2">
              填写合作方基本信息，创建新的合作方记录
            </p>
          </div>
        </div>
      </div>

      {/* 合作方表单 */}
      <PartnerForm
        mode="create"
        onSuccess={() => {
          // 创建成功后的回调将在表单组件内处理
        }}
      />
    </div>
  );
}
