"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { Button } from "@workspace/ui/components/button";
import { ArrowLeft, Loader2 } from "lucide-react";
import Link from "next/link";
import { usePermissions } from "@/hooks/usePermissions";
import { toast } from "sonner";
import { PERMISSIONS } from "@/config/permissions.config";
import { redirect } from "next/navigation";
import PartnerForm from "@/components/partners/partner-form";
import type { Partner } from "@/hooks/usePartners";

export default function EditPartnerPage() {
  const params = useParams();
  const { hasPermission } = usePermissions();
  const [partner, setPartner] = useState<Partner | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 权限检查
  if (!hasPermission(PERMISSIONS.PRODUCT.PARTNER.EDIT)) {
    redirect("/403");
  }

  // 获取合作方详情
  useEffect(() => {
    const fetchPartner = async () => {
      try {
        const response = await fetch(`/api/partners/${params.id}`);
        const result = await response.json();

        if (response.ok) {
          setPartner(result);
        } else {
          setError(result.error || "获取合作方信息失败");
          toast.error(result.error || "获取合作方信息失败");
        }
      } catch (error) {
        setError("获取合作方信息失败");
        toast.error("获取合作方信息失败");
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchPartner();
    }
  }, [params.id]);

  const handleSuccess = () => {
    toast.success("合作方更新成功");
    // 可以选择重新获取数据或导航到列表页
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (error || !partner) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">加载失败</h1>
          <p className="text-muted-foreground mt-2">{error}</p>
          <Link href="/products/partners">
            <Button className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回合作方列表
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面标题和导航 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/products/partners">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回合作方列表
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">编辑合作方</h1>
            <p className="text-muted-foreground mt-2">修改合作方信息和设置</p>
          </div>
        </div>
      </div>

      {/* 合作方表单 */}
      <PartnerForm mode="edit" initialData={partner} onSuccess={handleSuccess} />
    </div>
  );
}
