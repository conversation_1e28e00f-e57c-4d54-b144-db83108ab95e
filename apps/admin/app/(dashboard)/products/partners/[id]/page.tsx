"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  ArrowLeft,
  Edit,
  Building2,
  User,
  Phone,
  Mail,
  MapPin,
  Calendar,
  FileText,
  Loader2,
} from "lucide-react";
import Link from "next/link";
import { usePermissions } from "@/hooks/usePermissions";
import { toast } from "sonner";
import { PERMISSIONS } from "@/config/permissions.config";
import { redirect } from "next/navigation";
import type { Partner } from "@/hooks/usePartners";
import Image from "next/image";

// 合作类型映射
const COOPERATION_TYPE_MAP: Record<string, string> = {
  product_supply: "产品供应",
  service_provider: "服务提供",
  technology_partner: "技术合作",
  marketing_partner: "营销合作",
  distribution: "分销合作",
  strategic_alliance: "战略联盟",
  joint_venture: "合资企业",
  other: "其他",
};

export default function PartnerDetailPage() {
  const params = useParams();
  const { hasPermission } = usePermissions();
  const [partner, setPartner] = useState<Partner | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 权限检查
  const canView = hasPermission(PERMISSIONS.PRODUCT.PARTNER.VIEW);
  const canEdit = hasPermission(PERMISSIONS.PRODUCT.PARTNER.EDIT);

  if (!canView) {
    redirect("/403");
  }

  // 获取合作方详情
  useEffect(() => {
    const fetchPartner = async () => {
      try {
        const response = await fetch(`/api/partners/${params.id}`);
        const result = await response.json();

        if (response.ok) {
          setPartner(result);
        } else {
          setError(result.error || "获取合作方信息失败");
          toast.error(result.error || "获取合作方信息失败");
        }
      } catch (error) {
        setError("获取合作方信息失败");
        toast.error("获取合作方信息失败");
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchPartner();
    }
  }, [params.id]);

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (error || !partner) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">加载失败</h1>
          <p className="text-muted-foreground mt-2">{error}</p>
          <Link href="/products/partners">
            <Button className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回合作方列表
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面标题和导航 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/products/partners">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回合作方列表
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">{partner.name}</h1>
            <p className="text-muted-foreground mt-2">合作方详细信息</p>
          </div>
        </div>
        {canEdit && (
          <Link href={`/products/partners/${partner.id}/edit`}>
            <Button>
              <Edit className="h-4 w-4 mr-2" />
              编辑
            </Button>
          </Link>
        )}
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* 基本信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building2 className="mr-2 h-5 w-5" />
              基本信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3">
              {partner.logo && (
                <div className="relative h-12 w-12 rounded-md overflow-hidden">
                  <Image
                    src={partner.logo}
                    alt={partner.name}
                    fill
                    className="object-cover"
                  />
                </div>
              )}
              <div>
                <h3 className="font-semibold">{partner.name}</h3>
                <p className="text-sm text-muted-foreground">代码: {partner.code}</p>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">状态</span>
                <Badge variant={partner.isActive ? "default" : "secondary"}>
                  {partner.isActive ? "活跃" : "停用"}
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">创建时间</span>
                <span className="text-sm text-muted-foreground">
                  {new Date(partner.createdAt).toLocaleDateString()}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">更新时间</span>
                <span className="text-sm text-muted-foreground">
                  {new Date(partner.updatedAt).toLocaleDateString()}
                </span>
              </div>
            </div>

            {partner.description && (
              <div>
                <h4 className="text-sm font-medium mb-2">描述</h4>
                <p className="text-sm text-muted-foreground">{partner.description}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 联系信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="mr-2 h-5 w-5" />
              联系信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <User className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">联系人</p>
                  <p className="text-sm text-muted-foreground">{partner.contactName}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">电话</p>
                  <p className="text-sm text-muted-foreground">{partner.contactPhone}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">邮箱</p>
                  <p className="text-sm text-muted-foreground">{partner.contactEmail}</p>
                </div>
              </div>

              {partner.address && (
                <div className="flex items-center space-x-3">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">地址</p>
                    <p className="text-sm text-muted-foreground">{partner.address}</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 合作信息 */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="mr-2 h-5 w-5" />
              合作信息
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div>
              <h4 className="text-sm font-medium mb-3">合作类型</h4>
              <div className="flex flex-wrap gap-2">
                {partner.cooperationType.map((type) => (
                  <Badge key={type} variant="outline">
                    {COOPERATION_TYPE_MAP[type] || type}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
