"use client";

import { useState } from "react";
import { useRouter, usePara<PERSON> } from "next/navigation";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import { Separator } from "@workspace/ui/components/separator";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import {
  ArrowLeft,
  Edit,
  Trash2,
  Play,
  Pause,
  MessageSquare,
  Calendar,
  MapPin,
  Users,
  DollarSign,
  Package,
  FileText,
  ExternalLink,
  Loader2,
} from "lucide-react";
import { usePermissions } from "@/hooks/usePermissions";
import { useProduct, useProductActions } from "@/hooks/useProducts";
import { ProductType, ProductStatus } from "@workspace/database";
import Link from "next/link";
import { toast } from "sonner";
import { PERMISSIONS } from "@/config/permissions.config";
import Image from "next/image";

// 产品状态枚举
const PRODUCT_STATUS = {
  DRAFT: "草稿",
  ACTIVE: "上架",
  INACTIVE: "下架",
  SOLD_OUT: "售罄",
} as const;

// 产品类型枚举
const PRODUCT_TYPE = {
  STUDY_CAMP: "研学营",
  CERTIFICATE: "证书",
  BACKGROUND: "背景提升",
  SCHOOL_LINK: "名校链接",
  INSTITUTION: "机构提升",
  OTHER: "其他",
} as const;

export default function ProductDetailPage() {
  const router = useRouter();
  const params = useParams();
  const productId = params.id as string;

  const { hasPermission } = usePermissions();
  const { product, loading, error, refetch } = useProduct(productId);
  const {
    loading: actionLoading,
    updateProductStatus,
    deleteProduct,
  } = useProductActions();

  const [activeTab, setActiveTab] = useState("overview");

  // 权限检查
  const canEdit = hasPermission(PERMISSIONS.PRODUCT?.MANAGEMENT?.EDIT);
  const canDelete = hasPermission(PERMISSIONS.PRODUCT?.MANAGEMENT?.DELETE);
  const canPublish = hasPermission(PERMISSIONS.PRODUCT?.MANAGEMENT?.PUBLISH);

  // 处理产品操作
  const handleStatusChange = async (newStatus: ProductStatus) => {
    try {
      const result = await updateProductStatus(productId, newStatus);
      if (result.success) {
        toast.success("产品状态更新成功");
        refetch();
      } else {
        toast.error(result.message || "更新失败");
      }
    } catch (error) {
      toast.error("更新失败");
    }
  };

  const handleDelete = async () => {
    if (!confirm("确定要删除这个产品吗？此操作不可恢复。")) return;

    try {
      const result = await deleteProduct(productId);
      if (result.success) {
        toast.success("产品删除成功");
        router.push("/products");
      } else {
        toast.error(result.message || "删除失败");
      }
    } catch (error) {
      toast.error("删除失败");
    }
  };

  // 状态样式
  const getStatusBadge = (status: ProductStatus) => {
    const variants = {
      DRAFT: "secondary" as const,
      ACTIVE: "default" as const,
      INACTIVE: "destructive" as const,
      SOLD_OUT: "outline" as const,
    };

    return <Badge variant={variants[status]}>{PRODUCT_STATUS[status]}</Badge>;
  };

  // 类型样式
  const getTypeBadge = (type: ProductType) => {
    const colors: Record<ProductType, string> = {
      STUDY_CAMP: "bg-blue-100 text-blue-800",
      CERTIFICATE: "bg-green-100 text-green-800",
      BACKGROUND: "bg-purple-100 text-purple-800",
      SCHOOL_LINK: "bg-orange-100 text-orange-800",
      INSTITUTION: "bg-teal-100 text-teal-800",
      OTHER: "bg-gray-100 text-gray-800",
    };

    return (
      <Badge variant="outline" className={colors[type]}>
        {PRODUCT_TYPE[type] || type}
      </Badge>
    );
  };

  // 显示加载状态
  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">加载中...</span>
        </div>
      </div>
    );
  }

  // 显示错误状态
  if (error) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-500 mb-2">{error}</p>
            <Button onClick={refetch}>重试</Button>
          </div>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-muted-foreground mb-2">产品不存在</p>
            <Button onClick={() => router.push("/products")}>
              返回产品列表
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
          <div className="space-y-1">
            <div className="flex items-center space-x-2">
              <h1 className="text-2xl font-semibold tracking-tight">
                {product.name}
              </h1>
              {getStatusBadge(product.status)}
              {getTypeBadge(product.type)}
            </div>
            <p className="text-sm text-muted-foreground">
              产品编码: {product.code} | 创建时间:{" "}
              {new Date(product.createdAt).toLocaleDateString()}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Link href={`/products/${productId}/inquiries`}>
            <Button variant="outline" size="sm">
              <MessageSquare className="h-4 w-4 mr-2" />
              询价管理 ({product.inquiryCount || 0})
            </Button>
          </Link>

          {canPublish && (
            <>
              {product.status === "ACTIVE" ? (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleStatusChange("INACTIVE")}
                  disabled={actionLoading}
                >
                  <Pause className="h-4 w-4 mr-2" />
                  下架产品
                </Button>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleStatusChange("ACTIVE")}
                  disabled={actionLoading}
                >
                  <Play className="h-4 w-4 mr-2" />
                  上架产品
                </Button>
              )}
            </>
          )}

          {canEdit && (
            <Link href={`/products/${productId}/edit`}>
              <Button variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                编辑
              </Button>
            </Link>
          )}

          {canDelete && (
            <Button
              variant="destructive"
              size="sm"
              onClick={handleDelete}
              disabled={actionLoading}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              删除
            </Button>
          )}
        </div>
      </div>

      {/* 主要内容 */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="details">详细信息</TabsTrigger>
          <TabsTrigger value="media">媒体资源</TabsTrigger>
          <TabsTrigger value="seo">SEO设置</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* 基本信息卡片 */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">产品价格</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {product.price ? `¥${product.price}` : "面议"}
                </div>
                {product.priceUnit && (
                  <p className="text-xs text-muted-foreground">
                    单位: {product.priceUnit}
                  </p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">询价数量</CardTitle>
                <MessageSquare className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {product.inquiryCount || 0}
                </div>
                <p className="text-xs text-muted-foreground">总询价次数</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">容量限制</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {product.capacity || "不限"}
                </div>
                {product.minParticipants && (
                  <p className="text-xs text-muted-foreground">
                    最少: {product.minParticipants}人
                  </p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">产品分类</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{product.categoryName}</div>
                <p className="text-xs text-muted-foreground">
                  {PRODUCT_TYPE[product.type]}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* 产品描述 */}
          <Card>
            <CardHeader>
              <CardTitle>产品描述</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                  {product.description}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* 产品特点 */}
          {product.features && product.features.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>产品特点</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-2">
                  {product.features.map((feature, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div className="h-2 w-2 bg-primary rounded-full" />
                      <span className="text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="details" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* 时间和地点信息 */}
            <Card>
              <CardHeader>
                <CardTitle>时间和地点</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {product.duration && (
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">时长: {product.duration}</span>
                  </div>
                )}
                {product.startDate && (
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      开始时间:{" "}
                      {new Date(product.startDate).toLocaleDateString()}
                    </span>
                  </div>
                )}
                {product.endDate && (
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      结束时间: {new Date(product.endDate).toLocaleDateString()}
                    </span>
                  </div>
                )}
                {product.location && (
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">地点: {product.location}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 目标受众 */}
            {(product.targetAudience?.length > 0 ||
              product.ageRange ||
              product.gradeRange) && (
              <Card>
                <CardHeader>
                  <CardTitle>目标受众</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {product.targetAudience?.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">适用人群</h4>
                      <div className="flex flex-wrap gap-2">
                        {product.targetAudience.map((audience, index) => (
                          <Badge key={index} variant="outline">
                            {audience}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  {product.ageRange && (
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        年龄范围: {product.ageRange}
                      </span>
                    </div>
                  )}
                  {product.gradeRange && (
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        年级范围: {product.gradeRange}
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* 合作方信息 */}
            {product.partnerName && (
              <Card>
                <CardHeader>
                  <CardTitle>合作方信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-3">
                    {product.partner?.logo && (
                      <div className="h-12 w-12 rounded-lg bg-muted flex items-center justify-center overflow-hidden">
                        <Image
                          src={product.partner.logo}
                          alt={product.partner.name}
                          width={48}
                          height={48}
                          className="object-cover"
                        />
                      </div>
                    )}
                    <div>
                      <p className="font-medium">{product.partnerName}</p>
                      <p className="text-sm text-muted-foreground">合作伙伴</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 价格说明 */}
            {product.priceNote && (
              <Card>
                <CardHeader>
                  <CardTitle>价格说明</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    {product.priceNote}
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="media" className="space-y-6">
          {/* 产品图片 */}
          {product.images && product.images.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>产品图片</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {product.images.map((image, index) => (
                    <div
                      key={index}
                      className="relative aspect-video rounded-lg overflow-hidden bg-muted"
                    >
                      <Image
                        src={image}
                        alt={`${product.name} - 图片 ${index + 1}`}
                        fill
                        className="object-cover"
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 产品手册 */}
          {product.brochureUrl && (
            <Card>
              <CardHeader>
                <CardTitle>产品手册</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-3 p-4 border rounded-lg">
                  <FileText className="h-8 w-8 text-muted-foreground" />
                  <div className="flex-1">
                    <p className="font-medium">产品手册</p>
                    <p className="text-sm text-muted-foreground">
                      点击查看详细介绍
                    </p>
                  </div>
                  <Button variant="outline" size="sm" asChild>
                    <a
                      href={product.brochureUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      查看
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="seo" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>SEO设置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">页面标题</label>
                <p className="text-sm text-muted-foreground mt-1">
                  {product.metaTitle || product.name}
                </p>
              </div>

              <Separator />

              <div>
                <label className="text-sm font-medium">页面描述</label>
                <p className="text-sm text-muted-foreground mt-1">
                  {product.metaDescription ||
                    product.description.slice(0, 160) + "..."}
                </p>
              </div>

              <Separator />

              <div>
                <label className="text-sm font-medium">关键词</label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {product.metaKeywords && product.metaKeywords.length > 0 ? (
                    product.metaKeywords.map((keyword, index) => (
                      <Badge key={index} variant="secondary">
                        {keyword}
                      </Badge>
                    ))
                  ) : (
                    <p className="text-sm text-muted-foreground">暂无关键词</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
