"use client";

import { useState, useMemo } from "react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Badge } from "@workspace/ui/components/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import {
  Plus,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Play,
  Pause,
  MessageSquare,
  Calendar,
  MapPin,
  Search,
  Loader2,
  Package,
  Users,
  DollarSign,
  TrendingUp,
} from "lucide-react";
import { usePermissions } from "@/hooks/usePermissions";
import { useProducts, useProductActions, type Product } from "@/hooks/useProducts";
import { ProductType, ProductStatus } from "@workspace/database";
import Link from "next/link";
import { toast } from "sonner";
import { PERMISSIONS } from "@/config/permissions.config";

// 产品状态枚举
const PRODUCT_STATUS = {
  DRAFT: "草稿",
  ACTIVE: "上架",
  INACTIVE: "下架",
  SOLD_OUT: "售罄",
} as const;

// 产品类型枚举
const PRODUCT_TYPE = {
  STUDY_CAMP: "研学营",
  CERTIFICATE: "证书",
  BACKGROUND: "背景提升",
  SCHOOL_LINK: "名校链接",
  INSTITUTION: "机构提升",
  OTHER: "其他",
} as const;

export default function ProductsPage() {
  const { hasPermission } = usePermissions();

  // 搜索和过滤状态
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(1);

  // API查询参数
  const query = useMemo(
    () => ({
      search: searchTerm || undefined,
      status:
        statusFilter !== "all" ? (statusFilter as ProductStatus) : undefined,
      type: typeFilter !== "all" ? (typeFilter as ProductType) : undefined,
      page: currentPage,
      limit: 20,
      sortBy: "updatedAt" as const,
      sortOrder: "desc" as const,
    }),
    [searchTerm, statusFilter, typeFilter, currentPage],
  );

  // 使用API hooks
  const { products, loading, error, total, refetch } = useProducts(query);
  const {
    loading: actionLoading,
    updateProductStatus,
    deleteProduct,
  } = useProductActions();

  // 权限检查
  const canCreate = hasPermission(PERMISSIONS.PRODUCT?.MANAGEMENT?.CREATE);
  const canEdit = hasPermission(PERMISSIONS.PRODUCT?.MANAGEMENT?.EDIT);
  const canDelete = hasPermission(PERMISSIONS.PRODUCT?.MANAGEMENT?.DELETE);
  const canPublish = hasPermission(PERMISSIONS.PRODUCT?.MANAGEMENT?.PUBLISH);

  // 处理产品操作
  const handleStatusChange = async (
    productId: string,
    newStatus: ProductStatus,
  ) => {
    try {
      const result = await updateProductStatus(productId, newStatus);
      if (result.success) {
        toast.success("产品状态更新成功");
        refetch();
      } else {
        toast.error(result.message || "更新失败");
      }
    } catch (error) {
      toast.error("更新失败");
    }
  };

  const handleDelete = async (productId: string) => {
    if (!confirm("确定要删除这个产品吗？")) return;

    try {
      const result = await deleteProduct(productId);
      if (result.success) {
        toast.success("产品删除成功");
        refetch();
      } else {
        toast.error(result.message || "删除失败");
      }
    } catch (error) {
      toast.error("删除失败");
    }
  };

  // 状态样式
  const getStatusBadge = (status: ProductStatus) => {
    const variants = {
      DRAFT: "secondary" as const,
      ACTIVE: "default" as const,
      INACTIVE: "destructive" as const,
      SOLD_OUT: "outline" as const,
    };

    return <Badge variant={variants[status]}>{PRODUCT_STATUS[status]}</Badge>;
  };

  // 类型样式
  const getTypeBadge = (type: ProductType) => {
    const colors: Record<ProductType, string> = {
      STUDY_CAMP: "bg-blue-100 text-blue-800",
      CERTIFICATE: "bg-green-100 text-green-800",
      BACKGROUND: "bg-purple-100 text-purple-800",
      SCHOOL_LINK: "bg-orange-100 text-orange-800",
      INSTITUTION: "bg-teal-100 text-teal-800",
      OTHER: "bg-gray-100 text-gray-800",
    };

    return (
      <Badge variant="outline" className={colors[type]}>
        {PRODUCT_TYPE[type] || type}
      </Badge>
    );
  };

  // 显示加载状态
  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">加载中...</span>
        </div>
      </div>
    );
  }

  // 显示错误状态
  if (error) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-500 mb-2">{error}</p>
            <Button onClick={refetch}>重试</Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="space-y-1">
          <h1 className="text-2xl font-semibold tracking-tight">产品管理</h1>
          <p className="text-sm text-muted-foreground">
            管理平台上的所有产品内容
          </p>
        </div>
        {canCreate && (
          <Link href="/products/create">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              创建产品
            </Button>
          </Link>
        )}
      </div>

      {/* 统计概览 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
          <div className="p-6 flex flex-row items-center space-y-0 pb-2">
            <div className="space-y-1">
              <p className="text-sm font-medium leading-none">总产品数</p>
              <p className="text-2xl font-bold">{products.length}</p>
            </div>
            <Package className="h-4 w-4 text-muted-foreground ml-auto" />
          </div>
        </div>

        <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
          <div className="p-6 flex flex-row items-center space-y-0 pb-2">
            <div className="space-y-1">
              <p className="text-sm font-medium leading-none">总询价数</p>
              <p className="text-2xl font-bold">
                {products.reduce((sum, product) => sum + (product.inquiryCount || 0), 0)}
              </p>
            </div>
            <MessageSquare className="h-4 w-4 text-muted-foreground ml-auto" />
          </div>
        </div>

        <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
          <div className="p-6 flex flex-row items-center space-y-0 pb-2">
            <div className="space-y-1">
              <p className="text-sm font-medium leading-none">上架产品</p>
              <p className="text-2xl font-bold">
                {products.filter((product) => product.status === "ACTIVE").length}
              </p>
            </div>
            <TrendingUp className="h-4 w-4 text-muted-foreground ml-auto" />
          </div>
        </div>

        <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
          <div className="p-6 flex flex-row items-center space-y-0 pb-2">
            <div className="space-y-1">
              <p className="text-sm font-medium leading-none">付费产品</p>
              <p className="text-2xl font-bold">
                {products.filter((product) => product.price && product.price > 0).length}
              </p>
            </div>
            <DollarSign className="h-4 w-4 text-muted-foreground ml-auto" />
          </div>
        </div>
      </div>

      {/* 搜索和过滤 */}
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索产品名称或编码..."
            value={searchTerm}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setSearchTerm(e.target.value)
            }
            className="pl-8"
          />
        </div>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full md:w-[180px]">
            <SelectValue placeholder="产品状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="DRAFT">草稿</SelectItem>
            <SelectItem value="ACTIVE">上架</SelectItem>
            <SelectItem value="INACTIVE">下架</SelectItem>
            <SelectItem value="SOLD_OUT">售罄</SelectItem>
          </SelectContent>
        </Select>

        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-full md:w-[180px]">
            <SelectValue placeholder="产品类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部类型</SelectItem>
            <SelectItem value="STUDY_CAMP">研学营</SelectItem>
            <SelectItem value="CERTIFICATE">证书</SelectItem>
            <SelectItem value="BACKGROUND">背景提升</SelectItem>
            <SelectItem value="SCHOOL_LINK">名校链接</SelectItem>
            <SelectItem value="INSTITUTION">机构提升</SelectItem>
            <SelectItem value="OTHER">其他</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* 产品列表 */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>产品信息</TableHead>
              <TableHead>类型</TableHead>
              <TableHead>分类</TableHead>
              <TableHead>价格</TableHead>
              <TableHead>合作方</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>时间信息</TableHead>
              <TableHead>更新时间</TableHead>
              <TableHead className="text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {products.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="h-24 text-center">
                  没有找到符合条件的产品
                </TableCell>
              </TableRow>
            ) : (
              products.map((product: Product) => (
                <TableRow key={product.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      {product.images.length > 0 && (
                        <img
                          src={product.images[0]}
                          alt={product.name}
                          className="h-10 w-14 rounded object-cover"
                        />
                      )}
                      <div className="min-w-0 flex-1">
                        <p className="truncate font-medium">{product.name}</p>
                        <p className="truncate text-sm text-muted-foreground">
                          {product.code}
                        </p>
                        {product.duration && (
                          <p className="text-xs text-muted-foreground">
                            时长: {product.duration}
                          </p>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{getTypeBadge(product.type)}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{product.categoryName}</Badge>
                  </TableCell>
                  <TableCell>
                    {product.price ? (
                      <div className="space-y-1">
                        <span className="font-medium">¥{product.price}</span>
                        {product.priceUnit && (
                          <div className="text-xs text-muted-foreground">
                            /{product.priceUnit}
                          </div>
                        )}
                      </div>
                    ) : (
                      <Badge variant="secondary">面议</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    {product.partnerName ? (
                      <Badge variant="outline">{product.partnerName}</Badge>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>{getStatusBadge(product.status)}</TableCell>
                  <TableCell>
                    <div className="space-y-1 text-sm">
                      {product.startDate && (
                        <div className="flex items-center">
                          <Calendar className="mr-1 h-3 w-3" />
                          {new Date(product.startDate).toLocaleDateString()}
                        </div>
                      )}
                      {product.location && (
                        <div className="flex items-center text-muted-foreground">
                          <MapPin className="mr-1 h-3 w-3" />
                          {product.location}
                        </div>
                      )}
                      {product.capacity && (
                        <div className="flex items-center text-muted-foreground">
                          <Users className="mr-1 h-3 w-3" />
                          {product.capacity}人
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <time className="text-sm text-muted-foreground">
                      {new Date(product.updatedAt).toLocaleDateString()}
                    </time>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">打开菜单</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/products/${product.id}`}>
                            <Eye className="mr-2 h-4 w-4" />
                            查看详情
                          </Link>
                        </DropdownMenuItem>
                        {canEdit && (
                          <DropdownMenuItem asChild>
                            <Link href={`/products/${product.id}/edit`}>
                              <Edit className="mr-2 h-4 w-4" />
                              编辑产品
                            </Link>
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem asChild>
                          <Link href={`/products/${product.id}/inquiries`}>
                            <MessageSquare className="mr-2 h-4 w-4" />
                            查看询价
                          </Link>
                        </DropdownMenuItem>
                        {canPublish && (
                          <>
                            <DropdownMenuSeparator />
                            {product.status === "ACTIVE" ? (
                              <DropdownMenuItem
                                onClick={() =>
                                  handleStatusChange(product.id, "INACTIVE")
                                }
                                disabled={actionLoading}
                              >
                                <Pause className="mr-2 h-4 w-4" />
                                下架产品
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem
                                onClick={() =>
                                  handleStatusChange(product.id, "ACTIVE")
                                }
                                disabled={actionLoading}
                              >
                                <Play className="mr-2 h-4 w-4" />
                                上架产品
                              </DropdownMenuItem>
                            )}
                          </>
                        )}
                        {canDelete && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-destructive"
                              onClick={() => handleDelete(product.id)}
                              disabled={actionLoading}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              删除产品
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* 分页 */}
      {total > 20 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            显示 {(currentPage - 1) * 20 + 1} 到{" "}
            {Math.min(currentPage * 20, total)} 条，共 {total} 条
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              上一页
            </Button>
            <div className="flex items-center space-x-1">
              {Array.from(
                { length: Math.min(5, Math.ceil(total / 20)) },
                (_, i) => {
                  const page = i + 1;
                  return (
                    <Button
                      key={page}
                      variant={currentPage === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </Button>
                  );
                },
              )}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setCurrentPage((prev) =>
                  Math.min(Math.ceil(total / 20), prev + 1),
                )
              }
              disabled={currentPage === Math.ceil(total / 20)}
            >
              下一页
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

