"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Switch } from "@workspace/ui/components/switch";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { ArrowLeft, Save, Loader2 } from "lucide-react";
import { usePermissions } from "@/hooks/usePermissions";
import { useProductCategoryActions } from "@/hooks/useProducts";
import { ProductType } from "@workspace/database";
import Link from "next/link";
import { toast } from "sonner";
import { PERMISSIONS } from "@/config/permissions.config";

// 产品类型选项
const PRODUCT_TYPES = [
  { value: "STUDY_CAMP", label: "研学营" },
  { value: "CERTIFICATE", label: "证书" },
  { value: "BACKGROUND", label: "背景提升" },
  { value: "SCHOOL_LINK", label: "名校链接" },
  { value: "INSTITUTION", label: "机构提升" },
  { value: "OTHER", label: "其他" },
];

// 常用图标选项
const ICON_OPTIONS = [
  "📚", "🎓", "🏆", "🌟", "💼", "🔬", "🎨", "🏃", "🌍", "💡",
  "🎯", "🚀", "⭐", "🎪", "🎭", "🎵", "🎬", "📖", "✏️", "🔍"
];

export default function CreateProductCategoryPage() {
  const router = useRouter();
  const { hasPermission } = usePermissions();
  const { createCategory, loading: saving } = useProductCategoryActions();

  // 权限检查
  const canCreate = hasPermission(PERMISSIONS.PRODUCT?.CATEGORY?.CREATE);

  // 表单状态
  const [formData, setFormData] = useState({
    name: "",
    slug: "",
    description: "",
    type: "" as ProductType,
    icon: "",
    order: "0",
    isActive: true,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // 权限检查
  if (!canCreate) {
    router.push("/403");
    return null;
  }

  // 自动生成 slug
  const handleNameChange = (name: string) => {
    setFormData(prev => ({
      ...prev,
      name,
      slug: name.toLowerCase()
        .replace(/[^a-z0-9\u4e00-\u9fa5]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '')
    }));
  };

  // 表单验证
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "分类名称不能为空";
    }

    if (!formData.type) {
      newErrors.type = "请选择分类类型";
    }

    if (formData.order && isNaN(Number(formData.order))) {
      newErrors.order = "排序必须是数字";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("请检查表单中的错误信息");
      return;
    }

    try {
      const submitData = {
        ...formData,
        order: Number(formData.order),
        description: formData.description || undefined,
        icon: formData.icon || undefined,
      };

      const result = await createCategory(submitData);

      if (result.success) {
        toast.success("分类创建成功");
        router.push("/products/categories");
      } else {
        toast.error(result.message || "创建失败");
      }
    } catch (error) {
      toast.error("创建失败");
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/products/categories">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回分类列表
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">创建产品分类</h1>
            <p className="text-muted-foreground mt-2">
              填写分类基本信息，创建新的产品分类
            </p>
          </div>
        </div>
      </div>

      {/* 表单 */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 基本信息 */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>基本信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">分类名称 *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleNameChange(e.target.value)}
                      placeholder="请输入分类名称"
                      className={errors.name ? "border-destructive" : ""}
                    />
                    {errors.name && (
                      <p className="text-sm text-destructive">{errors.name}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="slug">URL标识符</Label>
                    <Input
                      id="slug"
                      value={formData.slug}
                      onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                      placeholder="自动生成或手动输入"
                    />
                    <p className="text-xs text-muted-foreground">
                      用于URL路径，建议使用英文和数字
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">分类描述</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="请输入分类描述（可选）"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="type">分类类型 *</Label>
                    <Select
                      value={formData.type}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, type: value as ProductType }))}
                    >
                      <SelectTrigger className={errors.type ? "border-destructive" : ""}>
                        <SelectValue placeholder="请选择分类类型" />
                      </SelectTrigger>
                      <SelectContent>
                        {PRODUCT_TYPES.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.type && (
                      <p className="text-sm text-destructive">{errors.type}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="order">排序</Label>
                    <Input
                      id="order"
                      type="number"
                      value={formData.order}
                      onChange={(e) => setFormData(prev => ({ ...prev, order: e.target.value }))}
                      placeholder="0"
                      className={errors.order ? "border-destructive" : ""}
                    />
                    {errors.order && (
                      <p className="text-sm text-destructive">{errors.order}</p>
                    )}
                    <p className="text-xs text-muted-foreground">
                      数字越小排序越靠前
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 侧边栏设置 */}
          <div className="space-y-6">
            {/* 图标设置 */}
            <Card>
              <CardHeader>
                <CardTitle>图标设置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="icon">自定义图标</Label>
                  <Input
                    id="icon"
                    value={formData.icon}
                    onChange={(e) => setFormData(prev => ({ ...prev, icon: e.target.value }))}
                    placeholder="输入emoji或图标"
                  />
                </div>

                <div className="space-y-2">
                  <Label>快捷选择</Label>
                  <div className="grid grid-cols-5 gap-2">
                    {ICON_OPTIONS.map((icon) => (
                      <Button
                        key={icon}
                        type="button"
                        variant={formData.icon === icon ? "default" : "outline"}
                        size="sm"
                        onClick={() => setFormData(prev => ({ ...prev, icon }))}
                        className="h-10 w-10 p-0"
                      >
                        {icon}
                      </Button>
                    ))}
                  </div>
                </div>

                {formData.icon && (
                  <div className="space-y-2">
                    <Label>预览</Label>
                    <div className="flex items-center space-x-2 p-3 border rounded-md">
                      <span className="text-lg">{formData.icon}</span>
                      <span className="font-medium">{formData.name || "分类名称"}</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 状态设置 */}
            <Card>
              <CardHeader>
                <CardTitle>状态设置</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="isActive">启用状态</Label>
                    <p className="text-sm text-muted-foreground">
                      启用后分类将在前台显示
                    </p>
                  </div>
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                  />
                </div>
              </CardContent>
            </Card>

            {/* 操作按钮 */}
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-2">
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={saving}
                  >
                    {saving && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    <Save className="h-4 w-4 mr-2" />
                    创建分类
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={() => router.back()}
                  >
                    取消
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  );
}
