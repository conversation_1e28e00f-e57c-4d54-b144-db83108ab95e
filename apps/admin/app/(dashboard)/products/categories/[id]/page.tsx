"use client";

import { useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import { Separator } from "@workspace/ui/components/separator";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@workspace/ui/components/alert-dialog";
import {
  ArrowLeft,
  Edit,
  Trash2,
  Package,
  Calendar,
  Hash,
  Type,
  Loader2,
} from "lucide-react";
import { usePermissions } from "@/hooks/usePermissions";
import { useProductCategory, useProductCategoryActions } from "@/hooks/useProducts";
import { PERMISSIONS } from "@/config/permissions.config";
import Link from "next/link";
import { toast } from "sonner";

// 产品类型映射
const PRODUCT_TYPE_MAP = {
  STUDY_CAMP: "研学营",
  CERTIFICATE: "证书",
  BACKGROUND: "背景提升",
  SCHOOL_LINK: "名校链接",
  INSTITUTION: "机构提升",
  OTHER: "其他",
} as const;

export default function ProductCategoryDetailPage() {
  const router = useRouter();
  const params = useParams();
  const categoryId = params.id as string;

  const { hasPermission } = usePermissions();
  const { category, loading, error } = useProductCategory(categoryId);
  const { deleteCategory, loading: actionLoading } = useProductCategoryActions();

  // 权限检查
  const canView = hasPermission(PERMISSIONS.PRODUCT?.CATEGORY?.VIEW);
  const canEdit = hasPermission(PERMISSIONS.PRODUCT?.CATEGORY?.EDIT);
  const canDelete = hasPermission(PERMISSIONS.PRODUCT?.CATEGORY?.DELETE);

  // 状态管理
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // 权限检查
  if (!canView) {
    router.push("/403");
    return null;
  }

  // 删除分类
  const handleDelete = async () => {
    try {
      const result = await deleteCategory(categoryId);
      if (result.success) {
        toast.success("分类删除成功");
        router.push("/products/categories");
      } else {
        toast.error(result.message || "删除失败");
      }
    } catch (error) {
      toast.error("删除失败");
    } finally {
      setDeleteDialogOpen(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error || !category) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-destructive">分类不存在</h1>
          <p className="text-muted-foreground mt-2">
            {error || "找不到指定的分类"}
          </p>
          <Link href="/products/categories">
            <Button className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回分类列表
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
          <div className="space-y-1">
            <div className="flex items-center space-x-2">
              {category.icon && (
                <span className="text-2xl">{category.icon}</span>
              )}
              <h1 className="text-2xl font-semibold tracking-tight">
                {category.name}
              </h1>
              <Badge variant={category.isActive ? "default" : "secondary"}>
                {category.isActive ? "启用" : "禁用"}
              </Badge>
              <Badge variant="outline">
                {PRODUCT_TYPE_MAP[category.type as keyof typeof PRODUCT_TYPE_MAP] || category.type}
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              分类标识: {category.slug} | 创建时间:{" "}
              {new Date(category.createdAt).toLocaleDateString()}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {canEdit && (
            <Link href={`/products/categories/${categoryId}/edit`}>
              <Button variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                编辑
              </Button>
            </Link>
          )}

          {canDelete && (
            <Button
              variant="destructive"
              size="sm"
              onClick={() => setDeleteDialogOpen(true)}
              disabled={actionLoading}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              删除
            </Button>
          )}
        </div>
      </div>

      {/* 主要内容 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 基本信息 */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <Type className="h-4 w-4" />
                    <span>分类名称</span>
                  </div>
                  <p className="font-medium">{category.name}</p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <Hash className="h-4 w-4" />
                    <span>URL标识符</span>
                  </div>
                  <p className="font-mono text-sm">{category.slug}</p>
                </div>
              </div>

              {category.description && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <span>分类描述</span>
                  </div>
                  <p className="text-sm leading-relaxed">{category.description}</p>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <span>分类类型</span>
                  </div>
                  <Badge variant="outline">
                    {PRODUCT_TYPE_MAP[category.type as keyof typeof PRODUCT_TYPE_MAP] || category.type}
                  </Badge>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <span>排序</span>
                  </div>
                  <p className="font-medium">{category.order}</p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <span>状态</span>
                  </div>
                  <Badge variant={category.isActive ? "default" : "secondary"}>
                    {category.isActive ? "启用" : "禁用"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 关联产品 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Package className="h-5 w-5" />
                <span>关联产品</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  该分类下有 {category.productCount || 0} 个产品
                </p>
                <Link href={`/products?categoryId=${categoryId}`}>
                  <Button variant="outline" className="mt-4">
                    查看产品列表
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 侧边栏信息 */}
        <div className="space-y-6">
          {/* 图标预览 */}
          {category.icon && (
            <Card>
              <CardHeader>
                <CardTitle>图标预览</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-6xl mb-4">{category.icon}</div>
                  <p className="text-sm text-muted-foreground">
                    分类图标
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 时间信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="h-5 w-5" />
                <span>时间信息</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">创建时间</p>
                <p className="text-sm font-medium">
                  {new Date(category.createdAt).toLocaleString()}
                </p>
              </div>
              <Separator />
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">更新时间</p>
                <p className="text-sm font-medium">
                  {new Date(category.updatedAt).toLocaleString()}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* 统计信息 */}
          <Card>
            <CardHeader>
              <CardTitle>统计信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">关联产品数</span>
                <Badge variant="secondary">
                  {category.productCount || 0}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 删除确认对话框 */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              此操作将永久删除分类 "{category.name}"。如果分类下有产品，请先移动或删除相关产品。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={actionLoading}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {actionLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
