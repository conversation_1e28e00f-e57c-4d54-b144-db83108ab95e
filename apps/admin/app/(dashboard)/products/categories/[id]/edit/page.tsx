"use client";

import { useState, useEffect } from "react";
import { useRouter, usePara<PERSON> } from "next/navigation";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Switch } from "@workspace/ui/components/switch";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { ArrowLeft, Save, Loader2 } from "lucide-react";
import { usePermissions } from "@/hooks/usePermissions";
import { useProductCategory, useProductCategoryActions } from "@/hooks/useProducts";
import { ProductType } from "@workspace/database";
import Link from "next/link";
import { toast } from "sonner";
import { PERMISSIONS } from "@/config/permissions.config";

// 产品类型选项
const PRODUCT_TYPES = [
  { value: "STUDY_CAMP", label: "研学营" },
  { value: "CERTIFICATE", label: "证书" },
  { value: "BACKGROUND", label: "背景提升" },
  { value: "SCHOOL_LINK", label: "名校链接" },
  { value: "INSTITUTION", label: "机构提升" },
  { value: "OTHER", label: "其他" },
];

// 常用图标选项
const ICON_OPTIONS = [
  "📚", "🎓", "🏆", "🌟", "💼", "🔬", "🎨", "🏃", "🌍", "💡",
  "🎯", "🚀", "⭐", "🎪", "🎭", "🎵", "🎬", "📖", "✏️", "🔍"
];

export default function EditProductCategoryPage() {
  const router = useRouter();
  const params = useParams();
  const categoryId = params.id as string;

  const { hasPermission } = usePermissions();
  const { category, loading: categoryLoading, error } = useProductCategory(categoryId);
  const { updateCategory, loading: saving } = useProductCategoryActions();

  // 权限检查
  const canEdit = hasPermission(PERMISSIONS.PRODUCT?.CATEGORY?.EDIT);

  // 表单状态
  const [formData, setFormData] = useState({
    name: "",
    slug: "",
    description: "",
    type: "" as ProductType,
    icon: "",
    order: "0",
    isActive: true,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // 权限检查
  if (!canEdit) {
    router.push("/403");
    return null;
  }

  // 加载分类数据
  useEffect(() => {
    if (category) {
      setFormData({
        name: category.name,
        slug: category.slug,
        description: category.description || "",
        type: category.type,
        icon: category.icon || "",
        order: category.order.toString(),
        isActive: category.isActive,
      });
    }
  }, [category]);

  // 表单验证
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "分类名称不能为空";
    }

    if (!formData.type) {
      newErrors.type = "请选择分类类型";
    }

    if (formData.order && isNaN(Number(formData.order))) {
      newErrors.order = "排序必须是数字";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("请检查表单中的错误信息");
      return;
    }

    try {
      const submitData = {
        ...formData,
        order: Number(formData.order),
        description: formData.description || undefined,
        icon: formData.icon || undefined,
      };

      const result = await updateCategory(categoryId, submitData);

      if (result.success) {
        toast.success("分类更新成功");
        router.push(`/products/categories/${categoryId}`);
      } else {
        toast.error(result.message || "更新失败");
      }
    } catch (error) {
      toast.error("更新失败");
    }
  };

  if (categoryLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error || !category) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-destructive">分类不存在</h1>
          <p className="text-muted-foreground mt-2">
            {error || "找不到指定的分类"}
          </p>
          <Link href="/products/categories">
            <Button className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回分类列表
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href={`/products/categories/${categoryId}`}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回分类详情
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">编辑产品分类</h1>
            <p className="text-muted-foreground mt-2">
              修改分类信息
            </p>
          </div>
        </div>
      </div>

      {/* 表单 */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 基本信息 */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>基本信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">分类名称 *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="请输入分类名称"
                      className={errors.name ? "border-destructive" : ""}
                    />
                    {errors.name && (
                      <p className="text-sm text-destructive">{errors.name}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="slug">URL标识符</Label>
                    <Input
                      id="slug"
                      value={formData.slug}
                      onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                      placeholder="URL标识符"
                    />
                    <p className="text-xs text-muted-foreground">
                      用于URL路径，建议使用英文和数字
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">分类描述</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="请输入分类描述（可选）"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="type">分类类型 *</Label>
                    <Select
                      value={formData.type}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, type: value as ProductType }))}
                    >
                      <SelectTrigger className={errors.type ? "border-destructive" : ""}>
                        <SelectValue placeholder="请选择分类类型" />
                      </SelectTrigger>
                      <SelectContent>
                        {PRODUCT_TYPES.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.type && (
                      <p className="text-sm text-destructive">{errors.type}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="order">排序</Label>
                    <Input
                      id="order"
                      type="number"
                      value={formData.order}
                      onChange={(e) => setFormData(prev => ({ ...prev, order: e.target.value }))}
                      placeholder="0"
                      className={errors.order ? "border-destructive" : ""}
                    />
                    {errors.order && (
                      <p className="text-sm text-destructive">{errors.order}</p>
                    )}
                    <p className="text-xs text-muted-foreground">
                      数字越小排序越靠前
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 侧边栏设置 */}
          <div className="space-y-6">
            {/* 图标设置 */}
            <Card>
              <CardHeader>
                <CardTitle>图标设置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="icon">自定义图标</Label>
                  <Input
                    id="icon"
                    value={formData.icon}
                    onChange={(e) => setFormData(prev => ({ ...prev, icon: e.target.value }))}
                    placeholder="输入emoji或图标"
                  />
                </div>

                <div className="space-y-2">
                  <Label>快捷选择</Label>
                  <div className="grid grid-cols-5 gap-2">
                    {ICON_OPTIONS.map((icon) => (
                      <Button
                        key={icon}
                        type="button"
                        variant={formData.icon === icon ? "default" : "outline"}
                        size="sm"
                        onClick={() => setFormData(prev => ({ ...prev, icon }))}
                        className="h-10 w-10 p-0"
                      >
                        {icon}
                      </Button>
                    ))}
                  </div>
                </div>

                {formData.icon && (
                  <div className="space-y-2">
                    <Label>预览</Label>
                    <div className="flex items-center space-x-2 p-3 border rounded-md">
                      <span className="text-lg">{formData.icon}</span>
                      <span className="font-medium">{formData.name || "分类名称"}</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 状态设置 */}
            <Card>
              <CardHeader>
                <CardTitle>状态设置</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="isActive">启用状态</Label>
                    <p className="text-sm text-muted-foreground">
                      启用后分类将在前台显示
                    </p>
                  </div>
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                  />
                </div>
              </CardContent>
            </Card>

            {/* 操作按钮 */}
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-2">
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={saving}
                  >
                    {saving && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    <Save className="h-4 w-4 mr-2" />
                    保存更改
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={() => router.back()}
                  >
                    取消
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  );
}
