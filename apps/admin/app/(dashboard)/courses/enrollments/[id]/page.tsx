"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Progress } from "@workspace/ui/components/progress";
import { Separator } from "@workspace/ui/components/separator";
import {
  ArrowLeft,
  User,
  Mail,
  Phone,
  BookOpen,
  Clock,
  Play,
  CheckCircle,
  DollarSign,
  Calendar,
  Activity,
  Download,
} from "lucide-react";

// 类型定义
interface EnrollmentDetail {
  id: string;
  courseId: string;
  userId: string;
  enrolledAt: Date;
  completedAt: Date | null;
  progress: number;
  lastAccessAt: Date | null;
  isPaid: boolean;
  paidAmount: number | null;
  paidAt: Date | null;
  user: {
    id: string;
    name: string;
    email: string;
    avatar: string | null;
    username: string;
    phone: string | null;
  };
  course: {
    id: string;
    title: string;
    cover: string;
    instructorName: string;
    price: number;
  };
  progressDetails: {
    totalLessons: number;
    completedLessons: number;
    totalDuration: number;
    watchedDuration: number;
    lastLessonId: string | null;
    lastLessonTitle: string | null;
  };
}

// 信息卡片组件
const InfoCard = ({
  title,
  value,
  icon: Icon,
  description,
}: {
  title: string;
  value: string | number;
  icon: any;
  description?: string;
}) => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      <Icon className="h-4 w-4 text-muted-foreground" />
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      {description && (
        <p className="text-xs text-muted-foreground mt-1">{description}</p>
      )}
    </CardContent>
  </Card>
);

// 主页面组件
export default function EnrollmentDetailPage() {
  const router = useRouter();
  const params = useParams();
  const enrollmentId = params.id as string;

  const [enrollment, setEnrollment] = useState<EnrollmentDetail | null>(null);
  const [loading, setLoading] = useState(true);

  // 加载数据
  const loadData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/enrollments/${enrollmentId}`);

      if (!response.ok) {
        throw new Error("获取数据失败");
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.message || "获取数据失败");
      }

      setEnrollment(result.data);
    } catch (error) {
      console.error("加载数据失败:", error);
      toast.error("加载数据失败");
    } finally {
      setLoading(false);
    }
  };

  // 格式化日期
  const formatDate = (date: Date | null) => {
    if (!date) return "-";
    return new Date(date).toLocaleString("zh-CN");
  };

  // 格式化货币
  const formatCurrency = (amount: number | null) => {
    if (!amount) return "免费";
    return `¥${amount.toFixed(2)}`;
  };

  // 格式化时长
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  useEffect(() => {
    loadData();
  }, [enrollmentId]);

  if (loading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-16 w-full" />
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  if (!enrollment) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">报名记录不存在</h2>
          <p className="text-muted-foreground mb-4">
            请检查链接是否正确或联系管理员
          </p>
          <Button onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
        </div>
      </div>
    );
  }

  const completionRate =
    enrollment.progressDetails.totalLessons > 0
      ? (enrollment.progressDetails.completedLessons /
          enrollment.progressDetails.totalLessons) *
        100
      : 0;

  const watchedRate =
    enrollment.progressDetails.totalDuration > 0
      ? (enrollment.progressDetails.watchedDuration /
          enrollment.progressDetails.totalDuration) *
        100
      : 0;

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
          <div>
            <h1 className="text-3xl font-bold">报名详情</h1>
            <p className="text-muted-foreground mt-1">
              {enrollment.user.name} - {enrollment.course.title}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            导出报告
          </Button>
          <Button
            onClick={() =>
              router.push(`/courses/${enrollment.courseId}/enrollments`)
            }
          >
            查看所有报名
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <InfoCard
          title="学习进度"
          value={`${enrollment.progress}%`}
          icon={Activity}
          description={`${enrollment.progressDetails.completedLessons}/${enrollment.progressDetails.totalLessons} 课时已完成`}
        />
        <InfoCard
          title="观看时长"
          value={formatDuration(enrollment.progressDetails.watchedDuration)}
          icon={Clock}
          description={`总时长 ${formatDuration(enrollment.progressDetails.totalDuration)}`}
        />
        <InfoCard
          title="支付状态"
          value={enrollment.isPaid ? "已支付" : "未支付"}
          icon={DollarSign}
          description={formatCurrency(enrollment.paidAmount)}
        />
        <InfoCard
          title="报名时间"
          value={new Date(enrollment.enrolledAt).toLocaleDateString("zh-CN")}
          icon={Calendar}
          description={
            enrollment.completedAt
              ? `已于 ${new Date(enrollment.completedAt).toLocaleDateString("zh-CN")} 完成`
              : "学习中"
          }
        />
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* 学员信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              学员信息
            </CardTitle>
            <CardDescription>学员的基本信息和联系方式</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center space-x-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={enrollment.user.avatar || undefined} />
                <AvatarFallback className="text-lg">
                  {enrollment.user.name.slice(0, 2)}
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 className="text-lg font-semibold">
                  {enrollment.user.name}
                </h3>
                <p className="text-muted-foreground">
                  @{enrollment.user.username}
                </p>
                <Badge variant={enrollment.isPaid ? "default" : "secondary"}>
                  {enrollment.isPaid ? "付费学员" : "免费学员"}
                </Badge>
              </div>
            </div>

            <Separator />

            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{enrollment.user.email}</span>
              </div>
              {enrollment.user.phone && (
                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{enrollment.user.phone}</span>
                </div>
              )}
            </div>

            <Separator />

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">报名时间</span>
                <span>{formatDate(enrollment.enrolledAt)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">最后访问</span>
                <span>{formatDate(enrollment.lastAccessAt)}</span>
              </div>
              {enrollment.paidAt && (
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">支付时间</span>
                  <span>{formatDate(enrollment.paidAt)}</span>
                </div>
              )}
              {enrollment.completedAt && (
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">完成时间</span>
                  <span>{formatDate(enrollment.completedAt)}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 课程信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BookOpen className="h-5 w-5 mr-2" />
              课程信息
            </CardTitle>
            <CardDescription>所报名课程的详细信息</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center space-x-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={enrollment.course.cover} />
                <AvatarFallback className="text-lg">
                  {enrollment.course.title.slice(0, 2)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <h3 className="text-lg font-semibold">
                  {enrollment.course.title}
                </h3>
                <p className="text-muted-foreground">
                  讲师：{enrollment.course.instructorName}
                </p>
                <div className="flex items-center space-x-2 mt-2">
                  <Badge variant="outline">
                    {formatCurrency(enrollment.course.price)}
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      router.push(`/courses/${enrollment.courseId}`)
                    }
                  >
                    查看课程
                  </Button>
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>课时完成进度</span>
                  <span>
                    {enrollment.progressDetails.completedLessons}/
                    {enrollment.progressDetails.totalLessons}
                  </span>
                </div>
                <Progress value={completionRate} className="h-2" />
                <p className="text-xs text-muted-foreground mt-1">
                  {completionRate.toFixed(1)}% 已完成
                </p>
              </div>

              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>观看时长进度</span>
                  <span>
                    {formatDuration(enrollment.progressDetails.watchedDuration)}
                    /{formatDuration(enrollment.progressDetails.totalDuration)}
                  </span>
                </div>
                <Progress value={watchedRate} className="h-2" />
                <p className="text-xs text-muted-foreground mt-1">
                  {watchedRate.toFixed(1)}% 已观看
                </p>
              </div>

              {enrollment.progressDetails.lastLessonTitle && (
                <div className="flex items-center space-x-2 p-3 bg-muted/50 rounded-lg">
                  <Play className="h-4 w-4 text-muted-foreground" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">最近学习</p>
                    <p className="text-xs text-muted-foreground">
                      {enrollment.progressDetails.lastLessonTitle}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 学习状态 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle className="h-5 w-5 mr-2" />
            学习状态
          </CardTitle>
          <CardDescription>详细的学习进度和状态信息</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-3">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {enrollment.progressDetails.totalLessons}
              </div>
              <p className="text-sm text-muted-foreground mt-1">总课时数</p>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {enrollment.progressDetails.completedLessons}
              </div>
              <p className="text-sm text-muted-foreground mt-1">已完成课时</p>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {enrollment.progressDetails.totalLessons -
                  enrollment.progressDetails.completedLessons}
              </div>
              <p className="text-sm text-muted-foreground mt-1">未完成课时</p>
            </div>
          </div>

          <Separator className="my-6" />

          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="font-medium">整体完成进度</span>
              <span className="text-lg font-bold">{enrollment.progress}%</span>
            </div>
            <Progress value={enrollment.progress} className="h-3" />

            {enrollment.completedAt ? (
              <div className="flex items-center space-x-2 text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span className="text-sm">
                  已于 {formatDate(enrollment.completedAt)} 完成课程
                </span>
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">
                继续学习以完成课程
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
