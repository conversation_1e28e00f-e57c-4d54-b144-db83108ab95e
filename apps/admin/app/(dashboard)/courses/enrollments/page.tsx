"use client";

import { useState, useMemo } from "react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Badge } from "@workspace/ui/components/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@workspace/ui/components/dialog";
import {
  Plus,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  UserPlus,
  DollarSign,
  Search,
  Loader2,
  Download,
  CheckCircle,
  XCircle,
  Clock,
  Users,
} from "lucide-react";
import { usePermissions } from "@/hooks/usePermissions";
import { PERMISSIONS } from "@/config/permissions.config";
import { toast } from "sonner";
import Link from "next/link";
import { EnrollmentCreateForm } from "@/components/courses/enrollment-create-form";
import { useEnrollments, type Enrollment } from "@/hooks/useEnrollments";

export default function EnrollmentsPage() {
  const { hasPermission } = usePermissions();

  // 搜索和过滤状态
  const [searchTerm, setSearchTerm] = useState("");
  const [courseFilter, setCourseFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [showCreateForm, setShowCreateForm] = useState(false);

  // API查询参数
  const query = useMemo(
    () => ({
      search: searchTerm || undefined,
      courseId: courseFilter !== "all" ? courseFilter : undefined,
      isPaid: statusFilter === "paid" ? true : statusFilter === "unpaid" ? false : undefined,
      page: currentPage,
      limit: 20,
      sortBy: "enrolledAt" as const,
      sortOrder: "desc" as const,
    }),
    [searchTerm, courseFilter, statusFilter, currentPage],
  );

  // 使用API hooks
  const { enrollments, loading, error, total, refetch } = useEnrollments(query);

  // 权限检查
  const canCreate = hasPermission(PERMISSIONS.COURSE.ENROLLMENT.CREATE);
  const canEdit = hasPermission(PERMISSIONS.COURSE.ENROLLMENT.EDIT);
  const canDelete = hasPermission(PERMISSIONS.COURSE.ENROLLMENT.DELETE);
  const canMarkPaid = hasPermission(PERMISSIONS.COURSE.ENROLLMENT.MARK_PAID);
  const canExport = hasPermission(PERMISSIONS.COURSE.ENROLLMENT.EXPORT);

  // 处理标记支付状态
  const handleMarkPaid = async (enrollmentId: string, isPaid: boolean) => {
    try {
      const response = await fetch(`/api/courses/enrollments/${enrollmentId}/payment`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ isPaid }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success(isPaid ? "已标记为已支付" : "已标记为未支付");
        refetch();
      } else {
        toast.error(result.message || "操作失败");
      }
    } catch (error) {
      toast.error("操作失败");
    }
  };

  // 处理删除报名
  const handleDelete = async (enrollmentId: string) => {
    if (!confirm("确定要删除这个报名记录吗？")) return;

    try {
      const response = await fetch(`/api/courses/enrollments/${enrollmentId}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (result.success) {
        toast.success("报名记录删除成功");
        refetch();
      } else {
        toast.error(result.message || "删除失败");
      }
    } catch (error) {
      toast.error("删除失败");
    }
  };

  // 导出报名数据
  const handleExport = async () => {
    try {
      const params = new URLSearchParams();
      if (searchTerm) params.append("search", searchTerm);
      if (courseFilter !== "all") params.append("courseId", courseFilter);
      if (statusFilter !== "all") params.append("isPaid", statusFilter === "paid" ? "true" : "false");

      const response = await fetch(`/api/courses/enrollments/export?${params.toString()}`);
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `course-enrollments-${new Date().toISOString().split("T")[0]}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        toast.success("导出成功");
      } else {
        toast.error("导出失败");
      }
    } catch (error) {
      toast.error("导出失败");
    }
  };

  // 状态样式
  const getStatusBadge = (isPaid: boolean, paidAt?: string) => {
    if (isPaid) {
      return (
        <Badge variant="default" className="bg-green-100 text-green-800">
          <CheckCircle className="mr-1 h-3 w-3" />
          已支付
        </Badge>
      );
    } else {
      return (
        <Badge variant="outline" className="bg-red-50 text-red-600">
          <XCircle className="mr-1 h-3 w-3" />
          未支付
        </Badge>
      );
    }
  };

  // 显示加载状态
  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">加载中...</span>
        </div>
      </div>
    );
  }

  // 显示错误状态
  if (error) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-500 mb-2">{error}</p>
            <Button onClick={refetch}>重试</Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="space-y-1">
          <h1 className="text-2xl font-semibold tracking-tight">课程报名管理</h1>
          <p className="text-sm text-muted-foreground">
            管理用户的课程报名记录，支持线下报名和支付状态管理
          </p>
        </div>
        <div className="flex items-center gap-2">
          {canExport && (
            <Button variant="outline" onClick={handleExport}>
              <Download className="mr-2 h-4 w-4" />
              导出数据
            </Button>
          )}
          {canCreate && (
            <Dialog open={showCreateForm} onOpenChange={setShowCreateForm}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  创建报名
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>创建课程报名</DialogTitle>
                  <DialogDescription>
                    为用户创建课程报名记录，支持标记支付状态
                  </DialogDescription>
                </DialogHeader>
                <EnrollmentCreateForm
                  onSuccess={() => {
                    setShowCreateForm(false);
                    refetch();
                  }}
                  onCancel={() => setShowCreateForm(false)}
                />
              </DialogContent>
            </Dialog>
          )}
        </div>
      </div>

      {/* 统计概览 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
          <div className="p-6 flex flex-row items-center space-y-0 pb-2">
            <div className="space-y-1">
              <p className="text-sm font-medium leading-none">总报名数</p>
              <p className="text-2xl font-bold">{enrollments.length}</p>
            </div>
            <Users className="h-4 w-4 text-muted-foreground ml-auto" />
          </div>
        </div>

        <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
          <div className="p-6 flex flex-row items-center space-y-0 pb-2">
            <div className="space-y-1">
              <p className="text-sm font-medium leading-none">已支付</p>
              <p className="text-2xl font-bold">
                {enrollments.filter((e) => e.isPaid).length}
              </p>
            </div>
            <CheckCircle className="h-4 w-4 text-green-500 ml-auto" />
          </div>
        </div>

        <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
          <div className="p-6 flex flex-row items-center space-y-0 pb-2">
            <div className="space-y-1">
              <p className="text-sm font-medium leading-none">未支付</p>
              <p className="text-2xl font-bold">
                {enrollments.filter((e) => !e.isPaid).length}
              </p>
            </div>
            <XCircle className="h-4 w-4 text-red-500 ml-auto" />
          </div>
        </div>

        <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
          <div className="p-6 flex flex-row items-center space-y-0 pb-2">
            <div className="space-y-1">
              <p className="text-sm font-medium leading-none">总收入</p>
              <p className="text-2xl font-bold">
                ¥{enrollments
                  .filter((e) => e.isPaid && e.paidAmount)
                  .reduce((sum, e) => sum + Number(e.paidAmount), 0)
                  .toLocaleString()}
              </p>
            </div>
            <DollarSign className="h-4 w-4 text-muted-foreground ml-auto" />
          </div>
        </div>
      </div>

      {/* 搜索和过滤 */}
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索用户名、邮箱或课程名称..."
            value={searchTerm}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setSearchTerm(e.target.value)
            }
            className="pl-8"
          />
        </div>

        <Select value={courseFilter} onValueChange={setCourseFilter}>
          <SelectTrigger className="w-full md:w-[200px]">
            <SelectValue placeholder="选择课程" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部课程</SelectItem>
            {/* 这里需要从API获取课程列表 */}
          </SelectContent>
        </Select>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full md:w-[180px]">
            <SelectValue placeholder="支付状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="paid">已支付</SelectItem>
            <SelectItem value="unpaid">未支付</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* 报名列表 */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>用户信息</TableHead>
              <TableHead>课程信息</TableHead>
              <TableHead>报名时间</TableHead>
              <TableHead>支付状态</TableHead>
              <TableHead>学习进度</TableHead>
              <TableHead>完成状态</TableHead>
              <TableHead className="text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {enrollments.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  没有找到符合条件的报名记录
                </TableCell>
              </TableRow>
            ) : (
              enrollments.map((enrollment) => (
                <TableRow key={enrollment.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <img
                        src={enrollment.user.avatar || "/default-avatar.png"}
                        alt={enrollment.user.name}
                        className="h-8 w-8 rounded-full"
                      />
                      <div>
                        <p className="font-medium">{enrollment.user.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {enrollment.user.email}
                        </p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="min-w-0">
                      <p className="font-medium truncate">{enrollment.course.title}</p>
                      <p className="text-sm text-muted-foreground">
                        {enrollment.course.instructorName}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <time className="text-sm">
                      {new Date(enrollment.enrolledAt).toLocaleDateString("zh-CN", {
                        year: "numeric",
                        month: "short",
                        day: "numeric",
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </time>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {getStatusBadge(enrollment.isPaid, enrollment.paidAt)}
                      {enrollment.isPaid && enrollment.paidAmount && (
                        <p className="text-xs text-muted-foreground">
                          ¥{enrollment.paidAmount}
                        </p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${enrollment.progress}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {enrollment.progress}%
                    </p>
                  </TableCell>
                  <TableCell>
                    {enrollment.completedAt ? (
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        <CheckCircle className="mr-1 h-3 w-3" />
                        已完成
                      </Badge>
                    ) : (
                      <Badge variant="outline">
                        <Clock className="mr-1 h-3 w-3" />
                        学习中
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">打开菜单</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/courses/enrollments/${enrollment.id}`}>
                            <Eye className="mr-2 h-4 w-4" />
                            查看详情
                          </Link>
                        </DropdownMenuItem>
                        {canMarkPaid && (
                          <>
                            <DropdownMenuSeparator />
                            {enrollment.isPaid ? (
                              <DropdownMenuItem
                                onClick={() => handleMarkPaid(enrollment.id, false)}
                              >
                                <XCircle className="mr-2 h-4 w-4" />
                                标记为未支付
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem
                                onClick={() => handleMarkPaid(enrollment.id, true)}
                              >
                                <CheckCircle className="mr-2 h-4 w-4" />
                                标记为已支付
                              </DropdownMenuItem>
                            )}
                          </>
                        )}
                        {canDelete && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-destructive"
                              onClick={() => handleDelete(enrollment.id)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              删除记录
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* 分页 */}
      {total > 20 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            显示 {(currentPage - 1) * 20 + 1} 到{" "}
            {Math.min(currentPage * 20, total)} 条，共 {total} 条
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              上一页
            </Button>
            <div className="flex items-center space-x-1">
              {Array.from(
                { length: Math.min(5, Math.ceil(total / 20)) },
                (_, i) => {
                  const page = i + 1;
                  return (
                    <Button
                      key={page}
                      variant={currentPage === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </Button>
                  );
                },
              )}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setCurrentPage((prev) =>
                  Math.min(Math.ceil(total / 20), prev + 1),
                )
              }
              disabled={currentPage === Math.ceil(total / 20)}
            >
              下一页
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
