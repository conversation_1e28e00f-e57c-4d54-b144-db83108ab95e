"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Badge } from "@workspace/ui/components/badge";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Progress } from "@workspace/ui/components/progress";
import { Skeleton } from "@workspace/ui/components/skeleton";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@workspace/ui/components/alert-dialog";
import {
  Users,
  BookOpen,
  TrendingUp,
  DollarSign,
  Search,
  Filter,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  Download,
  RefreshCw,
  Trash2,
  Edit,
} from "lucide-react";

// 类型定义
interface User {
  id: string;
  name: string;
  email: string;
  avatar: string | null;
  username: string;
}

interface CourseEnrollment {
  id: string;
  courseId: string;
  userId: string;
  enrolledAt: Date;
  completedAt: Date | null;
  progress: number;
  lastAccessAt: Date | null;
  isPaid: boolean;
  paidAmount: number | null;
  paidAt: Date | null;
  user: User;
}

interface EnrollmentStats {
  totalEnrollments: number;
  paidEnrollments: number;
  freeEnrollments: number;
  completedEnrollments: number;
  averageProgress: number;
  totalRevenue: number;
  completionRate: number;
  recentEnrollments: number;
}

interface Course {
  id: string;
  title: string;
  description: string;
  cover: string;
  price: number;
  instructorName: string;
}

interface CourseEnrollmentData {
  course: Course;
  enrollments: CourseEnrollment[];
  stats: EnrollmentStats;
  pagination: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

// 统计卡片组件
const StatsCard = ({
  title,
  value,
  icon: Icon,
  description,
  trend,
}: {
  title: string;
  value: string | number;
  icon: any;
  description?: string;
  trend?: number;
}) => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      <Icon className="h-4 w-4 text-muted-foreground" />
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      {description && (
        <p className="text-xs text-muted-foreground mt-1">{description}</p>
      )}
      {trend !== undefined && (
        <div className="flex items-center text-xs text-muted-foreground mt-1">
          <TrendingUp className="h-3 w-3 mr-1" />
          {trend > 0 ? "+" : ""}
          {trend}% 较上周
        </div>
      )}
    </CardContent>
  </Card>
);

// 报名表格组件
const EnrollmentTable = ({
  enrollments,
  onUpdateStatus,
  onDelete,
  loading,
}: {
  enrollments: CourseEnrollment[];
  onUpdateStatus: (id: string, updates: any) => void;
  onDelete: (id: string) => void;
  loading: boolean;
}) => {
  const formatDate = (date: Date | null) => {
    if (!date) return "-";
    return new Date(date).toLocaleDateString("zh-CN");
  };

  const formatCurrency = (amount: number | null) => {
    if (!amount) return "免费";
    return `¥${amount.toFixed(2)}`;
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <Skeleton key={i} className="h-12 w-full" />
        ))}
      </div>
    );
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>学员</TableHead>
          <TableHead>报名时间</TableHead>
          <TableHead>支付状态</TableHead>
          <TableHead>学习进度</TableHead>
          <TableHead>完成状态</TableHead>
          <TableHead>最后访问</TableHead>
          <TableHead className="text-right">操作</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {enrollments.map((enrollment) => (
          <TableRow key={enrollment.id}>
            <TableCell>
              <div className="flex items-center space-x-3">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={enrollment.user.avatar || undefined} />
                  <AvatarFallback>
                    {enrollment.user.name.slice(0, 2)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">{enrollment.user.name}</div>
                  <div className="text-sm text-muted-foreground">
                    {enrollment.user.email}
                  </div>
                </div>
              </div>
            </TableCell>
            <TableCell>{formatDate(enrollment.enrolledAt)}</TableCell>
            <TableCell>
              <div className="flex items-center space-x-2">
                <Badge variant={enrollment.isPaid ? "default" : "secondary"}>
                  {enrollment.isPaid ? "已支付" : "未支付"}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  {formatCurrency(enrollment.paidAmount)}
                </span>
              </div>
            </TableCell>
            <TableCell>
              <div className="flex items-center space-x-2">
                <Progress value={enrollment.progress} className="w-16" />
                <span className="text-sm">{enrollment.progress}%</span>
              </div>
            </TableCell>
            <TableCell>
              <Badge variant={enrollment.completedAt ? "default" : "outline"}>
                {enrollment.completedAt ? (
                  <CheckCircle className="h-3 w-3 mr-1" />
                ) : (
                  <XCircle className="h-3 w-3 mr-1" />
                )}
                {enrollment.completedAt ? "已完成" : "学习中"}
              </Badge>
            </TableCell>
            <TableCell>{formatDate(enrollment.lastAccessAt)}</TableCell>
            <TableCell className="text-right">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={() =>
                      onUpdateStatus(enrollment.id, {
                        isPaid: !enrollment.isPaid,
                      })
                    }
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    {enrollment.isPaid ? "标记未支付" : "标记已支付"}
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() =>
                      onUpdateStatus(enrollment.id, {
                        completedAt: enrollment.completedAt ? null : new Date(),
                        progress: enrollment.completedAt
                          ? enrollment.progress
                          : 100,
                      })
                    }
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    {enrollment.completedAt ? "标记未完成" : "标记已完成"}
                  </DropdownMenuItem>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                        <Trash2 className="h-4 w-4 mr-2" />
                        删除报名
                      </DropdownMenuItem>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>确认删除</AlertDialogTitle>
                        <AlertDialogDescription>
                          确定要删除 {enrollment.user.name}{" "}
                          的报名记录吗？此操作无法撤销。
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>取消</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => onDelete(enrollment.id)}
                        >
                          删除
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </DropdownMenuContent>
              </DropdownMenu>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

// 主页面组件
export default function CourseEnrollmentsPage() {
  const params = useParams();
  const courseId = params.id as string;

  const [data, setData] = useState<CourseEnrollmentData | null>(null);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    search: "",
    isPaid: "",
    isCompleted: "",
    page: 1,
    pageSize: 20,
  });

  // 加载数据
  const loadData = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      params.append("page", filters.page.toString());
      params.append("pageSize", filters.pageSize.toString());

      if (filters.search) params.append("search", filters.search);
      if (filters.isPaid) params.append("isPaid", filters.isPaid);
      if (filters.isCompleted)
        params.append("isCompleted", filters.isCompleted);

      const response = await fetch(
        `/api/courses/${courseId}/enrollments?${params}`,
      );

      if (!response.ok) {
        throw new Error("获取数据失败");
      }

      const result = await response.json();
      setData(result);
    } catch (error) {
      console.error("加载数据失败:", error);
      toast.error("加载数据失败");
    } finally {
      setLoading(false);
    }
  };

  // 更新报名状态
  const handleUpdateStatus = async (enrollmentId: string, updates: any) => {
    try {
      const response = await fetch(`/api/courses/${courseId}/enrollments`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ enrollmentId, updates }),
      });

      if (!response.ok) {
        throw new Error("更新失败");
      }

      toast.success("更新成功");
      loadData();
    } catch (error) {
      console.error("更新失败:", error);
      toast.error("更新失败");
    }
  };

  // 删除报名
  const handleDelete = async (enrollmentId: string) => {
    try {
      const response = await fetch(
        `/api/courses/${courseId}/enrollments?enrollmentId=${enrollmentId}`,
        {
          method: "DELETE",
        },
      );

      if (!response.ok) {
        throw new Error("删除失败");
      }

      toast.success("删除成功");
      loadData();
    } catch (error) {
      console.error("删除失败:", error);
      toast.error("删除失败");
    }
  };

  // 重置过滤器
  const resetFilters = () => {
    setFilters({
      search: "",
      isPaid: "",
      isCompleted: "",
      page: 1,
      pageSize: 20,
    });
  };

  // 应用过滤器
  const applyFilters = () => {
    setFilters((prev) => ({ ...prev, page: 1 }));
  };

  useEffect(() => {
    loadData();
  }, [courseId, filters]);

  const stats = data?.stats;

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">课程报名管理</h1>
          {data?.course && (
            <p className="text-muted-foreground mt-1">
              {data.course.title} - {data.course.instructorName}
            </p>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={loadData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            导出数据
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      {stats && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatsCard
            title="总报名数"
            value={stats.totalEnrollments}
            icon={Users}
            description="累计报名人数"
            trend={stats.recentEnrollments}
          />
          <StatsCard
            title="已完成"
            value={stats.completedEnrollments}
            icon={BookOpen}
            description={`完成率 ${stats.completionRate}%`}
          />
          <StatsCard
            title="总收入"
            value={`¥${stats.totalRevenue.toFixed(2)}`}
            icon={DollarSign}
            description={`付费用户 ${stats.paidEnrollments} 人`}
          />
          <StatsCard
            title="平均进度"
            value={`${stats.averageProgress}%`}
            icon={TrendingUp}
            description="所有学员平均进度"
          />
        </div>
      )}

      {/* 过滤器和表格 */}
      <Card>
        <CardHeader>
          <CardTitle>报名列表</CardTitle>
          <CardDescription>管理课程的所有报名记录</CardDescription>
        </CardHeader>
        <CardContent>
          {/* 过滤器 */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="flex-1">
              <Input
                placeholder="搜索学员姓名或邮箱..."
                value={filters.search}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, search: e.target.value }))
                }
                className="max-w-sm"
              />
            </div>
            <Select
              value={filters.isPaid}
              onValueChange={(value) =>
                setFilters((prev) => ({ ...prev, isPaid: value }))
              }
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="支付状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="true">已支付</SelectItem>
                <SelectItem value="false">未支付</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={filters.isCompleted}
              onValueChange={(value) =>
                setFilters((prev) => ({ ...prev, isCompleted: value }))
              }
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="完成状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="true">已完成</SelectItem>
                <SelectItem value="false">学习中</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={applyFilters}>
              <Search className="h-4 w-4 mr-2" />
              搜索
            </Button>
            <Button variant="outline" onClick={resetFilters}>
              <Filter className="h-4 w-4 mr-2" />
              重置
            </Button>
          </div>

          {/* 表格 */}
          <EnrollmentTable
            enrollments={data?.enrollments || []}
            onUpdateStatus={handleUpdateStatus}
            onDelete={handleDelete}
            loading={loading}
          />

          {/* 分页 */}
          {data?.pagination && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-muted-foreground">
                显示 {(data.pagination.page - 1) * data.pagination.pageSize + 1}{" "}
                到{" "}
                {Math.min(
                  data.pagination.page * data.pagination.pageSize,
                  data.pagination.total,
                )}{" "}
                条， 共 {data.pagination.total} 条记录
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setFilters((prev) => ({ ...prev, page: prev.page - 1 }))
                  }
                  disabled={data.pagination.page <= 1}
                >
                  上一页
                </Button>
                <div className="flex items-center space-x-1">
                  {Array.from(
                    { length: Math.min(5, data.pagination.totalPages) },
                    (_, i) => {
                      let pageNum;
                      if (data.pagination.totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (data.pagination.page <= 3) {
                        pageNum = i + 1;
                      } else if (
                        data.pagination.page >=
                        data.pagination.totalPages - 2
                      ) {
                        pageNum = data.pagination.totalPages - 4 + i;
                      } else {
                        pageNum = data.pagination.page - 2 + i;
                      }

                      return (
                        <Button
                          key={pageNum}
                          variant={
                            data.pagination.page === pageNum
                              ? "default"
                              : "outline"
                          }
                          size="sm"
                          onClick={() =>
                            setFilters((prev) => ({ ...prev, page: pageNum }))
                          }
                        >
                          {pageNum}
                        </Button>
                      );
                    },
                  )}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setFilters((prev) => ({ ...prev, page: prev.page + 1 }))
                  }
                  disabled={data.pagination.page >= data.pagination.totalPages}
                >
                  下一页
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
