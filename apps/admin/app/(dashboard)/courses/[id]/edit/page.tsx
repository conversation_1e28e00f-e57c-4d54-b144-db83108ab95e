"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { CourseForm } from "@/components/courses/course-form";
import { Button } from "@workspace/ui/components/button";
import { ArrowLeft, Loader2 } from "lucide-react";
import Link from "next/link";
import { usePermissions } from "@/hooks/usePermissions";
import { toast } from "sonner";
import { PERMISSIONS } from "@/config/permissions.config";
import { redirect } from "next/navigation";

// 课程数据类型
interface CourseData {
  id: string;
  title: string;
  subtitle?: string;
  description: string;
  cover: string;
  categoryId: string;
  level: "BEGINNER" | "INTERMEDIATE" | "ADVANCED";
  price: number;
  originalPrice?: number;
  isFree: boolean;
  requireLogin: boolean;
  instructorName: string;
  instructorTitle?: string;
  instructorAvatar?: string;
  instructorBio?: string;
  previewVideo?: string;
  tags: string[];
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords: string[];
  status: "DRAFT" | "REVIEWING" | "PUBLISHED" | "OFFLINE";
  createdAt: string;
  updatedAt: string;
}

export default function EditCoursePage() {
  const params = useParams();
  const { hasPermission } = usePermissions();
  const [course, setCourse] = useState<CourseData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const courseId = params.id as string;

  // 权限检查
  if (!hasPermission(PERMISSIONS.COURSE.MANAGEMENT.EDIT)) {
    redirect("/403");
  }

  // 获取课程数据
  useEffect(() => {
    const fetchCourse = async () => {
      try {
        setLoading(true);
        setError(null);

        // 添加includeContent=true以获取完整课程内容（包括章节和课时）
        const response = await fetch(`/api/courses/${courseId}?includeContent=true`);
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        
        if (!result.success) {
          throw new Error(result.message || "获取课程数据失败");
        }

        // 确保数据格式符合表单要求
        const courseData = result.data;
        
        // 处理章节和课时数据
        if (courseData.chapters) {
          // 确保每个章节和课时有正确的数据结构
          courseData.chapters = courseData.chapters.map((chapter: any, chapterIndex: number) => ({
            id: chapter.id,
            title: chapter.title,
            description: chapter.description || "",
            order: chapter.order || chapterIndex,
            lessons: chapter.lessons?.map((lesson: any, lessonIndex: number) => ({
              id: lesson.id,
              title: lesson.title,
              description: lesson.description || "",
              videoUrl: lesson.videoUrl || "",
              videoDuration: lesson.videoDuration || 0,
              videoSize: lesson.videoSize || 0,
              order: lesson.order || lessonIndex,
              isFree: lesson.isFree || false,
            })) || []
          }));
        } else {
          courseData.chapters = [];
        }
        
        // 确保其他必要字段存在
        courseData.tags = courseData.tags || [];
        courseData.metaKeywords = courseData.metaKeywords || [];
        
        setCourse(courseData);
      } catch (err) {
        console.error("获取课程数据失败:", err);
        const errorMessage = err instanceof Error ? err.message : "获取课程数据失败，请重试";
        setError(errorMessage);
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    if (courseId) {
      fetchCourse();
    }
  }, [courseId]);

  const handleSuccess = () => {
    toast.success("课程更新成功！");
    window.location.href = "/courses";
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>加载课程数据中...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error || !course) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <p className="text-destructive">{error || "课程不存在"}</p>
          <Link href="/courses">
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回课程列表
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面标题和导航 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/courses">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回课程列表
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">编辑课程</h1>
            <p className="text-muted-foreground mt-2">修改课程信息和设置</p>
          </div>
        </div>
      </div>

      {/* 课程表单 */}
      <CourseForm mode="edit" initialData={course} onSuccess={handleSuccess} />
    </div>
  );
}
