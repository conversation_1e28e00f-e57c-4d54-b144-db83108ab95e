"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { Button } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import { Separator } from "@workspace/ui/components/separator";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import {
  ArrowLeft,
  Edit,
  Eye,
  Clock,
  Users,
  Star,
  DollarSign,
  Play,
  Calendar,
  Globe,
  Lock,
  Loader2,
} from "lucide-react";
import { usePermissions } from "@/hooks/usePermissions";
import { PERMISSIONS } from "@/config/permissions.config";

// 课程数据类型
interface Course {
  id: string;
  title: string;
  subtitle?: string;
  description: string;
  cover: string;
  categoryName: string;
  level: "BEGINNER" | "INTERMEDIATE" | "ADVANCED";
  duration: number;
  lessonsCount: number;
  price: number;
  originalPrice?: number;
  isFree: boolean;
  status: "DRAFT" | "REVIEWING" | "PUBLISHED" | "OFFLINE";
  viewCount: number;
  enrollCount: number;
  rating?: number;
  instructorName: string;
  instructorTitle?: string;
  instructorAvatar?: string;
  instructorBio?: string;
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords: string[];
  tags: string[];
  previewVideo?: string;
  requireLogin: boolean;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export default function CourseDetailPage() {
  const params = useParams();
  const courseId = params.id as string;
  const { hasPermission } = usePermissions();
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);

  // 权限检查
  const canEdit = hasPermission(PERMISSIONS.COURSE.MANAGEMENT.EDIT);
  const canDelete = hasPermission(PERMISSIONS.COURSE.MANAGEMENT.DELETE);
  const canPublish = hasPermission(PERMISSIONS.COURSE.MANAGEMENT.PUBLISH);

  // 模拟获取课程数据
  useEffect(() => {
    const fetchCourse = async () => {
      try {
        // 这里应该调用实际的API
        // const response = await fetch(`/api/courses/${courseId}`);
        // const data = await response.json();

        // 模拟数据
        const mockCourse: Course = {
          id: courseId,
          title: "托福听力技巧全攻略",
          subtitle: "30天突破听力瓶颈",
          description:
            "本课程专门针对托福听力考试，通过系统的训练方法和技巧讲解，帮助学生在短时间内提升听力水平。课程内容包括：\n\n1. 托福听力考试结构分析\n2. 常见题型及解题技巧\n3. 听力材料精选训练\n4. 实战模拟练习\n\n适合有一定英语基础，准备参加托福考试的学生。课程采用理论结合实践的教学方式，通过大量的真题练习和模拟测试，帮助学生快速提升听力能力，达到目标分数。",
          cover: "https://via.placeholder.com/800x450",
          categoryName: "语言考试",
          level: "INTERMEDIATE",
          duration: 480,
          lessonsCount: 24,
          price: 299,
          originalPrice: 399,
          isFree: false,
          status: "PUBLISHED",
          viewCount: 1234,
          enrollCount: 89,
          rating: 4.8,
          instructorName: "张老师",
          instructorTitle: "资深托福讲师",
          instructorAvatar: "https://via.placeholder.com/100x100",
          instructorBio:
            "拥有10年托福教学经验，帮助超过1000名学生成功通过托福考试。擅长听力和口语教学，独创的听力训练方法深受学生喜爱。",
          metaTitle: "托福听力技巧全攻略 - 30天突破听力瓶颈",
          metaDescription:
            "专业的托福听力培训课程，通过系统训练快速提升听力成绩",
          metaKeywords: ["托福", "听力", "技巧", "培训"],
          tags: ["托福", "听力", "考试技巧"],
          previewVideo: "https://example.com/preview.mp4",
          requireLogin: true,
          publishedAt: "2024-01-15",
          createdAt: "2024-01-10",
          updatedAt: "2024-01-15",
        };

        setCourse(mockCourse);
      } catch (error) {
        console.error("获取课程信息失败:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchCourse();
  }, [courseId]);

  // 状态标签
  const getStatusBadge = (status: Course["status"]) => {
    const variants = {
      DRAFT: "secondary",
      REVIEWING: "default",
      PUBLISHED: "default",
      OFFLINE: "destructive",
    } as const;

    const labels = {
      DRAFT: "草稿",
      REVIEWING: "审核中",
      PUBLISHED: "已发布",
      OFFLINE: "已下架",
    };

    return <Badge variant={variants[status]}>{labels[status]}</Badge>;
  };

  // 级别标签
  const getLevelBadge = (level: Course["level"]) => {
    const colors = {
      BEGINNER: "bg-green-100 text-green-800",
      INTERMEDIATE: "bg-blue-100 text-blue-800",
      ADVANCED: "bg-purple-100 text-purple-800",
    };

    const labels = {
      BEGINNER: "初级",
      INTERMEDIATE: "中级",
      ADVANCED: "高级",
    };

    return (
      <Badge variant="outline" className={colors[level]}>
        {labels[level]}
      </Badge>
    );
  };

  // 格式化时长
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">加载中...</span>
        </div>
      </div>
    );
  }

  if (!course) {
    return (
      <div className="p-6 space-y-6">
        <div className="text-center py-12">
          <p className="text-muted-foreground">课程不存在</p>
          <Link href="/courses">
            <Button className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回课程列表
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/courses">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回课程列表
            </Button>
          </Link>
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-3xl font-bold">课程详情</h1>
              {getStatusBadge(course.status)}
            </div>
            <p className="text-muted-foreground mt-2">查看和管理课程信息</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {canEdit && (
            <Link href={`/courses/${course.id}/edit`}>
              <Button>
                <Edit className="h-4 w-4 mr-2" />
                编辑课程
              </Button>
            </Link>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧主要内容 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 封面图 */}
              <div className="relative aspect-video rounded-lg overflow-hidden">
                <img
                  src={course.cover}
                  alt={course.title}
                  className="w-full h-full object-cover"
                />
                {course.previewVideo && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                    <Button size="lg" className="rounded-full">
                      <Play className="h-6 w-6 mr-2" />
                      预览视频
                    </Button>
                  </div>
                )}
              </div>

              {/* 标题和副标题 */}
              <div>
                <h2 className="text-2xl font-bold">{course.title}</h2>
                {course.subtitle && (
                  <p className="text-lg text-muted-foreground mt-1">
                    {course.subtitle}
                  </p>
                )}
              </div>

              {/* 标签 */}
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">分类:</span>
                <Badge variant="outline">{course.categoryName}</Badge>
                {getLevelBadge(course.level)}
                {course.tags.map((tag) => (
                  <Badge key={tag} variant="secondary">
                    {tag}
                  </Badge>
                ))}
              </div>

              {/* 课程描述 */}
              <div>
                <h3 className="font-semibold mb-2">课程介绍</h3>
                <div className="whitespace-pre-wrap text-muted-foreground">
                  {course.description}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 讲师信息 */}
          <Card>
            <CardHeader>
              <CardTitle>讲师信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-start gap-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage
                    src={course.instructorAvatar}
                    alt={course.instructorName}
                  />
                  <AvatarFallback>
                    {course.instructorName.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <h4 className="font-semibold">{course.instructorName}</h4>
                  {course.instructorTitle && (
                    <p className="text-sm text-muted-foreground">
                      {course.instructorTitle}
                    </p>
                  )}
                  {course.instructorBio && (
                    <p className="text-sm text-muted-foreground mt-2">
                      {course.instructorBio}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* SEO信息 */}
          <Card>
            <CardHeader>
              <CardTitle>SEO设置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">页面标题</label>
                <p className="text-sm text-muted-foreground mt-1">
                  {course.metaTitle || course.title}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium">页面描述</label>
                <p className="text-sm text-muted-foreground mt-1">
                  {course.metaDescription || course.subtitle}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium">关键词</label>
                <div className="flex flex-wrap gap-1 mt-1">
                  {course.metaKeywords.map((keyword) => (
                    <Badge key={keyword} variant="outline" className="text-xs">
                      {keyword}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 右侧信息面板 */}
        <div className="space-y-6">
          {/* 价格信息 */}
          <Card>
            <CardHeader>
              <CardTitle>定价信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-muted-foreground" />
                {course.isFree ? (
                  <Badge variant="secondary">免费</Badge>
                ) : (
                  <div className="flex items-center gap-2">
                    <span className="text-2xl font-bold">¥{course.price}</span>
                    {course.originalPrice &&
                      course.originalPrice > course.price && (
                        <span className="text-sm text-muted-foreground line-through">
                          ¥{course.originalPrice}
                        </span>
                      )}
                  </div>
                )}
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                {course.requireLogin ? (
                  <>
                    <Lock className="h-4 w-4" />
                    需要登录
                  </>
                ) : (
                  <>
                    <Globe className="h-4 w-4" />
                    公开访问
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 课程统计 */}
          <Card>
            <CardHeader>
              <CardTitle>课程统计</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div className="font-semibold">
                    {formatDuration(course.duration)}
                  </div>
                  <div className="text-xs text-muted-foreground">总时长</div>
                </div>
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <Play className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div className="font-semibold">{course.lessonsCount}</div>
                  <div className="text-xs text-muted-foreground">课时数</div>
                </div>
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <Eye className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div className="font-semibold">
                    {course.viewCount.toLocaleString()}
                  </div>
                  <div className="text-xs text-muted-foreground">浏览量</div>
                </div>
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div className="font-semibold">{course.enrollCount}</div>
                  <div className="text-xs text-muted-foreground">报名数</div>
                </div>
              </div>

              {course.rating && (
                <div className="flex items-center justify-center gap-2 pt-2 border-t">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="font-semibold">{course.rating}</span>
                  <span className="text-sm text-muted-foreground">评分</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 时间信息 */}
          <Card>
            <CardHeader>
              <CardTitle>时间信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">创建时间:</span>
                <span>{new Date(course.createdAt).toLocaleDateString()}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">更新时间:</span>
                <span>{new Date(course.updatedAt).toLocaleDateString()}</span>
              </div>
              {course.publishedAt && (
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">发布时间:</span>
                  <span>
                    {new Date(course.publishedAt).toLocaleDateString()}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
