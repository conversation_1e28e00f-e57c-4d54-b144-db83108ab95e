"use client";

import { useState, useEffect } from "react";
import { Button } from "@workspace/ui/components/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { Badge } from "@workspace/ui/components/badge";
import {
  Plus,
  Edit,
  Trash2,
  MoreHorizontal,
  Loader2,
  FolderOpen,
  BookOpen,
  RefreshCw,
} from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { usePermissions } from "@/hooks/usePermissions";
import { PERMISSIONS } from "@/config/permissions.config";
import { CourseCategoryManager } from "@/components/courses/course-category-manager";

// 分类数据类型
interface CourseCategory {
  id: string;
  name: string;
  description?: string;
  slug: string;
  parentId?: string;
  icon?: string;
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  children?: CourseCategory[];
  parent?: CourseCategory;
  _count?: {
    courses: number;
  };
}

export default function CourseCategoriesPage() {
  const { hasPermission } = usePermissions();
  const [categories, setCategories] = useState<CourseCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // 权限检查
  const canCreate = hasPermission(PERMISSIONS.COURSE.CATEGORY.CREATE);
  const canEdit = hasPermission(PERMISSIONS.COURSE.CATEGORY.EDIT);
  const canDelete = hasPermission(PERMISSIONS.COURSE.CATEGORY.DELETE);

  // 加载分类数据
  const loadCategories = async () => {
    try {
      setRefreshing(true);
      const response = await fetch("/api/courses/category-tree");
      const result = await response.json();

      if (result.success) {
        setCategories(result.data || []);
      } else {
        toast.error(result.message || "获取分类列表失败");
      }
    } catch (error) {
      console.error("获取分类列表失败:", error);
      toast.error("获取分类列表失败，请重试");
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadCategories();
  }, []);

  // 删除分类
  const handleDelete = async (category: CourseCategory) => {
    const courseCount = category._count?.courses || 0;
    const childrenCount = category.children?.length || 0;

    if (courseCount > 0) {
      toast.error(`该分类下还有 ${courseCount} 个课程，无法删除`);
      return;
    }

    if (childrenCount > 0) {
      toast.error(`该分类下还有 ${childrenCount} 个子分类，无法删除`);
      return;
    }

    if (!confirm(`确定要删除分类"${category.name}"吗？`)) {
      return;
    }

    try {
      const response = await fetch(
        `/api/courses/categories?id=${category.id}`,
        {
          method: "DELETE",
        },
      );

      const result = await response.json();

      if (result.success) {
        toast.success("分类删除成功");
        loadCategories(); // 重新加载数据
      } else {
        toast.error(result.message || "删除失败，请重试");
      }
    } catch (error) {
      console.error("删除失败:", error);
      toast.error("删除失败，请重试");
    }
  };

  // 渲染分类树
  const renderCategoryTree = (cats: CourseCategory[], level = 0): React.ReactNode[] => {
    const result: React.ReactNode[] = [];
    
    cats.forEach((category) => {
      // 添加当前分类行
      result.push(
        <TableRow key={category.id}>
          <TableCell>
            <div
              className="flex items-center"
              style={{ paddingLeft: `${level * 20}px` }}
            >
              {level > 0 && (
                <div className="w-4 h-4 border-l border-b border-muted mr-2" />
              )}
              {category.icon ? (
                <span className="text-lg mr-2">{category.icon}</span>
              ) : (
                <FolderOpen className="h-4 w-4 mr-2 text-muted-foreground" />
              )}
              <div>
                <span className="font-medium">{category.name}</span>
                {category.description && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {category.description}
                  </p>
                )}
              </div>
            </div>
          </TableCell>
          <TableCell>
            <code className="text-xs bg-muted px-2 py-1 rounded">
              {category.slug}
            </code>
          </TableCell>
          <TableCell>
            <Badge variant={category.isActive ? "default" : "secondary"}>
              {category.isActive ? "启用" : "禁用"}
            </Badge>
          </TableCell>
          <TableCell>
            <div className="flex items-center gap-1">
              <BookOpen className="h-4 w-4 text-muted-foreground" />
              {category._count?.courses || 0}
            </div>
          </TableCell>
          <TableCell>{category.order}</TableCell>
          <TableCell>
            <time className="text-sm text-muted-foreground">
              {new Date(category.updatedAt).toLocaleDateString()}
            </time>
          </TableCell>
          <TableCell className="text-right">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">打开菜单</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {canEdit && (
                  <DropdownMenuItem asChild>
                    <Link href={`/courses/categories/${category.id}/edit`}>
                      <Edit className="mr-2 h-4 w-4" />
                      编辑
                    </Link>
                  </DropdownMenuItem>
                )}
                {canDelete && (
                  <DropdownMenuItem
                    onClick={() => handleDelete(category)}
                    className="text-destructive"
                    disabled={
                      (category._count?.courses || 0) > 0 ||
                      (category.children && category.children.length > 0)
                    }
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    删除
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </TableCell>
        </TableRow>
      );
      
      // 递归添加子分类行
      if (category.children && category.children.length > 0) {
        result.push(...renderCategoryTree(category.children, level + 1));
      }
    });
    
    return result;
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="space-y-1">
          <h1 className="text-2xl font-semibold tracking-tight">课程分类</h1>
          <p className="text-sm text-muted-foreground">
            管理课程分类，支持层级结构和拖拽排序
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={loadCategories}
            disabled={refreshing}
          >
            <RefreshCw
              className={`mr-2 h-4 w-4 ${refreshing ? "animate-spin" : ""}`}
            />
            刷新
          </Button>
          <CourseCategoryManager />
          {canCreate && (
            <Link href="/courses/categories/create">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                新建分类
              </Button>
            </Link>
          )}
        </div>
      </div>

      {/* 分类列表 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h2 className="text-lg font-medium">分类列表</h2>
            {!loading && (
              <p className="text-sm text-muted-foreground">
                共 {categories.length} 个分类
              </p>
            )}
          </div>
        </div>

        {loading ? (
          <div className="flex items-center justify-center h-32 rounded-lg border bg-card">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm text-muted-foreground">加载中...</span>
            </div>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>分类名称</TableHead>
                  <TableHead>URL别名</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>课程数</TableHead>
                  <TableHead>排序</TableHead>
                  <TableHead>更新时间</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {categories.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      <div className="text-muted-foreground">
                        暂无分类数据
                        {canCreate && (
                          <>
                            ，
                            <Link
                              href="/courses/categories/create"
                              className="text-primary hover:underline"
                            >
                              立即创建
                            </Link>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  renderCategoryTree(categories)
                )}
              </TableBody>
            </Table>
          </div>
        )}
      </div>
    </div>
  );
}
