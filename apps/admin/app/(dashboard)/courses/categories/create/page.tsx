"use client";

import { CategoryForm } from "@/components/courses/category-form";
import { Button } from "@workspace/ui/components/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { usePermissions } from "@/hooks/usePermissions";
import { redirect } from "next/navigation";
import { PERMISSIONS } from "@/config/permissions.config";

export default function CreateCategoryPage() {
  const { hasPermission } = usePermissions();

  // 权限检查
  if (!hasPermission(PERMISSIONS.COURSE.CATEGORY.CREATE)) {
    redirect("/403");
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面标题和导航 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/courses/categories">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回分类列表
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">创建新分类</h1>
            <p className="text-muted-foreground mt-2">
              填写分类基本信息，创建新的课程分类
            </p>
          </div>
        </div>
      </div>

      {/* 分类表单 */}
      <CategoryForm
        mode="create"
        onSuccess={() => {
          // 创建成功后的回调将在表单组件内处理
        }}
      />
    </div>
  );
}
