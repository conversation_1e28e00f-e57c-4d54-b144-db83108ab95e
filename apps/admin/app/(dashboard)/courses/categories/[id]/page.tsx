"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { Button } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import {
  ArrowLeft,
  Edit,
  Folder,
  Calendar,
  Hash,
  Globe,
  Users,
  Loader2,
  FolderTree,
} from "lucide-react";
import { usePermissions } from "@/hooks/usePermissions";
import { PERMISSIONS } from "@/config/permissions.config";

// 分类数据类型
interface CategoryData {
  id: string;
  name: string;
  slug: string;
  description?: string;
  parentId?: string;
  parent?: {
    id: string;
    name: string;
    slug: string;
  };
  children?: {
    id: string;
    name: string;
    slug: string;
    courseCount: number;
  }[];
  icon?: string;
  order: number;
  isActive: boolean;
  courseCount: number;
  createdAt: string;
  updatedAt: string;
}

export default function CategoryDetailPage() {
  const params = useParams();
  const { hasPermission } = usePermissions();
  const [category, setCategory] = useState<CategoryData | null>(null);
  const [loading, setLoading] = useState(true);

  const categoryId = params.id as string;

  // 权限检查
  const canEdit = hasPermission(PERMISSIONS.COURSE.CATEGORY.EDIT);
  const canDelete = hasPermission(PERMISSIONS.COURSE.CATEGORY.DELETE);

  // 模拟获取分类数据
  useEffect(() => {
    const fetchCategory = async () => {
      try {
        // 这里应该调用实际的API
        // const response = await fetch(`/api/categories/${categoryId}`);
        // const data = await response.json();

        // 模拟数据
        const mockCategory: CategoryData = {
          id: categoryId,
          name: "语言考试",
          slug: "language-tests",
          description:
            "各类语言考试培训课程，包括托福、雅思等国际标准化考试。我们提供专业的考试技巧和实战经验，帮助学生快速提升语言水平，达到理想分数。",
          icon: "🌐",
          order: 1,
          isActive: true,
          courseCount: 15,
          children: [
            {
              id: "11",
              name: "托福",
              slug: "toefl",
              courseCount: 8,
            },
            {
              id: "12",
              name: "雅思",
              slug: "ielts",
              courseCount: 7,
            },
          ],
          createdAt: "2024-01-01",
          updatedAt: "2024-01-15",
        };

        setCategory(mockCategory);
      } catch (error) {
        console.error("获取分类信息失败:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategory();
  }, [categoryId]);

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">加载中...</span>
        </div>
      </div>
    );
  }

  if (!category) {
    return (
      <div className="p-6 space-y-6">
        <div className="text-center py-12">
          <p className="text-muted-foreground">分类不存在</p>
          <Link href="/courses/categories">
            <Button className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回分类列表
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/courses/categories">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回分类列表
            </Button>
          </Link>
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-3xl font-bold">分类详情</h1>
              <Badge variant={category.isActive ? "default" : "secondary"}>
                {category.isActive ? "启用" : "禁用"}
              </Badge>
            </div>
            <p className="text-muted-foreground mt-2">查看和管理分类信息</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {canEdit && (
            <Link href={`/courses/categories/${category.id}/edit`}>
              <Button>
                <Edit className="h-4 w-4 mr-2" />
                编辑分类
              </Button>
            </Link>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧主要内容 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 分类名称和图标 */}
              <div className="flex items-center gap-4">
                <div className="p-4 border rounded-lg">
                  {category.icon ? (
                    <span className="text-4xl">{category.icon}</span>
                  ) : (
                    <FolderTree className="h-10 w-10 text-muted-foreground" />
                  )}
                </div>
                <div>
                  <h2 className="text-2xl font-bold">{category.name}</h2>
                  <div className="flex items-center gap-2 mt-1">
                    <Hash className="h-4 w-4 text-muted-foreground" />
                    <code className="text-sm bg-muted px-2 py-1 rounded">
                      {category.slug}
                    </code>
                  </div>
                </div>
              </div>

              {/* 分类描述 */}
              {category.description && (
                <div>
                  <h3 className="font-semibold mb-2">分类描述</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {category.description}
                  </p>
                </div>
              )}

              {/* 父分类 */}
              {category.parent && (
                <div>
                  <h3 className="font-semibold mb-2">父分类</h3>
                  <Link
                    href={`/courses/categories/${category.parent.id}`}
                    className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800"
                  >
                    <Folder className="h-4 w-4" />
                    {category.parent.name}
                  </Link>
                </div>
              )}

              {/* 子分类 */}
              {category.children && category.children.length > 0 && (
                <div>
                  <h3 className="font-semibold mb-3">子分类</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {category.children.map((child) => (
                      <Link
                        key={child.id}
                        href={`/courses/categories/${child.id}`}
                        className="p-3 border rounded-lg hover:bg-muted transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Folder className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">{child.name}</span>
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {child.courseCount} 门课程
                          </span>
                        </div>
                      </Link>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 右侧信息面板 */}
        <div className="space-y-6">
          {/* 统计信息 */}
          <Card>
            <CardHeader>
              <CardTitle>统计信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">关联课程</span>
                </div>
                <span className="text-2xl font-bold">
                  {category.courseCount}
                </span>
              </div>

              {category.children && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <FolderTree className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">子分类</span>
                  </div>
                  <span className="text-2xl font-bold">
                    {category.children.length}
                  </span>
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Hash className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">排序</span>
                </div>
                <span className="text-lg font-bold">{category.order}</span>
              </div>
            </CardContent>
          </Card>

          {/* URL信息 */}
          <Card>
            <CardHeader>
              <CardTitle>URL 信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2 text-sm">
                <Globe className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">分类链接:</span>
              </div>
              <code className="block p-2 bg-muted rounded text-xs break-all">
                /categories/{category.slug}
              </code>
            </CardContent>
          </Card>

          {/* 时间信息 */}
          <Card>
            <CardHeader>
              <CardTitle>时间信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">创建时间:</span>
                <span>{new Date(category.createdAt).toLocaleDateString()}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">更新时间:</span>
                <span>{new Date(category.updatedAt).toLocaleDateString()}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
