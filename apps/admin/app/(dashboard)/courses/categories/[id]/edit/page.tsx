"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { CategoryForm } from "@/components/courses/category-form";
import { Button } from "@workspace/ui/components/button";
import { ArrowLeft, Loader2 } from "lucide-react";
import Link from "next/link";
import { usePermissions } from "@/hooks/usePermissions";
import { toast } from "sonner";
import { PERMISSIONS } from "@/config/permissions.config";

// 分类数据类型
interface CategoryData {
  id: string;
  name: string;
  slug: string;
  description?: string;
  parentId?: string;
  icon?: string;
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function EditCategoryPage() {
  const params = useParams();
  const router = useRouter();
  const { hasPermission } = usePermissions();
  const [category, setCategory] = useState<CategoryData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const categoryId = params.id as string;

  // 权限检查
  if (!hasPermission(PERMISSIONS.COURSE.CATEGORY.EDIT)) {
    router.push("/403");
    return null;
  }

  // 获取分类数据
  useEffect(() => {
    const fetchCategory = async () => {
      try {
        setLoading(true);
        setError(null);

        // 直接调用正确的API端点
        const response = await fetch(`/api/courses/categories/${categoryId}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error("分类不存在");
          }
          throw new Error(`服务器错误: ${response.status}`);
        }

        const result = await response.json();

        if (result.success && result.data) {
          setCategory({
            id: result.data.id,
            name: result.data.name,
            slug: result.data.slug,
            description: result.data.description,
            parentId: result.data.parentId,
            icon: result.data.icon,
            order: result.data.order,
            isActive: result.data.isActive,
            createdAt: result.data.createdAt,
            updatedAt: result.data.updatedAt,
          });
        } else {
          throw new Error(result.message || "获取分类数据失败");
        }
      } catch (err) {
        console.error("获取分类数据失败:", err);
        setError(
          err instanceof Error ? err.message : "获取分类数据失败，请重试",
        );
        toast.error("获取分类数据失败");
      } finally {
        setLoading(false);
      }
    };

    if (categoryId) {
      fetchCategory();
    }
  }, [categoryId]);

  const handleSuccess = () => {
    toast.success("分类更新成功！");
    router.push("/courses/categories");
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>加载分类数据中...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error || !category) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-destructive mb-2">
              {error === "分类不存在" ? "分类未找到" : "加载失败"}
            </h2>
            <p className="text-muted-foreground mb-4">
              {error === "分类不存在" 
                ? `ID为 ${categoryId} 的分类不存在，可能已被删除或您没有访问权限。`
                : error || "获取分类数据时发生错误，请稍后重试。"
              }
            </p>
          </div>
          <div className="flex space-x-2">
            <Link href="/courses/categories">
              <Button variant="default">
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回分类列表
              </Button>
            </Link>
            <Button variant="outline" onClick={() => window.location.reload()}>
              重试
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面标题和导航 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/courses/categories">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回分类列表
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">编辑分类</h1>
            <p className="text-muted-foreground mt-2">修改分类信息和设置</p>
          </div>
        </div>
      </div>

      {/* 分类表单 */}
      <CategoryForm
        mode="edit"
        initialData={category}
        onSuccess={handleSuccess}
      />
    </div>
  );
}
