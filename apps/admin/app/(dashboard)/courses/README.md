## 课程模块功能分析报告 📊

基于你的 Prisma Schema 分析，我发现你的课程模块**缺少以下重要功能**：

### 🚨 缺失的核心功能模块

#### 1. **课程分类管理** (CourseCategory)

- ❌ **缺失**: 课程分类 CRUD 功能
- 📄 **对应表**: `CourseCategory` (支持层级结构)
- 🎯 **需要页面**:
  - `/courses/categories` - 分类列表页
  - `/courses/categories/create` - 创建分类页
  - `/courses/categories/[id]/edit` - 编辑分类页
- 🔧 **功能要求**:
  - 支持父子级联分类
  - 拖拽排序 (order 字段)
  - 启用/禁用状态管理

#### 2. **课程内容管理** (章节+课时)

- ❌ **缺失**: 课程章节和课时管理
- 📄 **对应表**: `CourseChapter`, `CourseLesson`, `LessonTimestamp`
- 🎯 **需要页面**:
  - `/courses/[id]/content` - 课程内容管理页
  - `/courses/[id]/chapters/create` - 创建章节
  - `/courses/[id]/lessons/create` - 创建课时
- 🔧 **功能要求**:
  - 章节课时拖拽排序
  - 视频上传/URL管理
  - 时间戳标记功能
  - 免费试看设置

#### 3. **课程报名管理** (CourseEnrollment)

- ❌ **缺失**: 报名学员管理
- 📄 **对应表**: `CourseEnrollment`, `LessonProgress`
- 🎯 **需要页面**:
  - `/courses/[id]/enrollments` - 报名管理页
  - `/courses/enrollments` - 全局报名统计
- 🔧 **功能要求**:
  - 学员列表(分页)
  - 学习进度跟踪
  - 支付状态管理
  - 完成证书发放

#### 4. **课程评价管理** (CourseReview)

- ❌ **缺失**: 课程评价系统
- 📄 **对应表**: `CourseReview`
- 🎯 **需要页面**:
  - `/courses/[id]/reviews` - 课程评价管理
  - `/courses/reviews` - 全局评价管理
- 🔧 **功能要求**:
  - 评价审核
  - 评分统计
  - 回复功能

### 📋 分页功能检查

当前**已实现分页**的模块：

- ✅ 课程列表页 (基础过滤，需要后端分页)

**需要添加分页**的功能：

- ❌ 课程分类列表
- ❌ 课程报名列表
- ❌ 课程评价列表
- ❌ 章节课时列表

### 🔐 权限控制检查

**已实现权限**：

- ✅ 课程创建/编辑/删除权限控制
- ✅ 基于角色的页面访问控制

**需要补充权限**：

- ❌ 课程分类管理权限
- ❌ 课程内容管理权限
- ❌ 报名数据查看权限
- ❌ 评价审核权限

### 🔗 前置依赖功能

创建课程时**必需的前置功能**：

1. **课程分类管理** ⚠️ **必需**

   ```typescript
   // 当前 course-form 中硬编码分类
   const courseCategories = [
     { id: "category-1", name: "语言考试" },
     // ... 需要改为动态获取
   ];
   ```

2. **管理员用户系统** ✅ **已有**

   - Schema 中 `createdById` 字段需要管理员信息

3. **文件上传服务** ⚠️ **建议**
   - 课程封面、讲师头像、视频文件上传

### 📝 建议的开发优先级

#### 🔥 **高优先级** (阻塞课程创建)

1. **课程分类管理模块** - 创建课程的前置依赖
2. **文件上传组件** - 替代当前的URL输入

#### 🔷 **中优先级** (完善课程功能)

3. **课程内容管理** - 章节课时管理
4. **课程报名管理** - 学员数据统计

#### 🔶 **低优先级** (增强用户体验)

5. **课程评价管理** - 用户反馈系统
6. **高级过滤和搜索** - 提升查找效率

### 🛠️ 具体实现建议

1. **立即实现课程分类管理**:

   ```bash
   # 需要创建的文件
   apps/admin/app/(dashboard)/courses/categories/page.tsx
   apps/admin/app/(dashboard)/courses/categories/create/page.tsx
   apps/admin/components/courses/category-form.tsx
   apps/admin/components/courses/category-tree.tsx
   ```

2. **修改课程表单**:
   - 将硬编码分类改为动态获取
   - 添加 `createdById` 字段处理

你的课程模块基础功能已经很完善，但**课程分类管理是当前最紧急需要实现的功能**，否则无法真正使用课程创建功能！
