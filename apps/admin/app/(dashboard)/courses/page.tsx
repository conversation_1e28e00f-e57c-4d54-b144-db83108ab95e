"use client";

import { useState, useMemo } from "react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Badge } from "@workspace/ui/components/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import {
  Plus,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Play,
  Pause,
  Users,
  Star,
  Clock,
  DollarSign,
  Search,
  Loader2,
} from "lucide-react";
import { usePermissions } from "@/hooks/usePermissions";
import { useCourses, useCourseActions, type Course } from "@/hooks/useCourses";
import { CourseStatus, CourseLevel } from "@workspace/database";
import Link from "next/link";
import { toast } from "sonner";
import { PERMISSIONS } from "@/config/permissions.config";

// 课程状态枚举
const COURSE_STATUS = {
  DRAFT: "草稿",
  REVIEWING: "审核中",
  PUBLISHED: "已发布",
  OFFLINE: "已下架",
} as const;

export default function CoursesPage() {
  const { hasPermission } = usePermissions();

  // 搜索和过滤状态
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [levelFilter, setLevelFilter] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(1);

  // API查询参数
  const query = useMemo(
    () => ({
      search: searchTerm || undefined,
      status:
        statusFilter !== "all" ? (statusFilter as CourseStatus) : undefined,
      level: levelFilter !== "all" ? (levelFilter as CourseLevel) : undefined,
      page: currentPage,
      limit: 20,
      sortBy: "updatedAt" as const,
      sortOrder: "desc" as const,
    }),
    [searchTerm, statusFilter, levelFilter, currentPage],
  );

  // 使用API hooks
  const { courses, loading, error, total, refetch } = useCourses(query);
  const {
    loading: actionLoading,
    updateCourseStatus,
    deleteCourse,
  } = useCourseActions();

  // 权限检查
  const canCreate = hasPermission(PERMISSIONS.COURSE.MANAGEMENT.CREATE);
  const canEdit = hasPermission(PERMISSIONS.COURSE.MANAGEMENT.EDIT);
  const canDelete = hasPermission(PERMISSIONS.COURSE.MANAGEMENT.DELETE);
  const canPublish = hasPermission(PERMISSIONS.COURSE.MANAGEMENT.PUBLISH);

  // 处理课程操作
  const handleStatusChange = async (
    courseId: string,
    newStatus: CourseStatus,
  ) => {
    try {
      const result = await updateCourseStatus(courseId, newStatus);
      if (result.success) {
        toast.success("课程状态更新成功");
        refetch();
      } else {
        toast.error(result.message || "更新失败");
      }
    } catch (error) {
      toast.error("更新失败");
    }
  };

  const handleDelete = async (courseId: string) => {
    if (!confirm("确定要删除这个课程吗？")) return;

    try {
      const result = await deleteCourse(courseId);
      if (result.success) {
        toast.success("课程删除成功");
        refetch();
      } else {
        toast.error(result.message || "删除失败");
      }
    } catch (error) {
      toast.error("删除失败");
    }
  };

  // 格式化时长
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  // 状态样式
  const getStatusBadge = (status: CourseStatus) => {
    const variants = {
      DRAFT: "secondary" as const,
      REVIEWING: "default" as const,
      PUBLISHED: "default" as const,
      OFFLINE: "destructive" as const,
    };

    return <Badge variant={variants[status]}>{COURSE_STATUS[status]}</Badge>;
  };

  // 级别样式
  const getLevelBadge = (level: CourseLevel) => {
    const colors: Record<CourseLevel, string> = {
      BEGINNER: "bg-green-100 text-green-800",
      INTERMEDIATE: "bg-blue-100 text-blue-800",
      ADVANCED: "bg-purple-100 text-purple-800",
      ALL: "bg-gray-100 text-gray-800",
    };

    const levelNames: Record<CourseLevel, string> = {
      BEGINNER: "初级",
      INTERMEDIATE: "中级",
      ADVANCED: "高级",
      ALL: "全部",
    };

    return (
      <Badge variant="outline" className={colors[level]}>
        {levelNames[level] || level}
      </Badge>
    );
  };

  // 显示加载状态
  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">加载中...</span>
        </div>
      </div>
    );
  }

  // 显示错误状态
  if (error) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-500 mb-2">{error}</p>
            <Button onClick={refetch}>重试</Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="space-y-1">
          <h1 className="text-2xl font-semibold tracking-tight">课程管理</h1>
          <p className="text-sm text-muted-foreground">
            管理平台上的所有课程内容
          </p>
        </div>
        {canCreate && (
          <Link href="/courses/create">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              创建课程
            </Button>
          </Link>
        )}
      </div>

      {/* 统计概览 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
          <div className="p-6 flex flex-row items-center space-y-0 pb-2">
            <div className="space-y-1">
              <p className="text-sm font-medium leading-none">总课程数</p>
              <p className="text-2xl font-bold">{courses.length}</p>
            </div>
            <Play className="h-4 w-4 text-muted-foreground ml-auto" />
          </div>
        </div>

        <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
          <div className="p-6 flex flex-row items-center space-y-0 pb-2">
            <div className="space-y-1">
              <p className="text-sm font-medium leading-none">总报名数</p>
              <p className="text-2xl font-bold">
                {courses.reduce((sum, course) => sum + course.enrollCount, 0)}
              </p>
            </div>
            <Users className="h-4 w-4 text-muted-foreground ml-auto" />
          </div>
        </div>

        <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
          <div className="p-6 flex flex-row items-center space-y-0 pb-2">
            <div className="space-y-1">
              <p className="text-sm font-medium leading-none">总浏览量</p>
              <p className="text-2xl font-bold">
                {courses
                  .reduce((sum, course) => sum + course.viewCount, 0)
                  .toLocaleString()}
              </p>
            </div>
            <Eye className="h-4 w-4 text-muted-foreground ml-auto" />
          </div>
        </div>

        <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
          <div className="p-6 flex flex-row items-center space-y-0 pb-2">
            <div className="space-y-1">
              <p className="text-sm font-medium leading-none">付费课程</p>
              <p className="text-2xl font-bold">
                {courses.filter((course) => !course.isFree).length}
              </p>
            </div>
            <DollarSign className="h-4 w-4 text-muted-foreground ml-auto" />
          </div>
        </div>
      </div>

      {/* 搜索和过滤 */}
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索课程名称或讲师..."
            value={searchTerm}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setSearchTerm(e.target.value)
            }
            className="pl-8"
          />
        </div>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full md:w-[180px]">
            <SelectValue placeholder="课程状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="DRAFT">草稿</SelectItem>
            <SelectItem value="REVIEWING">审核中</SelectItem>
            <SelectItem value="PUBLISHED">已发布</SelectItem>
            <SelectItem value="OFFLINE">已下架</SelectItem>
          </SelectContent>
        </Select>

        <Select value={levelFilter} onValueChange={setLevelFilter}>
          <SelectTrigger className="w-full md:w-[180px]">
            <SelectValue placeholder="课程级别" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部级别</SelectItem>
            <SelectItem value="BEGINNER">初级</SelectItem>
            <SelectItem value="INTERMEDIATE">中级</SelectItem>
            <SelectItem value="ADVANCED">高级</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* 课程列表 */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>课程信息</TableHead>
              <TableHead>分类</TableHead>
              <TableHead>级别</TableHead>
              <TableHead>时长</TableHead>
              <TableHead>价格</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>数据</TableHead>
              <TableHead>更新时间</TableHead>
              <TableHead className="text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {courses.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="h-24 text-center">
                  没有找到符合条件的课程
                </TableCell>
              </TableRow>
            ) : (
              courses.map((course: Course) => (
                <TableRow key={course.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <img
                        src={course.cover}
                        alt={course.title}
                        className="h-10 w-14 rounded object-cover"
                      />
                      <div className="min-w-0 flex-1">
                        <p className="truncate font-medium">{course.title}</p>
                        {course.subtitle && (
                          <p className="truncate text-sm text-muted-foreground">
                            {course.subtitle}
                          </p>
                        )}
                        <p className="text-xs text-muted-foreground">
                          讲师: {course.instructorName}
                        </p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{course.categoryName}</Badge>
                  </TableCell>
                  <TableCell>{getLevelBadge(course.level)}</TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div className="flex items-center">
                        <Clock className="mr-1 h-3 w-3" />
                        {formatDuration(course.duration)}
                      </div>
                      <div className="text-muted-foreground">
                        {course.lessonsCount} 课时
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {course.isFree ? (
                      <Badge variant="secondary">免费</Badge>
                    ) : (
                      <span className="font-medium">¥{course.price}</span>
                    )}
                  </TableCell>
                  <TableCell>{getStatusBadge(course.status)}</TableCell>
                  <TableCell>
                    <div className="space-y-1 text-sm">
                      <div className="flex items-center">
                        <Eye className="mr-1 h-3 w-3" />
                        {course.viewCount.toLocaleString()}
                      </div>
                      <div className="flex items-center">
                        <Users className="mr-1 h-3 w-3" />
                        {course.enrollCount}
                      </div>
                      {course.rating && (
                        <div className="flex items-center">
                          <Star className="mr-1 h-3 w-3 fill-yellow-400 text-yellow-400" />
                          {course.rating}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <time className="text-sm text-muted-foreground">
                      {new Date(course.updatedAt).toLocaleDateString()}
                    </time>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">打开菜单</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/courses/${course.id}`}>
                            <Eye className="mr-2 h-4 w-4" />
                            查看详情
                          </Link>
                        </DropdownMenuItem>
                        {canEdit && (
                          <DropdownMenuItem asChild>
                            <Link href={`/courses/${course.id}/edit`}>
                              <Edit className="mr-2 h-4 w-4" />
                              编辑课程
                            </Link>
                          </DropdownMenuItem>
                        )}
                        {canPublish && (
                          <>
                            <DropdownMenuSeparator />
                            {course.status === "PUBLISHED" ? (
                              <DropdownMenuItem
                                onClick={() =>
                                  handleStatusChange(course.id, "OFFLINE")
                                }
                                disabled={actionLoading}
                              >
                                <Pause className="mr-2 h-4 w-4" />
                                下架课程
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem
                                onClick={() =>
                                  handleStatusChange(course.id, "PUBLISHED")
                                }
                                disabled={actionLoading}
                              >
                                <Play className="mr-2 h-4 w-4" />
                                发布课程
                              </DropdownMenuItem>
                            )}
                          </>
                        )}
                        {canDelete && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-destructive"
                              onClick={() => handleDelete(course.id)}
                              disabled={actionLoading}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              删除课程
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* 分页 */}
      {total > 20 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            显示 {(currentPage - 1) * 20 + 1} 到{" "}
            {Math.min(currentPage * 20, total)} 条，共 {total} 条
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              上一页
            </Button>
            <div className="flex items-center space-x-1">
              {Array.from(
                { length: Math.min(5, Math.ceil(total / 20)) },
                (_, i) => {
                  const page = i + 1;
                  return (
                    <Button
                      key={page}
                      variant={currentPage === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </Button>
                  );
                },
              )}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setCurrentPage((prev) =>
                  Math.min(Math.ceil(total / 20), prev + 1),
                )
              }
              disabled={currentPage === Math.ceil(total / 20)}
            >
              下一页
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
