"use client";

import { Badge } from "@workspace/ui/components/badge";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Progress } from "@workspace/ui/components/progress";
import {
  Activity,
  ArrowDownRight,
  ArrowUpRight,
  DollarSign,
  FileText,
  GraduationCap,
  Package,
  Users,
} from "lucide-react";

// 统计卡片数据
const statsCards = [
  {
    title: "总用户数",
    value: "12,345",
    description: "较上月增长 12%",
    icon: Users,
    trend: "up",
    change: "+12%",
  },
  {
    title: "活跃租户",
    value: "234",
    description: "付费租户 189 个",
    icon: Activity,
    trend: "up",
    change: "+5%",
  },
  {
    title: "本月收入",
    value: "¥456,789",
    description: "较上月增长 8%",
    icon: DollarSign,
    trend: "up",
    change: "+8%",
  },
  {
    title: "内容发布",
    value: "3,456",
    description: "本月新增 234 篇",
    icon: FileText,
    trend: "down",
    change: "-3%",
  },
];

// 快捷操作
const quickActions = [
  {
    title: "发布公告",
    description: "向所有用户发送系统公告",
    icon: FileText,
    href: "/system/announcements/new",
  },
  {
    title: "课程审核",
    description: "有 5 个课程待审核",
    icon: GraduationCap,
    href: "/courses?status=pending",
    badge: "5",
  },
  {
    title: "产品询价",
    description: "有 12 个新询价单",
    icon: Package,
    href: "/products/inquiries",
    badge: "12",
  },
  {
    title: "用户管理",
    description: "管理平台用户和权限",
    icon: Users,
    href: "/users",
  },
];

// 近期活动
const recentActivities = [
  {
    type: "user",
    message: "新用户注册：张三",
    time: "5 分钟前",
    status: "success",
  },
  {
    type: "payment",
    message: "租户「优学教育」完成付款 ¥2,599",
    time: "15 分钟前",
    status: "success",
  },
  {
    type: "content",
    message: "课程「托福听力技巧」审核通过",
    time: "1 小时前",
    status: "info",
  },
  {
    type: "alert",
    message: "系统监控：CPU使用率过高",
    time: "2 小时前",
    status: "warning",
  },
  {
    type: "error",
    message: "支付接口异常，已自动恢复",
    time: "3 小时前",
    status: "error",
  },
];

export default function DashboardPage() {
  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">仪表盘</h1>
        <p className="text-muted-foreground">
          欢迎回来，这里是您的管理后台概览
        </p>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {statsCards.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {stat.trend === "up" ? (
                  <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <ArrowDownRight className="mr-1 h-3 w-3 text-red-500" />
                )}
                <span
                  className={
                    stat.trend === "up" ? "text-green-500" : "text-red-500"
                  }
                >
                  {stat.change}
                </span>
                <span className="ml-1">{stat.description}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* 快捷操作 */}
        <Card>
          <CardHeader>
            <CardTitle>快捷操作</CardTitle>
            <CardDescription>常用功能快速入口</CardDescription>
          </CardHeader>
          <CardContent className="grid gap-4">
            {quickActions.map((action, index) => (
              <div
                key={index}
                className="flex items-center space-x-4 rounded-lg border p-4 hover:bg-accent transition-colors cursor-pointer"
              >
                <action.icon className="h-8 w-8 text-muted-foreground" />
                <div className="flex-1">
                  <p className="text-sm font-medium">{action.title}</p>
                  <p className="text-xs text-muted-foreground">
                    {action.description}
                  </p>
                </div>
                {action.badge && (
                  <Badge variant="destructive">{action.badge}</Badge>
                )}
              </div>
            ))}
          </CardContent>
        </Card>

        {/* 近期活动 */}
        <Card>
          <CardHeader>
            <CardTitle>近期活动</CardTitle>
            <CardDescription>平台最新动态</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div
                    className={`mt-0.5 h-2 w-2 rounded-full ${
                      activity.status === "success"
                        ? "bg-green-500"
                        : activity.status === "warning"
                          ? "bg-yellow-500"
                          : activity.status === "error"
                            ? "bg-red-500"
                            : "bg-blue-500"
                    }`}
                  />
                  <div className="flex-1 space-y-1">
                    <p className="text-sm">{activity.message}</p>
                    <p className="text-xs text-muted-foreground">
                      {activity.time}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <Button variant="outline" className="w-full mt-4">
              查看全部活动
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* 系统状态 */}
      <Card>
        <CardHeader>
          <CardTitle>系统状态</CardTitle>
          <CardDescription>服务器资源使用情况</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>CPU 使用率</span>
              <span>65%</span>
            </div>
            <Progress value={65} />
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>内存使用率</span>
              <span>48%</span>
            </div>
            <Progress value={48} />
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>磁盘使用率</span>
              <span>72%</span>
            </div>
            <Progress value={72} />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
