import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { AdminAuthService, prisma } from "@workspace/database";
import { PostType, PostStatus } from "@workspace/database";
import { cookies } from "next/headers";
import { AUTH_COOKIE_NAME } from "@/lib/auth/types";

const adminAuthService = new AdminAuthService(prisma);
// 查询参数验证 schema
const querySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
  search: z.string().optional(),
  type: z.nativeEnum(PostType).optional(),
  status: z.nativeEnum(PostStatus).optional(),
  authorId: z.string().optional(),
  categoryId: z.string().optional(),
  isTop: z.string().optional().transform(val => val === "true"),
  isRecommended: z.string().optional().transform(val => val === "true"),
  sortBy: z.string().optional().default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
});

// 创建帖子验证 schema
const createPostSchema = z.object({
  title: z.string().min(1, "标题不能为空").max(200, "标题不能超过200个字符"),
  content: z.string().min(1, "内容不能为空"),
  summary: z.string().max(500, "摘要不能超过500个字符").optional(),
  cover: z.string().url("封面必须是有效的URL").optional(),
  type: z.nativeEnum(PostType),
  status: z.nativeEnum(PostStatus).default(PostStatus.DRAFT),
  categoryId: z.string().optional(),
  tags: z.array(z.string()).default([]),
  isTop: z.boolean().default(false),
  isRecommended: z.boolean().default(false),
  isOriginal: z.boolean().default(true),
  authorId: z.string().min(1, "作者ID不能为空"),
});

// GET /api/content/posts - 获取帖子列表
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    const searchParams = request.nextUrl.searchParams;
    const query = querySchema.parse(Object.fromEntries(searchParams));

    // 构建查询条件
    const where: any = {};

    if (query.search) {
      where.OR = [
        { title: { contains: query.search, mode: "insensitive" } },
        { content: { contains: query.search, mode: "insensitive" } },
        { summary: { contains: query.search, mode: "insensitive" } },
      ];
    }

    if (query.type) where.type = query.type;
    if (query.status) where.status = query.status;
    if (query.authorId) where.authorId = query.authorId;
    if (query.categoryId) where.categoryId = query.categoryId;
    if (query.isTop !== undefined) where.isTop = query.isTop;
    if (query.isRecommended !== undefined) where.isRecommended = query.isRecommended;

    // 计算分页
    const skip = (query.page - 1) * query.limit;

    // 查询帖子
    const [posts, total] = await Promise.all([
      prisma.post.findMany({
        where,
        skip,
        take: query.limit,
        orderBy: { [query.sortBy]: query.sortOrder },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              username: true,
              avatar: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          tags: {
            include: {
              tag: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                },
              },
            },
          },
          _count: {
            select: {
              comments: true,
              likes: true,
              favorites: true,
            },
          },
        },
      }),
      prisma.post.count({ where }),
    ]);

    // 格式化数据
    const formattedPosts = posts.map(post => ({
      ...post,
      tags: post.tags.map(pt => pt.tag),
    }));

    const totalPages = Math.ceil(total / query.limit);

    return NextResponse.json({
      success: true,
      data: {
        data: formattedPosts,
        total,
        page: query.page,
        limit: query.limit,
        totalPages,
      },
    });
  } catch (error) {
    console.error("获取帖子列表失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "获取帖子列表失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 }
    );
  }
}

// POST /api/content/posts - 创建帖子
export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    const body = await request.json();
    const data = createPostSchema.parse(body);

    // 处理标签
    const tagConnections = [];
    for (const tagName of data.tags) {
      // 创建或获取标签
      const tag = await prisma.tag.upsert({
        where: { name: tagName },
        update: { usageCount: { increment: 1 } },
        create: {
          name: tagName,
          slug: tagName.toLowerCase().replace(/\s+/g, "-"),
          usageCount: 1,
        },
      });
      tagConnections.push({ tagId: tag.id });
    }

    // 获取作者信息
    const author = await prisma.user.findUnique({
      where: { id: data.authorId },
      select: { name: true, avatar: true },
    });

    // 创建帖子
    const post = await prisma.post.create({
      data: {
        title: data.title,
        content: data.content,
        summary: data.summary,
        cover: data.cover,
        type: data.type,
        status: data.status,
        categoryId: data.categoryId,
        isTop: data.isTop,
        isRecommended: data.isRecommended,
        isOriginal: data.isOriginal,
        authorId: data.authorId,
        authorName: author?.name,
        authorAvatar: author?.avatar,
        publishedAt: data.status === PostStatus.PUBLISHED ? new Date() : null,
        tags: {
          create: tagConnections,
        },
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            username: true,
            avatar: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        tags: {
          include: {
            tag: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
        },
      },
    });

    // 格式化返回数据
    const formattedPost = {
      ...post,
      tags: post.tags.map(pt => pt.tag),
    };

    return NextResponse.json({
      success: true,
      data: formattedPost,
      message: "帖子创建成功",
    });
  } catch (error) {
    console.error("创建帖子失败:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "数据验证失败",
          errors: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "创建帖子失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 }
    );
  }
}
