import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { AdminAuthService, prisma } from "@workspace/database";
import { PostType, PostStatus } from "@workspace/database";
import { AUTH_COOKIE_NAME } from "@/lib/auth/types";
import { cookies } from "next/headers";

const adminAuthService = new AdminAuthService(prisma);

// 更新帖子验证 schema
const updatePostSchema = z.object({
  title: z.string().min(1, "标题不能为空").max(200, "标题不能超过200个字符").optional(),
  content: z.string().min(1, "内容不能为空").optional(),
  summary: z.string().max(500, "摘要不能超过500个字符").optional(),
  cover: z.string().url("封面必须是有效的URL").optional(),
  type: z.nativeEnum(PostType).optional(),
  status: z.nativeEnum(PostStatus).optional(),
  categoryId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  isTop: z.boolean().optional(),
  isRecommended: z.boolean().optional(),
  isOriginal: z.boolean().optional(),
});

// GET /api/content/posts/[id] - 获取单个帖子详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    const { id } = params;

    const post = await prisma.post.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            username: true,
            avatar: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        tags: {
          include: {
            tag: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
        },
        _count: {
          select: {
            comments: true,
            likes: true,
            favorites: true,
          },
        },
      },
    });

    if (!post) {
      return NextResponse.json(
        { success: false, message: "帖子不存在" },
        { status: 404 }
      );
    }

    // 格式化数据
    const formattedPost = {
      ...post,
      tags: post.tags.map(pt => pt.tag),
    };

    return NextResponse.json({
      success: true,
      data: formattedPost,
    });
  } catch (error) {
    console.error("获取帖子详情失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "获取帖子详情失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 }
    );
  }
}

// PUT /api/content/posts/[id] - 更新帖子
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    const { id } = params;
    const body = await request.json();
    const data = updatePostSchema.parse(body);

    // 检查帖子是否存在
    const existingPost = await prisma.post.findUnique({
      where: { id },
      include: { tags: true },
    });

    if (!existingPost) {
      return NextResponse.json(
        { success: false, message: "帖子不存在" },
        { status: 404 }
      );
    }

    // 处理标签更新
    let tagOperations = {};
    if (data.tags) {
      // 删除现有标签关联
      await prisma.postTag.deleteMany({
        where: { postId: id },
      });

      // 创建新的标签关联
      const tagConnections = [];
      for (const tagName of data.tags) {
        const tag = await prisma.tag.upsert({
          where: { name: tagName },
          update: { usageCount: { increment: 1 } },
          create: {
            name: tagName,
            slug: tagName.toLowerCase().replace(/\s+/g, "-"),
            usageCount: 1,
          },
        });
        tagConnections.push({ tagId: tag.id });
      }

      tagOperations = {
        tags: {
          create: tagConnections,
        },
      };
    }

    // 更新发布时间
    let publishedAt = existingPost.publishedAt;
    if (data.status === PostStatus.PUBLISHED && !existingPost.publishedAt) {
      publishedAt = new Date();
    } else if (data.status && data.status !== PostStatus.PUBLISHED) {
      publishedAt = null;
    }

    // 更新帖子
    const updatedPost = await prisma.post.update({
      where: { id },
      data: {
        ...data,
        publishedAt,
        ...tagOperations,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            username: true,
            avatar: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        tags: {
          include: {
            tag: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
        },
      },
    });

    // 格式化返回数据
    const formattedPost = {
      ...updatedPost,
      tags: updatedPost.tags.map(pt => pt.tag),
    };

    return NextResponse.json({
      success: true,
      data: formattedPost,
      message: "帖子更新成功",
    });
  } catch (error) {
    console.error("更新帖子失败:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "数据验证失败",
          errors: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "更新帖子失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 }
    );
  }
}

// DELETE /api/content/posts/[id] - 删除帖子
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    const { id } = params;

    // 检查帖子是否存在
    const existingPost = await prisma.post.findUnique({
      where: { id },
    });

    if (!existingPost) {
      return NextResponse.json(
        { success: false, message: "帖子不存在" },
        { status: 404 }
      );
    }

    // 软删除：更新状态为 DELETED
    await prisma.post.update({
      where: { id },
      data: {
        status: PostStatus.DELETED,
      },
    });

    return NextResponse.json({
      success: true,
      message: "帖子删除成功",
    });
  } catch (error) {
    console.error("删除帖子失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "删除帖子失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 }
    );
  }
}
