"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Badge } from "@workspace/ui/components/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialog<PERSON><PERSON>le,
} from "@workspace/ui/components/alert-dialog";
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Star,
  Pin,
  RefreshCw,
  FileText,
  MessageSquare,
  Heart,
  Bookmark,
} from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { usePermissions } from "@/hooks/usePermissions";
import { PERMISSIONS } from "@/config/permissions.config";
import { usePosts, usePostActions, PostType, PostStatus, type Post } from "@/hooks/usePosts";

// 帖子类型选项
const POST_TYPE_OPTIONS = [
  { value: "ARTICLE", label: "文章", color: "blue" },
  { value: "SHARE", label: "分享", color: "green" },
  { value: "QUESTION", label: "问题", color: "orange" },
  { value: "ANNOUNCEMENT", label: "公告", color: "red" },
  { value: "EXPERIENCE", label: "经验分享", color: "purple" },
];

// 帖子状态选项
const POST_STATUS_OPTIONS = [
  { value: "DRAFT", label: "草稿", color: "gray" },
  { value: "PENDING_REVIEW", label: "待审核", color: "yellow" },
  { value: "PUBLISHED", label: "已发布", color: "green" },
  { value: "HIDDEN", label: "隐藏", color: "orange" },
  { value: "DELETED", label: "已删除", color: "red" },
];

export default function PostsPage() {
  const router = useRouter();
  const { hasPermission } = usePermissions();
  const { deletePost, batchUpdatePosts, loading: actionLoading } = usePostActions();

  // 权限检查
  const canView = hasPermission(PERMISSIONS.CONTENT.POST.VIEW);
  const canCreate = hasPermission(PERMISSIONS.CONTENT.POST.CREATE);
  const canEdit = hasPermission(PERMISSIONS.CONTENT.POST.EDIT);
  const canDelete = hasPermission(PERMISSIONS.CONTENT.POST.DELETE);
  const canPublish = hasPermission(PERMISSIONS.CONTENT.POST.PUBLISH);
  const canReview = hasPermission(PERMISSIONS.CONTENT.POST.REVIEW);

  // 查询状态
  const [query, setQuery] = useState({
    page: 1,
    limit: 10,
    search: "",
    type: "" as PostType | "",
    status: "" as PostStatus | "",
    sortBy: "createdAt",
    sortOrder: "desc" as "asc" | "desc",
  });

  const { posts, loading, total, totalPages, refetch } = usePosts(query);

  // 删除确认对话框
  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean;
    post: Post | null;
  }>({
    open: false,
    post: null,
  });

  // 权限检查
  if (!canView) {
    router.push("/403");
    return null;
  }

  // 处理搜索
  const handleSearch = (value: string) => {
    setQuery(prev => ({ ...prev, search: value, page: 1 }));
  };

  // 处理筛选
  const handleFilter = (key: string, value: string) => {
    setQuery(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    setQuery(prev => ({ ...prev, page }));
  };

  // 处理删除
  const handleDelete = async (post: Post) => {
    const result = await deletePost(post.id);
    if (result.success) {
      toast.success("帖子删除成功");
      refetch();
      setDeleteDialog({ open: false, post: null });
    } else {
      toast.error(result.message || "删除失败");
    }
  };

  // 处理状态更新
  const handleStatusUpdate = async (post: Post, status: PostStatus) => {
    const result = await batchUpdatePosts([post.id], { status });
    if (result.success) {
      toast.success("状态更新成功");
      refetch();
    } else {
      toast.error(result.message || "更新失败");
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: PostStatus) => {
    const option = POST_STATUS_OPTIONS.find(opt => opt.value === status);
    return option?.color || "gray";
  };

  // 获取类型颜色
  const getTypeColor = (type: PostType) => {
    const option = POST_TYPE_OPTIONS.find(opt => opt.value === type);
    return option?.color || "blue";
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="space-y-1">
          <h1 className="text-2xl font-semibold tracking-tight">帖子管理</h1>
          <p className="text-sm text-muted-foreground">
            管理社区帖子，包括文章、分享、问题等内容
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={refetch}
            disabled={loading}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} />
            刷新
          </Button>
          {canCreate && (
            <Link href="/content/posts/create">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                新建帖子
              </Button>
            </Link>
          )}
        </div>
      </div>

      {/* 筛选和搜索 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="mr-2 h-5 w-5" />
            筛选条件
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">搜索</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="搜索标题、内容..."
                  value={query.search}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-9"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">帖子类型</label>
              <Select
                value={query.type}
                onValueChange={(value) => handleFilter("type", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="全部类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  {POST_TYPE_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">状态</label>
              <Select
                value={query.status}
                onValueChange={(value) => handleFilter("status", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="全部状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  {POST_STATUS_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">排序</label>
              <Select
                value={`${query.sortBy}-${query.sortOrder}`}
                onValueChange={(value) => {
                  const [sortBy, sortOrder] = value.split("-");
                  setQuery(prev => ({ ...prev, sortBy, sortOrder: sortOrder as "asc" | "desc" }));
                }}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="createdAt-desc">创建时间（新到旧）</SelectItem>
                  <SelectItem value="createdAt-asc">创建时间（旧到新）</SelectItem>
                  <SelectItem value="updatedAt-desc">更新时间（新到旧）</SelectItem>
                  <SelectItem value="viewCount-desc">浏览量（高到低）</SelectItem>
                  <SelectItem value="title-asc">标题（A-Z）</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 帖子列表 */}
      <Card>
        <CardHeader>
          <CardTitle>帖子列表</CardTitle>
          <CardDescription>
            共 {total} 个帖子，第 {query.page} 页，共 {totalPages} 页
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>标题</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>作者</TableHead>
                <TableHead>统计</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="flex items-center justify-center">
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      加载中...
                    </div>
                  </TableCell>
                </TableRow>
              ) : posts.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="text-muted-foreground">
                      <FileText className="mx-auto h-12 w-12 mb-4 opacity-50" />
                      暂无帖子数据
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                posts.map((post) => (
                  <TableRow key={post.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{post.title}</span>
                          {post.isTop && (
                            <Pin className="h-3 w-3 text-red-500" />
                          )}
                          {post.isRecommended && (
                            <Star className="h-3 w-3 text-yellow-500" />
                          )}
                        </div>
                        {post.summary && (
                          <p className="text-sm text-muted-foreground line-clamp-1">
                            {post.summary}
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className={`text-${getTypeColor(post.type)}-600`}>
                        {POST_TYPE_OPTIONS.find(opt => opt.value === post.type)?.label}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className={`text-${getStatusColor(post.status)}-600`}>
                        {POST_STATUS_OPTIONS.find(opt => opt.value === post.status)?.label}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {post.author?.avatar && (
                          <img
                            src={post.author.avatar}
                            alt={post.author.name}
                            className="h-6 w-6 rounded-full"
                          />
                        )}
                        <span className="text-sm">{post.author?.name || post.authorName}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <div className="flex items-center">
                          <Eye className="mr-1 h-3 w-3" />
                          {post.viewCount}
                        </div>
                        <div className="flex items-center">
                          <MessageSquare className="mr-1 h-3 w-3" />
                          {post._count?.comments || 0}
                        </div>
                        <div className="flex items-center">
                          <Heart className="mr-1 h-3 w-3" />
                          {post._count?.likes || 0}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-muted-foreground">
                        {new Date(post.createdAt).toLocaleDateString()}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>操作</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem asChild>
                            <Link href={`/content/posts/${post.id}`}>
                              <Eye className="mr-2 h-4 w-4" />
                              查看详情
                            </Link>
                          </DropdownMenuItem>
                          {canEdit && (
                            <DropdownMenuItem asChild>
                              <Link href={`/content/posts/${post.id}/edit`}>
                                <Edit className="mr-2 h-4 w-4" />
                                编辑
                              </Link>
                            </DropdownMenuItem>
                          )}
                          {canPublish && post.status === PostStatus.PENDING_REVIEW && (
                            <DropdownMenuItem
                              onClick={() => handleStatusUpdate(post, PostStatus.PUBLISHED)}
                            >
                              <Star className="mr-2 h-4 w-4" />
                              发布
                            </DropdownMenuItem>
                          )}
                          {canDelete && (
                            <DropdownMenuItem
                              onClick={() => setDeleteDialog({ open: true, post })}
                              className="text-destructive"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              删除
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                显示第 {(query.page - 1) * query.limit + 1} 到{" "}
                {Math.min(query.page * query.limit, total)} 条，共 {total} 条
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(query.page - 1)}
                  disabled={query.page <= 1}
                >
                  上一页
                </Button>
                <span className="text-sm">
                  第 {query.page} 页，共 {totalPages} 页
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(query.page + 1)}
                  disabled={query.page >= totalPages}
                >
                  下一页
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 删除确认对话框 */}
      <AlertDialog
        open={deleteDialog.open}
        onOpenChange={(open) => setDeleteDialog({ open, post: null })}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除帖子「{deleteDialog.post?.title}」吗？此操作不可撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteDialog.post && handleDelete(deleteDialog.post)}
              disabled={actionLoading}
            >
              {actionLoading ? "删除中..." : "确认删除"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
