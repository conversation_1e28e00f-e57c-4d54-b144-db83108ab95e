import { NextRequest, NextResponse } from "next/server";
import { prisma, EnrollmentsService } from "@workspace/database";

const enrollmentsService = new EnrollmentsService(prisma);

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "20");
    const isPaid = searchParams.get("isPaid");
    const isCompleted = searchParams.get("isCompleted");
    const search = searchParams.get("search");
    const dateFrom = searchParams.get("dateFrom");
    const dateTo = searchParams.get("dateTo");

    // 构建过滤条件
    const filters: any = {};
    
    if (isPaid !== null) {
      filters.isPaid = isPaid === "true";
    }
    
    if (isCompleted !== null) {
      filters.isCompleted = isCompleted === "true";
    }
    
    if (search) {
      filters.search = search;
    }
    
    if (dateFrom && dateTo) {
      filters.dateRange = {
        from: new Date(dateFrom),
        to: new Date(dateTo),
      };
    }

    const data = await enrollmentsService.getCourseEnrollmentData(
      params.id,
      filters,
      page,
      pageSize
    );

    return NextResponse.json(data);
  } catch (error) {
    console.error("获取课程报名数据失败:", error);
    return NextResponse.json(
      { error: "获取课程报名数据失败" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { enrollmentId, updates } = body;

    const updatedEnrollment = await enrollmentsService.updateEnrollmentStatus(
      enrollmentId,
      updates
    );

    return NextResponse.json(updatedEnrollment);
  } catch (error) {
    console.error("更新报名状态失败:", error);
    return NextResponse.json(
      { error: "更新报名状态失败" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const enrollmentId = searchParams.get("enrollmentId");

    if (!enrollmentId) {
      return NextResponse.json(
        { error: "缺少报名ID" },
        { status: 400 }
      );
    }

    await enrollmentsService.deleteEnrollment(enrollmentId);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("删除报名记录失败:", error);
    return NextResponse.json(
      { error: "删除报名记录失败" },
      { status: 500 }
    );
  }
}
