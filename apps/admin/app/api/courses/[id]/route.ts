import { NextRequest, NextResponse } from "next/server";
import { 
  CoursesService, 
  AdminAuthService, 
  CourseContentService,
  ChaptersService,
  LessonsService 
} from "@workspace/database/services";
import { PrismaClient, CourseStatus } from "@workspace/database";
import { AUTH_COOKIE_NAME } from "@/lib/auth/types";
import { cookies } from "next/headers";

const prisma = new PrismaClient();
const coursesService = new CoursesService(prisma);
const chaptersService = new ChaptersService(prisma);
const lessonsService = new LessonsService(prisma);
const courseContentService = new CourseContentService(
  prisma,
  coursesService,
  chaptersService,
  lessonsService
);
const adminAuthService = new AdminAuthService(prisma);

// GET /api/courses/[id] - 获取单个课程详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    
    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 }
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 }
      );
    }

    // 获取查询参数决定返回详细程度
    const { searchParams } = new URL(request.url);
    const includeContent = searchParams.get("includeContent") === "true";
    
    if (includeContent) {
      // 获取包含完整内容的课程
      const course = await courseContentService.getCourseWithFullContent(id);
      
      if (!course) {
        return NextResponse.json(
          { success: false, message: "课程不存在" }, 
          { status: 404 }
        );
      }

      // 转换为前端期望的格式
      const transformedCourse = {
        id: course.id,
        title: course.title,
        subtitle: course.subtitle,
        description: course.description,
        cover: course.cover,
        categoryId: course.categoryId,
        categoryName: course.category?.name || "未分类",
        level: course.level,
        duration: course.duration,
        lessonsCount: course.stats.totalLessons,
        price: course.price.toNumber(),
        originalPrice: course.originalPrice?.toNumber(),
        isFree: course.isFree,
        status: course.status,
        viewCount: course.viewCount,
        enrollCount: course.enrollCount,
        rating: course.rating,
        instructorName: course.instructorName,
        instructorTitle: course.instructorTitle,
        instructorAvatar: course.instructorAvatar,
        instructorBio: course.instructorBio,
        previewVideo: course.previewVideo,
        tags: course.tags,
        metaTitle: course.metaTitle,
        metaDescription: course.metaDescription,
        metaKeywords: course.metaKeywords,
        requireLogin: course.requireLogin,
        publishedAt: course.publishedAt,
        createdAt: course.createdAt,
        updatedAt: course.updatedAt,
        chapters: course.chapters.map(chapter => ({
          id: chapter.id,
          title: chapter.title,
          description: chapter.description,
          order: chapter.order,
          lessons: chapter.lessons.map(lesson => ({
            id: lesson.id,
            title: lesson.title,
            description: lesson.description,
            videoUrl: lesson.videoUrl,
            videoDuration: lesson.videoDuration,
            videoSize: lesson.videoSize,
            order: lesson.order,
            isFree: lesson.isFree,
            createdAt: lesson.createdAt,
            updatedAt: lesson.updatedAt,
          })),
          createdAt: chapter.createdAt,
          updatedAt: chapter.updatedAt,
        })),
        stats: course.stats,
      };

      return NextResponse.json({
        success: true,
        data: transformedCourse
      });
    } else {
      // 获取基本课程信息
      const course = await coursesService.getCourseWithBasicInfo(id);

      if (!course) {
        return NextResponse.json(
          { success: false, message: "课程不存在" }, 
          { status: 404 }
        );
      }

      // 转换为前端期望的格式
      const transformedCourse = {
        id: course.id,
        title: course.title,
        subtitle: course.subtitle,
        description: course.description,
        cover: course.cover,
        categoryId: course.categoryId,
        categoryName: course.category?.name || "未分类",
        level: course.level,
        duration: course.duration,
        lessonsCount: course._count?.chapters || 0,
        price: course.price.toNumber(),
        originalPrice: course.originalPrice?.toNumber(),
        isFree: course.isFree,
        status: course.status,
        viewCount: course.viewCount,
        enrollCount: course.enrollCount,
        rating: course.rating,
        instructorName: course.instructorName,
        instructorTitle: course.instructorTitle,
        instructorAvatar: course.instructorAvatar,
        instructorBio: course.instructorBio,
        previewVideo: course.previewVideo,
        tags: course.tags,
        metaTitle: course.metaTitle,
        metaDescription: course.metaDescription,
        metaKeywords: course.metaKeywords,
        requireLogin: course.requireLogin,
        publishedAt: course.publishedAt,
        createdAt: course.createdAt,
        updatedAt: course.updatedAt,
      };

      return NextResponse.json({
        success: true,
        data: transformedCourse
      });
    }
  } catch (error) {
    console.error("获取课程详情失败:", error);
    return NextResponse.json(
      { success: false, message: "获取课程详情失败" },
      { status: 500 }
    );
  }
}

// PUT /api/courses/[id] - 更新课程
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const body = await request.json();
    
    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 }
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 }
      );
    }

    // 处理更新数据
    const updateData = {
      title: body.title,
      subtitle: body.subtitle || null,
      description: body.description,
      cover: body.cover,
      categoryId: body.categoryId,
      level: body.level,
      price: body.price ? Number(body.price) : undefined,
      originalPrice: body.originalPrice ? Number(body.originalPrice) : null,
      isFree: body.isFree !== undefined ? Boolean(body.isFree) : undefined,
      status: body.status as CourseStatus,
      requireLogin: body.requireLogin !== undefined ? Boolean(body.requireLogin) : undefined,
      instructorName: body.instructorName,
      instructorTitle: body.instructorTitle || null,
      instructorAvatar: body.instructorAvatar || null,
      instructorBio: body.instructorBio || null,
      previewVideo: body.previewVideo || null,
      tags: body.tags || [],
      metaTitle: body.metaTitle || null,
      metaDescription: body.metaDescription || null,
      metaKeywords: body.metaKeywords || [],
    };

    // 过滤掉 undefined 值
    const filteredUpdateData = Object.fromEntries(
      Object.entries(updateData).filter(([_, value]) => value !== undefined)
    );

    // 检查是否包含章节更新
    if (body.chapters) {
      // 使用 CourseContentService 更新完整内容
      const result = await courseContentService.updateCourseWithContent(id, {
        courseData: filteredUpdateData,
        chapters: body.chapters,
      });

      return NextResponse.json({
        success: true,
        data: result,
        message: "课程更新成功"
      });
    } else {
      // 只更新基本课程信息
      const course = await coursesService.updateCourse(id, filteredUpdateData);

      return NextResponse.json({
        success: true,
        data: course,
        message: "课程更新成功"
      });
    }
  } catch (error) {
    console.error("更新课程失败:", error);
    return NextResponse.json(
      { success: false, message: error instanceof Error ? error.message : "更新课程失败" },
      { status: 500 }
    );
  }
}

// DELETE /api/courses/[id] - 删除课程
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    
    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 }
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 }
      );
    }

    await coursesService.deleteCourse(id);

    return NextResponse.json({
      success: true,
      message: "课程删除成功"
    });
  } catch (error) {
    console.error("删除课程失败:", error);
    return NextResponse.json(
      { success: false, message: error instanceof Error ? error.message : "删除课程失败" },
      { status: 500 }
    );
  }
}
