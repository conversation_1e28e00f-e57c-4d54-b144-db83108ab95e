import { AUTH_COOKIE_NAME } from "@/lib/auth/types";
import { AdminAuthService, CategoryService } from "@workspace/database/services";
import { PrismaClient } from "@workspace/database";
import { cookies } from "next/headers";

const prisma = new PrismaClient();
const adminAuthService = new AdminAuthService(prisma);
const categoryService = new CategoryService(prisma);

export async function GET(
  _request: Request,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;

    // 验证ID格式
    if (!id || typeof id !== 'string') {
      return Response.json(
        { success: false, message: "无效的分类ID" },
        { status: 400 },
      );
    }

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return Response.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return Response.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    const result = await categoryService.getCategoryById(id);
    
    // 根据服务层返回结果设置HTTP状态码
    if (!result.success) {
      if (result.message === "课程分类不存在") {
        return Response.json(result, { status: 404 });
      }
      return Response.json(result, { status: 400 });
    }
    
    return Response.json(result);
  } catch (error) {
    console.error("获取分类详情失败:", error);
    return Response.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 },
    );
  }
}
