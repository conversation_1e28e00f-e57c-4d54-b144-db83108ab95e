import { AUTH_COOKIE_NAME } from "@/lib/auth/types";
import { AdminAuthService, CategoryService } from "@workspace/database/services";
import { PrismaClient } from "@workspace/database";
import { cookies } from "next/headers";

const prisma = new PrismaClient();
const adminAuthService = new AdminAuthService(prisma);
const categoryService = new CategoryService(prisma);

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return Response.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return Response.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 获取分类列表
    const parentId = searchParams.get("parentId");
    const isActive = searchParams.get("isActive");
    const includeChildren = searchParams.get("includeChildren") === "true";
    const includeParent = searchParams.get("includeParent") === "true";
    const search = searchParams.get("search");
    const page = searchParams.get("page") ? parseInt(searchParams.get("page")!) : undefined;
    const limit = searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : undefined;

    const result = await categoryService.getCategories({
      parentId: parentId === "null" ? null : parentId || undefined,
      isActive: isActive ? isActive === "true" : undefined,
      includeChildren,
      includeParent,
      search: search || undefined,
      page,
      limit,
    });

    return Response.json(result);
  } catch (error) {
    console.error("GET请求处理失败:", error);
    return Response.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 },
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return Response.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return Response.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 创建课程分类
    const result = await categoryService.createCategory(body);

    if (result.success) {
      return Response.json(result, { status: 201 });
    } else {
      return Response.json(result, { status: 400 });
    }
  } catch (error) {
    console.error("POST请求处理失败:", error);
    return Response.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 },
    );
  }
}

export async function PATCH(request: Request) {
  try {
    const body = await request.json();

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return Response.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return Response.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 更新课程分类
    const result = await categoryService.updateCategory(body);

    if (result.success) {
      return Response.json(result);
    } else {
      return Response.json(result, { status: 400 });
    }
  } catch (error) {
    console.error("PATCH请求处理失败:", error);
    return Response.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 },
    );
  }
}

export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return Response.json(
        { success: false, message: "缺少ID参数" },
        { status: 400 },
      );
    }

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return Response.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return Response.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 删除课程分类
    const result = await categoryService.deleteCategory(id);

    if (result.success) {
      return Response.json(result);
    } else {
      return Response.json(result, { status: 400 });
    }
  } catch (error) {
    console.error("DELETE请求处理失败:", error);
    return Response.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 },
    );
  }
}

