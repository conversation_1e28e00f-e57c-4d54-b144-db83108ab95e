import { AUTH_COOKIE_NAME } from "@/lib/auth/types";
import { AdminAuthService, CategoryService } from "@workspace/database/services";
import { PrismaClient } from "@workspace/database";
import { cookies } from "next/headers";

const prisma = new PrismaClient();
const adminAuthService = new AdminAuthService(prisma);
const categoryService = new CategoryService(prisma);

export async function GET(request: Request) {
  try {
    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return Response.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return Response.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 获取分类树
    const result = await categoryService.getCategoryTree();
    return Response.json(result);
  } catch (error) {
    console.error("获取分类树失败:", error);
    return Response.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 },
    );
  }
}
