import { NextRequest, NextResponse } from "next/server";
import {
  CoursesService,
  AdminAuthService,
  CourseContentService,
  ChaptersService,
  LessonsService,
} from "@workspace/database/services";
import { PrismaClient, CourseStatus, CourseLevel } from "@workspace/database";
import { AUTH_COOKIE_NAME } from "@/lib/auth/types";
import { cookies } from "next/headers";

const prisma = new PrismaClient();
const coursesService = new CoursesService(prisma);
const chaptersService = new ChaptersService(prisma);
const lessonsService = new LessonsService(prisma);
const courseContentService = new CourseContentService(
  prisma,
  coursesService,
  chaptersService,
  lessonsService,
);
const adminAuthService = new AdminAuthService(prisma);

// GET /api/courses - 获取课程列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    const query = {
      categoryId: searchParams.get("categoryId") || undefined,
      status: searchParams.get("status") as CourseStatus | undefined,
      level: searchParams.get("level") as CourseLevel | undefined,
      isFree: searchParams.get("isFree")
        ? searchParams.get("isFree") === "true"
        : undefined,
      search: searchParams.get("search") || undefined,
      page: searchParams.get("page") ? parseInt(searchParams.get("page")!) : 1,
      limit: searchParams.get("limit")
        ? parseInt(searchParams.get("limit")!)
        : 20,
      sortBy: (searchParams.get("sortBy") || "updatedAt") as
        | "createdAt"
        | "updatedAt"
        | "title"
        | "price",
      sortOrder: (searchParams.get("sortOrder") || "desc") as "asc" | "desc",
    };

    const result = await coursesService.getCourses(query);

    // 转换为前端期望的格式
    const transformedData = result.data.map((course) => ({
      id: course.id,
      title: course.title,
      subtitle: course.subtitle,
      description: course.description,
      cover: course.cover,
      categoryId: course.categoryId,
      categoryName: course.category?.name || "未分类",
      level: course.level,
      duration: course.duration,
      lessonsCount: course._count?.chapters || 0, // 暂用章节数，后续可优化
      price: course.price.toNumber(),
      originalPrice: course.originalPrice?.toNumber(),
      isFree: course.isFree,
      status: course.status,
      viewCount: course.viewCount,
      enrollCount: course.enrollCount,
      rating: course.rating,
      instructorName: course.instructorName,
      instructorTitle: course.instructorTitle,
      instructorAvatar: course.instructorAvatar,
      instructorBio: course.instructorBio,
      tags: course.tags,
      publishedAt: course.publishedAt,
      createdAt: course.createdAt,
      updatedAt: course.updatedAt,
    }));

    return NextResponse.json({
      success: true,
      data: transformedData,
      total: result.total,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
    });
  } catch (error) {
    console.error("获取课程列表失败:", error);
    return NextResponse.json(
      { success: false, message: "获取课程列表失败" },
      { status: 500 },
    );
  }
}

// POST /api/courses - 创建课程
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 处理创建课程数据
    const courseData = {
      title: body.title,
      subtitle: body.subtitle || null,
      description: body.description,
      cover: body.cover,
      categoryId: body.categoryId,
      level: body.level,
      price: Number(body.price) || 0,
      originalPrice: body.originalPrice ? Number(body.originalPrice) : null,
      isFree: Boolean(body.isFree),
      status: (body.status as CourseStatus) || null,
      requireLogin: Boolean(body.requireLogin),
      instructorName: body.instructorName,
      instructorTitle: body.instructorTitle || null,
      instructorAvatar: body.instructorAvatar || null,
      instructorBio: body.instructorBio || null,
      previewVideo: body.previewVideo || null,
      tags: body.tags || [],
      metaTitle: body.metaTitle || null,
      metaDescription: body.metaDescription || null,
      metaKeywords: body.metaKeywords || [],
    };

    // 如果有章节数据，使用 CourseContentService 创建完整课程
    if (body.chapters && body.chapters.length > 0) {
      const result = await courseContentService.createCourseWithContent({
        ...courseData,
        createdById: user.id,
        chapters: body.chapters,
      });

      return NextResponse.json(
        {
          success: true,
          data: result,
          message: "课程创建成功",
        },
        { status: 201 },
      );
    } else {
      // 只创建基本课程信息
      const course = await coursesService.createCourse({
        ...courseData,
        createdById: user.id,
      });

      return NextResponse.json(
        {
          success: true,
          data: course,
          message: "课程创建成功",
        },
        { status: 201 },
      );
    }
  } catch (error) {
    console.error("创建课程失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "创建课程失败",
      },
      { status: 500 },
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 检查是否有课程ID
    if (!body.id) {
      return NextResponse.json(
        { success: false, message: "课程ID不能为空" },
        { status: 400 },
      );
    }

    const courseId = body.id;

    // 处理更新课程数据
    const courseData = {
      title: body.title,
      subtitle: body.subtitle || null,
      description: body.description,
      cover: body.cover,
      categoryId: body.categoryId,
      level: body.level,
      price: Number(body.price) || 0,
      originalPrice: body.originalPrice ? Number(body.originalPrice) : null,
      isFree: Boolean(body.isFree),
      status: (body.status as CourseStatus) || undefined,
      requireLogin: Boolean(body.requireLogin),
      instructorName: body.instructorName,
      instructorTitle: body.instructorTitle || null,
      instructorAvatar: body.instructorAvatar || null,
      instructorBio: body.instructorBio || null,
      previewVideo: body.previewVideo || null,
      tags: body.tags || [],
      metaTitle: body.metaTitle || null,
      metaDescription: body.metaDescription || null,
      metaKeywords: body.metaKeywords || [],
    };

    let result;

    // 如果有章节数据，使用 CourseContentService 更新完整课程
    if (body.chapters && body.chapters.length > 0) {
      result = await courseContentService.updateCourseWithContent(courseId, {
        courseData,
        chapters: body.chapters,
      });
    } else {
      // 只更新基本课程信息
      result = await coursesService.updateCourse(courseId, courseData);
    }

    return NextResponse.json({
      success: true,
      data: result,
      message: "课程更新成功",
    });
  } catch (error) {
    console.error("更新课程失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "更新课程失败",
      },
      { status: 500 },
    );
  }
}
