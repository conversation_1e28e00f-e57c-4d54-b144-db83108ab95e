"use server";
import { NextRequest, NextResponse } from "next/server";
import { prisma, AdminAuthService } from "@workspace/database";
import { AUTH_COOKIE_NAME } from "@/lib/auth/types";

// 创建认证服务实例
const adminAuthService = new AdminAuthService(prisma);

export async function POST(request: NextRequest) {
  try {
    // 从Cookie中获取token
    const token = request.cookies.get(AUTH_COOKIE_NAME)?.value;

    if (token) {
      // 使用logout方法处理登出逻辑（已包含Redis缓存删除）
      await adminAuthService.logout(token);
    }

    // 清除Cookie并返回成功响应
    const response = NextResponse.json({
      code: 1,
      message: "退出成功",
    });

    response.cookies.delete(AUTH_COOKIE_NAME);

    return response;
  } catch (error) {
    console.error("登出错误:", error);
    return NextResponse.json(
      {
        code: 0,
        message: "退出失败",
      },
      { status: 500 },
    );
  }
}
