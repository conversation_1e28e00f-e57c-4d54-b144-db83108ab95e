"use server";
import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@workspace/database";
import { AdminAuthService } from "@workspace/database";
import { AUTH_COOKIE_NAME } from "@/lib/auth/types";

// 创建认证服务实例
const adminAuthService = new AdminAuthService(prisma);

export async function GET(request: NextRequest) {
  try {
    // 1. 从Cookie中获取token
    const token = request.cookies.get(AUTH_COOKIE_NAME)?.value;

    // 2. 调用Service层验证会话
    const result = await adminAuthService.validateSession(token);

    if (!result.success) {
      return NextResponse.json(
        {
          code: 0,
          message: result.message,
        },
        { status: 401 },
      );
    }

    // 3. 返回用户信息
    return NextResponse.json({
      code: 1,
      message: "获取用户信息成功",
      data: {
        user: result.user,
      },
    });
  } catch (error) {
    console.error("获取用户信息错误:", error);
    return NextResponse.json(
      {
        code: 0,
        message: "系统错误",
      },
      { status: 500 },
    );
  }
}
