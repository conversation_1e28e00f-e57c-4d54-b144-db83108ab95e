"use server";
import { loginSchema } from "@/lib/auth/schemas";
import { prisma, AdminAuthService } from "@workspace/database";
import { NextRequest, NextResponse } from "next/server";
import { AUTH_COOKIE_NAME } from "@/lib/auth/types";

// 创建认证服务实例
const adminAuthService = new AdminAuthService(prisma);

export async function POST(request: NextRequest) {
  try {
    // 1. 解析请求数据
    const body = await request.json();

    // 2. Zod Schema 验证
    const validationResult = loginSchema.safeParse(body);
    if (!validationResult.success) {
      const errors: Record<string, string> = {};
      validationResult.error.errors.forEach((error) => {
        const field = error.path[0] as string;
        errors[field] = error.message;
      });

      return NextResponse.json(
        {
          code: 0,
          message: "表单数据验证失败",
          errors,
        },
        { status: 400 },
      );
    }

    // 3. 获取请求信息
    const forwardedFor = request.headers.get("x-forwarded-for");
    const realIp = request.headers.get("x-real-ip");
    const ip = forwardedFor || realIp || "unknown";
    const userAgent = request.headers.get("user-agent") || undefined;

    // 4. 调用Service层处理业务逻辑
    const result = await adminAuthService.login(
      validationResult.data,
      ip,
      userAgent,
    );

    if (!result.success) {
      return NextResponse.json(
        {
          code: 0,
          message: result.message,
          errors: result.errors,
        },
        { status: 401 },
      );
    }

    // 5. 设置Cookie并返回响应
    const response = NextResponse.json({
      code: 1,
      message: result.message,
      data: result.data,
    });

    response.cookies.set(AUTH_COOKIE_NAME, result.data!.token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      expires: validationResult.data.remember
        ? new Date(result.data!.expiresAt)
        : undefined,
      path: "/",
    });

    return response;
  } catch (error) {
    console.error("登录API错误:", error);
    return NextResponse.json(
      {
        code: 0,
        message: "系统错误，请稍后重试",
      },
      { status: 500 },
    );
  }
}
