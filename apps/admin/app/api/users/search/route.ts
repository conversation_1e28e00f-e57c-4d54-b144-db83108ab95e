import { NextRequest, NextResponse } from "next/server";
import { AdminAuthService } from "@workspace/database/services";
import { prisma } from "@workspace/database";
import { AUTH_COOKIE_NAME } from "@/lib/auth/types";
import { cookies } from "next/headers";
import { UserRole } from "@workspace/database";

const adminAuthService = new AdminAuthService(prisma);

// GET /api/users/search - 搜索用户
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 获取查询参数
    const search = searchParams.get("search");
    const role = searchParams.get("role") as UserRole;
    const isActive = searchParams.get("isActive");
    const emailVerified = searchParams.get("emailVerified");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");

    // 构建查询条件
    const where: any = {};
    
    // 搜索条件
    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { username: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
      ];
    }

    // 状态过滤
    if (isActive !== null && isActive !== undefined) {
      where.isActive = isActive === "true";
    }

    if (emailVerified !== null && emailVerified !== undefined) {
      where.emailVerified = emailVerified === "true";
    }

    // 分页计算
    const skip = (page - 1) * limit;

    // 执行查询
    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
        select: {
          id: true,
          email: true,
          username: true,
          name: true,
          avatar: true,
          phone: true,
          bio: true,
          isActive: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
          tenantUsers: {
            select: {
              id: true,
              role: true,
              isActive: true,
              joinedAt: true,
              tenant: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
            where: {
              isActive: true,
            },
          },
        },
      }),
      prisma.user.count({ where }),
    ]);

    // 如果有角色过滤，需要进一步过滤结果
    let filteredUsers = users;
    if (role) {
      filteredUsers = users.filter(user => 
        user.tenantUsers.some(tu => tu.role === role)
      );
    }

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: filteredUsers,
      total: filteredUsers.length,
      page,
      limit,
      totalPages,
    });
  } catch (error) {
    console.error("搜索用户失败:", error);
    return NextResponse.json(
      { success: false, message: "搜索用户失败" },
      { status: 500 },
    );
  }
}

