import { NextRequest, NextResponse } from "next/server";
import { AdminAuthService } from "@workspace/database/services";
import { prisma } from "@workspace/database";
import { AUTH_COOKIE_NAME } from "@/lib/auth/types";
import { cookies } from "next/headers";
import { UserRole } from "@workspace/database";
import bcrypt from "bcryptjs";

const adminAuthService = new AdminAuthService(prisma);

// POST /api/users - 创建新用户
export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user: adminUser } = await adminAuthService.validateSession(
      token.value,
    );
    if (!adminUser) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 解析请求体
    const body = await request.json();
    const { name, email, username, phone, password } = body;

    // 验证必填字段
    if (!name || !email || !username || !password) {
      return NextResponse.json(
        { success: false, message: "姓名、邮箱、用户名和密码为必填字段" },
        { status: 400 },
      );
    }

    // 检查邮箱是否已存在
    const existingEmailUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingEmailUser) {
      return NextResponse.json(
        { success: false, message: "邮箱已存在" },
        { status: 409 },
      );
    }

    // 检查用户名是否已存在
    const existingUsernameUser = await prisma.user.findUnique({
      where: { username },
    });

    if (existingUsernameUser) {
      return NextResponse.json(
        { success: false, message: "用户名已存在" },
        { status: 409 },
      );
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 12);

    // 创建用户
    const newUser = await prisma.user.create({
      data: {
        name,
        email,
        username,
        phone: phone || null,
        password: hashedPassword,
        isActive: true,
        emailVerified: false,
      },
      select: {
        id: true,
        email: true,
        username: true,
        name: true,
        avatar: true,
        phone: true,
        bio: true,
        isActive: true,
        emailVerified: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return NextResponse.json({
      success: true,
      data: newUser,
      message: "用户创建成功",
    });
  } catch (error) {
    console.error("创建用户失败:", error);
    return NextResponse.json(
      { success: false, message: "创建用户失败" },
      { status: 500 },
    );
  }
}
