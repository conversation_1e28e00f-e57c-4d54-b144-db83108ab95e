import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@workspace/database";
import { cookies } from "next/headers";
import { adminAuthService } from "@/lib/auth/admin-auth.service";
import { AUTH_COOKIE_NAME } from "@/lib/auth/constants";
import { ProductType } from "@workspace/database";

// GET /api/products/categories/[id] - 获取单个产品分类
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    const category = await prisma.productCategory.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    if (!category) {
      return NextResponse.json(
        { success: false, message: "分类不存在" },
        { status: 404 },
      );
    }

    const transformedData = {
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      type: category.type,
      icon: category.icon,
      order: category.order,
      isActive: category.isActive,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
      productCount: category._count.products,
    };

    return NextResponse.json({
      success: true,
      data: transformedData,
    });
  } catch (error) {
    console.error("获取产品分类详情失败:", error);
    return NextResponse.json(
      { success: false, message: "获取产品分类详情失败" },
      { status: 500 },
    );
  }
}

// PUT /api/products/categories/[id] - 更新产品分类
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 检查分类是否存在
    const existingCategory = await prisma.productCategory.findUnique({
      where: { id: params.id },
    });

    if (!existingCategory) {
      return NextResponse.json(
        { success: false, message: "分类不存在" },
        { status: 404 },
      );
    }

    // 如果更新了slug，检查是否与其他分类冲突
    if (body.slug && body.slug !== existingCategory.slug) {
      const slugExists = await prisma.productCategory.findFirst({
        where: {
          slug: body.slug,
          id: { not: params.id },
        },
      });

      if (slugExists) {
        return NextResponse.json(
          { success: false, message: "分类标识已存在" },
          { status: 400 },
        );
      }
    }

    const updateData = {
      name: body.name,
      slug: body.slug,
      description: body.description || null,
      type: body.type as ProductType,
      icon: body.icon || null,
      order: body.order ? Number(body.order) : 0,
      isActive: body.isActive !== undefined ? Boolean(body.isActive) : true,
    };

    const category = await prisma.productCategory.update({
      where: { id: params.id },
      data: updateData,
    });

    return NextResponse.json({
      success: true,
      data: category,
      message: "产品分类更新成功",
    });
  } catch (error) {
    console.error("更新产品分类失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "更新产品分类失败",
      },
      { status: 500 },
    );
  }
}

// DELETE /api/products/categories/[id] - 删除产品分类
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 检查分类是否存在
    const existingCategory = await prisma.productCategory.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    if (!existingCategory) {
      return NextResponse.json(
        { success: false, message: "分类不存在" },
        { status: 404 },
      );
    }

    // 检查是否有关联的产品
    if (existingCategory._count.products > 0) {
      return NextResponse.json(
        { 
          success: false, 
          message: `该分类下还有 ${existingCategory._count.products} 个产品，请先移动或删除相关产品` 
        },
        { status: 400 },
      );
    }

    await prisma.productCategory.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      success: true,
      message: "产品分类删除成功",
    });
  } catch (error) {
    console.error("删除产品分类失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "删除产品分类失败",
      },
      { status: 500 },
    );
  }
}
