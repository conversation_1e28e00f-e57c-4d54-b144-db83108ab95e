import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, ProductType } from "@workspace/database";
import { AUTH_COOKIE_NAME } from "@/lib/auth/types";
import { cookies } from "next/headers";
import { AdminAuthService } from "@workspace/database/services";

const prisma = new PrismaClient();
const adminAuthService = new AdminAuthService(prisma);

// GET /api/products/categories - 获取产品分类列表
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get("type") as ProductType | undefined;

    // 构建查询条件
    const where: any = {};
    if (type) {
      where.type = type;
    }

    const categories = await prisma.productCategory.findMany({
      where,
      orderBy: [
        { order: "asc" },
        { name: "asc" },
      ],
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    const transformedData = categories.map((category) => ({
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      type: category.type,
      icon: category.icon,
      order: category.order,
      isActive: category.isActive,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
      productCount: category._count.products,
    }));

    return NextResponse.json({
      success: true,
      data: transformedData,
    });
  } catch (error) {
    console.error("获取产品分类列表失败:", error);
    return NextResponse.json(
      { success: false, message: "获取产品分类列表失败" },
      { status: 500 },
    );
  }
}

// POST /api/products/categories - 创建产品分类
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 验证必需字段
    if (!body.name || !body.type) {
      return NextResponse.json(
        { success: false, message: "分类名称和类型为必填项" },
        { status: 400 },
      );
    }

    // 检查slug是否已存在
    const slug = body.slug || body.name.toLowerCase().replace(/\s+/g, "-");
    const existingCategory = await prisma.productCategory.findUnique({
      where: { slug },
    });

    if (existingCategory) {
      return NextResponse.json(
        { success: false, message: "分类标识已存在" },
        { status: 400 },
      );
    }

    const categoryData = {
      name: body.name,
      slug,
      description: body.description || null,
      type: body.type as ProductType,
      icon: body.icon || null,
      order: body.order ? Number(body.order) : 0,
      isActive: body.isActive !== undefined ? Boolean(body.isActive) : true,
    };

    const category = await prisma.productCategory.create({
      data: categoryData,
    });

    return NextResponse.json(
      {
        success: true,
        data: category,
        message: "产品分类创建成功",
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("创建产品分类失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "创建产品分类失败",
      },
      { status: 500 },
    );
  }
}

// PATCH /api/products/categories - 更新产品分类
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    if (!body.id) {
      return NextResponse.json(
        { success: false, message: "分类ID不能为空" },
        { status: 400 },
      );
    }

    // 检查分类是否存在
    const existingCategory = await prisma.productCategory.findUnique({
      where: { id: body.id },
    });

    if (!existingCategory) {
      return NextResponse.json(
        { success: false, message: "分类不存在" },
        { status: 404 },
      );
    }

    // 检查slug是否被其他分类使用
    if (body.slug && body.slug !== existingCategory.slug) {
      const duplicateCategory = await prisma.productCategory.findFirst({
        where: {
          slug: body.slug,
          id: { not: body.id },
        },
      });
      if (duplicateCategory) {
        return NextResponse.json(
          { success: false, message: "分类标识已存在" },
          { status: 400 },
        );
      }
    }

    // 准备更新数据
    const updateData: any = {};
    if (body.name !== undefined) updateData.name = body.name;
    if (body.slug !== undefined) updateData.slug = body.slug;
    if (body.description !== undefined) updateData.description = body.description;
    if (body.type !== undefined) updateData.type = body.type;
    if (body.icon !== undefined) updateData.icon = body.icon;
    if (body.order !== undefined) updateData.order = Number(body.order);
    if (body.isActive !== undefined) updateData.isActive = Boolean(body.isActive);

    const category = await prisma.productCategory.update({
      where: { id: body.id },
      data: updateData,
    });

    return NextResponse.json({
      success: true,
      data: category,
      message: "产品分类更新成功",
    });
  } catch (error) {
    console.error("更新产品分类失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "更新产品分类失败",
      },
      { status: 500 },
    );
  }
}

// DELETE /api/products/categories - 删除产品分类
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { success: false, message: "分类ID不能为空" },
        { status: 400 },
      );
    }

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 检查分类是否存在
    const existingCategory = await prisma.productCategory.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    if (!existingCategory) {
      return NextResponse.json(
        { success: false, message: "分类不存在" },
        { status: 404 },
      );
    }

    // 检查是否有相关产品
    if (existingCategory._count.products > 0) {
      return NextResponse.json(
        { success: false, message: "该分类下存在产品，无法删除" },
        { status: 400 },
      );
    }

    await prisma.productCategory.delete({
      where: { id },
    });

    return NextResponse.json({
      success: true,
      message: "产品分类删除成功",
    });
  } catch (error) {
    console.error("删除产品分类失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "删除产品分类失败",
      },
      { status: 500 },
    );
  }
}

