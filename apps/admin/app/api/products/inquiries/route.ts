import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, InquiryStatus } from "@workspace/database";
import { AUTH_COOKIE_NAME } from "@/lib/auth/types";
import { cookies } from "next/headers";
import { AdminAuthService } from "@workspace/database/services";

const prisma = new PrismaClient();
const adminAuthService = new AdminAuthService(prisma);

// GET /api/products/inquiries - 获取产品询价列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 解析查询参数
    const query = {
      productId: searchParams.get("productId") || undefined,
      status: searchParams.get("status") as InquiryStatus | undefined,
      assignedToId: searchParams.get("assignedToId") || undefined,
      search: searchParams.get("search") || undefined,
      page: searchParams.get("page") ? parseInt(searchParams.get("page")!) : 1,
      limit: searchParams.get("limit")
        ? parseInt(searchParams.get("limit")!)
        : 20,
      sortBy: (searchParams.get("sortBy") || "createdAt") as
        | "createdAt"
        | "updatedAt"
        | "lastContactAt"
        | "nextFollowUpAt",
      sortOrder: (searchParams.get("sortOrder") || "desc") as "asc" | "desc",
    };

    // 构建 where 条件
    const where: any = {};

    if (query.productId) {
      where.productId = query.productId;
    }

    if (query.status) {
      where.status = query.status;
    }

    if (query.assignedToId) {
      where.assignedToId = query.assignedToId;
    }

    if (query.search) {
      where.OR = [
        { contactName: { contains: query.search, mode: "insensitive" } },
        { contactPhone: { contains: query.search, mode: "insensitive" } },
        { contactEmail: { contains: query.search, mode: "insensitive" } },
        { organization: { contains: query.search, mode: "insensitive" } },
        { product: { name: { contains: query.search, mode: "insensitive" } } },
      ];
    }

    // 获取总数
    const total = await prisma.productInquiry.count({ where });

    // 计算分页
    const totalPages = Math.ceil(total / query.limit);
    const skip = (query.page - 1) * query.limit;

    // 获取询价列表
    const inquiries = await prisma.productInquiry.findMany({
      where,
      skip,
      take: query.limit,
      orderBy: {
        [query.sortBy]: query.sortOrder,
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            code: true,
            type: true,
            price: true,
            priceUnit: true,
          },
        },
        tenant: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
      },
    });

    // 转换为前端期望的格式
    const transformedData = inquiries.map((inquiry) => ({
      id: inquiry.id,
      productId: inquiry.productId,
      product: {
        ...inquiry.product,
        price: inquiry.product.price?.toNumber(),
      },
      tenantId: inquiry.tenantId,
      tenant: inquiry.tenant,
      userId: inquiry.userId,
      user: inquiry.user,
      contactName: inquiry.contactName,
      contactPhone: inquiry.contactPhone,
      contactEmail: inquiry.contactEmail,
      organization: inquiry.organization,
      participants: inquiry.participants,
      expectedDate: inquiry.expectedDate,
      budget: inquiry.budget,
      requirements: inquiry.requirements,
      message: inquiry.message,
      status: inquiry.status,
      assignedToId: inquiry.assignedToId,
      assignedTo: inquiry.assignedTo,
      followUpNotes: inquiry.followUpNotes,
      lastContactAt: inquiry.lastContactAt,
      nextFollowUpAt: inquiry.nextFollowUpAt,
      createdAt: inquiry.createdAt,
      updatedAt: inquiry.updatedAt,
    }));

    return NextResponse.json({
      success: true,
      data: transformedData,
      total,
      page: query.page,
      limit: query.limit,
      totalPages,
    });
  } catch (error) {
    console.error("获取询价列表失败:", error);
    return NextResponse.json(
      { success: false, message: "获取询价列表失败" },
      { status: 500 },
    );
  }
}

// POST /api/products/inquiries - 创建询价记录（主要用于管理员手动添加）
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 验证必需字段
    if (!body.productId || !body.contactName || !body.contactPhone) {
      return NextResponse.json(
        { success: false, message: "产品、联系人姓名和电话为必填项" },
        { status: 400 },
      );
    }

    // 验证产品是否存在
    const product = await prisma.product.findUnique({
      where: { id: body.productId },
    });

    if (!product) {
      return NextResponse.json(
        { success: false, message: "产品不存在" },
        { status: 404 },
      );
    }

    const inquiryData = {
      productId: body.productId,
      tenantId: body.tenantId || null,
      userId: body.userId || null,
      contactName: body.contactName,
      contactPhone: body.contactPhone,
      contactEmail: body.contactEmail || null,
      organization: body.organization || null,
      participants: body.participants ? Number(body.participants) : null,
      expectedDate: body.expectedDate ? new Date(body.expectedDate) : null,
      budget: body.budget || null,
      requirements: body.requirements || null,
      message: body.message || null,
      status: (body.status as InquiryStatus) || InquiryStatus.PENDING,
      assignedToId: body.assignedToId || null,
      followUpNotes: body.followUpNotes || null,
      lastContactAt: body.lastContactAt ? new Date(body.lastContactAt) : null,
      nextFollowUpAt: body.nextFollowUpAt ? new Date(body.nextFollowUpAt) : null,
    };

    const inquiry = await prisma.productInquiry.create({
      data: inquiryData,
      include: {
        product: {
          select: {
            id: true,
            name: true,
            code: true,
            type: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
      },
    });

    return NextResponse.json(
      {
        success: true,
        data: inquiry,
        message: "询价记录创建成功",
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("创建询价记录失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "创建询价记录失败",
      },
      { status: 500 },
    );
  }
}

// PATCH /api/products/inquiries - 批量更新询价记录
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 批量分配处理人
    if (body.action === "assign" && body.ids && body.assignedToId) {
      const result = await prisma.productInquiry.updateMany({
        where: {
          id: {
            in: body.ids,
          },
        },
        data: {
          assignedToId: body.assignedToId,
          status: InquiryStatus.CONTACTING,
        },
      });

      return NextResponse.json({
        success: true,
        data: { count: result.count },
        message: `成功分配 ${result.count} 条询价记录`,
      });
    }

    // 批量更新状态
    if (body.action === "updateStatus" && body.ids && body.status) {
      const result = await prisma.productInquiry.updateMany({
        where: {
          id: {
            in: body.ids,
          },
        },
        data: {
          status: body.status as InquiryStatus,
        },
      });

      return NextResponse.json({
        success: true,
        data: { count: result.count },
        message: `成功更新 ${result.count} 条询价记录状态`,
      });
    }

    return NextResponse.json(
      { success: false, message: "无效的操作" },
      { status: 400 },
    );
  } catch (error) {
    console.error("批量操作失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "批量操作失败",
      },
      { status: 500 },
    );
  }
}

