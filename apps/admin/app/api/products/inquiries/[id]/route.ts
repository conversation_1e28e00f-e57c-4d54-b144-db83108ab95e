import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, InquiryStatus } from "@workspace/database";
import { AUTH_COOKIE_NAME } from "@/lib/auth/types";
import { cookies } from "next/headers";
import { AdminAuthService } from "@workspace/database/services";

const prisma = new PrismaClient();
const adminAuthService = new AdminAuthService(prisma);

// GET /api/products/inquiries/[id] - 获取单个询价详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    const inquiry = await prisma.productInquiry.findUnique({
      where: { id: params.id },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            code: true,
            type: true,
            price: true,
            priceUnit: true,
            duration: true,
            location: true,
            startDate: true,
            endDate: true,
            capacity: true,
            minParticipants: true,
            images: true,
          },
        },
        tenant: {
          select: {
            id: true,
            name: true,
            type: true,
            contactName: true,
            contactPhone: true,
            contactEmail: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            avatar: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            username: true,
            email: true,
          },
        },
      },
    });

    if (!inquiry) {
      return NextResponse.json(
        { success: false, message: "询价记录不存在" },
        { status: 404 },
      );
    }

    // 转换数据格式
    const transformedData = {
      id: inquiry.id,
      productId: inquiry.productId,
      product: {
        ...inquiry.product,
        price: inquiry.product.price?.toNumber(),
      },
      tenantId: inquiry.tenantId,
      tenant: inquiry.tenant,
      userId: inquiry.userId,
      user: inquiry.user,
      contactName: inquiry.contactName,
      contactPhone: inquiry.contactPhone,
      contactEmail: inquiry.contactEmail,
      organization: inquiry.organization,
      participants: inquiry.participants,
      expectedDate: inquiry.expectedDate,
      budget: inquiry.budget,
      requirements: inquiry.requirements,
      message: inquiry.message,
      status: inquiry.status,
      assignedToId: inquiry.assignedToId,
      assignedTo: inquiry.assignedTo,
      followUpNotes: inquiry.followUpNotes,
      lastContactAt: inquiry.lastContactAt,
      nextFollowUpAt: inquiry.nextFollowUpAt,
      createdAt: inquiry.createdAt,
      updatedAt: inquiry.updatedAt,
    };

    return NextResponse.json({
      success: true,
      data: transformedData,
    });
  } catch (error) {
    console.error("获取询价详情失败:", error);
    return NextResponse.json(
      { success: false, message: "获取询价详情失败" },
      { status: 500 },
    );
  }
}

// PUT /api/products/inquiries/[id] - 更新询价记录
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 检查询价记录是否存在
    const existingInquiry = await prisma.productInquiry.findUnique({
      where: { id: params.id },
    });

    if (!existingInquiry) {
      return NextResponse.json(
        { success: false, message: "询价记录不存在" },
        { status: 404 },
      );
    }

    // 准备更新数据
    const updateData: any = {};

    if (body.contactName !== undefined) updateData.contactName = body.contactName;
    if (body.contactPhone !== undefined) updateData.contactPhone = body.contactPhone;
    if (body.contactEmail !== undefined) updateData.contactEmail = body.contactEmail;
    if (body.organization !== undefined) updateData.organization = body.organization;
    if (body.participants !== undefined) updateData.participants = body.participants ? Number(body.participants) : null;
    if (body.expectedDate !== undefined) updateData.expectedDate = body.expectedDate ? new Date(body.expectedDate) : null;
    if (body.budget !== undefined) updateData.budget = body.budget;
    if (body.requirements !== undefined) updateData.requirements = body.requirements;
    if (body.message !== undefined) updateData.message = body.message;
    if (body.status !== undefined) updateData.status = body.status as InquiryStatus;
    if (body.assignedToId !== undefined) updateData.assignedToId = body.assignedToId;
    if (body.followUpNotes !== undefined) updateData.followUpNotes = body.followUpNotes;
    if (body.lastContactAt !== undefined) updateData.lastContactAt = body.lastContactAt ? new Date(body.lastContactAt) : null;
    if (body.nextFollowUpAt !== undefined) updateData.nextFollowUpAt = body.nextFollowUpAt ? new Date(body.nextFollowUpAt) : null;

    const inquiry = await prisma.productInquiry.update({
      where: { id: params.id },
      data: updateData,
      include: {
        product: {
          select: {
            id: true,
            name: true,
            code: true,
            type: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: inquiry,
      message: "询价记录更新成功",
    });
  } catch (error) {
    console.error("更新询价记录失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "更新询价记录失败",
      },
      { status: 500 },
    );
  }
}

// DELETE /api/products/inquiries/[id] - 删除询价记录
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 检查询价记录是否存在
    const existingInquiry = await prisma.productInquiry.findUnique({
      where: { id: params.id },
    });

    if (!existingInquiry) {
      return NextResponse.json(
        { success: false, message: "询价记录不存在" },
        { status: 404 },
      );
    }

    await prisma.productInquiry.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      success: true,
      message: "询价记录删除成功",
    });
  } catch (error) {
    console.error("删除询价记录失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "删除询价记录失败",
      },
      { status: 500 },
    );
  }
}

