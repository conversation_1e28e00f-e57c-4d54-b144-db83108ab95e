import { NextRequest, NextResponse } from "next/server";
import {
  PrismaClient,
  ProductType,
  ProductStatus,
  InquiryStatus,
} from "@workspace/database";
import { AUTH_COOKIE_NAME } from "@/lib/auth/types";
import { cookies } from "next/headers";
import { AdminAuthService } from "@workspace/database/services";

const prisma = new PrismaClient();
const adminAuthService = new AdminAuthService(prisma);

// GET /api/products - 获取产品列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 解析查询参数
    const query = {
      categoryId: searchParams.get("categoryId") || undefined,
      type: searchParams.get("type") as ProductType | undefined,
      status: searchParams.get("status") as ProductStatus | undefined,
      partnerId: searchParams.get("partnerId") || undefined,
      search: searchParams.get("search") || undefined,
      page: searchParams.get("page") ? parseInt(searchParams.get("page")!) : 1,
      limit: searchParams.get("limit")
        ? parseInt(searchParams.get("limit")!)
        : 20,
      sortBy: (searchParams.get("sortBy") || "updatedAt") as
        | "createdAt"
        | "updatedAt"
        | "name"
        | "priority",
      sortOrder: (searchParams.get("sortOrder") || "desc") as "asc" | "desc",
    };

    // 构建 where 条件
    const where: any = {};

    if (query.categoryId) {
      where.categoryId = query.categoryId;
    }

    if (query.type) {
      where.type = query.type;
    }

    if (query.status) {
      where.status = query.status;
    }

    if (query.partnerId) {
      where.partnerId = query.partnerId;
    }

    if (query.search) {
      where.OR = [
        { name: { contains: query.search, mode: "insensitive" } },
        { code: { contains: query.search, mode: "insensitive" } },
        { description: { contains: query.search, mode: "insensitive" } },
      ];
    }

    // 获取总数
    const total = await prisma.product.count({ where });

    // 计算分页
    const totalPages = Math.ceil(total / query.limit);
    const skip = (query.page - 1) * query.limit;

    // 获取产品列表
    const products = await prisma.product.findMany({
      where,
      skip,
      take: query.limit,
      orderBy: {
        [query.sortBy]: query.sortOrder,
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        partner: {
          select: {
            id: true,
            name: true,
            logo: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
        _count: {
          select: {
            inquiries: true,
          },
        },
      },
    });

    // 转换为前端期望的格式
    const transformedData = products.map((product) => ({
      id: product.id,
      name: product.name,
      code: product.code,
      type: product.type,
      categoryId: product.categoryId,
      categoryName: product.category?.name || "未分类",
      categoryType: product.category?.type,
      description: product.description,
      features: product.features,
      highlights: product.highlights,
      images: product.images,
      brochureUrl: product.brochureUrl,
      price: product.price?.toNumber(),
      priceUnit: product.priceUnit,
      priceNote: product.priceNote,
      duration: product.duration,
      location: product.location,
      startDate: product.startDate,
      endDate: product.endDate,
      capacity: product.capacity,
      minParticipants: product.minParticipants,
      targetAudience: product.targetAudience,
      ageRange: product.ageRange,
      gradeRange: product.gradeRange,
      partnerId: product.partnerId,
      partnerName: product.partner?.name,
      partnerLogo: product.partner?.logo,
      status: product.status,
      priority: product.priority,
      metaTitle: product.metaTitle,
      metaDescription: product.metaDescription,
      metaKeywords: product.metaKeywords,
      publishedAt: product.publishedAt,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      createdBy: product.createdBy,
      inquiryCount: product._count.inquiries,
    }));

    return NextResponse.json({
      success: true,
      data: transformedData,
      total,
      page: query.page,
      limit: query.limit,
      totalPages,
    });
  } catch (error) {
    console.error("获取产品列表失败:", error);
    return NextResponse.json(
      { success: false, message: "获取产品列表失败" },
      { status: 500 },
    );
  }
}

// POST /api/products - 创建产品
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 验证必需字段
    if (!body.name || !body.type || !body.categoryId || !body.description) {
      return NextResponse.json(
        { success: false, message: "产品名称、类型、分类和描述为必填项" },
        { status: 400 },
      );
    }

    // 检查产品编码是否已存在
    if (body.code) {
      const existingProduct = await prisma.product.findUnique({
        where: { code: body.code },
      });
      if (existingProduct) {
        return NextResponse.json(
          { success: false, message: "产品编码已存在" },
          { status: 400 },
        );
      }
    }

    // 创建产品数据
    const productData = {
      name: body.name,
      code: body.code || `PROD_${Date.now()}`, // 如果没有提供编码，自动生成
      type: body.type as ProductType,
      categoryId: body.categoryId,
      description: body.description,
      features: body.features || [],
      highlights: body.highlights || null,
      images: body.images || [],
      brochureUrl: body.brochureUrl || null,
      price: body.price ? Number(body.price) : null,
      priceUnit: body.priceUnit || null,
      priceNote: body.priceNote || null,
      duration: body.duration || null,
      location: body.location || null,
      startDate: body.startDate ? new Date(body.startDate) : null,
      endDate: body.endDate ? new Date(body.endDate) : null,
      capacity: body.capacity ? Number(body.capacity) : null,
      minParticipants: body.minParticipants ? Number(body.minParticipants) : null,
      targetAudience: body.targetAudience || [],
      ageRange: body.ageRange || null,
      gradeRange: body.gradeRange || null,
      partnerId: body.partnerId || null,
      status: (body.status as ProductStatus) || ProductStatus.DRAFT,
      priority: body.priority ? Number(body.priority) : 0,
      metaTitle: body.metaTitle || null,
      metaDescription: body.metaDescription || null,
      metaKeywords: body.metaKeywords || [],
      createdById: user.id,
    };

    const product = await prisma.product.create({
      data: productData,
      include: {
        category: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        partner: {
          select: {
            id: true,
            name: true,
            logo: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
      },
    });

    return NextResponse.json(
      {
        success: true,
        data: product,
        message: "产品创建成功",
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("创建产品失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "创建产品失败",
      },
      { status: 500 },
    );
  }
}

// PATCH /api/products - 批量更新产品
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 批量更新状态
    if (body.action === "updateStatus" && body.ids && body.status) {
      const result = await prisma.product.updateMany({
        where: {
          id: {
            in: body.ids,
          },
        },
        data: {
          status: body.status as ProductStatus,
          ...(body.status === ProductStatus.ACTIVE && {
            publishedAt: new Date(),
          }),
        },
      });

      return NextResponse.json({
        success: true,
        data: { count: result.count },
        message: `成功更新 ${result.count} 个产品状态`,
      });
    }

    // 批量删除
    if (body.action === "delete" && body.ids) {
      const result = await prisma.product.deleteMany({
        where: {
          id: {
            in: body.ids,
          },
        },
      });

      return NextResponse.json({
        success: true,
        data: { count: result.count },
        message: `成功删除 ${result.count} 个产品`,
      });
    }

    return NextResponse.json(
      { success: false, message: "无效的操作" },
      { status: 400 },
    );
  } catch (error) {
    console.error("批量操作失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "批量操作失败",
      },
      { status: 500 },
    );
  }
}

