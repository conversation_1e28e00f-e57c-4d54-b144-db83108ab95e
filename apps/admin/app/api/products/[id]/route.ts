import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, ProductStatus } from "@workspace/database";
import { AUTH_COOKIE_NAME } from "@/lib/auth/types";
import { cookies } from "next/headers";
import { AdminAuthService } from "@workspace/database/services";

const prisma = new PrismaClient();
const adminAuthService = new AdminAuthService(prisma);

// GET /api/products/[id] - 获取单个产品详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    const product = await prisma.product.findUnique({
      where: { id: params.id },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
            type: true,
          },
        },
        partner: {
          select: {
            id: true,
            name: true,
            code: true,
            logo: true,
            contactName: true,
            contactPhone: true,
            contactEmail: true,
            cooperationType: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
        _count: {
          select: {
            inquiries: true,
          },
        },
      },
    });

    if (!product) {
      return NextResponse.json(
        { success: false, message: "产品不存在" },
        { status: 404 },
      );
    }

    // 转换数据格式
    const transformedData = {
      id: product.id,
      name: product.name,
      code: product.code,
      type: product.type,
      categoryId: product.categoryId,
      category: product.category,
      description: product.description,
      features: product.features,
      highlights: product.highlights,
      images: product.images,
      brochureUrl: product.brochureUrl,
      price: product.price?.toNumber(),
      priceUnit: product.priceUnit,
      priceNote: product.priceNote,
      duration: product.duration,
      location: product.location,
      startDate: product.startDate,
      endDate: product.endDate,
      capacity: product.capacity,
      minParticipants: product.minParticipants,
      targetAudience: product.targetAudience,
      ageRange: product.ageRange,
      gradeRange: product.gradeRange,
      partnerId: product.partnerId,
      partner: product.partner,
      status: product.status,
      priority: product.priority,
      metaTitle: product.metaTitle,
      metaDescription: product.metaDescription,
      metaKeywords: product.metaKeywords,
      publishedAt: product.publishedAt,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      createdBy: product.createdBy,
      inquiryCount: product._count.inquiries,
    };

    return NextResponse.json({
      success: true,
      data: transformedData,
    });
  } catch (error) {
    console.error("获取产品详情失败:", error);
    return NextResponse.json(
      { success: false, message: "获取产品详情失败" },
      { status: 500 },
    );
  }
}

// PUT /api/products/[id] - 更新产品
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 检查产品是否存在
    const existingProduct = await prisma.product.findUnique({
      where: { id: params.id },
    });

    if (!existingProduct) {
      return NextResponse.json(
        { success: false, message: "产品不存在" },
        { status: 404 },
      );
    }

    // 检查产品编码是否已被其他产品使用
    if (body.code && body.code !== existingProduct.code) {
      const duplicateProduct = await prisma.product.findFirst({
        where: {
          code: body.code,
          id: { not: params.id },
        },
      });
      if (duplicateProduct) {
        return NextResponse.json(
          { success: false, message: "产品编码已存在" },
          { status: 400 },
        );
      }
    }

    // 准备更新数据
    const updateData: any = {};

    if (body.name !== undefined) updateData.name = body.name;
    if (body.code !== undefined) updateData.code = body.code;
    if (body.type !== undefined) updateData.type = body.type;
    if (body.categoryId !== undefined) updateData.categoryId = body.categoryId;
    if (body.description !== undefined) updateData.description = body.description;
    if (body.features !== undefined) updateData.features = body.features;
    if (body.highlights !== undefined) updateData.highlights = body.highlights;
    if (body.images !== undefined) updateData.images = body.images;
    if (body.brochureUrl !== undefined) updateData.brochureUrl = body.brochureUrl;
    if (body.price !== undefined) updateData.price = body.price ? Number(body.price) : null;
    if (body.priceUnit !== undefined) updateData.priceUnit = body.priceUnit;
    if (body.priceNote !== undefined) updateData.priceNote = body.priceNote;
    if (body.duration !== undefined) updateData.duration = body.duration;
    if (body.location !== undefined) updateData.location = body.location;
    if (body.startDate !== undefined) updateData.startDate = body.startDate ? new Date(body.startDate) : null;
    if (body.endDate !== undefined) updateData.endDate = body.endDate ? new Date(body.endDate) : null;
    if (body.capacity !== undefined) updateData.capacity = body.capacity ? Number(body.capacity) : null;
    if (body.minParticipants !== undefined) updateData.minParticipants = body.minParticipants ? Number(body.minParticipants) : null;
    if (body.targetAudience !== undefined) updateData.targetAudience = body.targetAudience;
    if (body.ageRange !== undefined) updateData.ageRange = body.ageRange;
    if (body.gradeRange !== undefined) updateData.gradeRange = body.gradeRange;
    if (body.partnerId !== undefined) updateData.partnerId = body.partnerId;
    if (body.priority !== undefined) updateData.priority = Number(body.priority);
    if (body.metaTitle !== undefined) updateData.metaTitle = body.metaTitle;
    if (body.metaDescription !== undefined) updateData.metaDescription = body.metaDescription;
    if (body.metaKeywords !== undefined) updateData.metaKeywords = body.metaKeywords;

    // 状态更新时的特殊处理
    if (body.status !== undefined) {
      updateData.status = body.status as ProductStatus;
      
      // 如果状态变为上架，设置发布时间
      if (body.status === ProductStatus.ACTIVE && !existingProduct.publishedAt) {
        updateData.publishedAt = new Date();
      }
    }

    const product = await prisma.product.update({
      where: { id: params.id },
      data: updateData,
      include: {
        category: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        partner: {
          select: {
            id: true,
            name: true,
            logo: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: product,
      message: "产品更新成功",
    });
  } catch (error) {
    console.error("更新产品失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "更新产品失败",
      },
      { status: 500 },
    );
  }
}

// DELETE /api/products/[id] - 删除产品
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 检查产品是否存在
    const existingProduct = await prisma.product.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            inquiries: true,
          },
        },
      },
    });

    if (!existingProduct) {
      return NextResponse.json(
        { success: false, message: "产品不存在" },
        { status: 404 },
      );
    }

    // 检查是否有相关的询价记录
    if (existingProduct._count.inquiries > 0) {
      return NextResponse.json(
        { success: false, message: "该产品存在询价记录，无法删除" },
        { status: 400 },
      );
    }

    await prisma.product.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      success: true,
      message: "产品删除成功",
    });
  } catch (error) {
    console.error("删除产品失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "删除产品失败",
      },
      { status: 500 },
    );
  }
}

