import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@workspace/database";
import { z } from "zod";

// 创建合作方数据验证
const createPartnerSchema = z.object({
  name: z.string().min(1, "合作方名称不能为空"),
  code: z.string().min(1, "合作方代码不能为空"),
  logo: z.string().optional(),
  contactName: z.string().min(1, "联系人姓名不能为空"),
  contactPhone: z.string().min(1, "联系电话不能为空"),
  contactEmail: z.string().email("请输入有效的邮箱地址"),
  address: z.string().optional(),
  cooperationType: z.array(z.string()).min(1, "请选择至少一种合作类型"),
  description: z.string().optional(),
});

// 更新合作方数据验证
const updatePartnerSchema = createPartnerSchema.partial();

// GET - 获取合作方列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // 解析查询参数
    const search = searchParams.get("search") || undefined;
    const isActive = searchParams.get("isActive");
    const cooperationType = searchParams.get("cooperationType") || undefined;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const sortBy = searchParams.get("sortBy") || "createdAt";
    const sortOrder = searchParams.get("sortOrder") || "desc";
    const simple = searchParams.get("simple") === "true";

    // 构建查询条件
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { code: { contains: search, mode: "insensitive" } },
        { contactName: { contains: search, mode: "insensitive" } },
        { contactPhone: { contains: search } },
        { contactEmail: { contains: search, mode: "insensitive" } },
      ];
    }

    if (isActive !== null) {
      where.isActive = isActive === "true";
    }

    if (cooperationType) {
      where.cooperationType = {
        has: cooperationType,
      };
    }

    // 如果是简单查询，只返回基本信息
    if (simple) {
      const partners = await prisma.partner.findMany({
        where,
        select: {
          id: true,
          name: true,
          code: true,
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
      });

      return NextResponse.json({
        success: true,
        data: partners,
      });
    }

    // 计算分页
    const skip = (page - 1) * limit;

    // 查询数据
    const [partners, total] = await Promise.all([
      prisma.partner.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        include: {
          _count: {
            select: {
              products: true,
            },
          },
        },
      }),
      prisma.partner.count({ where }),
    ]);

    // 格式化数据
    const formattedPartners = partners.map((partner) => ({
      ...partner,
      productCount: partner._count.products,
      _count: undefined,
    }));

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      data: formattedPartners,
      total,
      page,
      limit,
      totalPages,
    });
  } catch (error) {
    console.error("获取合作方列表失败:", error);
    return NextResponse.json(
      { error: "获取合作方列表失败" },
      { status: 500 }
    );
  }
}

// POST - 创建合作方
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 数据验证
    const validatedData = createPartnerSchema.parse(body);

    // 检查代码是否已存在
    const existingPartner = await prisma.partner.findUnique({
      where: { code: validatedData.code },
    });

    if (existingPartner) {
      return NextResponse.json(
        { error: "合作方代码已存在" },
        { status: 400 }
      );
    }

    // 创建合作方
    const partner = await prisma.partner.create({
      data: {
        ...validatedData,
        isActive: true,
      },
    });

    return NextResponse.json(partner, { status: 201 });
  } catch (error) {
    console.error("创建合作方失败:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "数据验证失败", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "创建合作方失败" },
      { status: 500 }
    );
  }
}

