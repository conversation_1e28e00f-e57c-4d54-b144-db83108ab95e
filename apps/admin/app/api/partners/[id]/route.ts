import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@workspace/database";
import { z } from "zod";

// 更新合作方数据验证
const updatePartnerSchema = z.object({
  name: z.string().min(1, "合作方名称不能为空").optional(),
  code: z.string().min(1, "合作方代码不能为空").optional(),
  logo: z.string().optional(),
  contactName: z.string().min(1, "联系人姓名不能为空").optional(),
  contactPhone: z.string().min(1, "联系电话不能为空").optional(),
  contactEmail: z.string().email("请输入有效的邮箱地址").optional(),
  address: z.string().optional(),
  cooperationType: z
    .array(z.string())
    .min(1, "请选择至少一种合作类型")
    .optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
});

// GET - 获取单个合作方详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;

    const partner = await prisma.partner.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
        products: {
          select: {
            id: true,
            name: true,
            type: true,
            status: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 10, // 只返回最近的10个产品
        },
      },
    });

    if (!partner) {
      return NextResponse.json({ error: "合作方不存在" }, { status: 404 });
    }

    // 格式化数据
    const formattedPartner = {
      ...partner,
      productCount: partner._count.products,
      _count: undefined,
    };

    return NextResponse.json(formattedPartner);
  } catch (error) {
    console.error("获取合作方详情失败:", error);
    return NextResponse.json({ error: "获取合作方详情失败" }, { status: 500 });
  }
}

// PUT - 更新合作方
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const body = await request.json();

    // 数据验证
    const validatedData = updatePartnerSchema.parse(body);

    // 检查合作方是否存在
    const existingPartner = await prisma.partner.findUnique({
      where: { id },
    });

    if (!existingPartner) {
      return NextResponse.json({ error: "合作方不存在" }, { status: 404 });
    }

    // 如果要更新代码，检查是否与其他合作方冲突
    if (validatedData.code && validatedData.code !== existingPartner.code) {
      const codeExists = await prisma.partner.findFirst({
        where: {
          code: validatedData.code,
          id: { not: id },
        },
      });

      if (codeExists) {
        return NextResponse.json(
          { error: "合作方代码已存在" },
          { status: 400 },
        );
      }
    }

    // 更新合作方
    const updatedPartner = await prisma.partner.update({
      where: { id },
      data: validatedData,
    });

    return NextResponse.json(updatedPartner);
  } catch (error) {
    console.error("更新合作方失败:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "数据验证失败", details: error.errors },
        { status: 400 },
      );
    }

    return NextResponse.json({ error: "更新合作方失败" }, { status: 500 });
  }
}

// DELETE - 删除合作方
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;

    // 检查合作方是否存在
    const existingPartner = await prisma.partner.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    if (!existingPartner) {
      return NextResponse.json({ error: "合作方不存在" }, { status: 404 });
    }

    // 检查是否有关联的产品
    if (existingPartner._count.products > 0) {
      return NextResponse.json(
        { error: "该合作方下还有关联的产品，无法删除" },
        { status: 400 },
      );
    }

    // 删除合作方
    await prisma.partner.delete({
      where: { id },
    });

    return NextResponse.json({
      success: true,
      message: "合作方删除成功",
    });
  } catch (error) {
    console.error("删除合作方失败:", error);
    return NextResponse.json({ error: "删除合作方失败" }, { status: 500 });
  }
}
