/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest } from "next/server";
import { v4 as uuidv4 } from "uuid";
import COS from "cos-nodejs-sdk-v5";
import { writeFile, unlink } from "fs/promises";
import { join } from "path";
import { tmpdir } from "os";

// 创建 COS 实例
const cos = new COS({
  SecretId: process.env.COS_SECRET_ID,
  SecretKey: process.env.COS_SECRET_KEY,
});

// 上传文件到 COS
async function uploadToCOS(
  file: Buffer,
  fileName: string,
  folder: string = "topic",
): Promise<{ url: string }> {
  return new Promise((resolve, reject) => {
    cos.putObject(
      {
        Bucket: "baihuabei-1258807705",
        Region: "ap-chongqing",
        Key: `${folder}/${fileName}`,
        Body: file,
        // SliceSize: 1024 * 1024 * 10, // 10MB 分片
      },
      (err: any, data: any) => {
        if (err) {
          reject(err);
        } else {
          const url = `https://${data.Location}`;
          resolve({ url });
        }
      },
    );
  });
}

// 大文件上传到 COS
async function uploadLargeFileToCOS(
  filePath: string,
  fileName: string,
  folder: string = "topic",
): Promise<{ url: string }> {
  return new Promise((resolve, reject) => {
    cos.uploadFile(
      {
        Bucket: "baihuabei-1258807705",
        Region: "ap-chongqing",
        Key: `${folder}/${fileName}`,
        FilePath: filePath,
        SliceSize: 1024 * 1024 * 10, // 10MB 分片
      },
      async (err: any, data: any) => {
        if (err) {
          reject(err);
        } else {
          const url = `https://${data.Location}`;
          resolve({ url });
        }
      },
    );
  });
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const folder = (formData.get("folder") as string) || "topic";

    if (!file) {
      return new Response(JSON.stringify({ error: "No file provided" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    // 获取文件扩展名
    const ext = file.name.split(".").pop();
    const fileName = `${uuidv4()}.${ext}`;

    // 对于小文件（< 10MB），直接使用 putObject
    if (file.size < 10 * 1024 * 1024) {
      const buffer = await file.arrayBuffer();
      const result = await uploadToCOS(Buffer.from(buffer), fileName, folder);

      return new Response(JSON.stringify(result), {
        headers: { "Content-Type": "application/json" },
      });
    }
    // 对于大文件，使用临时文件和 uploadFile 方法
    else {
      const tempDir = tmpdir();
      const tempFilePath = join(tempDir, fileName);

      try {
        // 将文件写入临时目录
        const buffer = await file.arrayBuffer();
        await writeFile(tempFilePath, Buffer.from(buffer));

        // 上传文件
        const result = await uploadLargeFileToCOS(
          tempFilePath,
          fileName,
          folder,
        );

        // 清理临时文件
        await unlink(tempFilePath);

        return new Response(JSON.stringify(result), {
          headers: { "Content-Type": "application/json" },
        });
      } catch (error) {
        // 确保清理临时文件
        try {
          await unlink(tempFilePath);
        } catch (e) {
          console.error("Error deleting temp file:", e);
        }
        throw error;
      }
    }
  } catch (error) {
    console.error("Upload error:", error);
    return new Response(JSON.stringify({ error: "Upload failed" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

// 用于处理视频等大文件的端点
export async function PUT(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const folder = (formData.get("folder") as string) || "topic";

    if (!file) {
      return new Response(JSON.stringify({ error: "No file provided" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    const ext = file.name.split(".").pop();
    const fileName = `${uuidv4()}.${ext}`;
    const tempDir = tmpdir();
    const tempFilePath = join(tempDir, fileName);

    try {
      // 将文件写入临时目录
      const buffer = await file.arrayBuffer();
      await writeFile(tempFilePath, Buffer.from(buffer));

      // 使用 uploadFile 方法上传大文件
      const result = await uploadLargeFileToCOS(tempFilePath, fileName, folder);

      // 清理临时文件
      await unlink(tempFilePath);

      return new Response(JSON.stringify(result), {
        headers: { "Content-Type": "application/json" },
      });
    } catch (error) {
      // 确保清理临时文件
      try {
        await unlink(tempFilePath);
      } catch (e) {
        console.error("Error deleting temp file:", e);
      }
      throw error;
    }
  } catch (error) {
    console.error("Upload error:", error);
    return new Response(JSON.stringify({ error: "Upload failed" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}
