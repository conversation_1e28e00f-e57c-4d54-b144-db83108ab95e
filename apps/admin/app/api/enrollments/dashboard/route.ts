import { NextRequest, NextResponse } from "next/server";
import {
  DashboardService,
  AdminAuthService,
} from "@workspace/database/services";
import { prisma } from "@workspace/database";
import { AUTH_COOKIE_NAME } from "@/lib/auth/types";
import { cookies } from "next/headers";

const dashboardService = new DashboardService(prisma);
const adminAuthService = new AdminAuthService(prisma);

// GET /api/enrollments/dashboard - 获取报名 Dashboard 数据
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 获取查询参数
    const trendDays = searchParams.get("trendDays")
      ? parseInt(searchParams.get("trendDays")!)
      : 30;
    const topCoursesLimit = searchParams.get("topCoursesLimit")
      ? parseInt(searchParams.get("topCoursesLimit")!)
      : 10;

    // 获取 Dashboard 数据
    const dashboardData = await dashboardService.getDashboardData(
      trendDays,
      topCoursesLimit,
    );

    return NextResponse.json({
      success: true,
      data: dashboardData,
    });
  } catch (error) {
    console.error("获取 Dashboard 数据失败:", error);
    return NextResponse.json(
      { success: false, message: "获取 Dashboard 数据失败" },
      { status: 500 },
    );
  }
}
