import { NextRequest, NextResponse } from "next/server";
import {
  DashboardService,
  AdminAuthService,
} from "@workspace/database/services";
import { prisma } from "@workspace/database";
import { AUTH_COOKIE_NAME } from "@/lib/auth/types";
import { cookies } from "next/headers";

const dashboardService = new DashboardService(prisma);
const adminAuthService = new AdminAuthService(prisma);

// 验证管理员权限的通用函数
async function validateAdminAuth() {
  const cookieStore = await cookies();
  const token = cookieStore.get(AUTH_COOKIE_NAME);
  if (!token?.value) {
    return { error: { success: false, message: "未找到认证信息" }, status: 401 };
  }

  const { user } = await adminAuthService.validateSession(token.value);
  if (!user) {
    return { error: { success: false, message: "认证失败" }, status: 401 };
  }

  return { user };
}

// GET /api/enrollments/[id] - 获取单个报名详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;

    // 验证管理员权限
    const authResult = await validateAdminAuth();
    if (authResult.error) {
      return NextResponse.json(authResult.error, { status: authResult.status });
    }

    // 获取报名详情
    const enrollmentDetail = await dashboardService.getEnrollmentDetail(id);

    if (!enrollmentDetail) {
      return NextResponse.json(
        { success: false, message: "报名记录不存在" },
        { status: 404 },
      );
    }

    return NextResponse.json({
      success: true,
      data: enrollmentDetail,
    });
  } catch (error) {
    console.error("获取报名详情失败:", error);
    return NextResponse.json(
      { success: false, message: "获取报名详情失败" },
      { status: 500 },
    );
  }
}

// PUT /api/enrollments/[id] - 更新报名信息
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;

    // 验证管理员权限
    const authResult = await validateAdminAuth();
    if (authResult.error) {
      return NextResponse.json(authResult.error, { status: authResult.status });
    }

    // 解析请求体
    const body = await request.json();
    const { isPaid, paidAmount, paidAt, progress, notes } = body;

    // 检查报名记录是否存在
    const existingEnrollment = await prisma.courseEnrollment.findUnique({
      where: { id },
    });

    if (!existingEnrollment) {
      return NextResponse.json(
        { success: false, message: "报名记录不存在" },
        { status: 404 },
      );
    }

    // 构建更新数据
    const updateData: any = {};
    if (isPaid !== undefined) updateData.isPaid = isPaid;
    if (paidAmount !== undefined) updateData.paidAmount = paidAmount ? parseFloat(paidAmount) : null;
    if (paidAt !== undefined) updateData.paidAt = paidAt ? new Date(paidAt) : null;
    if (progress !== undefined) updateData.progress = parseFloat(progress);
    if (notes !== undefined) updateData.notes = notes;

    // 如果设置为已支付但没有支付时间，设置当前时间
    if (isPaid && !paidAt && !existingEnrollment.paidAt) {
      updateData.paidAt = new Date();
    }

    // 更新报名记录
    const updatedEnrollment = await prisma.courseEnrollment.update({
      where: { id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
            username: true,
          },
        },
        course: {
          select: {
            id: true,
            title: true,
            cover: true,
            price: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedEnrollment,
      message: "报名信息更新成功",
    });
  } catch (error) {
    console.error("更新报名失败:", error);
    return NextResponse.json(
      { success: false, message: "更新报名失败" },
      { status: 500 },
    );
  }
}

// DELETE /api/enrollments/[id] - 删除报名记录
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;

    // 验证管理员权限
    const authResult = await validateAdminAuth();
    if (authResult.error) {
      return NextResponse.json(authResult.error, { status: authResult.status });
    }

    // 检查报名记录是否存在
    const existingEnrollment = await prisma.courseEnrollment.findUnique({
      where: { id },
      include: {
        course: {
          select: { id: true },
        },
      },
    });

    if (!existingEnrollment) {
      return NextResponse.json(
        { success: false, message: "报名记录不存在" },
        { status: 404 },
      );
    }

    // 删除报名记录
    await prisma.courseEnrollment.delete({
      where: { id },
    });

    // 更新课程报名计数
    await prisma.course.update({
      where: { id: existingEnrollment.courseId },
      data: {
        enrollCount: {
          decrement: 1,
        },
      },
    });

    return NextResponse.json({
      success: true,
      message: "报名记录删除成功",
    });
  } catch (error) {
    console.error("删除报名失败:", error);
    return NextResponse.json(
      { success: false, message: "删除报名失败" },
      { status: 500 },
    );
  }
}
