import { NextRequest, NextResponse } from "next/server";
import {
  EnrollmentsService,
  AdminAuthService,
} from "@workspace/database/services";
import { prisma } from "@workspace/database";
import { AUTH_COOKIE_NAME } from "@/lib/auth/types";
import { cookies } from "next/headers";

const enrollmentsService = new EnrollmentsService(prisma);
const adminAuthService = new AdminAuthService(prisma);

// GET /api/enrollments - 获取报名列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user } = await adminAuthService.validateSession(token.value);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 获取查询参数
    const courseId = searchParams.get("courseId");
    const userId = searchParams.get("userId");
    const search = searchParams.get("search");
    const isPaid = searchParams.get("isPaid");
    const isCompleted = searchParams.get("isCompleted");
    const dateFrom = searchParams.get("dateFrom");
    const dateTo = searchParams.get("dateTo");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const sortBy = searchParams.get("sortBy") || "enrolledAt";
    const sortOrder = searchParams.get("sortOrder") || "desc";

    // 构建查询条件
    const where: any = {};
    
    if (courseId) where.courseId = courseId;
    if (userId) where.userId = userId;
    if (isPaid !== null && isPaid !== undefined) where.isPaid = isPaid === "true";
    if (isCompleted === "true") {
      where.completedAt = { not: null };
    } else if (isCompleted === "false") {
      where.completedAt = null;
    }

    // 日期范围过滤
    if (dateFrom && dateTo) {
      where.enrolledAt = {
        gte: new Date(dateFrom),
        lte: new Date(dateTo),
      };
    }

    // 搜索条件
    if (search) {
      where.OR = [
        {
          user: {
            OR: [
              { name: { contains: search, mode: "insensitive" } },
              { email: { contains: search, mode: "insensitive" } },
              { username: { contains: search, mode: "insensitive" } },
            ],
          },
        },
        {
          course: {
            title: { contains: search, mode: "insensitive" },
          },
        },
      ];
    }

    // 构建排序条件
    const orderBy: any = {};
    if (sortBy === "enrolledAt") {
      orderBy.enrolledAt = sortOrder;
    } else if (sortBy === "progress") {
      orderBy.progress = sortOrder;
    } else if (sortBy === "paidAmount") {
      orderBy.paidAmount = sortOrder;
    } else {
      orderBy.enrolledAt = "desc";
    }

    // 执行查询
    const [enrollments, total] = await Promise.all([
      prisma.courseEnrollment.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
              username: true,
            },
          },
          course: {
            select: {
              id: true,
              title: true,
              cover: true,
              price: true,
              isFree: true,
              instructorName: true,
            },
          },
        },
        orderBy,
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.courseEnrollment.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: enrollments,
      total,
      page,
      limit,
      totalPages,
    });
  } catch (error) {
    console.error("获取报名列表失败:", error);
    return NextResponse.json(
      { success: false, message: "获取报名列表失败" },
      { status: 500 },
    );
  }
}

// POST /api/enrollments - 创建新报名
export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME);
    if (!token?.value) {
      return NextResponse.json(
        { success: false, message: "未找到认证信息" },
        { status: 401 },
      );
    }

    const { user: adminUser } = await adminAuthService.validateSession(token.value);
    if (!adminUser) {
      return NextResponse.json(
        { success: false, message: "认证失败" },
        { status: 401 },
      );
    }

    // 解析请求体
    const body = await request.json();
    const { userId, courseId, isPaid, paidAmount, paidAt } = body;

    // 验证必填字段
    if (!userId || !courseId) {
      return NextResponse.json(
        { success: false, message: "userId和courseId为必填字段" },
        { status: 400 },
      );
    }

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.json(
        { success: false, message: "用户不存在" },
        { status: 404 },
      );
    }

    // 检查课程是否存在
    const course = await prisma.course.findUnique({
      where: { id: courseId },
    });

    if (!course) {
      return NextResponse.json(
        { success: false, message: "课程不存在" },
        { status: 404 },
      );
    }

    // 检查是否已经报名
    const existingEnrollment = await prisma.courseEnrollment.findUnique({
      where: {
        courseId_userId: {
          courseId,
          userId,
        },
      },
    });

    if (existingEnrollment) {
      return NextResponse.json(
        { success: false, message: "用户已经报名该课程" },
        { status: 409 },
      );
    }

    // 创建报名记录
    const enrollment = await prisma.courseEnrollment.create({
      data: {
        userId,
        courseId,
        isPaid: isPaid || false,
        paidAmount: paidAmount ? parseFloat(paidAmount) : null,
        paidAt: paidAt ? new Date(paidAt) : null,
        enrolledAt: new Date(),
        progress: 0,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
            username: true,
          },
        },
        course: {
          select: {
            id: true,
            title: true,
            cover: true,
            price: true,
          },
        },
      },
    });

    // 更新课程报名计数
    await prisma.course.update({
      where: { id: courseId },
      data: {
        enrollCount: {
          increment: 1,
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: enrollment,
      message: "报名创建成功",
    });
  } catch (error) {
    console.error("创建报名失败:", error);
    return NextResponse.json(
      { success: false, message: "创建报名失败" },
      { status: 500 },
    );
  }
}

