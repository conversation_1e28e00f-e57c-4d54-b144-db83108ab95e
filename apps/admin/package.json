{"name": "admin", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@workspace/database": "workspace:*", "@workspace/ioredis": "workspace:*", "@workspace/ui": "workspace:*", "babel-plugin-react-compiler": "19.1.0-rc.2", "bcryptjs": "^3.0.2", "cos-nodejs-sdk-v5": "2.16.0-beta.3", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "framer-motion": "^12.16.0", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.475.0", "next": "^15.3.3", "next-themes": "^0.4.4", "openai": "^5.5.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "sonner": "^2.0.5", "uuid": "^11.1.0", "zod": "^3.25.63"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.8", "@types/crypto-js": "^4.2.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "tailwindcss": "^4.1.8", "typescript": "^5.7.3"}}