#!/bin/bash

# Docker 管理脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker 未运行，请先启动 Docker"
        exit 1
    fi
}

# 启动服务
start() {
    print_message "启动 Redis 和 PostgreSQL 服务..."
    check_docker
    docker-compose up -d postgres redis
    print_message "服务启动完成！"
    print_message "PostgreSQL: localhost:5432"
    print_message "Redis: localhost:6379"
}

# 启动所有服务（包括管理工具）
start_all() {
    print_message "启动所有服务（包括管理工具）..."
    check_docker
    docker-compose --profile tools up -d
    print_message "所有服务启动完成！"
    print_message "PostgreSQL: localhost:5432"
    print_message "Redis: localhost:6379"
    print_message "pgAdmin: http://localhost:8080"
    print_message "Redis Commander: http://localhost:8081"
}

# 停止服务
stop() {
    print_message "停止服务..."
    docker-compose down
    print_message "服务已停止"
}

# 重启服务
restart() {
    print_message "重启服务..."
    stop
    start
}

# 查看服务状态
status() {
    print_message "服务状态："
    docker-compose ps
}

# 查看日志
logs() {
    if [ -z "$2" ]; then
        docker-compose logs -f
    else
        docker-compose logs -f "$2"
    fi
}

# 清理数据
clean() {
    print_warning "这将删除所有数据，确定要继续吗？(y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_message "停止服务并清理数据..."
        docker-compose down -v
        docker volume prune -f
        print_message "数据清理完成"
    else
        print_message "操作已取消"
    fi
}

# 进入 PostgreSQL 命令行
psql() {
    print_message "连接到 PostgreSQL..."
    docker-compose exec postgres psql -U qyqm_user -d qyqm_teachers
}

# 进入 Redis 命令行
redis_cli() {
    print_message "连接到 Redis..."
    docker-compose exec redis redis-cli -a qyqm_redis_password
}

# 备份数据库
backup() {
    print_message "备份数据库..."
    timestamp=$(date +%Y%m%d_%H%M%S)
    backup_file="backup/postgres_backup_${timestamp}.sql"
    mkdir -p backup
    docker-compose exec postgres pg_dump -U qyqm_user qyqm_teachers > "$backup_file"
    print_message "数据库备份完成: $backup_file"
}

# 显示帮助信息
help() {
    echo "Docker 管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start      启动 Redis 和 PostgreSQL 服务"
    echo "  start-all  启动所有服务（包括管理工具）"
    echo "  stop       停止所有服务"
    echo "  restart    重启服务"
    echo "  status     查看服务状态"
    echo "  logs       查看所有服务日志"
    echo "  logs <服务名>  查看指定服务日志"
    echo "  clean      清理所有数据（危险操作）"
    echo "  psql       进入 PostgreSQL 命令行"
    echo "  redis-cli  进入 Redis 命令行"
    echo "  backup     备份数据库"
    echo "  help       显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start"
    echo "  $0 logs postgres"
    echo "  $0 psql"
}

# 主逻辑
case "${1:-help}" in
    start)
        start
        ;;
    start-all)
        start_all
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs "$@"
        ;;
    clean)
        clean
        ;;
    psql)
        psql
        ;;
    redis-cli)
        redis_cli
        ;;
    backup)
        backup
        ;;
    help|--help|-h)
        help
        ;;
    *)
        print_error "未知命令: $1"
        help
        exit 1
        ;;
esac
